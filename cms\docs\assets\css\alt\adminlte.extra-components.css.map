{"version": 3, "file": "adminlte.extra-components.css", "sources": ["../../../build/scss/AdminLTE-extra-components.scss", "../../../node_modules/bootstrap/scss/_functions.scss", "../../../build/scss/_bootstrap-variables.scss", "../../../node_modules/bootstrap/scss/_mixins.scss", "../../../node_modules/bootstrap/scss/vendor/_rfs.scss", "../../../node_modules/bootstrap/scss/mixins/_deprecate.scss", "../../../node_modules/bootstrap/scss/mixins/_breakpoints.scss", "../../../node_modules/bootstrap/scss/mixins/_hover.scss", "../../../node_modules/bootstrap/scss/mixins/_image.scss", "../../../node_modules/bootstrap/scss/mixins/_badge.scss", "../../../node_modules/bootstrap/scss/mixins/_resize.scss", "../../../node_modules/bootstrap/scss/mixins/_screen-reader.scss", "../../../node_modules/bootstrap/scss/mixins/_size.scss", "../../../node_modules/bootstrap/scss/mixins/_reset-text.scss", "../../../node_modules/bootstrap/scss/mixins/_text-emphasis.scss", "../../../node_modules/bootstrap/scss/mixins/_text-hide.scss", "../../../node_modules/bootstrap/scss/mixins/_text-truncate.scss", "../../../node_modules/bootstrap/scss/mixins/_visibility.scss", "../../../node_modules/bootstrap/scss/mixins/_alert.scss", "../../../node_modules/bootstrap/scss/mixins/_buttons.scss", "../../../node_modules/bootstrap/scss/mixins/_caret.scss", "../../../node_modules/bootstrap/scss/mixins/_pagination.scss", "../../../node_modules/bootstrap/scss/mixins/_lists.scss", "../../../node_modules/bootstrap/scss/mixins/_list-group.scss", "../../../node_modules/bootstrap/scss/mixins/_nav-divider.scss", "../../../node_modules/bootstrap/scss/mixins/_forms.scss", "../../../node_modules/bootstrap/scss/mixins/_table-row.scss", "../../../node_modules/bootstrap/scss/mixins/_background-variant.scss", "../../../node_modules/bootstrap/scss/mixins/_border-radius.scss", "../../../node_modules/bootstrap/scss/mixins/_box-shadow.scss", "../../../node_modules/bootstrap/scss/mixins/_gradients.scss", "../../../node_modules/bootstrap/scss/mixins/_transition.scss", "../../../node_modules/bootstrap/scss/mixins/_clearfix.scss", "../../../node_modules/bootstrap/scss/mixins/_grid-framework.scss", "../../../node_modules/bootstrap/scss/mixins/_grid.scss", "../../../node_modules/bootstrap/scss/mixins/_float.scss", "../../../build/scss/_variables.scss", "../../../build/scss/_mixins.scss", "../../../build/scss/mixins/_cards.scss", "../../../build/scss/mixins/_sidebar.scss", "../../../build/scss/mixins/_navbar.scss", "../../../build/scss/mixins/_accent.scss", "../../../build/scss/mixins/_custom-forms.scss", "../../../build/scss/mixins/_backgrounds.scss", "../../../build/scss/mixins/_direct-chat.scss", "../../../build/scss/mixins/_toasts.scss", "../../../build/scss/mixins/_miscellaneous.scss", "../../../build/scss/parts/_extra-components.scss", "../../../build/scss/_small-box.scss", "../../../build/scss/_info-box.scss", "../../../build/scss/_timeline.scss", "../../../build/scss/_products.scss", "../../../build/scss/_direct-chat.scss", "../../../build/scss/_users-list.scss", "../../../build/scss/_social-widgets.scss"], "sourcesContent": ["/*!\n *   AdminLTE v3.0.5\n *     Only Extra Components\n *   Author: Colorlib\n *   Website: AdminLTE.io <http://adminlte.io>\n *   License: Open source - MIT <http://opensource.org/licenses/MIT>\n */\n// Bootstrap\n// ---------------------------------------------------\n@import '~bootstrap/scss/functions';\n@import 'bootstrap-variables';\n@import '~bootstrap/scss/mixins';\n// @import '~bootstrap/scss/bootstrap';\n\n// Variables and Mixins\n// ---------------------------------------------------\n@import 'variables';\n@import 'mixins';\n\n@import 'parts/extra-components';\n", "// Bootstrap functions\n//\n// Utility mixins and functions for evaluating source code across our variables, maps, and mixins.\n\n// Ascending\n// Used to evaluate Sass maps like our grid breakpoints.\n@mixin _assert-ascending($map, $map-name) {\n  $prev-key: null;\n  $prev-num: null;\n  @each $key, $num in $map {\n    @if $prev-num == null or unit($num) == \"%\" or unit($prev-num) == \"%\" {\n      // Do nothing\n    } @else if not comparable($prev-num, $num) {\n      @warn \"Potentially invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} whose unit makes it incomparable to #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    } @else if $prev-num >= $num {\n      @warn \"Invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} which isn't greater than #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    }\n    $prev-key: $key;\n    $prev-num: $num;\n  }\n}\n\n// Starts at zero\n// Used to ensure the min-width of the lowest breakpoint starts at 0.\n@mixin _assert-starts-at-zero($map, $map-name: \"$grid-breakpoints\") {\n  $values: map-values($map);\n  $first-value: nth($values, 1);\n  @if $first-value != 0 {\n    @warn \"First breakpoint in #{$map-name} must start at 0, but starts at #{$first-value}.\";\n  }\n}\n\n// Replace `$search` with `$replace` in `$string`\n// Used on our SVG icon backgrounds for custom forms.\n//\n// <AUTHOR> Giraudel\n// @param {String} $string - Initial string\n// @param {String} $search - Substring to replace\n// @param {String} $replace ('') - New value\n// @return {String} - Updated string\n@function str-replace($string, $search, $replace: \"\") {\n  $index: str-index($string, $search);\n\n  @if $index {\n    @return str-slice($string, 1, $index - 1) + $replace + str-replace(str-slice($string, $index + str-length($search)), $search, $replace);\n  }\n\n  @return $string;\n}\n\n// See https://codepen.io/kevinweber/pen/dXWoRw\n@function escape-svg($string) {\n  @if str-index($string, \"data:image/svg+xml\") {\n    @each $char, $encoded in $escaped-characters {\n      $string: str-replace($string, $char, $encoded);\n    }\n  }\n\n  @return $string;\n}\n\n// Color contrast\n@function color-yiq($color, $dark: $yiq-text-dark, $light: $yiq-text-light) {\n  $r: red($color);\n  $g: green($color);\n  $b: blue($color);\n\n  $yiq: (($r * 299) + ($g * 587) + ($b * 114)) / 1000;\n\n  @if ($yiq >= $yiq-contrasted-threshold) {\n    @return $dark;\n  } @else {\n    @return $light;\n  }\n}\n\n// Retrieve color Sass maps\n@function color($key: \"blue\") {\n  @return map-get($colors, $key);\n}\n\n@function theme-color($key: \"primary\") {\n  @return map-get($theme-colors, $key);\n}\n\n@function gray($key: \"100\") {\n  @return map-get($grays, $key);\n}\n\n// Request a theme color level\n@function theme-color-level($color-name: \"primary\", $level: 0) {\n  $color: theme-color($color-name);\n  $color-base: if($level > 0, $black, $white);\n  $level: abs($level);\n\n  @return mix($color-base, $color, $level * $theme-color-interval);\n}\n\n// Return valid calc\n@function add($value1, $value2, $return-calc: true) {\n  @if $value1 == null {\n    @return $value2;\n  }\n\n  @if $value2 == null {\n    @return $value1;\n  }\n\n  @if type-of($value1) == number and type-of($value2) == number and comparable($value1, $value2) {\n    @return $value1 + $value2;\n  }\n\n  @return if($return-calc == true, calc(#{$value1} + #{$value2}), $value1 + unquote(\" + \") + $value2);\n}\n\n@function subtract($value1, $value2, $return-calc: true) {\n  @if $value1 == null and $value2 == null {\n    @return null;\n  }\n\n  @if $value1 == null {\n    @return -$value2;\n  }\n\n  @if $value2 == null {\n    @return $value1;\n  }\n\n  @if type-of($value1) == number and type-of($value2) == number and comparable($value1, $value2) {\n    @return $value1 - $value2;\n  }\n\n  @return if($return-calc == true, calc(#{$value1} - #{$value2}), $value1 + unquote(\" - \") + $value2);\n}\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n\n//\n// Color system\n//\n\n// stylelint-disable\n$white:    #ffffff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n\n$grays: () !default;\n$grays: map-merge((\n        \"100\": $gray-100,\n        \"200\": $gray-200,\n        \"300\": $gray-300,\n        \"400\": $gray-400,\n        \"500\": $gray-500,\n        \"600\": $gray-600,\n        \"700\": $gray-700,\n        \"800\": $gray-800,\n        \"900\": $gray-900\n), $grays);\n\n$blue:    #007bff !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #e83e8c !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #28a745 !default;\n$teal:    #20c997 !default;\n$cyan:    #17a2b8 !default;\n\n$colors: () !default;\n$colors: map-merge((\n        \"blue\":       $blue,\n        \"indigo\":     $indigo,\n        \"purple\":     $purple,\n        \"pink\":       $pink,\n        \"red\":        $red,\n        \"orange\":     $orange,\n        \"yellow\":     $yellow,\n        \"green\":      $green,\n        \"teal\":       $teal,\n        \"cyan\":       $cyan,\n        \"white\":      $white,\n        \"gray\":       $gray-600,\n        \"gray-dark\":  $gray-800\n), $colors);\n\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-800 !default;\n\n$theme-colors: () !default;\n$theme-colors: map-merge((\n        \"primary\":    $primary,\n        \"secondary\":  $secondary,\n        \"success\":    $success,\n        \"info\":       $info,\n        \"warning\":    $warning,\n        \"danger\":     $danger,\n        \"light\":      $light,\n        \"dark\":       $dark\n), $theme-colors);\n// stylelint-enable\n\n// Set a specific jump point for requesting color jumps\n$theme-color-interval:      8% !default;\n\n// The yiq lightness value that determines when the lightness of color changes from \"dark\" to \"light\". Acceptable values are between 0 and 255.\n$yiq-contrasted-threshold: 150 !default;\n\n// Customize the light and dark text colors for use in our YIQ color contrast function.\n$yiq-text-dark: #1F2D3D !default;\n$yiq-text-light: $white !default;\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                                true !default;\n$enable-rounded:                              true !default;\n$enable-shadows:                              true !default;\n$enable-gradients:                            false !default;\n$enable-transitions:                          true !default;\n$enable-prefers-reduced-motion-media-query:   true !default;\n$enable-hover-media-query:                    false !default; // Deprecated, no longer affects any compiled CSS\n$enable-grid-classes:                         true !default;\n$enable-pointer-cursor-for-buttons:           true !default;\n$enable-print-styles:                         true !default;\n$enable-responsive-font-sizes:                false !default;\n$enable-validation-icons:                     true !default;\n$enable-deprecation-messages:                 true !default;\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// stylelint-disable\n$spacer: 1rem !default;\n$spacers: () !default;\n$spacers: map-merge((\n        0: 0,\n        1: ($spacer * .25),\n        2: ($spacer * .5),\n        3: $spacer,\n        4: ($spacer * 1.5),\n        5: ($spacer * 3)\n), $spacers);\n\n// This variable affects the `.h-*` and `.w-*` classes.\n$sizes: () !default;\n$sizes: map-merge((\n        25: 25%,\n        50: 50%,\n        75: 75%,\n        100: 100%\n), $sizes);\n// stylelint-enable\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   $white !default;\n$body-color:                $gray-900 !default;\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                theme-color(\"primary\") !default;\n$link-decoration:           none !default;\n$link-hover-color:          darken($link-color, 15%) !default;\n$link-hover-decoration:     none !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n$grid-breakpoints: (\n        xs: 0,\n        sm: 576px,\n        md: 768px,\n        lg: 992px,\n        xl: 1200px\n) !default;\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints);\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n$container-max-widths: (\n        sm: 540px,\n        md: 720px,\n        lg: 960px,\n        xl: 1140px\n) !default;\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           15px !default;\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n$line-height-lg:              1.5 !default;\n$line-height-sm:              1.5 !default;\n\n$border-width:                1px !default;\n$border-color:                $gray-300 !default;\n\n$border-radius:               .25rem !default;\n$border-radius-lg:            .3rem !default;\n$border-radius-sm:            .2rem !default;\n\n$component-active-color:      $white !default;\n$component-active-bg:         theme-color(\"primary\") !default;\n\n$caret-width:                 .3em !default;\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n$transition-collapse:         height .35s ease !default;\n\n\n// Fonts\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      \"Source Sans Pro\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n$font-family-base:            $font-family-sans-serif !default;\n// stylelint-enable value-keyword-case\n\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-lg:                ($font-size-base * 1.25) !default;\n$font-size-sm:                ($font-size-base * .875) !default;\n\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-bold:            700 !default;\n\n$font-weight-base:            $font-weight-normal !default;\n$line-height-base:            1.5 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n\n$headings-margin-bottom:      ($spacer / 2) !default;\n$headings-font-family:        inherit !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              inherit !default;\n\n$display1-size:               6rem !default;\n$display2-size:               5.5rem !default;\n$display3-size:               4.5rem !default;\n$display4-size:               3.5rem !default;\n\n$display1-weight:             300 !default;\n$display2-weight:             300 !default;\n$display3-weight:             300 !default;\n$display4-weight:             300 !default;\n$display-line-height:         $headings-line-height !default;\n\n$lead-font-size:              ($font-size-base * 1.25) !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             80% !default;\n\n$text-muted:                  $gray-600 !default;\n\n$blockquote-small-color:      $gray-600 !default;\n$blockquote-font-size:        ($font-size-base * 1.25) !default;\n\n$hr-border-color:             rgba($black, .1) !default;\n$hr-border-width:             $border-width !default;\n\n$mark-padding:                .2em !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$kbd-box-shadow:              inset 0 -.1rem 0 rgba($black, .25) !default;\n$nested-kbd-font-weight:      $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-bg:                     #fcf8e3 !default;\n\n$hr-margin-y:                 $spacer !default;\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n$table-cell-padding:          .75rem !default;\n$table-cell-padding-sm:       .3rem !default;\n\n$table-bg:                    transparent !default;\n$table-accent-bg:             rgba($black, .05) !default;\n$table-hover-bg:              rgba($black, .075) !default;\n$table-active-bg:             $table-hover-bg !default;\n\n$table-border-width:          $border-width !default;\n$table-border-color:          $gray-300 !default;\n\n$table-head-bg:               $gray-200 !default;\n$table-head-color:            $gray-700 !default;\n\n$table-dark-bg:               $gray-900 !default;\n$table-dark-accent-bg:        rgba($white, .05) !default;\n$table-dark-hover-bg:         rgba($white, .075) !default;\n$table-dark-border-color:     lighten($gray-900, 10%) !default;\n$table-dark-color:            $body-bg !default;\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:       .2rem !default;\n$input-btn-focus-color:       rgba($component-active-bg, .25) !default;\n$input-btn-focus-box-shadow:  0 0 0 $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-line-height-sm:    $line-height-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-line-height-lg:    $line-height-lg !default;\n\n$input-btn-border-width:      $border-width !default;\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-line-height:             $input-btn-line-height !default;\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-line-height-sm:          $input-btn-line-height-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-line-height-lg:          $input-btn-line-height-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              none !default;\n$btn-focus-width:             0 !default;\n$btn-focus-box-shadow:        none !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       none !default;\n\n$btn-link-disabled-color:     $gray-600 !default;\n\n$btn-block-spacing-y:         .5rem !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius !default;\n$btn-border-radius-lg:        $border-radius-lg !default;\n$btn-border-radius-sm:        $border-radius-sm !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n\n// Forms\n\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-line-height-sm:                  $input-btn-line-height-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-line-height-lg:                  $input-btn-line-height-lg !default;\n\n$input-bg:                              $white !default;\n$input-disabled-bg:                     $gray-200 !default;\n\n$input-color:                           $gray-700 !default;\n$input-border-color:                    $gray-400 !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      inset 0 0 0 rgba($black, 0) !default;\n\n$input-border-radius:                   $border-radius !default;\n$input-border-radius-lg:                $border-radius-lg !default;\n$input-border-radius-sm:                $border-radius-sm !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              lighten($component-active-bg, 25%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     0 !default;\n$input-focus-box-shadow:                none !default;\n\n$input-placeholder-color:               lighten($gray-600, 15%) !default;\n\n$input-height-border:                   $input-border-width * 2 !default;\n\n$input-height-inner:                    ($font-size-base * $input-btn-line-height) + ($input-btn-padding-y * 2) !default;\n$input-height-inner-half:               calc(#{$input-line-height * .5em} + #{$input-padding-y}) !default;\n$input-height-inner-quarter:            calc(#{$input-line-height * .25em} + #{$input-padding-y / 2}) !default;\n\n$input-height:                          calc(#{$input-height-inner} + #{$input-height-border}) !default;\n\n$input-height-inner-sm:                 ($font-size-sm * $input-btn-line-height-sm) + ($input-btn-padding-y-sm * 2) !default;\n$input-height-sm:                       calc(#{$input-height-inner-sm} + #{$input-height-border}) !default;\n\n$input-height-inner-lg:                 ($font-size-lg * $input-btn-line-height-lg) + ($input-btn-padding-y-lg * 2) !default;\n$input-height-lg:                       calc(#{$input-height-inner-lg} + #{$input-height-border}) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-text-margin-top:                  .25rem !default;\n\n$form-check-input-gutter:               1.25rem !default;\n$form-check-input-margin-y:             .3rem !default;\n$form-check-input-margin-x:             .25rem !default;\n\n$form-check-inline-margin-x:            .75rem !default;\n$form-check-inline-input-margin-x:      .3125rem !default;\n\n$form-group-margin-bottom:              1rem !default;\n\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  $gray-200 !default;\n$input-group-addon-border-color:        $input-border-color !default;\n\n$custom-control-gutter:                 .5rem !default;\n$custom-control-spacer-x:               1rem !default;\n\n$custom-control-indicator-size:         1rem !default;\n$custom-control-indicator-bg:           $gray-300 !default;\n$custom-control-indicator-bg-size:      50% 50% !default;\n$custom-control-indicator-box-shadow:   inset 0 .25rem .25rem rgba($black, .1) !default;\n\n$custom-control-indicator-disabled-bg:          $gray-200 !default;\n$custom-control-label-disabled-color:           $gray-600 !default;\n\n$custom-control-indicator-checked-color:        $component-active-color !default;\n$custom-control-indicator-checked-bg:           $component-active-bg !default;\n$custom-control-indicator-checked-disabled-bg:  rgba(theme-color(\"primary\"), .5) !default;\n$custom-control-indicator-checked-box-shadow:   none !default;\n\n$custom-control-indicator-focus-box-shadow:     0 0 0 1px $body-bg, $input-btn-focus-box-shadow !default;\n\n$custom-control-indicator-active-color:         $component-active-color !default;\n$custom-control-indicator-active-bg:            lighten($component-active-bg, 35%) !default;\n$custom-control-indicator-active-box-shadow:    none !default;\n\n$custom-checkbox-indicator-border-radius:       $border-radius !default;\n$custom-checkbox-indicator-icon-checked:        str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='#{$custom-control-indicator-checked-color}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$custom-checkbox-indicator-indeterminate-bg:    $component-active-bg !default;\n$custom-checkbox-indicator-indeterminate-color: $custom-control-indicator-checked-color !default;\n$custom-checkbox-indicator-icon-indeterminate:  str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath stroke='#{$custom-checkbox-indicator-indeterminate-color}' d='M0 2h4'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$custom-checkbox-indicator-indeterminate-box-shadow: none !default;\n\n$custom-radio-indicator-border-radius:          50% !default;\n$custom-radio-indicator-icon-checked:           str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='#{$custom-control-indicator-checked-color}'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$custom-select-padding-y:           .375rem !default;\n$custom-select-padding-x:          .75rem !default;\n$custom-select-height:              $input-height !default;\n$custom-select-indicator-padding:   1rem !default; // Extra padding to account for the presence of the background-image based indicator\n$custom-select-line-height:         $input-btn-line-height !default;\n$custom-select-color:               $input-color !default;\n$custom-select-disabled-color:      $gray-600 !default;\n$custom-select-bg:                  $white !default;\n$custom-select-disabled-bg:         $gray-200 !default;\n$custom-select-bg-size:             8px 10px !default; // In pixels because image dimensions\n$custom-select-indicator-color:     $gray-800 !default;\n$custom-select-indicator:           str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='#{$custom-select-indicator-color}' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$custom-select-border-width:        $input-btn-border-width !default;\n$custom-select-border-color:        $input-border-color !default;\n$custom-select-border-radius:       $border-radius !default;\n\n$custom-select-focus-border-color:  $input-focus-border-color !default;\n$custom-select-focus-box-shadow:    none !default;\n\n$custom-select-font-size-sm:        75% !default;\n$custom-select-height-sm:           $input-height-sm !default;\n\n$custom-select-font-size-lg:        125% !default;\n$custom-select-height-lg:           $input-height-lg !default;\n\n$custom-file-height:                $input-height !default;\n$custom-file-focus-border-color:    $input-focus-border-color !default;\n$custom-file-focus-box-shadow:      $custom-select-focus-box-shadow !default;\n\n$custom-file-padding-y:             $input-btn-padding-y !default;\n$custom-file-padding-x:             $input-btn-padding-x !default;\n$custom-file-line-height:           $input-btn-line-height !default;\n$custom-file-color:                 $input-color !default;\n$custom-file-bg:                    $input-bg !default;\n$custom-file-border-width:          $input-btn-border-width !default;\n$custom-file-border-color:          $input-border-color !default;\n$custom-file-border-radius:         $input-border-radius !default;\n$custom-file-box-shadow:            $custom-select-focus-box-shadow !default;\n$custom-file-button-color:          $custom-file-color !default;\n$custom-file-button-bg:             $input-group-addon-bg !default;\n$custom-file-text: (\n        en: \"Browse\"\n) !default;\n\n$custom-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-btn-focus-box-shadow !default;\n\n\n// Form validation\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $small-font-size !default;\n$form-feedback-valid-color:         theme-color(\"success\") !default;\n$form-feedback-invalid-color:       theme-color(\"danger\") !default;\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-bg:                       $white !default;\n$dropdown-border-color:             rgba($black, .15) !default;\n$dropdown-border-radius:            $border-radius !default;\n$dropdown-border-width:             $border-width !default;\n$dropdown-divider-bg:               $gray-200 !default;\n$dropdown-box-shadow:               0 .5rem 1rem rgba($black, .175) !default;\n\n$dropdown-link-color:               $gray-900 !default;\n$dropdown-link-hover-color:         darken($gray-900, 5%) !default;\n$dropdown-link-hover-bg:            $gray-100 !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      $gray-600 !default;\n\n$dropdown-item-padding-y:           .25rem !default;\n$dropdown-item-padding-x:           1rem !default;\n\n$dropdown-header-color:             $gray-600 !default;\n\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-modal-backdrop:             1040 !default;\n$zindex-modal:                      1050 !default;\n$zindex-popover:                    1060 !default;\n$zindex-tooltip:                    1070 !default;\n\n// Navs\n\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-disabled-color:           $gray-600 !default;\n\n$nav-tabs-border-color:             $gray-300 !default;\n$nav-tabs-border-width:             $border-width !default;\n$nav-tabs-border-radius:            $border-radius !default;\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        $gray-700 !default;\n$nav-tabs-link-active-bg:           $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           $border-radius !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n\n// Navbar\n\n$navbar-padding-y:                  ($spacer / 2) !default;\n$navbar-padding-x:                  ($spacer / 2) !default;\n\n$navbar-nav-link-padding-x:         1rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   ($font-size-base * $line-height-base + $nav-link-padding-y * 2) !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) / 2 !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n\n$navbar-dark-color:                 rgba($white, .75) !default;\n$navbar-dark-hover-color:           rgba($white, 1) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg:       str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#{$navbar-dark-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n\n$navbar-light-color:                rgba($black, .5) !default;\n$navbar-light-hover-color:          rgba($black, .7) !default;\n$navbar-light-active-color:         rgba($black, .9) !default;\n$navbar-light-disabled-color:       rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg:      str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#{$navbar-light-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n// Pagination\n\n$pagination-padding-y:              .5rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n$pagination-line-height:            1.25 !default;\n\n$pagination-color:                  $link-color !default;\n$pagination-bg:                     $white !default;\n$pagination-border-width:           $border-width !default;\n$pagination-border-color:           $gray-300 !default;\n\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n\n$pagination-hover-color:            $link-hover-color !default;\n$pagination-hover-bg:               $gray-200 !default;\n$pagination-hover-border-color:     $gray-300 !default;\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $pagination-active-bg !default;\n\n$pagination-disabled-color:         $gray-600 !default;\n$pagination-disabled-bg:            $white !default;\n$pagination-disabled-border-color:  $gray-300 !default;\n\n\n// Jumbotron\n\n$jumbotron-padding:                 2rem !default;\n$jumbotron-bg:                      $gray-200 !default;\n\n\n// Cards\n\n$card-spacer-y:                     .75rem !default;\n$card-spacer-x:                     1.25rem !default;\n$card-border-width:                 0 !default; //$border-width !default;\n$card-border-radius:                $border-radius !default;\n$card-border-color:                 rgba($black, .125) !default;\n$card-inner-border-radius:          calc(#{$card-border-radius} - #{$card-border-width}) !default;\n$card-cap-bg:                       rgba($black, .03) !default;\n$card-bg:                           $white !default;\n\n$card-img-overlay-padding:          1.25rem !default;\n\n$card-group-margin:                 ($grid-gutter-width / 2) !default;\n$card-deck-margin:                  $card-group-margin !default;\n\n$card-columns-count:                3 !default;\n$card-columns-gap:                  1.25rem !default;\n$card-columns-margin:               $card-spacer-y !default;\n\n\n// Tooltips\n\n$tooltip-font-size:           $font-size-sm !default;\n$tooltip-max-width:           200px !default;\n$tooltip-color:               $white !default;\n$tooltip-bg:                  $black !default;\n$tooltip-border-radius:        $border-radius !default;\n$tooltip-opacity:             .9 !default;\n$tooltip-padding-y:           .25rem !default;\n$tooltip-padding-x:           .5rem !default;\n$tooltip-margin:              0 !default;\n\n$tooltip-arrow-width:         .8rem !default;\n$tooltip-arrow-height:        .4rem !default;\n$tooltip-arrow-color:         $tooltip-bg !default;\n\n// Form tooltips must come after regular tooltips\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   $line-height-base !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n\n// Popovers\n\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        $white !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              $border-width !default;\n$popover-border-color:              rgba($black, .2) !default;\n$popover-border-radius:             $border-radius-lg !default;\n$popover-box-shadow:                0 .25rem .5rem rgba($black, .2) !default;\n\n$popover-header-bg:                 darken($popover-bg, 3%) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          .75rem !default;\n\n$popover-body-color:                $body-color !default;\n$popover-body-padding-y:            $popover-header-padding-y !default;\n$popover-body-padding-x:            $popover-header-padding-x !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n$popover-arrow-color:               $popover-bg !default;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05) !default;\n\n\n// Badges\n\n$badge-font-size:                   75% !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-padding-y:                   .25em !default;\n$badge-padding-x:                   .4em !default;\n$badge-border-radius:               $border-radius !default;\n\n$badge-pill-padding-x:              .6em !default;\n// Use a higher than normal value to ensure completely rounded edges when\n// customizing padding or font-size on labels.\n$badge-pill-border-radius:          10rem !default;\n\n\n// Modals\n\n// Padding applied to the modal body\n$modal-inner-padding:         1rem !default;\n\n$modal-dialog-margin:         .5rem !default;\n$modal-dialog-margin-y-sm-up: 1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-bg:               $white !default;\n$modal-content-border-color:     rgba($black, .2) !default;\n$modal-content-border-width:     $border-width !default;\n$modal-content-box-shadow-xs:    0 .25rem .5rem rgba($black, .5) !default;\n$modal-content-box-shadow-sm-up: 0 .5rem 1rem rgba($black, .5) !default;\n\n$modal-backdrop-bg:           $black !default;\n$modal-backdrop-opacity:      .5 !default;\n$modal-header-border-color:   $gray-200 !default;\n$modal-footer-border-color:   $modal-header-border-color !default;\n$modal-header-border-width:   $modal-content-border-width !default;\n$modal-footer-border-width:   $modal-header-border-width !default;\n$modal-header-padding:        1rem !default;\n\n$modal-lg:                          800px !default;\n$modal-md:                          500px !default;\n$modal-sm:                          300px !default;\n\n$modal-transition:                  transform .3s ease-out !default;\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n$alert-padding-y:                   .75rem !default;\n$alert-padding-x:                   1.25rem !default;\n$alert-margin-bottom:               1rem !default;\n$alert-border-radius:               $border-radius !default;\n$alert-link-font-weight:            $font-weight-bold !default;\n$alert-border-width:                $border-width !default;\n\n$alert-bg-level:                    -10 !default;\n$alert-border-level:                -9 !default;\n$alert-color-level:                 6 !default;\n\n\n// Progress bars\n\n$progress-height:                   1rem !default;\n$progress-font-size:                ($font-size-base * .75) !default;\n$progress-bg:                       $gray-200 !default;\n$progress-border-radius:            $border-radius !default;\n$progress-box-shadow:               inset 0 .1rem .1rem rgba($black, .1) !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   theme-color(\"primary\") !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n\n// List group\n\n$list-group-bg:                     $white !default;\n$list-group-border-color:           rgba($black, .125) !default;\n$list-group-border-width:           $border-width !default;\n$list-group-border-radius:          $border-radius !default;\n\n$list-group-item-padding-y:         .75rem !default;\n$list-group-item-padding-x:         1.25rem !default;\n\n$list-group-hover-bg:               $gray-100 !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         $gray-600 !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           $gray-700 !default;\n$list-group-action-hover-color:     $list-group-action-color !default;\n\n$list-group-action-active-color:    $body-color !default;\n$list-group-action-active-bg:       $gray-200 !default;\n\n\n// Image thumbnails\n\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      $body-bg !default;\n$thumbnail-border-width:            $border-width !default;\n$thumbnail-border-color:            $gray-300 !default;\n$thumbnail-border-radius:           $border-radius !default;\n$thumbnail-box-shadow:              0 1px 2px rgba($black, .075) !default;\n\n\n// Figures\n\n$figure-caption-font-size:          90% !default;\n$figure-caption-color:              $gray-600 !default;\n\n\n// Breadcrumbs\n\n$breadcrumb-padding-y:              .75rem !default;\n$breadcrumb-padding-x:              1rem !default;\n$breadcrumb-item-padding:           .5rem !default;\n\n$breadcrumb-margin-bottom:          1rem !default;\n\n$breadcrumb-bg:                     $gray-200 !default;\n$breadcrumb-divider-color:          $gray-600 !default;\n$breadcrumb-active-color:           $gray-600 !default;\n$breadcrumb-divider:                \"/\" !default;\n\n\n// Carousel\n\n$carousel-control-color:            $white !default;\n$carousel-control-width:            15% !default;\n$carousel-control-opacity:          .5 !default;\n\n$carousel-indicator-width:          30px !default;\n$carousel-indicator-height:         3px !default;\n$carousel-indicator-spacer:         3px !default;\n$carousel-indicator-active-bg:      $white !default;\n\n$carousel-caption-width:            70% !default;\n$carousel-caption-color:            $white !default;\n\n$carousel-control-icon-width:       20px !default;\n\n$carousel-control-prev-icon-bg:     str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3E%3Cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$carousel-control-next-icon-bg:     str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3E%3Cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$carousel-transition:               transform .6s ease !default;\n\n\n// Close\n\n$close-font-size:                   $font-size-base * 1.5 !default;\n$close-font-weight:                 $font-weight-bold !default;\n$close-color:                       $black !default;\n$close-text-shadow:                 0 1px 0 $white !default;\n\n// Code\n\n$code-font-size:                    87.5% !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .2rem !default;\n$kbd-padding-x:                     .4rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         $white !default;\n$kbd-bg:                            $gray-900 !default;\n\n$pre-color:                         $gray-900 !default;\n$pre-scrollable-max-height:         340px !default;\n\n\n// Printing\n$print-page-size:                   a3 !default;\n$print-body-min-width:              map-get($grid-breakpoints, \"lg\") !default;\n", "// Toggles\n//\n// Used in conjunction with global variables to enable certain theme features.\n\n// Vendor\n@import \"vendor/rfs\";\n\n// Deprecate\n@import \"mixins/deprecate\";\n\n// Utilities\n@import \"mixins/breakpoints\";\n@import \"mixins/hover\";\n@import \"mixins/image\";\n@import \"mixins/badge\";\n@import \"mixins/resize\";\n@import \"mixins/screen-reader\";\n@import \"mixins/size\";\n@import \"mixins/reset-text\";\n@import \"mixins/text-emphasis\";\n@import \"mixins/text-hide\";\n@import \"mixins/text-truncate\";\n@import \"mixins/visibility\";\n\n// Components\n@import \"mixins/alert\";\n@import \"mixins/buttons\";\n@import \"mixins/caret\";\n@import \"mixins/pagination\";\n@import \"mixins/lists\";\n@import \"mixins/list-group\";\n@import \"mixins/nav-divider\";\n@import \"mixins/forms\";\n@import \"mixins/table-row\";\n\n// Skins\n@import \"mixins/background-variant\";\n@import \"mixins/border-radius\";\n@import \"mixins/box-shadow\";\n@import \"mixins/gradients\";\n@import \"mixins/transition\";\n\n// Layout\n@import \"mixins/clearfix\";\n@import \"mixins/grid-framework\";\n@import \"mixins/grid\";\n@import \"mixins/float\";\n", "// stylelint-disable property-blacklist, scss/dollar-variable-default\n\n// SCSS RFS mixin\n//\n// Automated font-resizing\n//\n// See https://github.com/twbs/rfs\n\n// Configuration\n\n// Base font size\n$rfs-base-font-size: 1.25rem !default;\n$rfs-font-size-unit: rem !default;\n\n// Breakpoint at where font-size starts decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n// Resize font-size based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != \"number\" or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-responsive-font-sizes to false\n$enable-responsive-font-sizes: true !default;\n\n// Cache $rfs-base-font-size unit\n$rfs-base-font-size-unit: unit($rfs-base-font-size);\n\n// Remove px-unit from $rfs-base-font-size for calculations\n@if $rfs-base-font-size-unit == \"px\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1);\n}\n@else if $rfs-base-font-size-unit == \"rem\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1 / $rfs-rem-value);\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == \"px\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == \"rem\" or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1 / $rfs-rem-value);\n}\n\n// Responsive font-size mixin\n@mixin rfs($fs, $important: false) {\n  // Cache $fs unit\n  $fs-unit: if(type-of($fs) == \"number\", unit($fs), false);\n\n  // Add !important suffix if needed\n  $rfs-suffix: if($important, \" !important\", \"\");\n\n  // If $fs isn't a number (like inherit) or $fs has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n  @if not $fs-unit or $fs-unit != \"\" and $fs-unit != \"px\" and $fs-unit != \"rem\" or $fs == 0 {\n    font-size: #{$fs}#{$rfs-suffix};\n  }\n  @else {\n    // Variables for storing static and fluid rescaling\n    $rfs-static: null;\n    $rfs-fluid: null;\n\n    // Remove px-unit from $fs for calculations\n    @if $fs-unit == \"px\" {\n      $fs: $fs / ($fs * 0 + 1);\n    }\n    @else if $fs-unit == \"rem\" {\n      $fs: $fs / ($fs * 0 + 1 / $rfs-rem-value);\n    }\n\n    // Set default font-size\n    @if $rfs-font-size-unit == rem {\n      $rfs-static: #{$fs / $rfs-rem-value}rem#{$rfs-suffix};\n    }\n    @else if $rfs-font-size-unit == px {\n      $rfs-static: #{$fs}px#{$rfs-suffix};\n    }\n    @else {\n      @error \"`#{$rfs-font-size-unit}` is not a valid unit for $rfs-font-size-unit. Use `px` or `rem`.\";\n    }\n\n    // Only add media query if font-size is bigger as the minimum font-size\n    // If $rfs-factor == 1, no rescaling will take place\n    @if $fs > $rfs-base-font-size and $enable-responsive-font-sizes {\n      $min-width: null;\n      $variable-unit: null;\n\n      // Calculate minimum font-size for given font-size\n      $fs-min: $rfs-base-font-size + ($fs - $rfs-base-font-size) / $rfs-factor;\n\n      // Calculate difference between given font-size and minimum font-size for given font-size\n      $fs-diff: $fs - $fs-min;\n\n      // Base font-size formatting\n      // No need to check if the unit is valid, because we did that before\n      $min-width: if($rfs-font-size-unit == rem, #{$fs-min / $rfs-rem-value}rem, #{$fs-min}px);\n\n      // If two-dimensional, use smallest of screen width and height\n      $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n      // Calculate the variable width between 0 and $rfs-breakpoint\n      $variable-width: #{$fs-diff * 100 / $rfs-breakpoint}#{$variable-unit};\n\n      // Set the calculated font-size.\n      $rfs-fluid: calc(#{$min-width} + #{$variable-width}) #{$rfs-suffix};\n    }\n\n    // Rendering\n    @if $rfs-fluid == null {\n      // Only render static font-size if no fluid font-size is available\n      font-size: $rfs-static;\n    }\n    @else {\n      $mq-value: null;\n\n      // RFS breakpoint formatting\n      @if $rfs-breakpoint-unit == em or $rfs-breakpoint-unit == rem {\n        $mq-value: #{$rfs-breakpoint / $rfs-rem-value}#{$rfs-breakpoint-unit};\n      }\n      @else if $rfs-breakpoint-unit == px {\n        $mq-value: #{$rfs-breakpoint}px;\n      }\n      @else {\n        @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n      }\n\n      @if $rfs-class == \"disable\" {\n        // Adding an extra class increases specificity,\n        // which prevents the media query to override the font size\n        &,\n        .disable-responsive-font-size &,\n        &.disable-responsive-font-size {\n          font-size: $rfs-static;\n        }\n      }\n      @else {\n        font-size: $rfs-static;\n      }\n\n      @if $rfs-two-dimensional {\n        @media (max-width: #{$mq-value}), (max-height: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n      @else {\n        @media (max-width: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n    }\n  }\n}\n\n// The font-size & responsive-font-size mixin uses RFS to rescale font sizes\n@mixin font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n\n@mixin responsive-font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n", "// Deprecate mixin\n//\n// This mixin can be used to deprecate mixins or functions.\n// `$enable-deprecation-messages` is a global variable, `$ignore-warning` is a variable that can be passed to\n// some deprecated mixins to suppress the warning (for example if the mixin is still be used in the current version of Bootstrap)\n@mixin deprecate($name, $deprecate-version, $remove-version, $ignore-warning: false) {\n  @if ($enable-deprecation-messages != false and $ignore-warning != true) {\n    @warn \"#{$name} has been deprecated as of #{$deprecate-version}. It will be removed entirely in #{$remove-version}.\";\n  }\n}\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n != null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\n// The maximum value is calculated as the minimum of the next one less 0.02px\n// to work around the limitations of `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $next: breakpoint-next($name, $breakpoints);\n  @return if($next, breakpoint-min($next, $breakpoints) - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  $max: breakpoint-max($name, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($name, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "// Hover mixin and `$enable-hover-media-query` are deprecated.\n//\n// Originally added during our alphas and maintained during betas, this mixin was\n// designed to prevent `:hover` stickiness on iOS-an issue where hover styles\n// would persist after initial touch.\n//\n// For backward compatibility, we've kept these mixins and updated them to\n// always return their regular pseudo-classes instead of a shimmed media query.\n//\n// Issue: https://github.com/twbs/bootstrap/issues/25195\n\n@mixin hover() {\n  &:hover { @content; }\n}\n\n@mixin hover-focus() {\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin plain-hover-focus() {\n  &,\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin hover-focus-active() {\n  &:hover,\n  &:focus,\n  &:active {\n    @content;\n  }\n}\n", "// Image Mixins\n// - Responsive image\n// - Retina image\n\n\n// Responsive image\n//\n// Keep images from scaling beyond the width of their parents.\n\n@mixin img-fluid() {\n  // Part 1: Set a maximum relative to the parent\n  max-width: 100%;\n  // Part 2: Override the height to auto, otherwise images will be stretched\n  // when setting a width and height attribute on the img element.\n  height: auto;\n}\n\n\n// Retina image\n//\n// Short retina mixin for setting background-image and -size.\n\n@mixin img-retina($file-1x, $file-2x, $width-1x, $height-1x) {\n  background-image: url($file-1x);\n\n  // Autoprefixer takes care of adding -webkit-min-device-pixel-ratio and -o-min-device-pixel-ratio,\n  // but doesn't convert dppx=>dpi.\n  // There's no such thing as unprefixed min-device-pixel-ratio since it's nonstandard.\n  // Compatibility info: https://caniuse.com/#feat=css-media-resolution\n  @media only screen and (min-resolution: 192dpi), // IE9-11 don't support dppx\n    only screen and (min-resolution: 2dppx) { // Standardized\n    background-image: url($file-2x);\n    background-size: $width-1x $height-1x;\n  }\n  @include deprecate(\"`img-retina()`\", \"v4.3.0\", \"v5\");\n}\n", "@mixin badge-variant($bg) {\n  color: color-yiq($bg);\n  background-color: $bg;\n\n  @at-root a#{&} {\n    @include hover-focus() {\n      color: color-yiq($bg);\n      background-color: darken($bg, 10%);\n    }\n\n    &:focus,\n    &.focus {\n      outline: 0;\n      box-shadow: 0 0 0 $badge-focus-width rgba($bg, .5);\n    }\n  }\n}\n", "// Resize anything\n\n@mixin resizable($direction) {\n  overflow: auto; // Per CSS3 UI, `resize` only applies when `overflow` isn't `visible`\n  resize: $direction; // Options: horizontal, vertical, both\n}\n", "// Only display content to screen readers\n//\n// See: https://a11yproject.com/posts/how-to-hide-content/\n// See: https://hugogiraudel.com/2016/10/13/css-hide-and-seek/\n\n@mixin sr-only() {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px; // Fix for https://github.com/twbs/bootstrap/issues/25686\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n}\n\n// Use in conjunction with .sr-only to only display content when it's focused.\n//\n// Useful for \"Skip to main content\" links; see https://www.w3.org/TR/2013/NOTE-WCAG20-TECHS-20130905/G1\n//\n// Credit: HTML5 Boilerplate\n\n@mixin sr-only-focusable() {\n  &:active,\n  &:focus {\n    position: static;\n    width: auto;\n    height: auto;\n    overflow: visible;\n    clip: auto;\n    white-space: normal;\n  }\n}\n", "// Sizing shortcuts\n\n@mixin size($width, $height: $width) {\n  width: $width;\n  height: $height;\n  @include deprecate(\"`size()`\", \"v4.3.0\", \"v5\");\n}\n", "@mixin reset-text() {\n  font-family: $font-family-base;\n  // We deliberately do NOT reset font-size or word-wrap.\n  font-style: normal;\n  font-weight: $font-weight-normal;\n  line-height: $line-height-base;\n  text-align: left; // Fallback for where `start` is not supported\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  word-spacing: normal;\n  white-space: normal;\n  line-break: auto;\n}\n", "// stylelint-disable declaration-no-important\n\n// Typography\n\n@mixin text-emphasis-variant($parent, $color, $ignore-warning: false) {\n  #{$parent} {\n    color: $color !important;\n  }\n  @if $emphasized-link-hover-darken-percentage != 0 {\n    a#{$parent} {\n      @include hover-focus() {\n        color: darken($color, $emphasized-link-hover-darken-percentage) !important;\n      }\n    }\n  }\n  @include deprecate(\"`text-emphasis-variant()`\", \"v4.4.0\", \"v5\", $ignore-warning);\n}\n", "// CSS image replacement\n@mixin text-hide($ignore-warning: false) {\n  // stylelint-disable-next-line font-family-no-missing-generic-family-keyword\n  font: 0/0 a;\n  color: transparent;\n  text-shadow: none;\n  background-color: transparent;\n  border: 0;\n\n  @include deprecate(\"`text-hide()`\", \"v4.1.0\", \"v5\", $ignore-warning);\n}\n", "// Text truncate\n// Requires inline-block or block for proper styling\n\n@mixin text-truncate() {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n", "// stylelint-disable declaration-no-important\n\n// Visibility\n\n@mixin invisible($visibility) {\n  visibility: $visibility !important;\n  @include deprecate(\"`invisible()`\", \"v4.3.0\", \"v5\");\n}\n", "@mixin alert-variant($background, $border, $color) {\n  color: $color;\n  @include gradient-bg($background);\n  border-color: $border;\n\n  hr {\n    border-top-color: darken($border, 5%);\n  }\n\n  .alert-link {\n    color: darken($color, 10%);\n  }\n}\n", "// Button variants\n//\n// Easily pump out default styles, as well as :hover, :focus, :active,\n// and disabled options for all buttons\n\n@mixin button-variant($background, $border, $hover-background: darken($background, 7.5%), $hover-border: darken($border, 10%), $active-background: darken($background, 10%), $active-border: darken($border, 12.5%)) {\n  color: color-yiq($background);\n  @include gradient-bg($background);\n  border-color: $border;\n  @include box-shadow($btn-box-shadow);\n\n  @include hover() {\n    color: color-yiq($hover-background);\n    @include gradient-bg($hover-background);\n    border-color: $hover-border;\n  }\n\n  &:focus,\n  &.focus {\n    color: color-yiq($hover-background);\n    @include gradient-bg($hover-background);\n    border-color: $hover-border;\n    // Avoid using mixin so we can pass custom focus shadow properly\n    @if $enable-shadows {\n      box-shadow: $btn-box-shadow, 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n    } @else {\n      box-shadow: 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n    }\n  }\n\n  // Disabled comes first so active can properly restyle\n  &.disabled,\n  &:disabled {\n    color: color-yiq($background);\n    background-color: $background;\n    border-color: $border;\n    // Remove CSS gradients if they're enabled\n    @if $enable-gradients {\n      background-image: none;\n    }\n  }\n\n  &:not(:disabled):not(.disabled):active,\n  &:not(:disabled):not(.disabled).active,\n  .show > &.dropdown-toggle {\n    color: color-yiq($active-background);\n    background-color: $active-background;\n    @if $enable-gradients {\n      background-image: none; // Remove the gradient for the pressed/active state\n    }\n    border-color: $active-border;\n\n    &:focus {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      @if $enable-shadows and $btn-active-box-shadow != none {\n        box-shadow: $btn-active-box-shadow, 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n      } @else {\n        box-shadow: 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n      }\n    }\n  }\n}\n\n@mixin button-outline-variant($color, $color-hover: color-yiq($color), $active-background: $color, $active-border: $color) {\n  color: $color;\n  border-color: $color;\n\n  @include hover() {\n    color: $color-hover;\n    background-color: $active-background;\n    border-color: $active-border;\n  }\n\n  &:focus,\n  &.focus {\n    box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);\n  }\n\n  &.disabled,\n  &:disabled {\n    color: $color;\n    background-color: transparent;\n  }\n\n  &:not(:disabled):not(.disabled):active,\n  &:not(:disabled):not(.disabled).active,\n  .show > &.dropdown-toggle {\n    color: color-yiq($active-background);\n    background-color: $active-background;\n    border-color: $active-border;\n\n    &:focus {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      @if $enable-shadows and $btn-active-box-shadow != none {\n        box-shadow: $btn-active-box-shadow, 0 0 0 $btn-focus-width rgba($color, .5);\n      } @else {\n        box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);\n      }\n    }\n  }\n}\n\n// Button sizes\n@mixin button-size($padding-y, $padding-x, $font-size, $line-height, $border-radius) {\n  padding: $padding-y $padding-x;\n  @include font-size($font-size);\n  line-height: $line-height;\n  // Manually declare to provide an override to the browser default\n  @include border-radius($border-radius, 0);\n}\n", "@mixin caret-down() {\n  border-top: $caret-width solid;\n  border-right: $caret-width solid transparent;\n  border-bottom: 0;\n  border-left: $caret-width solid transparent;\n}\n\n@mixin caret-up() {\n  border-top: 0;\n  border-right: $caret-width solid transparent;\n  border-bottom: $caret-width solid;\n  border-left: $caret-width solid transparent;\n}\n\n@mixin caret-right() {\n  border-top: $caret-width solid transparent;\n  border-right: 0;\n  border-bottom: $caret-width solid transparent;\n  border-left: $caret-width solid;\n}\n\n@mixin caret-left() {\n  border-top: $caret-width solid transparent;\n  border-right: $caret-width solid;\n  border-bottom: $caret-width solid transparent;\n}\n\n@mixin caret($direction: down) {\n  @if $enable-caret {\n    &::after {\n      display: inline-block;\n      margin-left: $caret-spacing;\n      vertical-align: $caret-vertical-align;\n      content: \"\";\n      @if $direction == down {\n        @include caret-down();\n      } @else if $direction == up {\n        @include caret-up();\n      } @else if $direction == right {\n        @include caret-right();\n      }\n    }\n\n    @if $direction == left {\n      &::after {\n        display: none;\n      }\n\n      &::before {\n        display: inline-block;\n        margin-right: $caret-spacing;\n        vertical-align: $caret-vertical-align;\n        content: \"\";\n        @include caret-left();\n      }\n    }\n\n    &:empty::after {\n      margin-left: 0;\n    }\n  }\n}\n", "// Pagination\n\n@mixin pagination-size($padding-y, $padding-x, $font-size, $line-height, $border-radius) {\n  .page-link {\n    padding: $padding-y $padding-x;\n    @include font-size($font-size);\n    line-height: $line-height;\n  }\n\n  .page-item {\n    &:first-child {\n      .page-link {\n        @include border-left-radius($border-radius);\n      }\n    }\n    &:last-child {\n      .page-link {\n        @include border-right-radius($border-radius);\n      }\n    }\n  }\n}\n", "// Lists\n\n// Unstyled keeps list items block level, just removes default browser padding and list-style\n@mixin list-unstyled() {\n  padding-left: 0;\n  list-style: none;\n}\n", "// List Groups\n\n@mixin list-group-item-variant($state, $background, $color) {\n  .list-group-item-#{$state} {\n    color: $color;\n    background-color: $background;\n\n    &.list-group-item-action {\n      @include hover-focus() {\n        color: $color;\n        background-color: darken($background, 5%);\n      }\n\n      &.active {\n        color: $white;\n        background-color: $color;\n        border-color: $color;\n      }\n    }\n  }\n}\n", "// Horizontal dividers\n//\n// Dividers (basically an hr) within dropdowns and nav lists\n\n@mixin nav-divider($color: $nav-divider-color, $margin-y: $nav-divider-margin-y, $ignore-warning: false) {\n  height: 0;\n  margin: $margin-y 0;\n  overflow: hidden;\n  border-top: 1px solid $color;\n  @include deprecate(\"The `nav-divider()` mixin\", \"v4.4.0\", \"v5\", $ignore-warning);\n}\n", "// Form control focus state\n//\n// Generate a customized focus state and for any input with the specified color,\n// which defaults to the `$input-focus-border-color` variable.\n//\n// We highly encourage you to not customize the default value, but instead use\n// this to tweak colors on an as-needed basis. This aesthetic change is based on\n// WebKit's default styles, but applicable to a wider range of browsers. Its\n// usability and accessibility should be taken into account with any change.\n//\n// Example usage: change the default blue border and shadow to white for better\n// contrast against a dark gray background.\n@mixin form-control-focus($ignore-warning: false) {\n  &:focus {\n    color: $input-focus-color;\n    background-color: $input-focus-bg;\n    border-color: $input-focus-border-color;\n    outline: 0;\n    // Avoid using mixin so we can pass custom focus shadow properly\n    @if $enable-shadows {\n      box-shadow: $input-box-shadow, $input-focus-box-shadow;\n    } @else {\n      box-shadow: $input-focus-box-shadow;\n    }\n  }\n  @include deprecate(\"The `form-control-focus()` mixin\", \"v4.4.0\", \"v5\", $ignore-warning);\n}\n\n// This mixin uses an `if()` technique to be compatible with Dart Sass\n// See https://github.com/sass/sass/issues/1873#issuecomment-********* for more details\n@mixin form-validation-state-selector($state) {\n  @if ($state == \"valid\" or $state == \"invalid\") {\n    .was-validated #{if(&, \"&\", \"\")}:#{$state},\n    #{if(&, \"&\", \"\")}.is-#{$state} {\n      @content;\n    }\n  } @else {\n    #{if(&, \"&\", \"\")}.is-#{$state} {\n      @content;\n    }\n  }\n}\n\n@mixin form-validation-state($state, $color, $icon) {\n  .#{$state}-feedback {\n    display: none;\n    width: 100%;\n    margin-top: $form-feedback-margin-top;\n    @include font-size($form-feedback-font-size);\n    color: $color;\n  }\n\n  .#{$state}-tooltip {\n    position: absolute;\n    top: 100%;\n    z-index: 5;\n    display: none;\n    max-width: 100%; // Contain to parent when possible\n    padding: $form-feedback-tooltip-padding-y $form-feedback-tooltip-padding-x;\n    margin-top: .1rem;\n    @include font-size($form-feedback-tooltip-font-size);\n    line-height: $form-feedback-tooltip-line-height;\n    color: color-yiq($color);\n    background-color: rgba($color, $form-feedback-tooltip-opacity);\n    @include border-radius($form-feedback-tooltip-border-radius);\n  }\n\n  @include form-validation-state-selector($state) {\n    ~ .#{$state}-feedback,\n    ~ .#{$state}-tooltip {\n      display: block;\n    }\n  }\n\n  .form-control {\n    @include form-validation-state-selector($state) {\n      border-color: $color;\n\n      @if $enable-validation-icons {\n        padding-right: $input-height-inner;\n        background-image: escape-svg($icon);\n        background-repeat: no-repeat;\n        background-position: right $input-height-inner-quarter center;\n        background-size: $input-height-inner-half $input-height-inner-half;\n      }\n\n      &:focus {\n        border-color: $color;\n        box-shadow: 0 0 0 $input-focus-width rgba($color, .25);\n      }\n    }\n  }\n\n  // stylelint-disable-next-line selector-no-qualifying-type\n  textarea.form-control {\n    @include form-validation-state-selector($state) {\n      @if $enable-validation-icons {\n        padding-right: $input-height-inner;\n        background-position: top $input-height-inner-quarter right $input-height-inner-quarter;\n      }\n    }\n  }\n\n  .custom-select {\n    @include form-validation-state-selector($state) {\n      border-color: $color;\n\n      @if $enable-validation-icons {\n        padding-right: $custom-select-feedback-icon-padding-right;\n        background: $custom-select-background, escape-svg($icon) $custom-select-bg no-repeat $custom-select-feedback-icon-position / $custom-select-feedback-icon-size;\n      }\n\n      &:focus {\n        border-color: $color;\n        box-shadow: 0 0 0 $input-focus-width rgba($color, .25);\n      }\n    }\n  }\n\n  .form-check-input {\n    @include form-validation-state-selector($state) {\n      ~ .form-check-label {\n        color: $color;\n      }\n\n      ~ .#{$state}-feedback,\n      ~ .#{$state}-tooltip {\n        display: block;\n      }\n    }\n  }\n\n  .custom-control-input {\n    @include form-validation-state-selector($state) {\n      ~ .custom-control-label {\n        color: $color;\n\n        &::before {\n          border-color: $color;\n        }\n      }\n\n      &:checked {\n        ~ .custom-control-label::before {\n          border-color: lighten($color, 10%);\n          @include gradient-bg(lighten($color, 10%));\n        }\n      }\n\n      &:focus {\n        ~ .custom-control-label::before {\n          box-shadow: 0 0 0 $input-focus-width rgba($color, .25);\n        }\n\n        &:not(:checked) ~ .custom-control-label::before {\n          border-color: $color;\n        }\n      }\n    }\n  }\n\n  // custom file\n  .custom-file-input {\n    @include form-validation-state-selector($state) {\n      ~ .custom-file-label {\n        border-color: $color;\n      }\n\n      &:focus {\n        ~ .custom-file-label {\n          border-color: $color;\n          box-shadow: 0 0 0 $input-focus-width rgba($color, .25);\n        }\n      }\n    }\n  }\n}\n", "// Tables\n\n@mixin table-row-variant($state, $background, $border: null) {\n  // Exact selectors below required to override `.table-striped` and prevent\n  // inheritance to nested tables.\n  .table-#{$state} {\n    &,\n    > th,\n    > td {\n      background-color: $background;\n    }\n\n    @if $border != null {\n      th,\n      td,\n      thead th,\n      tbody + tbody {\n        border-color: $border;\n      }\n    }\n  }\n\n  // Hover states for `.table-hover`\n  // Note: this is not available for cells or rows within `thead` or `tfoot`.\n  .table-hover {\n    $hover-background: darken($background, 5%);\n\n    .table-#{$state} {\n      @include hover() {\n        background-color: $hover-background;\n\n        > td,\n        > th {\n          background-color: $hover-background;\n        }\n      }\n    }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n// Contextual backgrounds\n\n@mixin bg-variant($parent, $color, $ignore-warning: false) {\n  #{$parent} {\n    background-color: $color !important;\n  }\n  a#{$parent},\n  button#{$parent} {\n    @include hover-focus() {\n      background-color: darken($color, 10%) !important;\n    }\n  }\n  @include deprecate(\"The `bg-variant` mixin\", \"v4.4.0\", \"v5\", $ignore-warning);\n}\n\n@mixin bg-gradient-variant($parent, $color) {\n  #{$parent} {\n    background: $color linear-gradient(180deg, mix($body-bg, $color, 15%), $color) repeat-x !important;\n  }\n}\n", "// stylelint-disable property-blacklist\n// Single side border-radius\n\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: $radius;\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n    border-top-right-radius: $radius;\n  }\n}\n\n@mixin border-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: $radius;\n    border-bottom-right-radius: $radius;\n  }\n}\n\n@mixin border-bottom-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: $radius;\n    border-bottom-left-radius: $radius;\n  }\n}\n\n@mixin border-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n    border-bottom-left-radius: $radius;\n  }\n}\n\n@mixin border-top-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n  }\n}\n\n@mixin border-top-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: $radius;\n  }\n}\n\n@mixin border-bottom-right-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: $radius;\n  }\n}\n\n@mixin border-bottom-left-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: $radius;\n  }\n}\n", "@mixin box-shadow($shadow...) {\n  @if $enable-shadows {\n    $result: ();\n\n    @if (length($shadow) == 1) {\n      // We can pass `@include box-shadow(none);`\n      $result: $shadow;\n    } @else {\n      // Filter to avoid invalid properties for example `box-shadow: none, 1px 1px black;`\n      @for $i from 1 through length($shadow) {\n        @if nth($shadow, $i) != \"none\" {\n          $result: append($result, nth($shadow, $i), \"comma\");\n        }\n      }\n    }\n    @if (length($result) > 0) {\n      box-shadow: $result;\n    }\n  }\n}\n", "// Gradients\n\n@mixin gradient-bg($color) {\n  @if $enable-gradients {\n    background: $color linear-gradient(180deg, mix($body-bg, $color, 15%), $color) repeat-x;\n  } @else {\n    background-color: $color;\n  }\n}\n\n// Horizontal gradient, from left to right\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-x($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to right, $start-color $start-percent, $end-color $end-percent);\n  background-repeat: repeat-x;\n}\n\n// Vertical gradient, from top to bottom\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-y($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to bottom, $start-color $start-percent, $end-color $end-percent);\n  background-repeat: repeat-x;\n}\n\n@mixin gradient-directional($start-color: $gray-700, $end-color: $gray-800, $deg: 45deg) {\n  background-image: linear-gradient($deg, $start-color, $end-color);\n  background-repeat: repeat-x;\n}\n@mixin gradient-x-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient(to right, $start-color, $mid-color $color-stop, $end-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-y-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient($start-color, $mid-color $color-stop, $end-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-radial($inner-color: $gray-700, $outer-color: $gray-800) {\n  background-image: radial-gradient(circle, $inner-color, $outer-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-striped($color: rgba($white, .15), $angle: 45deg) {\n  background-image: linear-gradient($angle, $color 25%, transparent 25%, transparent 50%, $color 50%, $color 75%, transparent 75%, transparent);\n}\n", "// stylelint-disable property-blacklist\n@mixin transition($transition...) {\n  @if $enable-transitions {\n    @if length($transition) == 0 {\n      transition: $transition-base;\n    } @else {\n      transition: $transition;\n    }\n  }\n\n  @if $enable-prefers-reduced-motion-media-query {\n    @media (prefers-reduced-motion: reduce) {\n      transition: none;\n    }\n  }\n}\n", "@mixin clearfix() {\n  &::after {\n    display: block;\n    clear: both;\n    content: \"\";\n  }\n}\n", "// Framework grid generation\n//\n// Used only by <PERSON><PERSON><PERSON> to generate the correct number of grid classes given\n// any value of `$grid-columns`.\n\n@mixin make-grid-columns($columns: $grid-columns, $gutter: $grid-gutter-width, $breakpoints: $grid-breakpoints) {\n  // Common properties for all breakpoints\n  %grid-column {\n    position: relative;\n    width: 100%;\n    padding-right: $gutter / 2;\n    padding-left: $gutter / 2;\n  }\n\n  @each $breakpoint in map-keys($breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\n\n    // Allow columns to stretch full width below their breakpoints\n    @for $i from 1 through $columns {\n      .col#{$infix}-#{$i} {\n        @extend %grid-column;\n      }\n    }\n    .col#{$infix},\n    .col#{$infix}-auto {\n      @extend %grid-column;\n    }\n\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      // Provide basic `.col-{bp}` classes for equal-width flexbox columns\n      .col#{$infix} {\n        flex-basis: 0;\n        flex-grow: 1;\n        max-width: 100%;\n      }\n\n      @for $i from 1 through $grid-row-columns {\n        .row-cols#{$infix}-#{$i} {\n          @include row-cols($i);\n        }\n      }\n\n      .col#{$infix}-auto {\n        @include make-col-auto();\n      }\n\n      @for $i from 1 through $columns {\n        .col#{$infix}-#{$i} {\n          @include make-col($i, $columns);\n        }\n      }\n\n      .order#{$infix}-first { order: -1; }\n\n      .order#{$infix}-last { order: $columns + 1; }\n\n      @for $i from 0 through $columns {\n        .order#{$infix}-#{$i} { order: $i; }\n      }\n\n      // `$columns - 1` because offsetting by the width of an entire row isn't possible\n      @for $i from 0 through ($columns - 1) {\n        @if not ($infix == \"\" and $i == 0) { // Avoid emitting useless .offset-0\n          .offset#{$infix}-#{$i} {\n            @include make-col-offset($i, $columns);\n          }\n        }\n      }\n    }\n  }\n}\n", "/// Grid system\n//\n// Generate semantic grid columns with these mixins.\n\n@mixin make-container($gutter: $grid-gutter-width) {\n  width: 100%;\n  padding-right: $gutter / 2;\n  padding-left: $gutter / 2;\n  margin-right: auto;\n  margin-left: auto;\n}\n\n\n// For each breakpoint, define the maximum width of the container in a media query\n@mixin make-container-max-widths($max-widths: $container-max-widths, $breakpoints: $grid-breakpoints) {\n  @each $breakpoint, $container-max-width in $max-widths {\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      max-width: $container-max-width;\n    }\n  }\n}\n\n@mixin make-row($gutter: $grid-gutter-width) {\n  display: flex;\n  flex-wrap: wrap;\n  margin-right: -$gutter / 2;\n  margin-left: -$gutter / 2;\n}\n\n@mixin make-col-ready($gutter: $grid-gutter-width) {\n  position: relative;\n  // Prevent columns from becoming too narrow when at smaller grid tiers by\n  // always setting `width: 100%;`. This works because we use `flex` values\n  // later on to override this initial width.\n  width: 100%;\n  padding-right: $gutter / 2;\n  padding-left: $gutter / 2;\n}\n\n@mixin make-col($size, $columns: $grid-columns) {\n  flex: 0 0 percentage($size / $columns);\n  // Add a `max-width` to ensure content within each column does not blow out\n  // the width of the column. Applies to IE10+ and Firefox. Chrome and Safari\n  // do not appear to require this.\n  max-width: percentage($size / $columns);\n}\n\n@mixin make-col-auto() {\n  flex: 0 0 auto;\n  width: auto;\n  max-width: 100%; // Reset earlier grid tiers\n}\n\n@mixin make-col-offset($size, $columns: $grid-columns) {\n  $num: $size / $columns;\n  margin-left: if($num == 0, 0, percentage($num));\n}\n\n// Row columns\n//\n// Specify on a parent element(e.g., .row) to force immediate children into NN\n// numberof columns. Supports wrapping to new lines, but does not do a Masonry\n// style grid.\n@mixin row-cols($count) {\n  & > * {\n    flex: 0 0 100% / $count;\n    max-width: 100% / $count;\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n@mixin float-left() {\n  float: left !important;\n  @include deprecate(\"The `float-left` mixin\", \"v4.3.0\", \"v5\");\n}\n@mixin float-right() {\n  float: right !important;\n  @include deprecate(\"The `float-right` mixin\", \"v4.3.0\", \"v5\");\n}\n@mixin float-none() {\n  float: none !important;\n  @include deprecate(\"The `float-none` mixin\", \"v4.3.0\", \"v5\");\n}\n", "//\n// Core: Variables\n//\n\n// COLORS\n// --------------------------------------------------------\n$blue: #0073b7 !default;\n$lightblue: #3c8dbc !default;\n$navy: #001f3f !default;\n$teal: #39cccc !default;\n$olive: #3d9970 !default;\n$lime: #01ff70 !default;\n$orange: #ff851b !default;\n$fuchsia: #f012be !default;\n$purple: #605ca8 !default;\n$maroon: #d81b60 !default;\n$black: #111 !default;\n$gray-x-light: #d2d6de !default;\n\n$colors: map-merge((\n    'lightblue': $lightblue,\n    'navy': $navy,\n    'olive': $olive,\n    'lime': $lime,\n    'fuchsia': $fuchsia,\n    'maroon': $maroon,\n), $colors);\n\n// LAYOUT\n// --------------------------------------------------------\n\n$font-size-root: 1rem !default;\n\n// Sidebar\n$sidebar-width: 250px !default;\n$sidebar-padding-x: 0.5rem !default;\n$sidebar-padding-y: 0 !default;\n\n// Boxed layout maximum width\n$boxed-layout-max-width: 1250px !default;\n\n// When to show the smaller logo\n$screen-header-collapse: map-get($grid-breakpoints, md) !default;\n\n// Body background (Affects main content background only)\n$main-bg: #f4f6f9 !default;\n\n// Content padding\n$content-padding-y: 0 !default;\n$content-padding-x: $navbar-padding-x !default;\n\n// IMAGE SIZES\n// --------------------------------------------------------\n$img-size-sm: 1.875rem !default;\n$img-size-md: 3.75rem !default;\n$img-size-lg: 6.25rem !default;\n$img-size-push: .625rem !default;\n\n// MAIN HEADER\n// --------------------------------------------------------\n$main-header-bottom-border-width: $border-width !default;\n$main-header-bottom-border-color: $gray-300 !default;\n$main-header-bottom-border: $main-header-bottom-border-width solid $main-header-bottom-border-color !default;\n$main-header-link-padding-y: $navbar-padding-y !default;\n$main-header-link-padding-x: $navbar-padding-x !default;\n$main-header-brand-padding-y: $navbar-brand-padding-y !default;\n$main-header-brand-padding-x: $navbar-padding-x !default;\n$main-header-height-inner: ($nav-link-height + ($main-header-link-padding-y * 2)) !default;\n$main-header-height: calc(#{$main-header-height-inner} + #{$main-header-bottom-border-width}) !default;\n$nav-link-sm-padding-y: .35rem !default;\n$nav-link-sm-height: ($font-size-sm * $line-height-sm + $nav-link-sm-padding-y * 1.785) !default;\n$main-header-height-sm-inner: ($nav-link-sm-height + ($main-header-link-padding-y * 2)) !default;\n$main-header-height-sm: calc(#{$main-header-height-sm-inner} + #{$main-header-bottom-border-width}) !default;\n\n\n// Main header skins\n$main-header-dark-form-control-bg: hsla(100, 100%, 100%, 0.2) !default;\n$main-header-dark-form-control-focused-bg: hsla(100, 100%, 100%, 0.6) !default;\n$main-header-dark-form-control-focused-color: $gray-800 !default;\n$main-header-dark-form-control-border: 0 !default;\n$main-header-dark-form-control-focused-border: 0 !default;\n$main-header-dark-placeholder-color: hsla(100, 100%, 100%, 0.6) !default;\n\n$main-header-light-form-control-bg: darken($gray-100, 2%) !default;\n$main-header-light-form-control-focused-bg: $gray-200 !default;\n$main-header-light-form-control-focused-color: $gray-800 !default;\n$main-header-light-form-control-border: 0 !default;\n$main-header-light-form-control-focused-border: 0 !default;\n$main-header-light-placeholder-color: hsla(0, 0%, 0%, 0.6) !default;\n\n// MAIN FOOTER\n// --------------------------------------------------------\n$main-footer-padding: 1rem !default;\n$main-footer-padding-sm: $main-footer-padding * .812 !default;\n$main-footer-border-top-width: 1px !default;\n$main-footer-border-top-color: $gray-300 !default;\n$main-footer-border-top: $main-footer-border-top-width solid $main-footer-border-top-color !default;\n$main-footer-height-inner: (($font-size-root * $line-height-base) + ($main-footer-padding * 2)) !default;\n$main-footer-height: calc(#{$main-footer-height-inner} + #{$main-footer-border-top-width}) !default;\n$main-footer-height-sm-inner: (($font-size-sm * $line-height-base) + ($main-footer-padding-sm * 2)) !default;\n$main-footer-height-sm: calc(#{$main-footer-height-sm-inner} + #{$main-footer-border-top-width}) !default;\n$main-footer-bg: $white !default;\n\n// SIDEBAR SKINS\n// --------------------------------------------------------\n\n// Dark sidebar\n$sidebar-dark-bg: $dark !default;\n$sidebar-dark-hover-bg: hsla(100, 100%, 100%, 0.1) !default;\n$sidebar-dark-color: #C2C7D0 !default;\n$sidebar-dark-hover-color: $white !default;\n$sidebar-dark-active-color: $white !default;\n$sidebar-dark-submenu-bg: transparent !default;\n$sidebar-dark-submenu-color: #C2C7D0 !default;\n$sidebar-dark-submenu-hover-color: $white !default;\n$sidebar-dark-submenu-hover-bg: $sidebar-dark-hover-bg !default;\n$sidebar-dark-submenu-active-color: $sidebar-dark-bg !default;\n$sidebar-dark-submenu-active-bg: hsla(100, 100%, 100%, 0.9) !default;\n$sidebar-dark-header-color: $white !default;\n\n// Light sidebar\n$sidebar-light-bg: $white !default;\n$sidebar-light-hover-bg: rgba($black, .1) !default;\n$sidebar-light-color: $gray-800 !default;\n$sidebar-light-hover-color: $gray-900 !default;\n$sidebar-light-active-color: $black !default;\n$sidebar-light-submenu-bg: transparent !default;\n$sidebar-light-submenu-color: #777 !default;\n$sidebar-light-submenu-hover-color: #000 !default;\n$sidebar-light-submenu-hover-bg: $sidebar-light-hover-bg !default;\n$sidebar-light-submenu-active-color: $sidebar-light-hover-color !default;\n$sidebar-light-submenu-active-bg: $sidebar-light-submenu-hover-bg !default;\n$sidebar-light-header-color: $gray-800 !default;\n\n// SIDEBAR MINI\n// --------------------------------------------------------\n$sidebar-mini-width: ($nav-link-padding-x + $sidebar-padding-x + .8rem) * 2 !default;\n$sidebar-nav-icon-width: $sidebar-mini-width - (($sidebar-padding-x + $nav-link-padding-x) * 2) !default;\n$sidebar-user-image-width: $sidebar-nav-icon-width + ($nav-link-padding-x / 2) !default;\n\n// CONTROL SIDEBAR\n// --------------------------------------------------------\n$control-sidebar-width: $sidebar-width !default;\n\n// Cards\n// --------------------------------------------------------\n$card-border-color: $gray-100 !default;\n$card-dark-border-color: lighten($gray-900, 10%) !default;\n$card-shadow: 0 0 1px rgba(0, 0, 0, .125), 0 1px 3px rgba(0, 0, 0, .2) !default;\n$card-title-font-size: 1.1rem !default;\n$card-title-font-size-sm: 1rem !default;\n$card-title-font-weight: $font-weight-normal !default;\n$card-nav-link-padding-sm-y: .4rem !default;\n$card-nav-link-padding-sm-x: .8rem !default;\n$card-img-size: $img-size-sm !default;\n\n// PROGRESS BARS\n// --------------------------------------------------------\n$progress-bar-border-radius: 1px !default;\n$progress-bar-sm-border-radius: 1px !default;\n$progress-bar-xs-border-radius: 1px !default;\n\n// DIRECT CHAT\n// --------------------------------------------------------\n$direct-chat-height: 250px !default;\n$direct-chat-default-msg-bg: $gray-x-light !default;\n$direct-chat-default-font-color: #444 !default;\n$direct-chat-default-msg-border-color: $gray-x-light !default;\n\n// CHAT WIDGET\n// --------------------------------------------------------\n$attachment-border-radius: 3px !default;\n\n// Z-INDEX\n// --------------------------------------------------------\n$zindex-main-header: $zindex-fixed + 4 !default;\n$zindex-main-sidebar: $zindex-fixed + 8 !default;\n$zindex-main-footer: $zindex-fixed + 2 !default;\n$zindex-control-sidebar: $zindex-fixed + 1 !default;\n$zindex-sidebar-mini-links: 010 !default;\n$zindex-toasts: $zindex-main-sidebar + 2 !default;\n\n// TRANSITIONS SETTINGS\n// --------------------------------------------------------\n\n// Transition global options\n$transition-speed: 0.3s !default;\n$transition-fn: ease-in-out !default;\n\n// TEXT\n// --------------------------------------------------------\n$font-size-xs: ($font-size-base * .75) !default;\n$font-size-xl: ($font-size-base * 2) !default;\n\n\n// BUTTON\n// --------------------------------------------------------\n$button-default-background-color: $gray-100 !default;\n$button-default-color: #444 !default;\n$button-default-border-color: #ddd !default;\n\n$button-padding-y-xs: .125rem !default;\n$button-padding-x-xs: .25rem !default;\n$button-line-height-xs: $line-height-sm !default;\n$button-font-size-xs: ($font-size-base * .75) !default;\n$button-border-radius-xs: .15rem !default;\n\n  \n// ELEVATION\n// --------------------------------------------------------\n$elevations: ();\n$elevations: map-merge((\n    1: unquote('0 1px 3px ' + rgba($black, 0.12) + ', 0 1px 2px ' + rgba($black, 0.24)),\n    2: unquote('0 3px 6px ' + rgba($black, 0.16) + ', 0 3px 6px ' + rgba($black, 0.23)),\n    3: unquote('0 10px 20px ' + rgba($black, 0.19) + ', 0 6px 6px ' + rgba($black, 0.23)),\n    4: unquote('0 14px 28px ' + rgba($black, 0.25) + ', 0 10px 10px ' + rgba($black, 0.22)),\n    5: unquote('0 19px 38px ' + rgba($black, 0.30) + ', 0 15px 12px ' + rgba($black, 0.22)),\n), $elevations);\n  \n// RIBBON\n// --------------------------------------------------------\n$ribbon-border-size: 3px !default;\n$ribbon-line-height: 100% !default;\n$ribbon-padding: .375rem 0 !default;\n$ribbon-font-size: .8rem !default;\n$ribbon-width: 90px !default;\n$ribbon-wrapper-size: 70px !default;\n$ribbon-top: 10px !default;\n$ribbon-right: -2px !default;\n$ribbon-lg-wrapper-size: 120px !default;\n$ribbon-lg-width: 160px !default;\n$ribbon-lg-top: 26px !default;\n$ribbon-lg-right: 0px !default;\n$ribbon-xl-wrapper-size: 180px !default;\n$ribbon-xl-width: 240px !default;\n$ribbon-xl-top: 47px !default;\n$ribbon-xl-right: 4px !default;\n", "//\n// General: Mixins\n//\n\n@import 'mixins/cards';\n@import 'mixins/sidebar';\n@import 'mixins/navbar';\n@import 'mixins/accent';\n@import 'mixins/custom-forms';\n@import 'mixins/backgrounds';\n@import 'mixins/direct-chat';\n@import 'mixins/toasts';\n@import 'mixins/miscellaneous';\n", "//\n// Mixins: Cards Variant\n//\n\n@mixin cards-variant($name, $color) {\n  .card-#{$name} {\n    &:not(.card-outline) {\n      > .card-header {\n        background-color: $color;\n\n        &,\n        a {\n          color: color-yiq($color);\n        }\n\n        a.active {\n          color: color-yiq($white);\n        }\n      }\n    }\n\n    &.card-outline {\n      border-top: 3px solid $color;\n    }\n\n    &.card-outline-tabs {\n      > .card-header {\n        a {\n          &:hover {\n            border-top: 3px solid $nav-tabs-border-color;\n          }\n\n          &.active {\n            border-top: 3px solid $color;\n          }\n        }\n      }\n    }\n  }\n\n  .bg-#{$name},\n  .bg-gradient-#{$name},\n  .card-#{$name}:not(.card-outline) {\n    .btn-tool {\n      color: rgba(color-yiq($color), 0.8);\n\n      &:hover {\n        color: color-yiq($color);\n      }\n    }\n  }\n\n  .card.bg-#{$name},\n  .card.bg-gradient-#{$name} {\n    .bootstrap-datetimepicker-widget {\n      .table td,\n      .table th {\n        border: none;\n      }\n\n      table thead tr:first-child th:hover,\n      table td.day:hover,\n      table td.hour:hover,\n      table td.minute:hover,\n      table td.second:hover {\n        background: darken($color, 8%);\n        color: color-yiq($color);\n      }\n\n      table td.today::before {\n        border-bottom-color: color-yiq($color);\n      }\n\n      table td.active,\n      table td.active:hover {\n        background: lighten($color, 10%);\n        color: color-yiq($color);\n      }\n    }\n  }\n}\n\n", "//\r\n// Mixins: Sidebar\r\n//\r\n\r\n// Sidebar Color\r\n@mixin sidebar-color($color) {\r\n  .nav-sidebar > .nav-item {\r\n    & > .nav-link.active {\r\n      background-color: $color;\r\n      color: color-yiq($color);\r\n    }\r\n  }\r\n\r\n  .nav-sidebar.nav-legacy > .nav-item {\r\n    & > .nav-link.active {\r\n      border-color: $color;\r\n    }\r\n  }\r\n}\r\n\r\n// Sidebar Mini Breakpoints\r\n@mixin sidebar-mini-breakpoint() {\r\n  // A fix for text overflow while transitioning from sidebar mini to full sidebar\r\n  .nav-sidebar,\r\n  .nav-sidebar > .nav-header,\r\n  .nav-sidebar .nav-link {\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n  }\r\n\r\n  // When the sidebar is collapsed...\r\n  &.sidebar-collapse {\r\n    .d-hidden-mini {\r\n      display: none;\r\n    }\r\n\r\n    // Apply the new margins to the main content and footer\r\n    .content-wrapper,\r\n    .main-footer,\r\n    .main-header {\r\n      margin-left: $sidebar-mini-width !important;\r\n    }\r\n\r\n    // Make the sidebar headers\r\n    .nav-sidebar .nav-header {\r\n      display: none;\r\n    }\r\n\r\n    .nav-sidebar .nav-link p {\r\n      width: 0;\r\n    }\r\n\r\n    .sidebar .user-panel > .info,\r\n    .nav-sidebar .nav-link p,\r\n    .brand-text {\r\n      margin-left: -10px;\r\n      animation-name: fadeOut;\r\n      animation-duration: $transition-speed;\r\n      animation-fill-mode: both;\r\n      visibility: hidden;\r\n    }\r\n\r\n    .logo-xl {\r\n      animation-name: fadeOut;\r\n      animation-duration: $transition-speed;\r\n      animation-fill-mode: both;\r\n      visibility: hidden;\r\n    }\r\n\r\n    .logo-xs {\r\n      display: inline-block;\r\n      animation-name: fadeIn;\r\n      animation-duration: $transition-speed;\r\n      animation-fill-mode: both;\r\n      visibility: visible;\r\n    }\r\n\r\n    // Modify the sidebar to shrink instead of disappearing\r\n    .main-sidebar {\r\n      overflow-x: hidden;\r\n\r\n      &,\r\n      &::before {\r\n        // Don't go away! Just shrink\r\n        margin-left: 0;\r\n        width: $sidebar-mini-width;\r\n      }\r\n\r\n      .user-panel {\r\n        .image {\r\n          float: none;\r\n        }\r\n      }\r\n\r\n      &:hover,\r\n      &.sidebar-focused {\r\n        width: $sidebar-width;\r\n\r\n        .brand-link {\r\n          width: $sidebar-width;\r\n        }\r\n\r\n        .user-panel {\r\n          text-align: left;\r\n\r\n          .image {\r\n            float: left;\r\n          }\r\n        }\r\n\r\n        .user-panel > .info,\r\n        .nav-sidebar .nav-link p,\r\n        .brand-text,\r\n        .logo-xl {\r\n          display: inline-block;\r\n          margin-left: 0;\r\n          animation-name: fadeIn;\r\n          animation-duration: $transition-speed;\r\n          animation-fill-mode: both;\r\n          visibility: visible;\r\n        }\r\n\r\n        .logo-xs {\r\n          animation-name: fadeOut;\r\n          animation-duration: $transition-speed;\r\n          animation-fill-mode: both;\r\n          visibility: hidden;\r\n        }\r\n\r\n        .brand-image {\r\n          margin-right: .5rem;\r\n        }\r\n\r\n        // Make the sidebar links, menus, labels, badges\r\n        // and angle icons disappear\r\n        .sidebar-form,\r\n        .user-panel > .info {\r\n          display: block !important;\r\n          -webkit-transform: translateZ(0);\r\n        }\r\n\r\n        .nav-sidebar > .nav-item > .nav-link > span {\r\n          display: inline-block !important;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Make an element visible only when sidebar mini is active\r\n    .visible-sidebar-mini {\r\n      display: block !important;\r\n    }\r\n\r\n    &.layout-fixed {\r\n      .main-sidebar:hover {\r\n        .brand-link {\r\n          width: $sidebar-width;\r\n        }\r\n      }\r\n\r\n      .brand-link {\r\n        width: $sidebar-mini-width;\r\n      }\r\n    }\r\n  }\r\n}\r\n", "//\n// Mixins: Navbar\n//\n\n// Navbar Variant\n@mixin navbar-variant($color, $font-color: rgba(255, 255, 255, 0.8), $hover-color: #f6f6f6, $hover-bg: rgba(0, 0, 0, 0.1)) {\n  background-color: $color;\n\n  .nav > li > a {\n    color: $font-color;\n  }\n\n  .nav > li > a:hover,\n  .nav > li > a:active,\n  .nav > li > a:focus,\n  .nav .open > a,\n  .nav .open > a:hover,\n  .nav .open > a:focus,\n  .nav > .active > a {\n    background: $hover-bg;\n    color: $hover-color;\n  }\n\n  // Add color to the sidebar toggle button\n  .sidebar-toggle {\n    color: $font-color;\n\n    &:hover,\n    &:focus {\n      background: $hover-bg;\n      color: $hover-color;\n    }\n  }\n}\n", "//\n// Mixins: Accent\n//\n\n// Accent Variant\n@mixin accent-variant($name, $color) {\n  .accent-#{$name} {\n    $link-color: $color;\n    $link-hover-color: darken($color, 15%);\n    $pagination-active-bg: $color;\n    $pagination-active-border-color: $color;\n\n    .btn-link,\n    a:not(.dropdown-item):not(.btn-app):not(.nav-link):not(.brand-link):not(.page-link):not(.btn) {\n      color: $link-color;\n\n      @include hover {\n        color: $link-hover-color;\n      }\n    }\n\n    .dropdown-item {\n      &:active,\n      &.active {\n        background: $color;\n        color: color-yiq($color);\n      }\n    }\n\n    .custom-control-input:checked ~ .custom-control-label {\n      &::before {\n        background: $color;\n        border-color: darken($color, 20%);\n      }\n\n      &::after {\n        $newColor: color-yiq($color);\n        background-image: str-replace($custom-checkbox-indicator-icon-checked, str-replace(#{$custom-control-indicator-checked-color}, '#', '%23'), str-replace(#{$newColor}, '#', '%23'));\n      }\n    }\n\n    .form-control:focus:not(.is-invalid):not(.is-warning):not(.is-valid),\n    .custom-select:focus,\n    .custom-control-input:focus:not(:checked) ~ .custom-control-label::before,\n    .custom-file-input:focus ~ .custom-file-label {\n      border-color: lighten($color, 25%);\n    }\n    \n    .page-item {\n      .page-link {\n        color: $link-color;\n      }\n\n      &.active a,\n      &.active .page-link {\n        background-color: $pagination-active-bg;\n        border-color: $pagination-active-border-color;\n        color: $pagination-active-color;\n      }\n\n      &.disabled a,\n      &.disabled .page-link {\n        background-color: $pagination-disabled-bg;\n        border-color: $pagination-disabled-border-color;\n        color: $pagination-disabled-color;\n      }\n    }\n\n    [class*=\"sidebar-dark-\"] {\n      .sidebar {\n        a:not(.dropdown-item):not(.btn-app):not(.nav-link):not(.brand-link) {\n          color: $sidebar-dark-color;\n      \n          @include hover {\n            color: $sidebar-dark-hover-color;\n          }\n        }\n      }\n    }\n\n    [class*=\"sidebar-light-\"] {\n      .sidebar {\n        a:not(.dropdown-item):not(.btn-app):not(.nav-link):not(.brand-link) {\n          color: $sidebar-light-color;\n\n          @include hover {\n            color: $sidebar-light-hover-color;\n          }\n        }\n      }\n    }\n  }\n}\n\n", "//\n// Mixins: Custom Forms\n//\n\n// Custom Switch Variant\n@mixin custom-switch-variant($name, $color) {\n  &.custom-switch-off-#{$name} {\n    & .custom-control-input ~ .custom-control-label::before {\n      background: #{$color};\n      border-color: darken($color, 20%);\n    }\n\n    & .custom-control-input:focus ~ .custom-control-label::before {\n      box-shadow: 0 0 0 1px $body-bg, 0 0 0 2px rgba($color, .25);\n    }\n\n    & .custom-control-input ~ .custom-control-label::after {\n      background: darken($color, 25%);\n    }\n  }\n\n  &.custom-switch-on-#{$name} {\n    & .custom-control-input:checked ~ .custom-control-label::before {\n      background: #{$color};\n      border-color: darken($color, 20%);\n    }\n\n    & .custom-control-input:checked:focus ~ .custom-control-label::before {\n      box-shadow: 0 0 0 1px $body-bg, 0 0 0 2px rgba($color, .25);\n    }\n\n    & .custom-control-input:checked ~ .custom-control-label::after {\n      background: lighten($color, 30%);\n    }\n  }\n}\n\n// Custom Range Variant\n@mixin custom-range-variant($name, $color) {\n  &.custom-range-#{$name} {\n    &:focus {\n      outline: none;\n\n      &::-webkit-slider-thumb {\n        box-shadow: 0 0 0 1px $body-bg, 0 0 0 2px rgba($color, .25);\n      }\n\n      &::-moz-range-thumb     {\n        box-shadow: 0 0 0 1px $body-bg, 0 0 0 2px rgba($color, .25);\n      }\n\n      &::-ms-thumb            {\n        box-shadow: 0 0 0 1px $body-bg, 0 0 0 2px rgba($color, .25);\n      }\n    }\n\n    &::-webkit-slider-thumb {\n      background-color: $color;\n\n      &:active {\n        background-color: lighten($color, 35%);\n      }\n    }\n\n    &::-moz-range-thumb {\n      background-color: $color;\n\n      &:active {\n        background-color: lighten($color, 35%);\n      }\n    }\n\n    &::-ms-thumb {\n      background-color: $color;\n\n      &:active {\n        background-color: lighten($color, 35%);\n      }\n    }\n  }\n}\n", "//\n// Mixins: Backgrounds\n//\n\n// Background Variant\n@mixin background-variant($name, $color) {\n  .bg-#{$name} {\n    background-color: #{$color} !important;\n\n    &,\n    > a {\n      color: color-yiq($color) !important;\n    }\n\n    &.btn {\n      &:hover {\n        border-color: darken($color, 10%);\n        color: darken(color-yiq($color), 7.5%);\n      }\n\n      &:not(:disabled):not(.disabled):active,\n      &:not(:disabled):not(.disabled).active,\n      &:active,\n      &.active {\n        background-color: darken($color, 10%) !important;\n        border-color: darken($color, 12.5%);\n        color: color-yiq(darken($color, 10%));\n      }\n    }\n  }\n}\n\n// Background Gradient Variant\n@mixin background-gradient-variant($name, $color) {\n  .bg-gradient-#{$name} {\n    @include bg-gradient-variant('&', $color);\n    color: color-yiq($color);\n\n    &.btn {\n      &.disabled,\n      &:disabled,\n      &:not(:disabled):not(.disabled):active,\n      &:not(:disabled):not(.disabled).active,\n      .show > &.dropdown-toggle {\n        background-image: none !important;\n      }\n\n      &:hover {\n        @include bg-gradient-variant('&', darken($color, 7.5%));\n        border-color: darken($color, 10%);\n        color: darken(color-yiq($color), 7.5%);\n      }\n\n      &:not(:disabled):not(.disabled):active,\n      &:not(:disabled):not(.disabled).active,\n      &:active,\n      &.active {\n        @include bg-gradient-variant('&', darken($color, 10%));\n        border-color: darken($color, 12.5%);\n        color: color-yiq(darken($color, 10%));\n      }\n    }\n  }\n}\n", "//\n// Mixins: Direct Chat\n//\n\n// Direct Chat Variant\n@mixin direct-chat-variant($bg-color, $color: #fff) {\n  .right > .direct-chat-text {\n    background: $bg-color;\n    border-color: $bg-color;\n    color: color-yiq($bg-color);\n\n    &::after,\n    &::before {\n      border-left-color: $bg-color;\n    }\n  }\n}\n", "//\n// Mixins: Toasts\n//\n\n// Toast Variant\n@mixin toast-variant($name, $color) {\n  &.bg-#{$name} {\n    background: rgba($color, .9) !important;\n    @if (color-yiq($color) == $yiq-text-light) {\n\n      .close {\n        color: color-yiq($color);\n        text-shadow: 0 1px 0 #000;\n      }\n    }\n\n    .toast-header {\n      background: rgba($color, .85);\n      color: color-yiq($color);\n    }\n  }\n}\n\n", "//\n// Mixins: Miscellaneous\n//\n\n// ETC\n@mixin translate($x, $y) {\n  transform: translate($x, $y);\n}\n\n// Different radius each side\n@mixin border-radius-sides($top-left, $top-right, $bottom-left, $bottom-right) {\n  border-radius: $top-left $top-right $bottom-left $bottom-right;\n}\n\n@mixin calc($property, $expression) {\n  #{$property}: calc(#{$expression});\n}\n\n@mixin rotate($value) {\n  transform: rotate($value);\n}\n\n@mixin animation($animation) {\n  animation: $animation;\n}\n\n// Gradient background\n@mixin gradient($color: #F5F5F5, $start: #EEE, $stop: #FFF) {\n  background: $color;\n  background: -webkit-gradient(linear, left bottom, left top, color-stop(0, $start), color-stop(1, $stop));\n  background: -ms-linear-gradient(bottom, $start, $stop);\n  background: -moz-linear-gradient(center bottom, $start 0%, $stop 100%);\n  background: -o-linear-gradient($stop, $start);\n}\n\n", "//\n// Part: Extra Components\n//\n\n@import '../small-box';\n@import '../info-box';\n@import '../timeline';\n@import '../products';\n@import '../direct-chat';\n@import '../users-list';\n@import '../social-widgets';\n", "//\n// Component: Small Box\n//\n\n.small-box {\n  @include border-radius($border-radius);\n  @include box-shadow($card-shadow);\n\n  display: block;\n  margin-bottom: 20px;\n  position: relative;\n\n  // content wrapper\n  > .inner {\n    padding: 10px;\n  }\n\n  > .small-box-footer {\n    background: rgba($black, 0.1);\n    color: rgba($white, 0.8);\n    display: block;\n    padding: 3px 0;\n    position: relative;\n    text-align: center;\n    text-decoration: none;\n    z-index: 10;\n\n    &:hover {\n      background: rgba($black, 0.15);\n      color: $white;\n    }\n  }\n\n  h3 {\n    @include font-size(2.2rem);\n    font-weight: bold;\n    margin: 0 0 10px 0;\n    padding: 0;\n    white-space: nowrap;\n  }\n\n  @include media-breakpoint-up(lg) {\n    .col-xl-2 &,\n    .col-lg-2 &,\n    .col-md-2 & {\n      h3 {\n        @include font-size(1.6rem);\n      }\n    }\n\n    .col-xl-3 &,\n    .col-lg-3 &,\n    .col-md-3 & {\n      h3 {\n        @include font-size(1.6rem);\n      }\n    }\n  }\n\n  @include media-breakpoint-up(xl) {\n    .col-xl-2 &,\n    .col-lg-2 &,\n    .col-md-2 & {\n      h3 {\n        @include font-size(2.2rem);\n      }\n    }\n\n    .col-xl-3 &,\n    .col-lg-3 &,\n    .col-md-3 & {\n      h3 {\n        @include font-size(2.2rem);\n      }\n    }\n  }\n\n  p {\n    font-size: 1rem;\n\n    > small {\n      color: $gray-100;\n      display: block;\n      font-size: 0.9rem;\n      margin-top: 5px;\n    }\n  }\n\n  h3,\n  p {\n    z-index: 5;\n  }\n\n  // the icon\n  .icon {\n    color: rgba($black, 0.15);\n    z-index: 0;\n\n    > i {\n      font-size: 90px;\n      position: absolute;\n      right: 15px;\n      top: 15px;\n      transition: all $transition-speed linear;\n\n      &.fa,\n      &.fas,\n      &.far,\n      &.fab,\n      &.glyphicon,\n      &.ion {\n        font-size: 70px;\n        top: 20px;\n      }\n    }\n\n  }\n\n  // Small box hover state\n  &:hover {\n    text-decoration: none;\n\n    // Animate icons on small box hover\n    .icon > i {\n      font-size: 95px;\n\n      &.fa,\n      &.fas,\n      &.far,\n      &.fab,\n      &.glyphicon,\n      &.ion {\n        font-size: 75px;\n      }\n    }\n  }\n}\n\n@include media-breakpoint-down(sm) {\n  // No need for icons on very small devices\n  .small-box {\n    text-align: center;\n\n    .icon {\n      display: none;\n    }\n\n    p {\n      font-size: 12px;\n    }\n  }\n}\n", "//\n// Component: Info Box\n//\n \n.info-box {\n  @include box-shadow($card-shadow);\n  @include border-radius($border-radius);\n\n  background: $white;\n  display: flex;\n  margin-bottom: map-get($spacers, 3);\n  min-height: 80px;\n  padding: .5rem;\n  position: relative;\n  width: 100%;\n\n  .progress {\n    background-color: rgba($black, .125);\n    height: 2px;\n    margin: 5px 0;\n\n    .progress-bar {\n      background-color: $white;\n    }\n  }\n\n  .info-box-icon {\n    @if $enable-rounded {\n      border-radius: $border-radius;\n    }\n\n    align-items: center;\n    display: flex;\n    font-size: 1.875rem;\n    justify-content: center;\n    text-align: center;\n    width: 70px;\n\n    > img {\n      max-width: 100%;\n    }\n  }\n\n  .info-box-content {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    line-height: 120%;\n    flex: 1;\n    padding: 0 10px;\n  }\n\n  .info-box-number {\n    display: block;\n    margin-top: .25rem;\n    font-weight: $font-weight-bold;\n  }\n\n  .progress-description,\n  .info-box-text {\n    display: block;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n\n  @each $name, $color in $theme-colors {\n    .info-box {\n      .bg-#{$name},\n      .bg-gradient-#{$name} {\n        color: color-yiq($color);\n\n        .progress-bar {\n          background-color: color-yiq($color);\n        }\n      }\n    }\n  }\n\n  .info-box-more {\n    display: block;\n  }\n\n  .progress-description {\n    margin: 0;\n\n  }\n\n  @include media-breakpoint-up(md) {\n    .col-xl-2 &,\n    .col-lg-2 &,\n    .col-md-2 & {\n      .progress-description {\n        display: none;\n      }\n    }\n\n    .col-xl-3 &,\n    .col-lg-3 &,\n    .col-md-3 & {\n      .progress-description {\n        display: none;\n      }\n    }\n  }\n\n  @include media-breakpoint-up(lg) {\n    .col-xl-2 &,\n    .col-lg-2 &,\n    .col-md-2 & {\n      .progress-description {\n        @include font-size(.75rem);\n        display: block;\n      }\n    }\n\n    .col-xl-3 &,\n    .col-lg-3 &,\n    .col-md-3 & {\n      .progress-description {\n        @include font-size(.75rem);\n        display: block;\n      }\n    }\n  }\n\n  @include media-breakpoint-up(xl) {\n    .col-xl-2 &,\n    .col-lg-2 &,\n    .col-md-2 & {\n      .progress-description {\n        @include font-size(1rem);\n        display: block;\n      }\n    }\n\n    .col-xl-3 &,\n    .col-lg-3 &,\n    .col-md-3 & {\n      .progress-description {\n        @include font-size(1rem);\n        display: block;\n      }\n    }\n  }\n}\n", "//\n// Component: Timeline\n//\n\n.timeline {\n  margin: 0 0 45px;\n  padding: 0;\n  position: relative;\n  // The line\n  &::before {\n    @include border-radius($border-radius);\n    background: $gray-300;\n    bottom: 0;\n    content: '';\n    left: 31px;\n    margin: 0;\n    position: absolute;\n    top: 0;\n    width: 4px;\n  }\n  // Element\n  > div {\n    &::before,\n    &::after {\n      content: \"\";\n      display: table;\n    }\n\n    margin-bottom: 15px;\n    margin-right: 10px;\n    position: relative;\n    // The content\n    > .timeline-item {\n      @include box-shadow($card-shadow);\n      @include border-radius($border-radius);\n      background: $white;\n      color: $gray-700;\n      margin-left: 60px;\n      margin-right: 15px;\n      margin-top: 0;\n      padding: 0;\n      position: relative;\n      // The time and header\n      > .time {\n        color: #999;\n        float: right;\n        font-size: 12px;\n        padding: 10px;\n      }\n      // Header\n      > .timeline-header {\n        border-bottom: 1px solid $card-border-color;\n        color: $gray-700;\n        font-size: 16px;\n        line-height: 1.1;\n        margin: 0;\n        padding: 10px;\n        // Link in header\n        > a {\n          font-weight: 600;\n        }\n      }\n      // Item body and footer\n      > .timeline-body,\n      > .timeline-footer {\n        padding: 10px;\n      }\n\n      > .timeline-body {\n        > img {\n          margin: 10px;\n        }\n        > dl, ol, ul {\n          margin: 0;\n        }\n      }\n\n      > .timeline-footer {\n        > a {\n          color: $white;\n        }\n      }\n    }\n    // The icons at line\n    > .fa,\n    > .fas,\n    > .far,\n    > .fab,\n    > .glyphicon,\n    > .ion {\n      background: $gray-500;\n      border-radius: 50%;\n      font-size: 15px;\n      height: 30px;\n      left: 18px;\n      line-height: 30px;\n      position: absolute;\n      text-align: center;\n      top: 0;\n      width: 30px;\n    }\n  }\n  // Time label\n  > .time-label {\n    > span {\n      @include border-radius(4px);\n      background-color: $white;\n      display: inline-block;\n      font-weight: 600;\n      padding: 5px;\n    }\n  }\n}\n\n.timeline-inverse {\n  > div {\n    > .timeline-item {\n      @include box-shadow(none);\n      background: $gray-100;\n      border: 1px solid $gray-300;\n\n      > .timeline-header {\n        border-bottom-color: $gray-300;\n      }\n    }\n  }\n}\n", "//\n// Component: Products\n//\n\n.products-list {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n\n  > .item {\n    @include clearfix;\n\n    @if $enable-rounded {\n      @include border-radius($border-radius);\n    }\n\n    background: $white;\n    padding: 10px 0;\n  }\n\n  .product-img {\n    float: left;\n\n    img {\n      height: 50px;\n      width: 50px;\n    }\n  }\n\n  .product-info {\n    margin-left: 60px;\n  }\n\n  .product-title {\n    font-weight: 600;\n  }\n\n  .product-description {\n    color: $gray-600;\n    display: block;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n}\n\n.product-list-in-card > .item {\n  @include border-radius(0);\n  border-bottom: 1px solid $card-border-color;\n\n  &:last-of-type {\n    border-bottom-width: 0;\n  }\n}\n", "//\n// Component: Direct Chat\n//\n \n.direct-chat {\n  .card-body {\n    overflow-x: hidden;\n    padding: 0;\n    position: relative;\n  }\n\n  &.chat-pane-open {\n    .direct-chat-contacts {\n      @include translate(0, 0);\n    }\n  }\n\n\n  &.timestamp-light {\n    .direct-chat-timestamp {\n      color: lighten(color-yiq($yiq-text-light), 10%);\n    }\n  }\n\n  &.timestamp-dark {\n    .direct-chat-timestamp {\n      color: darken(color-yiq($yiq-text-dark), 20%);\n    }\n  }\n}\n\n.direct-chat-messages {\n  @include translate(0, 0);\n  height: 250px;\n  overflow: auto;\n  padding: 10px;\n}\n\n.direct-chat-msg,\n.direct-chat-text {\n  display: block;\n}\n\n.direct-chat-msg {\n  @include clearfix;\n  margin-bottom: 10px;\n}\n\n.direct-chat-messages,\n.direct-chat-contacts {\n  transition: transform .5s ease-in-out;\n}\n\n.direct-chat-text {\n  @if $enable-rounded {\n    @include border-radius($border-radius-lg);\n  }\n\n  background: $direct-chat-default-msg-bg;\n  border: 1px solid $direct-chat-default-msg-border-color;\n  color: $direct-chat-default-font-color;\n  margin: 5px 0 0 50px;\n  padding: 5px 10px;\n  position: relative;\n\n  //Create the arrow\n  &::after,\n  &::before {\n    border: solid transparent;\n    border-right-color: $direct-chat-default-msg-border-color;\n    content: ' ';\n    height: 0;\n    pointer-events: none;\n    position: absolute;\n    right: 100%;\n    top: 15px;\n    width: 0;\n  }\n\n  &::after {\n    border-width: 5px;\n    margin-top: -5px;\n  }\n\n  &::before {\n    border-width: 6px;\n    margin-top: -6px;\n  }\n\n  .right & {\n    margin-left: 0;\n    margin-right: 50px;\n\n    &::after,\n    &::before {\n      border-left-color: $direct-chat-default-msg-border-color;\n      border-right-color: transparent;\n      left: 100%;\n      right: auto;\n    }\n  }\n}\n\n.direct-chat-img {\n  @include border-radius(50%);\n  float: left;\n  height: 40px;\n  width: 40px;\n\n  .right & {\n    float: right;\n  }\n}\n\n.direct-chat-infos {\n  display: block;\n  font-size: $font-size-sm;\n  margin-bottom: 2px;\n}\n\n.direct-chat-name {\n  font-weight: 600;\n}\n\n.direct-chat-timestamp {\n  color: darken($gray-500, 25%);\n}\n\n//Direct chat contacts pane\n.direct-chat-contacts-open {\n  .direct-chat-contacts {\n    @include translate(0, 0);\n  }\n}\n\n.direct-chat-contacts {\n  @include translate(101%, 0);\n  background: $dark;\n  bottom: 0;\n  color: $white;\n  height: 250px;\n  overflow: auto;\n  position: absolute;\n  top: 0;\n  width: 100%;\n}\n\n.direct-chat-contacts-light {\n  background: $light;\n\n  .contacts-list-name {\n    color: $gray-700;\n  }\n\n  .contacts-list-date {\n    color: $gray-600;\n  }\n\n  .contacts-list-msg {\n    color: darken($gray-600, 10%);\n  }\n}\n\n//Contacts list -- for displaying contacts in direct chat contacts pane\n.contacts-list {\n  @include list-unstyled;\n\n  > li {\n    @include clearfix;\n    border-bottom: 1px solid rgba($black, 0.2);\n    margin: 0;\n    padding: 10px;\n\n    &:last-of-type {\n      border-bottom: 0;\n    }\n  }\n}\n\n.contacts-list-img {\n  @include border-radius(50%);\n  float: left;\n  width: 40px;\n}\n\n.contacts-list-info {\n  color: $white;\n  margin-left: 45px;\n}\n\n.contacts-list-name,\n.contacts-list-status {\n  display: block;\n}\n\n.contacts-list-name {\n  font-weight: 600;\n}\n\n.contacts-list-status {\n  font-size: $font-size-sm;\n}\n\n.contacts-list-date {\n  color: $gray-400;\n  font-weight: normal;\n}\n\n.contacts-list-msg {\n  color: darken($gray-400, 10%);\n}\n\n// Color variants\n@each $name, $color in $theme-colors {\n  .direct-chat-#{$name} {\n    @include direct-chat-variant($color);\n  }\n}\n\n@each $name, $color in $colors {\n  .direct-chat-#{$name} {\n    @include direct-chat-variant($color);\n  }\n}\n", "//\n// Component: Users List\n//\n\n.users-list {\n  @include list-unstyled;\n\n  > li {\n    float: left;\n    padding: 10px;\n    text-align: center;\n    width: 25%;\n\n    img {\n      @include border-radius(50%);\n      height: auto;\n      max-width: 100%;\n    }\n\n    > a:hover {\n      &,\n      .users-list-name {\n        color: #999;\n      }\n    }\n  }\n}\n\n.users-list-name,\n.users-list-date {\n  display: block;\n}\n\n.users-list-name {\n  color: $gray-700;\n  font-size: $font-size-sm;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.users-list-date {\n  color: darken($gray-500, 20%);\n  font-size: 12px;\n}\n", "//\n// Component: Social Widgets\n//\n\n//General widget style\n.card-widget {\n  border: 0;\n  position: relative;\n}\n\n//User Widget Style 1\n.widget-user {\n\n  //User name container\n  .widget-user-header {\n    @if $enable-rounded {\n      @include border-top-radius($border-radius);\n    }\n\n    height: 135px;\n    padding: 1rem;\n    text-align: center;\n  }\n\n  //User name\n  .widget-user-username {\n    font-size: 25px;\n    font-weight: 300;\n    margin-bottom: 0;\n    margin-top: 0;\n    text-shadow: 0 1px 1px rgba($black, 0.2);\n  }\n\n  //User single line description\n  .widget-user-desc {\n    margin-top: 0;\n  }\n\n  //User image container\n  .widget-user-image {\n    left: 50%;\n    margin-left: -45px;\n    position: absolute;\n    top: 80px;\n\n    > img {\n      border: 3px solid $white;\n      height: auto;\n      width: 90px;\n    }\n  }\n\n  .card-footer {\n    padding-top: 50px;\n  }\n}\n\n//User Widget Style 2\n.widget-user-2 {\n\n  //User name container\n  .widget-user-header {\n    @include border-top-radius($border-radius);\n    padding: 1rem;\n  }\n\n  //User name\n  .widget-user-username {\n    font-size: 25px;\n    font-weight: 300;\n    margin-bottom: 5px;\n    margin-top: 5px;\n  }\n\n  //User single line description\n  .widget-user-desc {\n    margin-top: 0;\n  }\n\n  .widget-user-username,\n  .widget-user-desc {\n    margin-left: 75px;\n  }\n\n  //User image container\n  .widget-user-image {\n    > img {\n      float: left;\n      height: auto;\n      width: 65px;\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;GAMG;AgDFH,AAAA,UAAU,CAAC;EpBCP,aAAa,E1BgNa,OAAM;E2BrM9B,UAAU,EOoIF,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,oBAAmB,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAiB;EY5IpE,OAAO,EAAE,KAAK;EACd,aAAa,EAAE,IAAI;EACnB,QAAQ,EAAE,QAAQ;CA8HnB;;AApID,AASE,UATQ,GASN,MAAM,CAAC;EACP,OAAO,EAAE,IAAI;CACd;;AAXH,AAaE,UAbQ,GAaN,iBAAiB,CAAC;EAClB,UAAU,E9CGH,kBAAI;E8CFX,KAAK,E9CRE,wBAAO;E8CSd,OAAO,EAAE,KAAK;EACd,OAAO,EAAE,KAAK;EACd,QAAQ,EAAE,QAAQ;EAClB,UAAU,EAAE,MAAM;EAClB,eAAe,EAAE,IAAI;EACrB,OAAO,EAAE,EAAE;CAMZ;;AA3BH,AAuBI,UAvBM,GAaN,iBAAiB,AAUhB,MAAM,CAAC;EACN,UAAU,E9CPL,mBAAI;E8CQT,KAAK,E9ClBA,OAAO;C8CmBb;;AA1BL,AA6BE,UA7BQ,CA6BR,EAAE,CAAC;E5C8FC,SAAS,EAtCE,MAAC;E4CtDd,WAAW,EAAE,IAAI;EACjB,MAAM,EAAE,UAAU;EAClB,OAAO,EAAE,CAAC;EACV,WAAW,EAAE,MAAM;CACpB;;A1CqBC,MAAM,EAAE,SAAS,EAAE,KAAK;E0ClBxB,AAGE,SAHO,CAtCb,UAAU,CAyCJ,EAAE;EAFJ,SAAS,CAvCb,UAAU,CAyCJ,EAAE;EADJ,SAAS,CAxCb,UAAU,CAyCJ,EAAE,CAAC;I5CkFH,SAAS,EAtCE,MAAC;G4C1CX;EAGH,AAGE,SAHO,CA9Cb,UAAU,CAiDJ,EAAE;EAFJ,SAAS,CA/Cb,UAAU,CAiDJ,EAAE;EADJ,SAAS,CAhDb,UAAU,CAiDJ,EAAE,CAAC;I5C0EH,SAAS,EAtCE,MAAC;G4ClCX;;;A1CKH,MAAM,EAAE,SAAS,EAAE,MAAM;E0CAzB,AAGE,SAHO,CAxDb,UAAU,CA2DJ,EAAE;EAFJ,SAAS,CAzDb,UAAU,CA2DJ,EAAE;EADJ,SAAS,CA1Db,UAAU,CA2DJ,EAAE,CAAC;I5CgEH,SAAS,EAtCE,MAAC;G4CxBX;EAGH,AAGE,SAHO,CAhEb,UAAU,CAmEJ,EAAE;EAFJ,SAAS,CAjEb,UAAU,CAmEJ,EAAE;EADJ,SAAS,CAlEb,UAAU,CAmEJ,EAAE,CAAC;I5CwDH,SAAS,EAtCE,MAAC;G4ChBX;;;AArEP,AAyEE,UAzEQ,CAyER,CAAC,CAAC;EACA,SAAS,EAAE,IAAI;CAQhB;;AAlFH,AA4EI,UA5EM,CAyER,CAAC,GAGG,KAAK,CAAC;EACN,KAAK,E9CrEA,OAAO;E8CsEZ,OAAO,EAAE,KAAK;EACd,SAAS,EAAE,MAAM;EACjB,UAAU,EAAE,GAAG;CAChB;;AAjFL,AAoFE,UApFQ,CAoFR,EAAE;AApFJ,UAAU,CAqFR,CAAC,CAAC;EACA,OAAO,EAAE,CAAC;CACX;;AAvFH,AA0FE,UA1FQ,CA0FR,KAAK,CAAC;EACJ,KAAK,E9C1EE,mBAAI;E8C2EX,OAAO,EAAE,CAAC;CAoBX;;AAhHH,AA8FI,UA9FM,CA0FR,KAAK,GAID,CAAC,CAAC;EACF,SAAS,EAAE,IAAI;EACf,QAAQ,EAAE,QAAQ;EAClB,KAAK,EAAE,IAAI;EACX,GAAG,EAAE,IAAI;EACT,UAAU,EAAE,GAAG,CZmFF,IAAI,CYnFiB,MAAM;CAWzC;;AA9GL,AAqGM,UArGI,CA0FR,KAAK,GAID,CAAC,AAOA,GAAG,EArGV,UAAU,CA0FR,KAAK,GAID,CAAC,AAQA,IAAI,EAtGX,UAAU,CA0FR,KAAK,GAID,CAAC,AASA,IAAI,EAvGX,UAAU,CA0FR,KAAK,GAID,CAAC,AAUA,IAAI,EAxGX,UAAU,CA0FR,KAAK,GAID,CAAC,AAWA,UAAU,EAzGjB,UAAU,CA0FR,KAAK,GAID,CAAC,AAYA,IAAI,CAAC;EACJ,SAAS,EAAE,IAAI;EACf,GAAG,EAAE,IAAI;CACV;;AA7GP,AAmHE,UAnHQ,AAmHP,MAAM,CAAC;EACN,eAAe,EAAE,IAAI;CAetB;;AAnIH,AAuHI,UAvHM,AAmHP,MAAM,CAIL,KAAK,GAAG,CAAC,CAAC;EACR,SAAS,EAAE,IAAI;CAUhB;;AAlIL,AA0HM,UA1HI,AAmHP,MAAM,CAIL,KAAK,GAAG,CAAC,AAGN,GAAG,EA1HV,UAAU,AAmHP,MAAM,CAIL,KAAK,GAAG,CAAC,AAIN,IAAI,EA3HX,UAAU,AAmHP,MAAM,CAIL,KAAK,GAAG,CAAC,AAKN,IAAI,EA5HX,UAAU,AAmHP,MAAM,CAIL,KAAK,GAAG,CAAC,AAMN,IAAI,EA7HX,UAAU,AAmHP,MAAM,CAIL,KAAK,GAAG,CAAC,AAON,UAAU,EA9HjB,UAAU,AAmHP,MAAM,CAIL,KAAK,GAAG,CAAC,AAQN,IAAI,CAAC;EACJ,SAAS,EAAE,IAAI;CAChB;;A1C5DH,MAAM,EAAE,SAAS,EAAE,QAAQ;E0CmE7B,AAAA,UAAU,CAAC;IACT,UAAU,EAAE,MAAM;GASnB;EAVD,AAGE,UAHQ,CAGR,KAAK,CAAC;IACJ,OAAO,EAAE,IAAI;GACd;EALH,AAOE,UAPQ,CAOR,CAAC,CAAC;IACA,SAAS,EAAE,IAAI;GAChB;;;ACjJL,AAAA,SAAS,CAAC;EpBYJ,UAAU,EOoIF,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,oBAAmB,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAiB;ER/IlE,aAAa,E1BgNa,OAAM;E+C7MlC,UAAU,E/CGD,OAAO;E+CFhB,OAAO,EAAE,IAAI;EACb,aAAa,E/C+GN,IAAI;E+C9GX,UAAU,EAAE,IAAI;EAChB,OAAO,EAAE,KAAK;EACd,QAAQ,EAAE,QAAQ;EAClB,KAAK,EAAE,IAAI;CAmIZ;;AA7ID,AAYE,SAZO,CAYP,SAAS,CAAC;EACR,gBAAgB,E/CIT,oBAAI;E+CHX,MAAM,EAAE,GAAG;EACX,MAAM,EAAE,KAAK;CAKd;;AApBH,AAiBI,SAjBK,CAYP,SAAS,CAKP,aAAa,CAAC;EACZ,gBAAgB,E/CXX,OAAO;C+CYb;;AAnBL,AAsBE,SAtBO,CAsBP,cAAc,CAAC;EAEX,aAAa,E/CyLW,OAAM;E+CtLhC,WAAW,EAAE,MAAM;EACnB,OAAO,EAAE,IAAI;EACb,SAAS,EAAE,QAAQ;EACnB,eAAe,EAAE,MAAM;EACvB,UAAU,EAAE,MAAM;EAClB,KAAK,EAAE,IAAI;CAKZ;;AArCH,AAkCI,SAlCK,CAsBP,cAAc,GAYV,GAAG,CAAC;EACJ,SAAS,EAAE,IAAI;CAChB;;AApCL,AAuCE,SAvCO,CAuCP,iBAAiB,CAAC;EAChB,OAAO,EAAE,IAAI;EACb,cAAc,EAAE,MAAM;EACtB,eAAe,EAAE,MAAM;EACvB,WAAW,EAAE,IAAI;EACjB,IAAI,EAAE,CAAC;EACP,OAAO,EAAE,MAAM;CAChB;;AA9CH,AAgDE,SAhDO,CAgDP,gBAAgB,CAAC;EACf,OAAO,EAAE,KAAK;EACd,UAAU,EAAE,MAAM;EAClB,WAAW,E/C4Le,GAAG;C+C3L9B;;AApDH,AAsDE,SAtDO,CAsDP,qBAAqB;AAtDvB,SAAS,CAuDP,cAAc,CAAC;EACb,OAAO,EAAE,KAAK;EACd,QAAQ,EAAE,MAAM;EAChB,aAAa,EAAE,QAAQ;EACvB,WAAW,EAAE,MAAM;CACpB;;AA5DH,AAgEM,SAhEG,CA+DL,SAAS,CACP,WAAW;AAhEjB,SAAS,CA+DL,SAAS,CAEP,oBAAoB,CAAE;EACpB,KAAK,E/C3DF,OAAO;C+CgEX;;AAvEP,AAoEQ,SApEC,CA+DL,SAAS,CACP,WAAW,CAIT,aAAa;AApErB,SAAS,CA+DL,SAAS,CAEP,oBAAoB,CAGlB,aAAa,CAAC;EACZ,gBAAgB,E/C9Df,OAAO;C+C+DT;;AAtET,AAgEM,SAhEG,CA+DL,SAAS,CACP,aAAa;AAhEnB,SAAS,CA+DL,SAAS,CAEP,sBAAsB,CAAA;EACpB,KAAK,E/C3DF,OAAO;C+CgEX;;AAvEP,AAoEQ,SApEC,CA+DL,SAAS,CACP,aAAa,CAIX,aAAa;AApErB,SAAS,CA+DL,SAAS,CAEP,sBAAsB,CAGpB,aAAa,CAAC;EACZ,gBAAgB,E/C9Df,OAAO;C+C+DT;;AAtET,AAgEM,SAhEG,CA+DL,SAAS,CACP,WAAW;AAhEjB,SAAS,CA+DL,SAAS,CAEP,oBAAoB,CAAE;EACpB,KAAK,E/C3DF,OAAO;C+CgEX;;AAvEP,AAoEQ,SApEC,CA+DL,SAAS,CACP,WAAW,CAIT,aAAa;AApErB,SAAS,CA+DL,SAAS,CAEP,oBAAoB,CAGlB,aAAa,CAAC;EACZ,gBAAgB,E/C9Df,OAAO;C+C+DT;;AAtET,AAgEM,SAhEG,CA+DL,SAAS,CACP,QAAQ;AAhEd,SAAS,CA+DL,SAAS,CAEP,iBAAiB,CAAK;EACpB,KAAK,E/C3DF,OAAO;C+CgEX;;AAvEP,AAoEQ,SApEC,CA+DL,SAAS,CACP,QAAQ,CAIN,aAAa;AApErB,SAAS,CA+DL,SAAS,CAEP,iBAAiB,CAGf,aAAa,CAAC;EACZ,gBAAgB,E/C9Df,OAAO;C+C+DT;;AAtET,AAgEM,SAhEG,CA+DL,SAAS,CACP,WAAW;AAhEjB,SAAS,CA+DL,SAAS,CAEP,oBAAoB,CAAE;EACpB,KAAK,E/CuBG,OAAO;C+ClBhB;;AAvEP,AAoEQ,SApEC,CA+DL,SAAS,CACP,WAAW,CAIT,aAAa;AApErB,SAAS,CA+DL,SAAS,CAEP,oBAAoB,CAGlB,aAAa,CAAC;EACZ,gBAAgB,E/CoBV,OAAO;C+CnBd;;AAtET,AAgEM,SAhEG,CA+DL,SAAS,CACP,UAAU;AAhEhB,SAAS,CA+DL,SAAS,CAEP,mBAAmB,CAAG;EACpB,KAAK,E/C3DF,OAAO;C+CgEX;;AAvEP,AAoEQ,SApEC,CA+DL,SAAS,CACP,UAAU,CAIR,aAAa;AApErB,SAAS,CA+DL,SAAS,CAEP,mBAAmB,CAGjB,aAAa,CAAC;EACZ,gBAAgB,E/C9Df,OAAO;C+C+DT;;AAtET,AAgEM,SAhEG,CA+DL,SAAS,CACP,SAAS;AAhEf,SAAS,CA+DL,SAAS,CAEP,kBAAkB,CAAI;EACpB,KAAK,E/CuBG,OAAO;C+ClBhB;;AAvEP,AAoEQ,SApEC,CA+DL,SAAS,CACP,SAAS,CAIP,aAAa;AApErB,SAAS,CA+DL,SAAS,CAEP,kBAAkB,CAGhB,aAAa,CAAC;EACZ,gBAAgB,E/CoBV,OAAO;C+CnBd;;AAtET,AAgEM,SAhEG,CA+DL,SAAS,CACP,QAAQ;AAhEd,SAAS,CA+DL,SAAS,CAEP,iBAAiB,CAAK;EACpB,KAAK,E/C3DF,OAAO;C+CgEX;;AAvEP,AAoEQ,SApEC,CA+DL,SAAS,CACP,QAAQ,CAIN,aAAa;AApErB,SAAS,CA+DL,SAAS,CAEP,iBAAiB,CAGf,aAAa,CAAC;EACZ,gBAAgB,E/C9Df,OAAO;C+C+DT;;AAtET,AA2EE,SA3EO,CA2EP,cAAc,CAAC;EACb,OAAO,EAAE,KAAK;CACf;;AA7EH,AA+EE,SA/EO,CA+EP,qBAAqB,CAAC;EACpB,MAAM,EAAE,CAAC;CAEV;;A3C1BC,MAAM,EAAE,SAAS,EAAE,KAAK;E2C6BxB,AAGE,SAHO,CArFb,SAAS,CAwFH,qBAAqB;EAFvB,SAAS,CAtFb,SAAS,CAwFH,qBAAqB;EADvB,SAAS,CAvFb,SAAS,CAwFH,qBAAqB,CAAC;IACpB,OAAO,EAAE,IAAI;GACd;EAGH,AAGE,SAHO,CA7Fb,SAAS,CAgGH,qBAAqB;EAFvB,SAAS,CA9Fb,SAAS,CAgGH,qBAAqB;EADvB,SAAS,CA/Fb,SAAS,CAgGH,qBAAqB,CAAC;IACpB,OAAO,EAAE,IAAI;GACd;;;A3C1CH,MAAM,EAAE,SAAS,EAAE,KAAK;E2C+CxB,AAGE,SAHO,CAvGb,SAAS,CA0GH,qBAAqB;EAFvB,SAAS,CAxGb,SAAS,CA0GH,qBAAqB;EADvB,SAAS,CAzGb,SAAS,CA0GH,qBAAqB,CAAC;I7CiBtB,SAAS,EAtCE,OAAC;I6CuBV,OAAO,EAAE,KAAK;GACf;EAGH,AAGE,SAHO,CAhHb,SAAS,CAmHH,qBAAqB;EAFvB,SAAS,CAjHb,SAAS,CAmHH,qBAAqB;EADvB,SAAS,CAlHb,SAAS,CAmHH,qBAAqB,CAAC;I7CQtB,SAAS,EAtCE,OAAC;I6CgCV,OAAO,EAAE,KAAK;GACf;;;A3C9DH,MAAM,EAAE,SAAS,EAAE,MAAM;E2CmEzB,AAGE,SAHO,CA3Hb,SAAS,CA8HH,qBAAqB;EAFvB,SAAS,CA5Hb,SAAS,CA8HH,qBAAqB;EADvB,SAAS,CA7Hb,SAAS,CA8HH,qBAAqB,CAAC;I7CHtB,SAAS,EAtCE,IAAC;I6C2CV,OAAO,EAAE,KAAK;GACf;EAGH,AAGE,SAHO,CApIb,SAAS,CAuIH,qBAAqB;EAFvB,SAAS,CArIb,SAAS,CAuIH,qBAAqB;EADvB,SAAS,CAtIb,SAAS,CAuIH,qBAAqB,CAAC;I7CZtB,SAAS,EAtCE,IAAC;I6CoDV,OAAO,EAAE,KAAK;GACf;;;AC1IP,AAAA,SAAS,CAAC;EACR,MAAM,EAAE,QAAQ;EAChB,OAAO,EAAE,CAAC;EACV,QAAQ,EAAE,QAAQ;CAyGnB;;AA5GD,AAKE,SALO,AAKN,QAAQ,CAAC;EtBJR,aAAa,E1BgNa,OAAM;EgD1MhC,UAAU,EhDGH,OAAO;EgDFd,MAAM,EAAE,CAAC;EACT,OAAO,EAAE,EAAE;EACX,IAAI,EAAE,IAAI;EACV,MAAM,EAAE,CAAC;EACT,QAAQ,EAAE,QAAQ;EAClB,GAAG,EAAE,CAAC;EACN,KAAK,EAAE,GAAG;CACX;;AAfH,AAiBE,SAjBO,GAiBL,GAAG,CAAC;EAOJ,aAAa,EAAE,IAAI;EACnB,YAAY,EAAE,IAAI;EAClB,QAAQ,EAAE,QAAQ;CAuEnB;;AAjGH,AAkBI,SAlBK,GAiBL,GAAG,AACF,QAAQ,EAlBb,SAAS,GAiBL,GAAG,AAEF,OAAO,CAAC;EACP,OAAO,EAAE,EAAE;EACX,OAAO,EAAE,KAAK;CACf;;AAtBL,AA4BI,SA5BK,GAiBL,GAAG,GAWD,cAAc,CAAC;ErBhBf,UAAU,EOoIF,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,oBAAmB,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAiB;ER/IlE,aAAa,E1BgNa,OAAM;EgDlL9B,UAAU,EhDxBL,OAAO;EgDyBZ,KAAK,EhDlBA,OAAO;EgDmBZ,WAAW,EAAE,IAAI;EACjB,YAAY,EAAE,IAAI;EAClB,UAAU,EAAE,CAAC;EACb,OAAO,EAAE,CAAC;EACV,QAAQ,EAAE,QAAQ;CAyCnB;;AA9EL,AAuCM,SAvCG,GAiBL,GAAG,GAWD,cAAc,GAWZ,KAAK,CAAC;EACN,KAAK,EAAE,IAAI;EACX,KAAK,EAAE,KAAK;EACZ,SAAS,EAAE,IAAI;EACf,OAAO,EAAE,IAAI;CACd;;AA5CP,AA8CM,SA9CG,GAiBL,GAAG,GAWD,cAAc,GAkBZ,gBAAgB,CAAC;EACjB,aAAa,EAAE,GAAG,CAAC,KAAK,ChD9BrB,oBAAI;EgD+BP,KAAK,EhDlCF,OAAO;EgDmCV,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,GAAG;EAChB,MAAM,EAAE,CAAC;EACT,OAAO,EAAE,IAAI;CAKd;;AAzDP,AAsDQ,SAtDC,GAiBL,GAAG,GAWD,cAAc,GAkBZ,gBAAgB,GAQd,CAAC,CAAC;EACF,WAAW,EAAE,GAAG;CACjB;;AAxDT,AA2DM,SA3DG,GAiBL,GAAG,GAWD,cAAc,GA+BZ,cAAc;AA3DtB,SAAS,GAiBL,GAAG,GAWD,cAAc,GAgCZ,gBAAgB,CAAC;EACjB,OAAO,EAAE,IAAI;CACd;;AA9DP,AAiEQ,SAjEC,GAiBL,GAAG,GAWD,cAAc,GAoCZ,cAAc,GACZ,GAAG,CAAC;EACJ,MAAM,EAAE,IAAI;CACb;;AAnET,AAoEQ,SApEC,GAiBL,GAAG,GAWD,cAAc,GAoCZ,cAAc,GAIZ,EAAE,EApEZ,SAAS,GAiBL,GAAG,GAWD,cAAc,GAoCZ,cAAc,CAIR,EAAE,EApEhB,SAAS,GAiBL,GAAG,GAWD,cAAc,GAoCZ,cAAc,CAIJ,EAAE,CAAC;EACX,MAAM,EAAE,CAAC;CACV;;AAtET,AA0EQ,SA1EC,GAiBL,GAAG,GAWD,cAAc,GA6CZ,gBAAgB,GACd,CAAC,CAAC;EACF,KAAK,EhDpEJ,OAAO;CgDqET;;AA5ET,AAgFI,SAhFK,GAiBL,GAAG,GA+DD,GAAG;AAhFT,SAAS,GAiBL,GAAG,GAgED,IAAI;AAjFV,SAAS,GAiBL,GAAG,GAiED,IAAI;AAlFV,SAAS,GAiBL,GAAG,GAkED,IAAI;AAnFV,SAAS,GAiBL,GAAG,GAmED,UAAU;AApFhB,SAAS,GAiBL,GAAG,GAoED,IAAI,CAAC;EACL,UAAU,EhD1EL,OAAO;EgD2EZ,aAAa,EAAE,GAAG;EAClB,SAAS,EAAE,IAAI;EACf,MAAM,EAAE,IAAI;EACZ,IAAI,EAAE,IAAI;EACV,WAAW,EAAE,IAAI;EACjB,QAAQ,EAAE,QAAQ;EAClB,UAAU,EAAE,MAAM;EAClB,GAAG,EAAE,CAAC;EACN,KAAK,EAAE,IAAI;CACZ;;AAhGL,AAoGI,SApGK,GAmGL,WAAW,GACT,IAAI,CAAC;EtBnGP,aAAa,EsBoGY,GAAG;EAC1B,gBAAgB,EhD/FX,OAAO;EgDgGZ,OAAO,EAAE,YAAY;EACrB,WAAW,EAAE,GAAG;EAChB,OAAO,EAAE,GAAG;CACb;;AAIL,AAEI,iBAFa,GACb,GAAG,GACD,cAAc,CAAC;ErBpGf,UAAU,EqBqGU,IAAI;EACxB,UAAU,EhD1GL,OAAO;EgD2GZ,MAAM,EAAE,GAAG,CAAC,KAAK,ChDzGZ,OAAO;CgD8Gb;;AAVL,AAOM,iBAPW,GACb,GAAG,GACD,cAAc,GAKZ,gBAAgB,CAAC;EACjB,mBAAmB,EhD5GhB,OAAO;CgD6GX;;ACvHP,AAAA,cAAc,CAAC;EACb,UAAU,EAAE,IAAI;EAChB,MAAM,EAAE,CAAC;EACT,OAAO,EAAE,CAAC;CAqCX;;AAxCD,AAKE,cALY,GAKV,KAAK,CAAC;EvBJN,aAAa,E1BgNa,OAAM;EiDrMhC,UAAU,EjDLH,OAAO;EiDMd,OAAO,EAAE,MAAM;CAChB;;AAdH,AnBHE,cmBGY,GAKV,KAAK,AnBRN,OAAO,CAAC;EACP,OAAO,EAAE,KAAK;EACd,KAAK,EAAE,IAAI;EACX,OAAO,EAAE,EAAE;CACZ;;AmBDH,AAgBE,cAhBY,CAgBZ,YAAY,CAAC;EACX,KAAK,EAAE,IAAI;CAMZ;;AAvBH,AAmBI,cAnBU,CAgBZ,YAAY,CAGV,GAAG,CAAC;EACF,MAAM,EAAE,IAAI;EACZ,KAAK,EAAE,IAAI;CACZ;;AAtBL,AAyBE,cAzBY,CAyBZ,aAAa,CAAC;EACZ,WAAW,EAAE,IAAI;CAClB;;AA3BH,AA6BE,cA7BY,CA6BZ,cAAc,CAAC;EACb,WAAW,EAAE,GAAG;CACjB;;AA/BH,AAiCE,cAjCY,CAiCZ,oBAAoB,CAAC;EACnB,KAAK,EjDrBE,OAAO;EiDsBd,OAAO,EAAE,KAAK;EACd,QAAQ,EAAE,MAAM;EAChB,aAAa,EAAE,QAAQ;EACvB,WAAW,EAAE,MAAM;CACpB;;AAGH,AAAA,qBAAqB,GAAG,KAAK,CAAC;EvBzC1B,aAAa,EuB0CQ,CAAC;EACxB,aAAa,EAAE,GAAG,CAAC,KAAK,CjD3Bf,oBAAI;CiDgCd;;AAPD,AAIE,qBAJmB,GAAG,KAAK,AAI1B,aAAa,CAAC;EACb,mBAAmB,EAAE,CAAC;CACvB;;AChDH,AACE,YADU,CACV,UAAU,CAAC;EACT,UAAU,EAAE,MAAM;EAClB,OAAO,EAAE,CAAC;EACV,QAAQ,EAAE,QAAQ;CACnB;;AALH,AAQI,YARQ,AAOT,eAAe,CACd,qBAAqB,CAAC;ENNxB,SAAS,EAAE,eAAiB;CMQzB;;AAVL,AAeI,YAfQ,AAcT,gBAAgB,CACf,sBAAsB,CAAC;EACrB,KAAK,EAAE,OAAwC;CAChD;;AAjBL,AAqBI,YArBQ,AAoBT,eAAe,CACd,sBAAsB,CAAC;EACrB,KAAK,EAAE,OAAsC;CAC9C;;AAIL,AAAA,qBAAqB,CAAC;ENzBpB,SAAS,EAAE,eAAiB;EM2B5B,MAAM,EAAE,KAAK;EACb,QAAQ,EAAE,IAAI;EACd,OAAO,EAAE,IAAI;CACd;;AAED,AAAA,gBAAgB;AAChB,iBAAiB,CAAC;EAChB,OAAO,EAAE,KAAK;CACf;;AAED,AAAA,gBAAgB,CAAC;EAEf,aAAa,EAAE,IAAI;CACpB;;AAHD,ApB1CE,gBoB0Cc,ApB1Cb,OAAO,CAAC;EACP,OAAO,EAAE,KAAK;EACd,KAAK,EAAE,IAAI;EACX,OAAO,EAAE,EAAE;CACZ;;AoB2CH,AAAA,qBAAqB;AACrB,qBAAqB,CAAC;EACpB,UAAU,EAAE,yBAAyB;CACtC;;AAED,AAAA,iBAAiB,CAAC;ExBhDd,aAAa,E1BiNa,MAAK;EkD5JjC,UAAU,EhBzCG,OAAO;EgB0CpB,MAAM,EAAE,GAAG,CAAC,KAAK,ChB1CJ,OAAO;EgB2CpB,KAAK,EhB0G0B,IAAI;EgBzGnC,MAAM,EAAE,YAAY;EACpB,OAAO,EAAE,QAAQ;EACjB,QAAQ,EAAE,QAAQ;CAsCnB;;AAhDD,AAaE,iBAbe,AAad,OAAO,EAbV,iBAAiB,AAcd,QAAQ,CAAC;EACR,MAAM,EAAE,iBAAiB;EACzB,kBAAkB,EhBpDP,OAAO;EgBqDlB,OAAO,EAAE,GAAG;EACZ,MAAM,EAAE,CAAC;EACT,cAAc,EAAE,IAAI;EACpB,QAAQ,EAAE,QAAQ;EAClB,KAAK,EAAE,IAAI;EACX,GAAG,EAAE,IAAI;EACT,KAAK,EAAE,CAAC;CACT;;AAxBH,AA0BE,iBA1Be,AA0Bd,OAAO,CAAC;EACP,YAAY,EAAE,GAAG;EACjB,UAAU,EAAE,IAAI;CACjB;;AA7BH,AA+BE,iBA/Be,AA+Bd,QAAQ,CAAC;EACR,YAAY,EAAE,GAAG;EACjB,UAAU,EAAE,IAAI;CACjB;;AAED,AAAA,MAAM,CApCR,iBAAiB,CAoCN;EACP,WAAW,EAAE,CAAC;EACd,YAAY,EAAE,IAAI;CASnB;;AAXD,AAIE,MAJI,CApCR,iBAAiB,AAwCZ,OAAO,EAJV,MAAM,CApCR,iBAAiB,AAyCZ,QAAQ,CAAC;EACR,iBAAiB,EhB9ER,OAAO;EgB+EhB,kBAAkB,EAAE,WAAW;EAC/B,IAAI,EAAE,IAAI;EACV,KAAK,EAAE,IAAI;CACZ;;AAIL,AAAA,gBAAgB,CAAC;ExBlGb,aAAa,EwBmGQ,GAAG;EAC1B,KAAK,EAAE,IAAI;EACX,MAAM,EAAE,IAAI;EACZ,KAAK,EAAE,IAAI;CAKZ;;AAHC,AAAA,MAAM,CANR,gBAAgB,CAML;EACP,KAAK,EAAE,KAAK;CACb;;AAGH,AAAA,kBAAkB,CAAC;EACjB,OAAO,EAAE,KAAK;EACd,SAAS,ElD2HmB,QAAwB;EkD1HpD,aAAa,EAAE,GAAG;CACnB;;AAED,AAAA,iBAAiB,CAAC;EAChB,WAAW,EAAE,GAAG;CACjB;;AAED,AAAA,sBAAsB,CAAC;EACrB,KAAK,EAAE,OAAsB;CAC9B;;AAGD,AACE,0BADwB,CACxB,qBAAqB,CAAC;EN5HtB,SAAS,EAAE,eAAiB;CM8H3B;;AAGH,AAAA,qBAAqB,CAAC;ENjIpB,SAAS,EAAE,kBAAiB;EMmI5B,UAAU,ElDtHD,OAAO;EkDuHhB,MAAM,EAAE,CAAC;EACT,KAAK,ElDhII,OAAO;EkDiIhB,MAAM,EAAE,KAAK;EACb,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,QAAQ;EAClB,GAAG,EAAE,CAAC;EACN,KAAK,EAAE,IAAI;CACZ;;AAED,AAAA,2BAA2B,CAAC;EAC1B,UAAU,ElDxID,OAAO;CkDqJjB;;AAdD,AAGE,2BAHyB,CAGzB,mBAAmB,CAAC;EAClB,KAAK,ElDrIE,OAAO;CkDsIf;;AALH,AAOE,2BAPyB,CAOzB,mBAAmB,CAAC;EAClB,KAAK,ElD1IE,OAAO;CkD2If;;AATH,AAWE,2BAXyB,CAWzB,kBAAkB,CAAC;EACjB,KAAK,EAAE,OAAsB;CAC9B;;AAIH,AAAA,cAAc,CAAC;E9BhKb,YAAY,EAAE,CAAC;EACf,UAAU,EAAE,IAAI;C8B4KjB;;AAbD,AAGE,cAHY,GAGV,EAAE,CAAC;EAEH,aAAa,EAAE,GAAG,CAAC,KAAK,ClDpJjB,kBAAI;EkDqJX,MAAM,EAAE,CAAC;EACT,OAAO,EAAE,IAAI;CAKd;;AAZH,ApBnKE,coBmKY,GAGV,EAAE,ApBtKH,OAAO,CAAC;EACP,OAAO,EAAE,KAAK;EACd,KAAK,EAAE,IAAI;EACX,OAAO,EAAE,EAAE;CACZ;;AoB+JH,AASI,cATU,GAGV,EAAE,AAMD,aAAa,CAAC;EACb,aAAa,EAAE,CAAC;CACjB;;AAIL,AAAA,kBAAkB,CAAC;ExB9Kf,aAAa,EwB+KQ,GAAG;EAC1B,KAAK,EAAE,IAAI;EACX,KAAK,EAAE,IAAI;CACZ;;AAED,AAAA,mBAAmB,CAAC;EAClB,KAAK,ElD/KI,OAAO;EkDgLhB,WAAW,EAAE,IAAI;CAClB;;AAED,AAAA,mBAAmB;AACnB,qBAAqB,CAAC;EACpB,OAAO,EAAE,KAAK;CACf;;AAED,AAAA,mBAAmB,CAAC;EAClB,WAAW,EAAE,GAAG;CACjB;;AAED,AAAA,qBAAqB,CAAC;EACpB,SAAS,ElDuCmB,QAAwB;CkDtCrD;;AAED,AAAA,mBAAmB,CAAC;EAClB,KAAK,ElD7LI,OAAO;EkD8LhB,WAAW,EAAE,MAAM;CACpB;;AAED,AAAA,kBAAkB,CAAC;EACjB,KAAK,EAAE,OAAsB;CAC9B;;AAIC,ARhNA,oBQgNoB,CRhNpB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,E1C6BJ,OAAO;E0C5Bb,YAAY,E1C4BN,OAAO;E0C3Bb,KAAK,E1CEE,OAAO;C0CIf;;AQuMD,AR3ME,oBQ2MkB,CRhNpB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQ2MV,oBAAoB,CRhNpB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,E1CuBb,OAAO;C0CtBZ;;AQwMH,ARhNA,sBQgNsB,CRhNtB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,E1CUH,OAAO;E0CTd,YAAY,E1CSL,OAAO;E0CRd,KAAK,E1CEE,OAAO;C0CIf;;AQuMD,AR3ME,sBQ2MoB,CRhNtB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQ2MV,sBAAsB,CRhNtB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,E1CIZ,OAAO;C0CHb;;AQwMH,ARhNA,oBQgNoB,CRhNpB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,E1CoCJ,OAAO;E0CnCb,YAAY,E1CmCN,OAAO;E0ClCb,KAAK,E1CEE,OAAO;C0CIf;;AQuMD,AR3ME,oBQ2MkB,CRhNpB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQ2MV,oBAAoB,CRhNpB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,E1C8Bb,OAAO;C0C7BZ;;AQwMH,ARhNA,iBQgNiB,CRhNjB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,E1CsCJ,OAAO;E0CrCb,YAAY,E1CqCN,OAAO;E0CpCb,KAAK,E1CEE,OAAO;C0CIf;;AQuMD,AR3ME,iBQ2Me,CRhNjB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQ2MV,iBAAiB,CRhNjB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,E1CgCb,OAAO;C0C/BZ;;AQwMH,ARhNA,oBQgNoB,CRhNpB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,E1CmCJ,OAAO;E0ClCb,YAAY,E1CkCN,OAAO;E0CjCb,KAAK,E1CoFO,OAAO;C0C9EpB;;AQuMD,AR3ME,oBQ2MkB,CRhNpB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQ2MV,oBAAoB,CRhNpB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,E1C6Bb,OAAO;C0C5BZ;;AQwMH,ARhNA,mBQgNmB,CRhNnB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,E1CiCJ,OAAO;E0ChCb,YAAY,E1CgCN,OAAO;E0C/Bb,KAAK,E1CEE,OAAO;C0CIf;;AQuMD,AR3ME,mBQ2MiB,CRhNnB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQ2MV,mBAAmB,CRhNnB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,E1C2Bb,OAAO;C0C1BZ;;AQwMH,ARhNA,kBQgNkB,CRhNlB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,E1CKH,OAAO;E0CJd,YAAY,E1CIL,OAAO;E0CHd,KAAK,E1CoFO,OAAO;C0C9EpB;;AQuMD,AR3ME,kBQ2MgB,CRhNlB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQ2MV,kBAAkB,CRhNlB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,E1CDZ,OAAO;C0CEb;;AQwMH,ARhNA,iBQgNiB,CRhNjB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,E1CYH,OAAO;E0CXd,YAAY,E1CWL,OAAO;E0CVd,KAAK,E1CEE,OAAO;C0CIf;;AQuMD,AR3ME,iBQ2Me,CRhNjB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQ2MV,iBAAiB,CRhNjB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,E1CMZ,OAAO;C0CLb;;AQ8MH,ARtNA,sBQsNsB,CRtNtB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,ERAF,OAAO;EQCf,YAAY,ERDJ,OAAO;EQEf,KAAK,E1CEE,OAAO;C0CIf;;AQ6MD,ARjNE,sBQiNoB,CRtNtB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQiNV,sBAAsB,CRtNtB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,ERNX,OAAO;CQOd;;AQ8MH,ARtNA,iBQsNiB,CRtNjB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,ERCP,OAAO;EQAV,YAAY,ERAT,OAAO;EQCV,KAAK,E1CEE,OAAO;C0CIf;;AQ6MD,ARjNE,iBQiNe,CRtNjB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQiNV,iBAAiB,CRtNjB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,ERLhB,OAAO;CQMT;;AQ8MH,ARtNA,kBQsNkB,CRtNlB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,ERGN,OAAO;EQFX,YAAY,ERER,OAAO;EQDX,KAAK,E1CEE,OAAO;C0CIf;;AQ6MD,ARjNE,kBQiNgB,CRtNlB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQiNV,kBAAkB,CRtNlB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,ERHf,OAAO;CQIV;;AQ8MH,ARtNA,iBQsNiB,CRtNjB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,ERIP,OAAO;EQHV,YAAY,ERGT,OAAO;EQFV,KAAK,E1CoFO,OAAO;C0C9EpB;;AQ6MD,ARjNE,iBQiNe,CRtNjB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQiNV,iBAAiB,CRtNjB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,ERFhB,OAAO;CQGT;;AQ8MH,ARtNA,oBQsNoB,CRtNpB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,ERMJ,OAAO;EQLb,YAAY,ERKN,OAAO;EQJb,KAAK,E1CEE,OAAO;C0CIf;;AQ6MD,ARjNE,oBQiNkB,CRtNpB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQiNV,oBAAoB,CRtNpB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,ERAb,OAAO;CQCZ;;AQ8MH,ARtNA,mBQsNmB,CRtNnB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,ERQL,OAAO;EQPZ,YAAY,EROP,OAAO;EQNZ,KAAK,E1CEE,OAAO;C0CIf;;AQ6MD,ARjNE,mBQiNiB,CRtNnB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQiNV,mBAAmB,CRtNnB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,EREd,OAAO;CQDX;;AQ8MH,ARtNA,iBQsNiB,CRtNjB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,E1C6BJ,OAAO;E0C5Bb,YAAY,E1C4BN,OAAO;E0C3Bb,KAAK,E1CEE,OAAO;C0CIf;;AQ6MD,ARjNE,iBQiNe,CRtNjB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQiNV,iBAAiB,CRtNjB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,E1CuBb,OAAO;C0CtBZ;;AQ8MH,ARtNA,mBQsNmB,CRtNnB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,E1C8BJ,OAAO;E0C7Bb,YAAY,E1C6BN,OAAO;E0C5Bb,KAAK,E1CEE,OAAO;C0CIf;;AQ6MD,ARjNE,mBQiNiB,CRtNnB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQiNV,mBAAmB,CRtNnB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,E1CwBb,OAAO;C0CvBZ;;AQ8MH,ARtNA,mBQsNmB,CRtNnB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,E1C+BJ,OAAO;E0C9Bb,YAAY,E1C8BN,OAAO;E0C7Bb,KAAK,E1CEE,OAAO;C0CIf;;AQ6MD,ARjNE,mBQiNiB,CRtNnB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQiNV,mBAAmB,CRtNnB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,E1CyBb,OAAO;C0CxBZ;;AQ8MH,ARtNA,iBQsNiB,CRtNjB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,E1CgCJ,OAAO;E0C/Bb,YAAY,E1C+BN,OAAO;E0C9Bb,KAAK,E1CEE,OAAO;C0CIf;;AQ6MD,ARjNE,iBQiNe,CRtNjB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQiNV,iBAAiB,CRtNjB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,E1C0Bb,OAAO;C0CzBZ;;AQ8MH,ARtNA,gBQsNgB,CRtNhB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,E1CiCJ,OAAO;E0ChCb,YAAY,E1CgCN,OAAO;E0C/Bb,KAAK,E1CEE,OAAO;C0CIf;;AQ6MD,ARjNE,gBQiNc,CRtNhB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQiNV,gBAAgB,CRtNhB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,E1C2Bb,OAAO;C0C1BZ;;AQ8MH,ARtNA,mBQsNmB,CRtNnB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,E1CkCJ,OAAO;E0CjCb,YAAY,E1CiCN,OAAO;E0ChCb,KAAK,E1CoFO,OAAO;C0C9EpB;;AQ6MD,ARjNE,mBQiNiB,CRtNnB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQiNV,mBAAmB,CRtNnB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,E1C4Bb,OAAO;C0C3BZ;;AQ8MH,ARtNA,mBQsNmB,CRtNnB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,E1CmCJ,OAAO;E0ClCb,YAAY,E1CkCN,OAAO;E0CjCb,KAAK,E1CoFO,OAAO;C0C9EpB;;AQ6MD,ARjNE,mBQiNiB,CRtNnB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQiNV,mBAAmB,CRtNnB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,E1C6Bb,OAAO;C0C5BZ;;AQ8MH,ARtNA,kBQsNkB,CRtNlB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,E1CoCJ,OAAO;E0CnCb,YAAY,E1CmCN,OAAO;E0ClCb,KAAK,E1CEE,OAAO;C0CIf;;AQ6MD,ARjNE,kBQiNgB,CRtNlB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQiNV,kBAAkB,CRtNlB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,E1C8Bb,OAAO;C0C7BZ;;AQ8MH,ARtNA,iBQsNiB,CRtNjB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,E1CqCJ,OAAO;E0CpCb,YAAY,E1CoCN,OAAO;E0CnCb,KAAK,E1CEE,OAAO;C0CIf;;AQ6MD,ARjNE,iBQiNe,CRtNjB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQiNV,iBAAiB,CRtNjB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,E1C+Bb,OAAO;C0C9BZ;;AQ8MH,ARtNA,iBQsNiB,CRtNjB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,E1CsCJ,OAAO;E0CrCb,YAAY,E1CqCN,OAAO;E0CpCb,KAAK,E1CEE,OAAO;C0CIf;;AQ6MD,ARjNE,iBQiNe,CRtNjB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQiNV,iBAAiB,CRtNjB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,E1CgCb,OAAO;C0C/BZ;;AQ8MH,ARtNA,kBQsNkB,CRtNlB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,E1CIH,OAAO;E0CHd,YAAY,E1CGL,OAAO;E0CFd,KAAK,E1CoFO,OAAO;C0C9EpB;;AQ6MD,ARjNE,kBQiNgB,CRtNlB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQiNV,kBAAkB,CRtNlB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,E1CFZ,OAAO;C0CGb;;AQ8MH,ARtNA,iBQsNiB,CRtNjB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,E1CUH,OAAO;E0CTd,YAAY,E1CSL,OAAO;E0CRd,KAAK,E1CEE,OAAO;C0CIf;;AQ6MD,ARjNE,iBQiNe,CRtNjB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQiNV,iBAAiB,CRtNjB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,E1CIZ,OAAO;C0CHb;;AQ8MH,ARtNA,sBQsNsB,CRtNtB,MAAM,GAAG,iBAAiB,CAAC;EACzB,UAAU,E1CYH,OAAO;E0CXd,YAAY,E1CWL,OAAO;E0CVd,KAAK,E1CEE,OAAO;C0CIf;;AQ6MD,ARjNE,sBQiNoB,CRtNtB,MAAM,GAAG,iBAAiB,AAKvB,OAAO,EQiNV,sBAAsB,CRtNtB,MAAM,GAAG,iBAAiB,AAMvB,QAAQ,CAAC;EACR,iBAAiB,E1CMZ,OAAO;C0CLb;;ASVL,AAAA,WAAW,CAAC;E/BAV,YAAY,EAAE,CAAC;EACf,UAAU,EAAE,IAAI;C+BqBjB;;AAtBD,AAGE,WAHS,GAGP,EAAE,CAAC;EACH,KAAK,EAAE,IAAI;EACX,OAAO,EAAE,IAAI;EACb,UAAU,EAAE,MAAM;EAClB,KAAK,EAAE,GAAG;CAcX;;AArBH,AASI,WATO,GAGP,EAAE,CAMF,GAAG,CAAC;EzBRJ,aAAa,EyBSY,GAAG;EAC1B,MAAM,EAAE,IAAI;EACZ,SAAS,EAAE,IAAI;CAChB;;AAbL,AAgBM,WAhBK,GAGP,EAAE,GAYA,CAAC,AAAA,MAAM;AAfb,WAAW,GAGP,EAAE,GAYA,CAAC,AAAA,MAAM,CAEP,gBAAgB,CAAC;EACf,KAAK,EAAE,IAAI;CACZ;;AAKP,AAAA,gBAAgB;AAChB,gBAAgB,CAAC;EACf,OAAO,EAAE,KAAK;CACf;;AAED,AAAA,gBAAgB,CAAC;EACf,KAAK,EnDhBI,OAAO;EmDiBhB,SAAS,EnD4MmB,QAAwB;EmD3MpD,QAAQ,EAAE,MAAM;EAChB,aAAa,EAAE,QAAQ;EACvB,WAAW,EAAE,MAAM;CACpB;;AAED,AAAA,gBAAgB,CAAC;EACf,KAAK,EAAE,OAAsB;EAC7B,SAAS,EAAE,IAAI;CAChB;;ACvCD,AAAA,YAAY,CAAC;EACX,MAAM,EAAE,CAAC;EACT,QAAQ,EAAE,QAAQ;CACnB;;AAGD,AAGE,YAHU,CAGV,mBAAmB,CAAC;E1BAlB,sBAAsB,E1BuMI,OAAM;E0BtMhC,uBAAuB,E1BsMG,OAAM;EoDlMhC,MAAM,EAAE,KAAK;EACb,OAAO,EAAE,IAAI;EACb,UAAU,EAAE,MAAM;CACnB;;AAXH,AAcE,YAdU,CAcV,qBAAqB,CAAC;EACpB,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,GAAG;EAChB,aAAa,EAAE,CAAC;EAChB,UAAU,EAAE,CAAC;EACb,WAAW,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CpDTf,kBAAI;CoDUZ;;AApBH,AAuBE,YAvBU,CAuBV,iBAAiB,CAAC;EAChB,UAAU,EAAE,CAAC;CACd;;AAzBH,AA4BE,YA5BU,CA4BV,kBAAkB,CAAC;EACjB,IAAI,EAAE,GAAG;EACT,WAAW,EAAE,KAAK;EAClB,QAAQ,EAAE,QAAQ;EAClB,GAAG,EAAE,IAAI;CAOV;;AAvCH,AAkCI,YAlCQ,CA4BV,kBAAkB,GAMd,GAAG,CAAC;EACJ,MAAM,EAAE,GAAG,CAAC,KAAK,CpDnCZ,OAAO;EoDoCZ,MAAM,EAAE,IAAI;EACZ,KAAK,EAAE,IAAI;CACZ;;AAtCL,AAyCE,YAzCU,CAyCV,YAAY,CAAC;EACX,WAAW,EAAE,IAAI;CAClB;;AAIH,AAGE,cAHY,CAGZ,mBAAmB,CAAC;E1B/ClB,sBAAsB,E1BuMI,OAAM;E0BtMhC,uBAAuB,E1BsMG,OAAM;EoDtJhC,OAAO,EAAE,IAAI;CACd;;AANH,AASE,cATY,CASZ,qBAAqB,CAAC;EACpB,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,GAAG;EAChB,aAAa,EAAE,GAAG;EAClB,UAAU,EAAE,GAAG;CAChB;;AAdH,AAiBE,cAjBY,CAiBZ,iBAAiB,CAAC;EAChB,UAAU,EAAE,CAAC;CACd;;AAnBH,AAqBE,cArBY,CAqBZ,qBAAqB;AArBvB,cAAc,CAsBZ,iBAAiB,CAAC;EAChB,WAAW,EAAE,IAAI;CAClB;;AAxBH,AA4BI,cA5BU,CA2BZ,kBAAkB,GACd,GAAG,CAAC;EACJ,KAAK,EAAE,IAAI;EACX,MAAM,EAAE,IAAI;EACZ,KAAK,EAAE,IAAI;CACZ"}