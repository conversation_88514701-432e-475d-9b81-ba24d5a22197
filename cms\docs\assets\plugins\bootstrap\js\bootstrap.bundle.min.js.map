{"version": 3, "sources": ["../../js/src/util.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../node_modules/popper.js/dist/esm/popper.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/tools/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js"], "names": ["TRANSITION_END", "transitionEndEmulator", "duration", "_this", "this", "called", "$", "one", "<PERSON><PERSON>", "setTimeout", "triggerTransitionEnd", "getUID", "prefix", "Math", "random", "document", "getElementById", "getSelectorFromElement", "element", "selector", "getAttribute", "hrefAttr", "trim", "querySelector", "err", "getTransitionDurationFromElement", "transitionDuration", "css", "transitionDelay", "floatTransitionDuration", "parseFloat", "floatTransitionDelay", "split", "reflow", "offsetHeight", "trigger", "supportsTransitionEnd", "Boolean", "isElement", "obj", "nodeType", "typeCheckConfig", "componentName", "config", "configTypes", "property", "Object", "prototype", "hasOwnProperty", "call", "expectedTypes", "value", "valueType", "toString", "match", "toLowerCase", "RegExp", "test", "Error", "toUpperCase", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "ShadowRoot", "parentNode", "root", "jQueryDetection", "TypeError", "version", "fn", "j<PERSON>y", "emulateTransitionEnd", "event", "special", "bindType", "delegateType", "handle", "target", "is", "handleObj", "handler", "apply", "arguments", "NAME", "DATA_KEY", "EVENT_KEY", "JQUERY_NO_CONFLICT", "Event", "CLOSE", "CLOSED", "CLICK_DATA_API", "ClassName", "<PERSON><PERSON>", "_element", "close", "rootElement", "_getRootElement", "_triggerCloseEvent", "isDefaultPrevented", "_removeElement", "dispose", "removeData", "parent", "closest", "closeEvent", "removeClass", "hasClass", "_destroyElement", "detach", "remove", "_jQueryInterface", "each", "$element", "data", "_handleDismiss", "alertInstance", "preventDefault", "on", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "DATA_API_KEY", "Selector", "FOCUS_BLUR_DATA_API", "LOAD_DATA_API", "<PERSON><PERSON>", "toggle", "triggerChangeEvent", "addAriaPressed", "input", "type", "checked", "classList", "contains", "activeElement", "tagName", "focus", "hasAttribute", "setAttribute", "toggleClass", "button", "inputBtn", "window", "buttons", "slice", "querySelectorAll", "i", "len", "length", "add", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "Direction", "SLIDE", "SLID", "KEYDOWN", "MOUSEENTER", "MOUSELEAVE", "TOUCHSTART", "TOUCHMOVE", "TOUCHEND", "POINTERDOWN", "POINTERUP", "DRAG_START", "PointerType", "TOUCH", "PEN", "Carousel", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "MSPointerEvent", "_addEventListeners", "next", "_slide", "nextWhenVisible", "hidden", "prev", "cycle", "clearInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "direction", "off", "_objectSpread2", "_handleSwipe", "absDeltax", "abs", "_this2", "_keydown", "_addTouchEventListeners", "_this3", "start", "originalEvent", "pointerType", "clientX", "touches", "end", "clearTimeout", "e", "move", "which", "indexOf", "_getItemByDirection", "isNextDirection", "isPrevDirection", "lastItemIndex", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "slideEvent", "from", "_setActiveIndicatorElement", "indicators", "nextIndicator", "children", "addClass", "directionalClassName", "orderClassName", "_this4", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "slidEvent", "nextElementInterval", "parseInt", "defaultInterval", "action", "ride", "_dataApiClickHandler", "slideIndex", "carousels", "$carousel", "SHOW", "SHOWN", "HIDE", "HIDDEN", "Dimension", "Collapse", "_isTransitioning", "_triggerArray", "id", "toggleList", "elem", "filterElement", "filter", "foundElem", "_selector", "push", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "not", "startEvent", "dimension", "_getDimension", "style", "attr", "setTransitioning", "scrollSize", "getBoundingClientRect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "_getTargetFromElement", "trigger<PERSON><PERSON>y", "isOpen", "$this", "currentTarget", "$trigger", "selectors", "$target", "<PERSON><PERSON><PERSON><PERSON>", "timeoutDuration", "longerTimeoutBrowsers", "userAgent", "debounce", "Promise", "resolve", "then", "scheduled", "isFunction", "functionToCheck", "getStyleComputedProperty", "ownerDocument", "defaultView", "getComputedStyle", "getParentNode", "nodeName", "host", "getScrollParent", "body", "_getStyleComputedProp", "overflow", "overflowX", "overflowY", "getReferenceNode", "reference", "referenceNode", "isIE11", "MSInputMethodContext", "documentMode", "isIE10", "isIE", "getOffsetParent", "noOffsetParent", "offsetParent", "nextElement<PERSON><PERSON>ling", "getRoot", "node", "findCommonOffsetParent", "element1", "element2", "order", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "range", "createRange", "setStart", "setEnd", "commonAncestorContainer", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "isOffsetContainer", "element1root", "getScroll", "upperSide", "undefined", "html", "scrollingElement", "getBordersSize", "styles", "axis", "sideA", "sideB", "getSize", "computedStyle", "max", "getWindowSizes", "height", "width", "createClass", "protoProps", "staticProps", "defineProperties", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "key", "_extends", "assign", "source", "getClientRect", "offsets", "right", "left", "bottom", "top", "rect", "scrollTop", "scrollLeft", "result", "sizes", "clientWidth", "clientHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "vertScrollbar", "getOffsetRectRelativeToArbitraryNode", "fixedPosition", "isHTML", "childrenRect", "parentRect", "scrollParent", "borderTopWidth", "borderLeftWidth", "marginTop", "marginLeft", "subtract", "modifier", "includeScroll", "getFixedPositionOffsetParent", "parentElement", "el", "getBoundaries", "popper", "padding", "boundariesElement", "boundaries", "excludeScroll", "relativeOffset", "innerWidth", "innerHeight", "getViewportOffsetRectRelativeToArtbitraryNode", "boundariesNode", "isFixed", "_getWindowSizes", "isPaddingNumber", "computeAutoPlacement", "placement", "refRect", "rects", "sorted<PERSON>reas", "keys", "map", "area", "_ref", "getArea", "sort", "a", "b", "filtered<PERSON><PERSON>s", "_ref2", "computedPlacement", "variation", "getReferenceOffsets", "state", "getOuterSizes", "x", "marginBottom", "y", "marginRight", "getOppositePlacement", "hash", "replace", "matched", "getPopperOffsets", "referenceOffsets", "popperRect", "popperOffsets", "isHoriz", "mainSide", "secondarySide", "measurement", "secondaryMeasurement", "find", "arr", "check", "Array", "runModifiers", "modifiers", "ends", "prop", "findIndex", "cur", "for<PERSON>ach", "console", "warn", "enabled", "isModifierEnabled", "modifierName", "some", "name", "getSupportedPropertyName", "prefixes", "upperProp", "char<PERSON>t", "to<PERSON><PERSON><PERSON>", "getWindow", "setupEventListeners", "options", "updateBound", "addEventListener", "passive", "scrollElement", "attachToScrollParents", "callback", "scrollParents", "isBody", "eventsEnabled", "disableEventListeners", "cancelAnimationFrame", "scheduleUpdate", "removeEventListener", "removeEventListeners", "isNumeric", "n", "isNaN", "isFinite", "setStyles", "unit", "getRoundedOffsets", "shouldRound", "noRound", "v", "_data$offsets", "round", "floor", "referenceWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isVertical", "isVariation", "horizontalToInteger", "verticalToInteger", "isFirefox", "isModifierRequired", "requestingName", "requested<PERSON><PERSON>", "requesting", "isRequired", "_requesting", "requested", "placements", "validPlacements", "clockwise", "counter", "concat", "reverse", "BEHAVIORS", "parseOffset", "offset", "basePlacement", "useHeight", "fragments", "frag", "divider", "search", "splitRegex", "ops", "op", "mergeWithPrevious", "reduce", "str", "toValue", "index2", "De<PERSON>ults", "positionFixed", "removeOnDestroy", "onCreate", "onUpdate", "shift", "shiftvariation", "side", "shiftOffsets", "preventOverflow", "instance", "transformProp", "popperStyles", "transform", "priority", "primary", "escapeWithReference", "secondary", "min", "keepTogether", "opSide", "arrow", "_data$offsets$arrow", "arrowElement", "sideCapitalized", "altSide", "arrowElementSize", "center", "popperMarginSide", "popperBorderSide", "sideValue", "flip", "flipped", "originalPlacement", "placementOpposite", "flipOrder", "behavior", "step", "refOffsets", "overlapsRef", "overflowsLeft", "overflowsRight", "overflowsTop", "overflowsBottom", "overflowsBoundaries", "flippedVariationByRef", "flipVariations", "flippedVariationByContent", "flipVariationsByContent", "flippedVariation", "getOppositeVariation", "inner", "subtractLength", "bound", "attributes", "computeStyle", "legacyGpuAccelerationOption", "gpuAcceleration", "offsetParentRect", "position", "devicePixelRatio", "prefixedProperty", "<PERSON><PERSON><PERSON><PERSON>", "invertTop", "invertLeft", "x-placement", "arrowStyles", "applyStyle", "removeAttribute", "setAttributes", "onLoad", "modifierOptions", "<PERSON><PERSON>", "isDestroyed", "isCreated", "<PERSON><PERSON><PERSON><PERSON>", "classCallCheck", "requestAnimationFrame", "update", "enableEventListeners", "Utils", "global", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "REGEXP_KEYDOWN", "ARROW_UP_KEYCODE", "CLICK", "KEYDOWN_DATA_API", "KEYUP_DATA_API", "AttachmentMap", "boundary", "display", "popperConfig", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "disabled", "isActive", "_clearMenus", "usePopper", "showEvent", "_getParentFromElement", "referenceElement", "_getPopperConfig", "noop", "hideEvent", "destroy", "stopPropagation", "constructor", "_getPlacement", "$parentDropdown", "_getOffset", "toggles", "context", "clickEvent", "dropdownMenu", "_dataApiKeydownHandler", "items", "item", "backdrop", "HIDE_PREVENTED", "FOCUSIN", "RESIZE", "CLICK_DISMISS", "KEYDOWN_DISMISS", "MOUSEUP_DISMISS", "MOUSEDOWN_DISMISS", "Modal", "_dialog", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_scrollbarWidth", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "transition", "_hideModal", "htmlElement", "handleUpdate", "_triggerBackdropTransition", "hideEventPrevented", "defaultPrevented", "modalTransitionDuration", "modalBody", "ELEMENT_NODE", "append<PERSON><PERSON><PERSON>", "_enforceFocus", "transitionComplete", "shownEvent", "_this5", "has", "_this6", "_this7", "_this8", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "_this9", "animate", "createElement", "className", "appendTo", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "isModalOverflowing", "scrollHeight", "paddingLeft", "paddingRight", "_getScrollbarWidth", "_this10", "fixedContent", "sticky<PERSON>ontent", "actualPadding", "calculatedPadding", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "elements", "margin", "scrollDiv", "scrollbarWidth", "_this11", "uriAttrs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "*", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "sanitizeHtml", "unsafeHtml", "whiteList", "sanitizeFn", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "whitelist<PERSON><PERSON>s", "_loop", "el<PERSON>ame", "attributeList", "whitelistedAttributes", "allowedAttributeList", "attrName", "nodeValue", "regExp", "attrRegex", "l", "allowedAttribute", "innerHTML", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "container", "fallbackPlacement", "sanitize", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "HoverState", "INSERTED", "FOCUSOUT", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "dataKey", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "isWithContent", "shadowRoot", "isInTheDom", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "complete", "_fixTransition", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "text", "empty", "append", "_handlePopperPlacementChange", "eventIn", "eventOut", "_fixTitle", "titleType", "dataAttributes", "dataAttr", "$tip", "tabClass", "join", "popperData", "popperInstance", "initConfigAnimation", "Popover", "_getContent", "method", "ACTIVATE", "SCROLL", "OffsetMethod", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targetSelector", "targetBCR", "pageYOffset", "_getOffsetHeight", "maxScroll", "_activate", "_clear", "queries", "$link", "parents", "scrollSpys", "$spy", "Tab", "previous", "listElement", "itemSelector", "makeArray", "hiddenEvent", "_transitionComplete", "active", "dropdown<PERSON><PERSON>d", "dropdownElement", "dropdownToggleList", "autohide", "Toast", "_close"], "mappings": ";;;;;ypCAeA,IAAMA,EAAiB,gBAsBvB,SAASC,EAAsBC,GAAU,IAAAC,EAAAC,KACnCC,GAAS,EAYb,OAVAC,EAAEF,MAAMG,IAAIC,EAAKR,eAAgB,WAC/BK,GAAS,IAGXI,WAAW,WACJJ,GACHG,EAAKE,qBAAqBP,IAE3BD,GAEIE,KAcT,IAAMI,EAAO,CAEXR,eAAgB,kBAEhBW,OAJW,SAIJC,GACL,KAEEA,MAvDU,IAuDGC,KAAKC,UACXC,SAASC,eAAeJ,KACjC,OAAOA,GAGTK,uBAZW,SAYYC,GACrB,IAAIC,EAAWD,EAAQE,aAAa,eAEpC,IAAKD,GAAyB,MAAbA,EAAkB,CACjC,IAAME,EAAWH,EAAQE,aAAa,QACtCD,EAAWE,GAAyB,MAAbA,EAAmBA,EAASC,OAAS,GAG9D,IACE,OAAOP,SAASQ,cAAcJ,GAAYA,EAAW,KACrD,MAAOK,GACP,OAAO,OAIXC,iCA3BW,SA2BsBP,GAC/B,IAAKA,EACH,OAAO,EAIT,IAAIQ,EAAqBpB,EAAEY,GAASS,IAAI,uBACpCC,EAAkBtB,EAAEY,GAASS,IAAI,oBAE/BE,EAA0BC,WAAWJ,GACrCK,EAAuBD,WAAWF,GAGxC,OAAKC,GAA4BE,GAKjCL,EAAqBA,EAAmBM,MAAM,KAAK,GACnDJ,EAAkBA,EAAgBI,MAAM,KAAK,GA7FjB,KA+FpBF,WAAWJ,GAAsBI,WAAWF,KAP3C,GAUXK,OAnDW,SAmDJf,GACL,OAAOA,EAAQgB,cAGjBxB,qBAvDW,SAuDUQ,GACnBZ,EAAEY,GAASiB,QAAQnC,IAIrBoC,sBA5DW,WA6DT,OAAOC,QAAQrC,IAGjBsC,UAhEW,SAgEDC,GACR,OAAQA,EAAI,IAAMA,GAAKC,UAGzBC,gBApEW,SAoEKC,EAAeC,EAAQC,GACrC,IAAK,IAAMC,KAAYD,EACrB,GAAIE,OAAOC,UAAUC,eAAeC,KAAKL,EAAaC,GAAW,CAC/D,IAAMK,EAAgBN,EAAYC,GAC5BM,EAAgBR,EAAOE,GACvBO,EAAgBD,GAAS3C,EAAK8B,UAAUa,GAC1C,WAtHIZ,EAsHeY,EArHtB,GAAGE,SAASJ,KAAKV,GAAKe,MAAM,eAAe,GAAGC,eAuH/C,IAAK,IAAIC,OAAON,GAAeO,KAAKL,GAClC,MAAM,IAAIM,MACLhB,EAAciB,cAAjB,aACWd,EADX,oBACuCO,EADvC,wBAEsBF,EAFtB,MA1HZ,IAAgBX,GAkIdqB,eAtFW,SAsFI1C,GACb,IAAKH,SAAS8C,gBAAgBC,aAC5B,OAAO,KAIT,GAAmC,mBAAxB5C,EAAQ6C,YAKnB,OAAI7C,aAAmB8C,WACd9C,EAIJA,EAAQ+C,WAINzD,EAAKoD,eAAe1C,EAAQ+C,YAH1B,KAVP,IAAMC,EAAOhD,EAAQ6C,cACrB,OAAOG,aAAgBF,WAAaE,EAAO,MAe/CC,gBA7GW,WA8GT,GAAiB,oBAAN7D,EACT,MAAM,IAAI8D,UAAU,kGAGtB,IAAMC,EAAU/D,EAAEgE,GAAGC,OAAOvC,MAAM,KAAK,GAAGA,MAAM,KAOhD,GAAIqC,EAAQ,GALI,GAKYA,EAAQ,GAJnB,GAFA,IAMoCA,EAAQ,IAJ5C,IAI+DA,EAAQ,IAAmBA,EAAQ,GAHlG,GACA,GAEmHA,EAAQ,GAC1I,MAAM,IAAIX,MAAM,iFAKtBlD,EAAK2D,kBAzIH7D,EAAEgE,GAAGE,qBAAuBvE,EAC5BK,EAAEmE,MAAMC,QAAQlE,EAAKR,gBA9Bd,CACL2E,SAAU3E,EACV4E,aAAc5E,EACd6E,OAHK,SAGEJ,GACL,GAAInE,EAAEmE,EAAMK,QAAQC,GAAG3E,MACrB,OAAOqE,EAAMO,UAAUC,QAAQC,MAAM9E,KAAM+E,aCdnD,IAAMC,EAAsB,QAEtBC,EAAsB,WACtBC,EAAS,IAAiBD,EAE1BE,EAAsBjF,EAAEgE,GAAGc,GAM3BI,EAAQ,CACZC,MAAK,QAAoBH,EACzBI,OAAM,SAAoBJ,EAC1BK,eAAc,QAAWL,EAVC,aAatBM,EACI,QADJA,EAEI,OAFJA,EAGI,OASJC,aACJ,SAAAA,EAAY3E,GACVd,KAAK0F,SAAW5E,6BAWlB6E,MAAA,SAAM7E,GACJ,IAAI8E,EAAc5F,KAAK0F,SACnB5E,IACF8E,EAAc5F,KAAK6F,gBAAgB/E,IAGjBd,KAAK8F,mBAAmBF,GAE5BG,sBAIhB/F,KAAKgG,eAAeJ,MAGtBK,QAAA,WACE/F,EAAEgG,WAAWlG,KAAK0F,SAAUT,GAC5BjF,KAAK0F,SAAW,QAKlBG,gBAAA,SAAgB/E,GACd,IAAMC,EAAWX,EAAKS,uBAAuBC,GACzCqF,GAAa,EAUjB,OARIpF,IACFoF,EAASxF,SAASQ,cAAcJ,IAIhCoF,EADGA,GACMjG,EAAEY,GAASsF,QAAX,IAAuBZ,GAAmB,MAMvDM,mBAAA,SAAmBhF,GACjB,IAAMuF,EAAanG,EAAEkF,MAAMA,EAAMC,OAGjC,OADAnF,EAAEY,GAASiB,QAAQsE,GACZA,KAGTL,eAAA,SAAelF,GAAS,IAAAf,EAAAC,KAGtB,GAFAE,EAAEY,GAASwF,YAAYd,GAElBtF,EAAEY,GAASyF,SAASf,GAAzB,CAKA,IAAMlE,EAAqBlB,EAAKiB,iCAAiCP,GAEjEZ,EAAEY,GACCX,IAAIC,EAAKR,eAAgB,SAACyE,GAAD,OAAWtE,EAAKyG,gBAAgB1F,EAASuD,KAClED,qBAAqB9C,QARtBtB,KAAKwG,gBAAgB1F,MAWzB0F,gBAAA,SAAgB1F,GACdZ,EAAEY,GACC2F,SACA1E,QAAQqD,EAAME,QACdoB,YAKEC,iBAAP,SAAwBpE,GACtB,OAAOvC,KAAK4G,KAAK,WACf,IAAMC,EAAW3G,EAAEF,MACf8G,EAAaD,EAASC,KAAK7B,GAE1B6B,IACHA,EAAO,IAAIrB,EAAMzF,MACjB6G,EAASC,KAAK7B,EAAU6B,IAGX,UAAXvE,GACFuE,EAAKvE,GAAQvC,WAKZ+G,eAAP,SAAsBC,GACpB,OAAO,SAAU3C,GACXA,GACFA,EAAM4C,iBAGRD,EAAcrB,MAAM3F,gDA/FtB,MApCwB,iBA8I5BE,EAAES,UAAUuG,GACV9B,EAAMG,eAxII,yBA0IVE,EAAMsB,eAAe,IAAItB,IAS3BvF,EAAEgE,GAAGc,GAAoBS,EAAMkB,iBAC/BzG,EAAEgE,GAAGc,GAAMmC,YAAc1B,EACzBvF,EAAEgE,GAAGc,GAAMoC,WAAc,WAEvB,OADAlH,EAAEgE,GAAGc,GAAQG,EACNM,EAAMkB,kBChKf,IAAM3B,EAAsB,SAEtBC,EAAsB,YACtBC,EAAS,IAAiBD,EAC1BoC,EAAsB,YACtBlC,EAAsBjF,EAAEgE,GAAGc,GAE3BQ,EACK,SADLA,EAEK,MAFLA,EAGK,QAGL8B,EACmB,0BADnBA,EAEmB,0BAFnBA,EAGmB,yBAHnBA,EAImB,+BAJnBA,EAKmB,6BALnBA,EAMmB,UANnBA,EAOmB,OAGnBlC,EAAQ,CACZG,eAAc,QAAgBL,EAAYmC,EAC1CE,oBAAsB,QAAQrC,EAAYmC,EAApB,QACSnC,EAAYmC,EAC3CG,cAAa,OAAgBtC,EAAYmC,GASrCI,aACJ,SAAAA,EAAY3G,GACVd,KAAK0F,SAAW5E,6BAWlB4G,OAAA,WACE,IAAIC,GAAqB,EACrBC,GAAiB,EACfhC,EAAc1F,EAAEF,KAAK0F,UAAUU,QACnCkB,GACA,GAEF,GAAI1B,EAAa,CACf,IAAMiC,EAAQ7H,KAAK0F,SAASvE,cAAcmG,GAE1C,GAAIO,EAAO,CACT,GAAmB,UAAfA,EAAMC,KACR,GAAID,EAAME,SACR/H,KAAK0F,SAASsC,UAAUC,SAASzC,GACjCmC,GAAqB,MAChB,CACL,IAAMO,EAAgBtC,EAAYzE,cAAcmG,GAE5CY,GACFhI,EAAEgI,GAAe5B,YAAYd,OAGT,aAAfqC,EAAMC,KACe,UAA1B9H,KAAK0F,SAASyC,SAAuBN,EAAME,UAAY/H,KAAK0F,SAASsC,UAAUC,SAASzC,KAC1FmC,GAAqB,GAIvBA,GAAqB,EAGnBA,IACFE,EAAME,SAAW/H,KAAK0F,SAASsC,UAAUC,SAASzC,GAClDtF,EAAE2H,GAAO9F,QAAQ,WAGnB8F,EAAMO,QACNR,GAAiB,GAIf5H,KAAK0F,SAAS2C,aAAa,aAAerI,KAAK0F,SAASsC,UAAUC,SAAS,cAC3EL,GACF5H,KAAK0F,SAAS4C,aAAa,gBACxBtI,KAAK0F,SAASsC,UAAUC,SAASzC,IAGlCmC,GACFzH,EAAEF,KAAK0F,UAAU6C,YAAY/C,OAKnCS,QAAA,WACE/F,EAAEgG,WAAWlG,KAAK0F,SAAUT,GAC5BjF,KAAK0F,SAAW,QAKXiB,iBAAP,SAAwBpE,GACtB,OAAOvC,KAAK4G,KAAK,WACf,IAAIE,EAAO5G,EAAEF,MAAM8G,KAAK7B,GAEnB6B,IACHA,EAAO,IAAIW,EAAOzH,MAClBE,EAAEF,MAAM8G,KAAK7B,EAAU6B,IAGV,WAAXvE,GACFuE,EAAKvE,gDA3ET,MA3CwB,iBAkI5BrC,EAAES,UACCuG,GAAG9B,EAAMG,eAAgB+B,EAA6B,SAACjD,GACtD,IAAImE,EAASnE,EAAMK,OAMnB,GAJKxE,EAAEsI,GAAQjC,SAASf,KACtBgD,EAAStI,EAAEsI,GAAQpC,QAAQkB,GAAiB,KAGzCkB,GAAUA,EAAOH,aAAa,aAAeG,EAAOR,UAAUC,SAAS,YAC1E5D,EAAM4C,qBACD,CACL,IAAMwB,EAAWD,EAAOrH,cAAcmG,GAEtC,GAAImB,IAAaA,EAASJ,aAAa,aAAeI,EAAST,UAAUC,SAAS,aAEhF,YADA5D,EAAM4C,iBAIRQ,EAAOd,iBAAiB9D,KAAK3C,EAAEsI,GAAS,aAG3CtB,GAAG9B,EAAMmC,oBAAqBD,EAA6B,SAACjD,GAC3D,IAAMmE,EAAStI,EAAEmE,EAAMK,QAAQ0B,QAAQkB,GAAiB,GACxDpH,EAAEsI,GAAQD,YAAY/C,EAAiB,eAAenC,KAAKgB,EAAMyD,SAGrE5H,EAAEwI,QAAQxB,GAAG9B,EAAMoC,cAAe,WAKhC,IADA,IAAImB,EAAU,GAAGC,MAAM/F,KAAKlC,SAASkI,iBAAiBvB,IAC7CwB,EAAI,EAAGC,EAAMJ,EAAQK,OAAQF,EAAIC,EAAKD,IAAK,CAClD,IAAMN,EAASG,EAAQG,GACjBjB,EAAQW,EAAOrH,cAAcmG,GAC/BO,EAAME,SAAWF,EAAMQ,aAAa,WACtCG,EAAOR,UAAUiB,IAAIzD,GAErBgD,EAAOR,UAAUtB,OAAOlB,GAM5B,IAAK,IAAIsD,EAAI,EAAGC,GADhBJ,EAAU,GAAGC,MAAM/F,KAAKlC,SAASkI,iBAAiBvB,KACpB0B,OAAQF,EAAIC,EAAKD,IAAK,CAClD,IAAMN,EAASG,EAAQG,GACqB,SAAxCN,EAAOxH,aAAa,gBACtBwH,EAAOR,UAAUiB,IAAIzD,GAErBgD,EAAOR,UAAUtB,OAAOlB,MAW9BtF,EAAEgE,GAAGc,GAAQyC,EAAOd,iBACpBzG,EAAEgE,GAAGc,GAAMmC,YAAcM,EACzBvH,EAAEgE,GAAGc,GAAMoC,WAAa,WAEtB,OADAlH,EAAEgE,GAAGc,GAAQG,EACNsC,EAAOd,kBCjMhB,IAAM3B,EAAyB,WAEzBC,EAAyB,cACzBC,EAAS,IAAoBD,EAC7BoC,EAAyB,YACzBlC,EAAyBjF,EAAEgE,GAAGc,GAM9BkE,EAAU,CACdC,SAAW,IACXC,UAAW,EACXC,OAAW,EACXC,MAAW,QACXC,MAAW,EACXC,OAAW,GAGPC,EAAc,CAClBN,SAAW,mBACXC,SAAW,UACXC,MAAW,mBACXC,MAAW,mBACXC,KAAW,UACXC,MAAW,WAGPE,EACO,OADPA,EAEO,OAFPA,EAGO,OAHPA,EAIO,QAGPtE,EAAQ,CACZuE,MAAK,QAAoBzE,EACzB0E,KAAI,OAAoB1E,EACxB2E,QAAO,UAAoB3E,EAC3B4E,WAAU,aAAoB5E,EAC9B6E,WAAU,aAAoB7E,EAC9B8E,WAAU,aAAoB9E,EAC9B+E,UAAS,YAAoB/E,EAC7BgF,SAAQ,WAAoBhF,EAC5BiF,YAAW,cAAoBjF,EAC/BkF,UAAS,YAAoBlF,EAC7BmF,WAAU,YAAmBnF,EAC7BsC,cAAa,OAAWtC,EAAYmC,EACpC9B,eAAc,QAAWL,EAAYmC,GAGjC7B,EACY,WADZA,EAEY,SAFZA,EAGY,QAHZA,EAIY,sBAJZA,EAKY,qBALZA,EAMY,qBANZA,EAOY,qBAPZA,GASY,gBAGZ8B,GACU,UADVA,GAEU,wBAFVA,GAGU,iBAHVA,GAIU,qBAJVA,GAKU,2CALVA,GAMU,uBANVA,GAOU,gCAPVA,GAQU,yBAGVgD,GAAc,CAClBC,MAAQ,QACRC,IAAQ,OAQJC,cACJ,SAAAA,EAAY3J,EAASyB,GACnBvC,KAAK0K,OAAiB,KACtB1K,KAAK2K,UAAiB,KACtB3K,KAAK4K,eAAiB,KACtB5K,KAAK6K,WAAiB,EACtB7K,KAAK8K,YAAiB,EACtB9K,KAAK+K,aAAiB,KACtB/K,KAAKgL,YAAiB,EACtBhL,KAAKiL,YAAiB,EAEtBjL,KAAKkL,QAAqBlL,KAAKmL,WAAW5I,GAC1CvC,KAAK0F,SAAqB5E,EAC1Bd,KAAKoL,mBAAqBpL,KAAK0F,SAASvE,cAAcmG,IACtDtH,KAAKqL,gBAAqB,iBAAkB1K,SAAS8C,iBAA8C,EAA3B6H,UAAUC,eAClFvL,KAAKwL,cAAqBvJ,QAAQyG,OAAO+C,cAAgB/C,OAAOgD,gBAEhE1L,KAAK2L,gDAePC,KAAA,WACO5L,KAAK8K,YACR9K,KAAK6L,OAAOnC,MAIhBoC,gBAAA,YAGOnL,SAASoL,QACX7L,EAAEF,KAAK0F,UAAUf,GAAG,aAAsD,WAAvCzE,EAAEF,KAAK0F,UAAUnE,IAAI,eACzDvB,KAAK4L,UAITI,KAAA,WACOhM,KAAK8K,YACR9K,KAAK6L,OAAOnC,MAIhBJ,MAAA,SAAMjF,GACCA,IACHrE,KAAK6K,WAAY,GAGf7K,KAAK0F,SAASvE,cAAcmG,MAC9BlH,EAAKE,qBAAqBN,KAAK0F,UAC/B1F,KAAKiM,OAAM,IAGbC,cAAclM,KAAK2K,WACnB3K,KAAK2K,UAAY,QAGnBsB,MAAA,SAAM5H,GACCA,IACHrE,KAAK6K,WAAY,GAGf7K,KAAK2K,YACPuB,cAAclM,KAAK2K,WACnB3K,KAAK2K,UAAY,MAGf3K,KAAKkL,QAAQ/B,WAAanJ,KAAK6K,YACjC7K,KAAK2K,UAAYwB,aACdxL,SAASyL,gBAAkBpM,KAAK8L,gBAAkB9L,KAAK4L,MAAMS,KAAKrM,MACnEA,KAAKkL,QAAQ/B,cAKnBmD,GAAA,SAAGC,GAAO,IAAAxM,EAAAC,KACRA,KAAK4K,eAAiB5K,KAAK0F,SAASvE,cAAcmG,IAElD,IAAMkF,EAAcxM,KAAKyM,cAAczM,KAAK4K,gBAE5C,KAAI2B,EAAQvM,KAAK0K,OAAO1B,OAAS,GAAKuD,EAAQ,GAI9C,GAAIvM,KAAK8K,WACP5K,EAAEF,KAAK0F,UAAUvF,IAAIiF,EAAMwE,KAAM,WAAA,OAAM7J,EAAKuM,GAAGC,SADjD,CAKA,GAAIC,IAAgBD,EAGlB,OAFAvM,KAAKsJ,aACLtJ,KAAKiM,QAIP,IAAMS,EAAoBF,EAARD,EACd7C,EACAA,EAEJ1J,KAAK6L,OAAOa,EAAW1M,KAAK0K,OAAO6B,QAGrCtG,QAAA,WACE/F,EAAEF,KAAK0F,UAAUiH,IAAIzH,GACrBhF,EAAEgG,WAAWlG,KAAK0F,SAAUT,GAE5BjF,KAAK0K,OAAqB,KAC1B1K,KAAKkL,QAAqB,KAC1BlL,KAAK0F,SAAqB,KAC1B1F,KAAK2K,UAAqB,KAC1B3K,KAAK6K,UAAqB,KAC1B7K,KAAK8K,WAAqB,KAC1B9K,KAAK4K,eAAqB,KAC1B5K,KAAKoL,mBAAqB,QAK5BD,WAAA,SAAW5I,GAMT,OALAA,EAAMqK,EAAA,GACD1D,EADC,GAED3G,GAELnC,EAAKiC,gBAAgB2C,EAAMzC,EAAQkH,GAC5BlH,KAGTsK,aAAA,WACE,IAAMC,EAAYrM,KAAKsM,IAAI/M,KAAKiL,aAEhC,KAAI6B,GAxNuB,IAwN3B,CAIA,IAAMJ,EAAYI,EAAY9M,KAAKiL,aAEnCjL,KAAKiL,YAAc,GAGfyB,GACF1M,KAAKgM,OAIHU,EAAY,GACd1M,KAAK4L,WAITD,mBAAA,WAAqB,IAAAqB,EAAAhN,KACfA,KAAKkL,QAAQ9B,UACflJ,EAAEF,KAAK0F,UACJwB,GAAG9B,EAAMyE,QAAS,SAACxF,GAAD,OAAW2I,EAAKC,SAAS5I,KAGrB,UAAvBrE,KAAKkL,QAAQ5B,OACfpJ,EAAEF,KAAK0F,UACJwB,GAAG9B,EAAM0E,WAAY,SAACzF,GAAD,OAAW2I,EAAK1D,MAAMjF,KAC3C6C,GAAG9B,EAAM2E,WAAY,SAAC1F,GAAD,OAAW2I,EAAKf,MAAM5H,KAG5CrE,KAAKkL,QAAQ1B,OACfxJ,KAAKkN,6BAITA,wBAAA,WAA0B,IAAAC,EAAAnN,KACxB,GAAKA,KAAKqL,gBAAV,CAIA,IAAM+B,EAAQ,SAAC/I,GACT8I,EAAK3B,eAAiBlB,GAAYjG,EAAMgJ,cAAcC,YAAY/J,eACpE4J,EAAKnC,YAAc3G,EAAMgJ,cAAcE,QAC7BJ,EAAK3B,gBACf2B,EAAKnC,YAAc3G,EAAMgJ,cAAcG,QAAQ,GAAGD,UAahDE,EAAM,SAACpJ,GACP8I,EAAK3B,eAAiBlB,GAAYjG,EAAMgJ,cAAcC,YAAY/J,iBACpE4J,EAAKlC,YAAc5G,EAAMgJ,cAAcE,QAAUJ,EAAKnC,aAGxDmC,EAAKN,eACsB,UAAvBM,EAAKjC,QAAQ5B,QASf6D,EAAK7D,QACD6D,EAAKpC,cACP2C,aAAaP,EAAKpC,cAEpBoC,EAAKpC,aAAe1K,WAAW,SAACgE,GAAD,OAAW8I,EAAKlB,MAAM5H,IAtS9B,IAsS+D8I,EAAKjC,QAAQ/B,YAIvGjJ,EAAEF,KAAK0F,SAASmD,iBAAiBvB,KAAoBJ,GAAG9B,EAAMiF,WAAY,SAACsD,GAAD,OAAOA,EAAE1G,mBAC/EjH,KAAKwL,eACPtL,EAAEF,KAAK0F,UAAUwB,GAAG9B,EAAM+E,YAAa,SAAC9F,GAAD,OAAW+I,EAAM/I,KACxDnE,EAAEF,KAAK0F,UAAUwB,GAAG9B,EAAMgF,UAAW,SAAC/F,GAAD,OAAWoJ,EAAIpJ,KAEpDrE,KAAK0F,SAASsC,UAAUiB,IAAIzD,MAE5BtF,EAAEF,KAAK0F,UAAUwB,GAAG9B,EAAM4E,WAAY,SAAC3F,GAAD,OAAW+I,EAAM/I,KACvDnE,EAAEF,KAAK0F,UAAUwB,GAAG9B,EAAM6E,UAAW,SAAC5F,GAAD,OAxC1B,SAACA,GAERA,EAAMgJ,cAAcG,SAAgD,EAArCnJ,EAAMgJ,cAAcG,QAAQxE,OAC7DmE,EAAKlC,YAAc,EAEnBkC,EAAKlC,YAAc5G,EAAMgJ,cAAcG,QAAQ,GAAGD,QAAUJ,EAAKnC,YAmCnB4C,CAAKvJ,KACrDnE,EAAEF,KAAK0F,UAAUwB,GAAG9B,EAAM8E,SAAU,SAAC7F,GAAD,OAAWoJ,EAAIpJ,UAIvD4I,SAAA,SAAS5I,GACP,IAAI,kBAAkBhB,KAAKgB,EAAMK,OAAOyD,SAIxC,OAAQ9D,EAAMwJ,OACZ,KA/TyB,GAgUvBxJ,EAAM4C,iBACNjH,KAAKgM,OACL,MACF,KAlUyB,GAmUvB3H,EAAM4C,iBACNjH,KAAK4L,WAMXa,cAAA,SAAc3L,GAIZ,OAHAd,KAAK0K,OAAS5J,GAAWA,EAAQ+C,WAC7B,GAAG+E,MAAM/F,KAAK/B,EAAQ+C,WAAWgF,iBAAiBvB,KAClD,GACGtH,KAAK0K,OAAOoD,QAAQhN,MAG7BiN,oBAAA,SAAoBrB,EAAWxE,GAC7B,IAAM8F,EAAkBtB,IAAchD,EAChCuE,EAAkBvB,IAAchD,EAChC8C,EAAkBxM,KAAKyM,cAAcvE,GACrCgG,EAAkBlO,KAAK0K,OAAO1B,OAAS,EAI7C,IAHwBiF,GAAmC,IAAhBzB,GACnBwB,GAAmBxB,IAAgB0B,KAErClO,KAAKkL,QAAQ3B,KACjC,OAAOrB,EAGT,IACMiG,GAAa3B,GADDE,IAAchD,GAAkB,EAAI,IACZ1J,KAAK0K,OAAO1B,OAEtD,OAAsB,GAAfmF,EACHnO,KAAK0K,OAAO1K,KAAK0K,OAAO1B,OAAS,GAAKhJ,KAAK0K,OAAOyD,MAGxDC,mBAAA,SAAmBC,EAAeC,GAChC,IAAMC,EAAcvO,KAAKyM,cAAc4B,GACjCG,EAAYxO,KAAKyM,cAAczM,KAAK0F,SAASvE,cAAcmG,KAC3DmH,EAAavO,EAAEkF,MAAMA,EAAMuE,MAAO,CACtC0E,cAAAA,EACA3B,UAAW4B,EACXI,KAAMF,EACNlC,GAAIiC,IAKN,OAFArO,EAAEF,KAAK0F,UAAU3D,QAAQ0M,GAElBA,KAGTE,2BAAA,SAA2B7N,GACzB,GAAId,KAAKoL,mBAAoB,CAC3B,IAAMwD,EAAa,GAAGhG,MAAM/F,KAAK7C,KAAKoL,mBAAmBvC,iBAAiBvB,KAC1EpH,EAAE0O,GACCtI,YAAYd,GAEf,IAAMqJ,EAAgB7O,KAAKoL,mBAAmB0D,SAC5C9O,KAAKyM,cAAc3L,IAGjB+N,GACF3O,EAAE2O,GAAeE,SAASvJ,OAKhCqG,OAAA,SAAOa,EAAW5L,GAAS,IAQrBkO,EACAC,EACAX,EAVqBY,EAAAlP,KACnBkI,EAAgBlI,KAAK0F,SAASvE,cAAcmG,IAC5C6H,EAAqBnP,KAAKyM,cAAcvE,GACxCkH,EAAgBtO,GAAWoH,GAC/BlI,KAAK+N,oBAAoBrB,EAAWxE,GAChCmH,EAAmBrP,KAAKyM,cAAc2C,GACtCE,EAAYrN,QAAQjC,KAAK2K,WAgB/B,GAPE2D,EAHE5B,IAAchD,GAChBsF,EAAuBxJ,EACvByJ,EAAiBzJ,EACIkE,IAErBsF,EAAuBxJ,EACvByJ,EAAiBzJ,EACIkE,GAGnB0F,GAAelP,EAAEkP,GAAa7I,SAASf,GACzCxF,KAAK8K,YAAa,OAKpB,IADmB9K,KAAKoO,mBAAmBgB,EAAad,GACzCvI,sBAIVmC,GAAkBkH,EAAvB,CAKApP,KAAK8K,YAAa,EAEdwE,GACFtP,KAAKsJ,QAGPtJ,KAAK2O,2BAA2BS,GAEhC,IAAMG,EAAYrP,EAAEkF,MAAMA,EAAMwE,KAAM,CACpCyE,cAAee,EACf1C,UAAW4B,EACXI,KAAMS,EACN7C,GAAI+C,IAGN,GAAInP,EAAEF,KAAK0F,UAAUa,SAASf,GAAkB,CAC9CtF,EAAEkP,GAAaL,SAASE,GAExB7O,EAAKyB,OAAOuN,GAEZlP,EAAEgI,GAAe6G,SAASC,GAC1B9O,EAAEkP,GAAaL,SAASC,GAExB,IAAMQ,EAAsBC,SAASL,EAAYpO,aAAa,iBAAkB,IAC5EwO,GACFxP,KAAKkL,QAAQwE,gBAAkB1P,KAAKkL,QAAQwE,iBAAmB1P,KAAKkL,QAAQ/B,SAC5EnJ,KAAKkL,QAAQ/B,SAAWqG,GAExBxP,KAAKkL,QAAQ/B,SAAWnJ,KAAKkL,QAAQwE,iBAAmB1P,KAAKkL,QAAQ/B,SAGvE,IAAM7H,EAAqBlB,EAAKiB,iCAAiC6G,GAEjEhI,EAAEgI,GACC/H,IAAIC,EAAKR,eAAgB,WACxBM,EAAEkP,GACC9I,YAAe0I,EADlB,IAC0CC,GACvCF,SAASvJ,GAEZtF,EAAEgI,GAAe5B,YAAed,EAAhC,IAAoDyJ,EAApD,IAAsED,GAEtEE,EAAKpE,YAAa,EAElBzK,WAAW,WAAA,OAAMH,EAAEgP,EAAKxJ,UAAU3D,QAAQwN,IAAY,KAEvDnL,qBAAqB9C,QAExBpB,EAAEgI,GAAe5B,YAAYd,GAC7BtF,EAAEkP,GAAaL,SAASvJ,GAExBxF,KAAK8K,YAAa,EAClB5K,EAAEF,KAAK0F,UAAU3D,QAAQwN,GAGvBD,GACFtP,KAAKiM,YAMFtF,iBAAP,SAAwBpE,GACtB,OAAOvC,KAAK4G,KAAK,WACf,IAAIE,EAAO5G,EAAEF,MAAM8G,KAAK7B,GACpBiG,EAAO0B,EAAA,GACN1D,EADM,GAENhJ,EAAEF,MAAM8G,QAGS,iBAAXvE,IACT2I,EAAO0B,EAAA,GACF1B,EADE,GAEF3I,IAIP,IAAMoN,EAA2B,iBAAXpN,EAAsBA,EAAS2I,EAAQ7B,MAO7D,GALKvC,IACHA,EAAO,IAAI2D,EAASzK,KAAMkL,GAC1BhL,EAAEF,MAAM8G,KAAK7B,EAAU6B,IAGH,iBAAXvE,EACTuE,EAAKwF,GAAG/J,QACH,GAAsB,iBAAXoN,EAAqB,CACrC,GAA4B,oBAAjB7I,EAAK6I,GACd,MAAM,IAAI3L,UAAJ,oBAAkC2L,EAAlC,KAER7I,EAAK6I,UACIzE,EAAQ/B,UAAY+B,EAAQ0E,OACrC9I,EAAKwC,QACLxC,EAAKmF,cAKJ4D,qBAAP,SAA4BxL,GAC1B,IAAMtD,EAAWX,EAAKS,uBAAuBb,MAE7C,GAAKe,EAAL,CAIA,IAAM2D,EAASxE,EAAEa,GAAU,GAE3B,GAAK2D,GAAWxE,EAAEwE,GAAQ6B,SAASf,GAAnC,CAIA,IAAMjD,EAAMqK,EAAA,GACP1M,EAAEwE,GAAQoC,OADH,GAEP5G,EAAEF,MAAM8G,QAEPgJ,EAAa9P,KAAKgB,aAAa,iBAEjC8O,IACFvN,EAAO4G,UAAW,GAGpBsB,EAAS9D,iBAAiB9D,KAAK3C,EAAEwE,GAASnC,GAEtCuN,GACF5P,EAAEwE,GAAQoC,KAAK7B,GAAUqH,GAAGwD,GAG9BzL,EAAM4C,4DAjcN,MA3G2B,wCA+G3B,OAAOiC,WAucXhJ,EAAES,UACCuG,GAAG9B,EAAMG,eAAgB+B,GAAqBmD,GAASoF,sBAE1D3P,EAAEwI,QAAQxB,GAAG9B,EAAMoC,cAAe,WAEhC,IADA,IAAMuI,EAAY,GAAGnH,MAAM/F,KAAKlC,SAASkI,iBAAiBvB,KACjDwB,EAAI,EAAGC,EAAMgH,EAAU/G,OAAQF,EAAIC,EAAKD,IAAK,CACpD,IAAMkH,EAAY9P,EAAE6P,EAAUjH,IAC9B2B,GAAS9D,iBAAiB9D,KAAKmN,EAAWA,EAAUlJ,WAUxD5G,EAAEgE,GAAGc,GAAQyF,GAAS9D,iBACtBzG,EAAEgE,GAAGc,GAAMmC,YAAcsD,GACzBvK,EAAEgE,GAAGc,GAAMoC,WAAa,WAEtB,OADAlH,EAAEgE,GAAGc,GAAQG,EACNsF,GAAS9D,kBC5kBlB,IAAM3B,GAAsB,WAEtBC,GAAsB,cACtBC,GAAS,IAAiBD,GAE1BE,GAAsBjF,EAAEgE,GAAGc,IAE3BkE,GAAU,CACdxB,QAAS,EACTvB,OAAS,IAGLsD,GAAc,CAClB/B,OAAS,UACTvB,OAAS,oBAGLf,GAAQ,CACZ6K,KAAI,OAAoB/K,GACxBgL,MAAK,QAAoBhL,GACzBiL,KAAI,OAAoBjL,GACxBkL,OAAM,SAAoBlL,GAC1BK,eAAc,QAAWL,GAlBC,aAqBtBM,GACS,OADTA,GAES,WAFTA,GAGS,aAHTA,GAIS,YAGT6K,GACK,QADLA,GAEK,SAGL/I,GACU,qBADVA,GAEU,2BASVgJ,cACJ,SAAAA,EAAYxP,EAASyB,GACnBvC,KAAKuQ,kBAAmB,EACxBvQ,KAAK0F,SAAmB5E,EACxBd,KAAKkL,QAAmBlL,KAAKmL,WAAW5I,GACxCvC,KAAKwQ,cAAmB,GAAG5H,MAAM/F,KAAKlC,SAASkI,iBAC7C,mCAAmC/H,EAAQ2P,GAA3C,6CAC0C3P,EAAQ2P,GADlD,OAKF,IADA,IAAMC,EAAa,GAAG9H,MAAM/F,KAAKlC,SAASkI,iBAAiBvB,KAClDwB,EAAI,EAAGC,EAAM2H,EAAW1H,OAAQF,EAAIC,EAAKD,IAAK,CACrD,IAAM6H,EAAOD,EAAW5H,GAClB/H,EAAWX,EAAKS,uBAAuB8P,GACvCC,EAAgB,GAAGhI,MAAM/F,KAAKlC,SAASkI,iBAAiB9H,IAC3D8P,OAAO,SAACC,GAAD,OAAeA,IAAchQ,IAEtB,OAAbC,GAA4C,EAAvB6P,EAAc5H,SACrChJ,KAAK+Q,UAAYhQ,EACjBf,KAAKwQ,cAAcQ,KAAKL,IAI5B3Q,KAAKiR,QAAUjR,KAAKkL,QAAQ/E,OAASnG,KAAKkR,aAAe,KAEpDlR,KAAKkL,QAAQ/E,QAChBnG,KAAKmR,0BAA0BnR,KAAK0F,SAAU1F,KAAKwQ,eAGjDxQ,KAAKkL,QAAQxD,QACf1H,KAAK0H,oCAgBTA,OAAA,WACMxH,EAAEF,KAAK0F,UAAUa,SAASf,IAC5BxF,KAAKoR,OAELpR,KAAKqR,UAITA,KAAA,WAAO,IAMDC,EACAC,EAPCxR,EAAAC,KACL,IAAIA,KAAKuQ,mBACPrQ,EAAEF,KAAK0F,UAAUa,SAASf,MAOxBxF,KAAKiR,SAUgB,KATvBK,EAAU,GAAG1I,MAAM/F,KAAK7C,KAAKiR,QAAQpI,iBAAiBvB,KACnDuJ,OAAO,SAACF,GACP,MAAmC,iBAAxB5Q,EAAKmL,QAAQ/E,OACfwK,EAAK3P,aAAa,iBAAmBjB,EAAKmL,QAAQ/E,OAGpDwK,EAAK3I,UAAUC,SAASzC,OAGvBwD,SACVsI,EAAU,QAIVA,IACFC,EAAcrR,EAAEoR,GAASE,IAAIxR,KAAK+Q,WAAWjK,KAAK7B,MAC/BsM,EAAYhB,mBAFjC,CAOA,IAAMkB,EAAavR,EAAEkF,MAAMA,GAAM6K,MAEjC,GADA/P,EAAEF,KAAK0F,UAAU3D,QAAQ0P,IACrBA,EAAW1L,qBAAf,CAIIuL,IACFhB,EAAS3J,iBAAiB9D,KAAK3C,EAAEoR,GAASE,IAAIxR,KAAK+Q,WAAY,QAC1DQ,GACHrR,EAAEoR,GAASxK,KAAK7B,GAAU,OAI9B,IAAMyM,EAAY1R,KAAK2R,gBAEvBzR,EAAEF,KAAK0F,UACJY,YAAYd,IACZuJ,SAASvJ,IAEZxF,KAAK0F,SAASkM,MAAMF,GAAa,EAE7B1R,KAAKwQ,cAAcxH,QACrB9I,EAAEF,KAAKwQ,eACJlK,YAAYd,IACZqM,KAAK,iBAAiB,GAG3B7R,KAAK8R,kBAAiB,GAEtB,IAcMC,EAAU,UADaL,EAAU,GAAGnO,cAAgBmO,EAAU9I,MAAM,IAEpEtH,EAAqBlB,EAAKiB,iCAAiCrB,KAAK0F,UAEtExF,EAAEF,KAAK0F,UACJvF,IAAIC,EAAKR,eAlBK,WACfM,EAAEH,EAAK2F,UACJY,YAAYd,IACZuJ,SAASvJ,IACTuJ,SAASvJ,IAEZzF,EAAK2F,SAASkM,MAAMF,GAAa,GAEjC3R,EAAK+R,kBAAiB,GAEtB5R,EAAEH,EAAK2F,UAAU3D,QAAQqD,GAAM8K,SAS9B9L,qBAAqB9C,GAExBtB,KAAK0F,SAASkM,MAAMF,GAAgB1R,KAAK0F,SAASqM,GAAlD,UAGFX,KAAA,WAAO,IAAApE,EAAAhN,KACL,IAAIA,KAAKuQ,kBACNrQ,EAAEF,KAAK0F,UAAUa,SAASf,IAD7B,CAKA,IAAMiM,EAAavR,EAAEkF,MAAMA,GAAM+K,MAEjC,GADAjQ,EAAEF,KAAK0F,UAAU3D,QAAQ0P,IACrBA,EAAW1L,qBAAf,CAIA,IAAM2L,EAAY1R,KAAK2R,gBAEvB3R,KAAK0F,SAASkM,MAAMF,GAAgB1R,KAAK0F,SAASsM,wBAAwBN,GAA1E,KAEAtR,EAAKyB,OAAO7B,KAAK0F,UAEjBxF,EAAEF,KAAK0F,UACJqJ,SAASvJ,IACTc,YAAYd,IACZc,YAAYd,IAEf,IAAMyM,EAAqBjS,KAAKwQ,cAAcxH,OAC9C,GAAyB,EAArBiJ,EACF,IAAK,IAAInJ,EAAI,EAAGA,EAAImJ,EAAoBnJ,IAAK,CAC3C,IAAM/G,EAAU/B,KAAKwQ,cAAc1H,GAC7B/H,EAAWX,EAAKS,uBAAuBkB,GAE7C,GAAiB,OAAbhB,EACYb,EAAE,GAAG0I,MAAM/F,KAAKlC,SAASkI,iBAAiB9H,KAC7CwF,SAASf,KAClBtF,EAAE6B,GAASgN,SAASvJ,IACjBqM,KAAK,iBAAiB,GAMjC7R,KAAK8R,kBAAiB,GAUtB9R,KAAK0F,SAASkM,MAAMF,GAAa,GACjC,IAAMpQ,EAAqBlB,EAAKiB,iCAAiCrB,KAAK0F,UAEtExF,EAAEF,KAAK0F,UACJvF,IAAIC,EAAKR,eAZK,WACfoN,EAAK8E,kBAAiB,GACtB5R,EAAE8M,EAAKtH,UACJY,YAAYd,IACZuJ,SAASvJ,IACTzD,QAAQqD,GAAMgL,UAQhBhM,qBAAqB9C,QAG1BwQ,iBAAA,SAAiBI,GACflS,KAAKuQ,iBAAmB2B,KAG1BjM,QAAA,WACE/F,EAAEgG,WAAWlG,KAAK0F,SAAUT,IAE5BjF,KAAKkL,QAAmB,KACxBlL,KAAKiR,QAAmB,KACxBjR,KAAK0F,SAAmB,KACxB1F,KAAKwQ,cAAmB,KACxBxQ,KAAKuQ,iBAAmB,QAK1BpF,WAAA,SAAW5I,GAOT,OANAA,EAAMqK,EAAA,GACD1D,GADC,GAED3G,IAEEmF,OAASzF,QAAQM,EAAOmF,QAC/BtH,EAAKiC,gBAAgB2C,GAAMzC,EAAQkH,IAC5BlH,KAGToP,cAAA,WAEE,OADiBzR,EAAEF,KAAK0F,UAAUa,SAAS8J,IACzBA,GAAkBA,MAGtCa,WAAA,WAAa,IACP/K,EADOgH,EAAAnN,KAGPI,EAAK8B,UAAUlC,KAAKkL,QAAQ/E,SAC9BA,EAASnG,KAAKkL,QAAQ/E,OAGoB,oBAA/BnG,KAAKkL,QAAQ/E,OAAOhC,SAC7BgC,EAASnG,KAAKkL,QAAQ/E,OAAO,KAG/BA,EAASxF,SAASQ,cAAcnB,KAAKkL,QAAQ/E,QAG/C,IAAMpF,EAAQ,yCAC6Bf,KAAKkL,QAAQ/E,OAD1C,KAGR2I,EAAW,GAAGlG,MAAM/F,KAAKsD,EAAO0C,iBAAiB9H,IAQvD,OAPAb,EAAE4O,GAAUlI,KAAK,SAACkC,EAAGhI,GACnBqM,EAAKgE,0BACHb,EAAS6B,sBAAsBrR,GAC/B,CAACA,MAIEqF,KAGTgL,0BAAA,SAA0BrQ,EAASsR,GACjC,IAAMC,EAASnS,EAAEY,GAASyF,SAASf,IAE/B4M,EAAapJ,QACf9I,EAAEkS,GACC7J,YAAY/C,IAAsB6M,GAClCR,KAAK,gBAAiBQ,MAMtBF,sBAAP,SAA6BrR,GAC3B,IAAMC,EAAWX,EAAKS,uBAAuBC,GAC7C,OAAOC,EAAWJ,SAASQ,cAAcJ,GAAY,QAGhD4F,iBAAP,SAAwBpE,GACtB,OAAOvC,KAAK4G,KAAK,WACf,IAAM0L,EAAUpS,EAAEF,MACd8G,EAAYwL,EAAMxL,KAAK7B,IACrBiG,EAAO0B,EAAA,GACR1D,GADQ,GAERoJ,EAAMxL,OAFE,GAGU,iBAAXvE,GAAuBA,EAASA,EAAS,IAYrD,IATKuE,GAAQoE,EAAQxD,QAAU,YAAYrE,KAAKd,KAC9C2I,EAAQxD,QAAS,GAGdZ,IACHA,EAAO,IAAIwJ,EAAStQ,KAAMkL,GAC1BoH,EAAMxL,KAAK7B,GAAU6B,IAGD,iBAAXvE,EAAqB,CAC9B,GAA4B,oBAAjBuE,EAAKvE,GACd,MAAM,IAAIyB,UAAJ,oBAAkCzB,EAAlC,KAERuE,EAAKvE,iDAjQT,MApFwB,wCAwFxB,OAAO2G,YAyQXhJ,EAAES,UAAUuG,GAAG9B,GAAMG,eAAgB+B,GAAsB,SAAUjD,GAE/B,MAAhCA,EAAMkO,cAAcpK,SACtB9D,EAAM4C,iBAGR,IAAMuL,EAAWtS,EAAEF,MACbe,EAAWX,EAAKS,uBAAuBb,MACvCyS,EAAY,GAAG7J,MAAM/F,KAAKlC,SAASkI,iBAAiB9H,IAE1Db,EAAEuS,GAAW7L,KAAK,WAChB,IAAM8L,EAAUxS,EAAEF,MAEZuC,EADUmQ,EAAQ5L,KAAK7B,IACN,SAAWuN,EAAS1L,OAC3CwJ,GAAS3J,iBAAiB9D,KAAK6P,EAASnQ,OAU5CrC,EAAEgE,GAAGc,IAAQsL,GAAS3J,iBACtBzG,EAAEgE,GAAGc,IAAMmC,YAAcmJ,GACzBpQ,EAAEgE,GAAGc,IAAMoC,WAAa,WAEtB,OADAlH,EAAEgE,GAAGc,IAAQG,GACNmL,GAAS3J,kBCtXlB,IAAIgM,GAA8B,oBAAXjK,QAA8C,oBAAb/H,UAAiD,oBAAd2K,UAEvFsH,GAAkB,WAEpB,IADA,IAAIC,EAAwB,CAAC,OAAQ,UAAW,WACvC/J,EAAI,EAAGA,EAAI+J,EAAsB7J,OAAQF,GAAK,EACrD,GAAI6J,IAAsE,GAAzDrH,UAAUwH,UAAUhF,QAAQ+E,EAAsB/J,IACjE,OAAO,EAGX,OAAO,EAPa,GAqCtB,IAWIiK,GAXqBJ,IAAajK,OAAOsK,QA3B7C,SAA2B9O,GACzB,IAAIjE,GAAS,EACb,OAAO,WACDA,IAGJA,GAAS,EACTyI,OAAOsK,QAAQC,UAAUC,KAAK,WAC5BjT,GAAS,EACTiE,SAKN,SAAsBA,GACpB,IAAIiP,GAAY,EAChB,OAAO,WACAA,IACHA,GAAY,EACZ9S,WAAW,WACT8S,GAAY,EACZjP,KACC0O,OAyBT,SAASQ,GAAWC,GAElB,OAAOA,GAA8D,sBADvD,GACoBpQ,SAASJ,KAAKwQ,GAUlD,SAASC,GAAyBxS,EAAS2B,GACzC,GAAyB,IAArB3B,EAAQsB,SACV,MAAO,GAGT,IACIb,EADST,EAAQyS,cAAcC,YAClBC,iBAAiB3S,EAAS,MAC3C,OAAO2B,EAAWlB,EAAIkB,GAAYlB,EAUpC,SAASmS,GAAc5S,GACrB,MAAyB,SAArBA,EAAQ6S,SACH7S,EAEFA,EAAQ+C,YAAc/C,EAAQ8S,KAUvC,SAASC,GAAgB/S,GAEvB,IAAKA,EACH,OAAOH,SAASmT,KAGlB,OAAQhT,EAAQ6S,UACd,IAAK,OACL,IAAK,OACH,OAAO7S,EAAQyS,cAAcO,KAC/B,IAAK,YACH,OAAOhT,EAAQgT,KAKnB,IAAIC,EAAwBT,GAAyBxS,GACjDkT,EAAWD,EAAsBC,SACjCC,EAAYF,EAAsBE,UAClCC,EAAYH,EAAsBG,UAEtC,MAAI,wBAAwB7Q,KAAK2Q,EAAWE,EAAYD,GAC/CnT,EAGF+S,GAAgBH,GAAc5S,IAUvC,SAASqT,GAAiBC,GACxB,OAAOA,GAAaA,EAAUC,cAAgBD,EAAUC,cAAgBD,EAG1E,IAAIE,GAAS3B,OAAgBjK,OAAO6L,uBAAwB5T,SAAS6T,cACjEC,GAAS9B,IAAa,UAAUtP,KAAKiI,UAAUwH,WASnD,SAAS4B,GAAKzQ,GACZ,OAAgB,KAAZA,EACKqQ,GAEO,KAAZrQ,EACKwQ,GAEFH,IAAUG,GAUnB,SAASE,GAAgB7T,GACvB,IAAKA,EACH,OAAOH,SAAS8C,gBAQlB,IALA,IAAImR,EAAiBF,GAAK,IAAM/T,SAASmT,KAAO,KAG5Ce,EAAe/T,EAAQ+T,cAAgB,KAEpCA,IAAiBD,GAAkB9T,EAAQgU,oBAChDD,GAAgB/T,EAAUA,EAAQgU,oBAAoBD,aAGxD,IAAIlB,EAAWkB,GAAgBA,EAAalB,SAE5C,OAAKA,GAAyB,SAAbA,GAAoC,SAAbA,GAMsB,IAA1D,CAAC,KAAM,KAAM,SAAS7F,QAAQ+G,EAAalB,WAA2E,WAAvDL,GAAyBuB,EAAc,YACjGF,GAAgBE,GAGlBA,EATE/T,EAAUA,EAAQyS,cAAc9P,gBAAkB9C,SAAS8C,gBA4BtE,SAASsR,GAAQC,GACf,OAAwB,OAApBA,EAAKnR,WACAkR,GAAQC,EAAKnR,YAGfmR,EAWT,SAASC,GAAuBC,EAAUC,GAExC,KAAKD,GAAaA,EAAS9S,UAAa+S,GAAaA,EAAS/S,UAC5D,OAAOzB,SAAS8C,gBAIlB,IAAI2R,EAAQF,EAASG,wBAAwBF,GAAYG,KAAKC,4BAC1DnI,EAAQgI,EAAQF,EAAWC,EAC3B1H,EAAM2H,EAAQD,EAAWD,EAGzBM,EAAQ7U,SAAS8U,cACrBD,EAAME,SAAStI,EAAO,GACtBoI,EAAMG,OAAOlI,EAAK,GAClB,IAAImI,EAA0BJ,EAAMI,wBAIpC,GAAIV,IAAaU,GAA2BT,IAAaS,GAA2BxI,EAAMnF,SAASwF,GACjG,OApDJ,SAA2B3M,GACzB,IAAI6S,EAAW7S,EAAQ6S,SAEvB,MAAiB,SAAbA,IAGgB,SAAbA,GAAuBgB,GAAgB7T,EAAQ+U,qBAAuB/U,GA8CvEgV,CAAkBF,GACbA,EAGFjB,GAAgBiB,GAIzB,IAAIG,EAAehB,GAAQG,GAC3B,OAAIa,EAAanC,KACRqB,GAAuBc,EAAanC,KAAMuB,GAE1CF,GAAuBC,EAAUH,GAAQI,GAAUvB,MAY9D,SAASoC,GAAUlV,EAAnB,GACE,IAEImV,EAAqB,SAFK,EAAnBlR,UAAUiE,aAA+BkN,IADtD,EAAA,EACiF,OAE9C,YAAc,aAC3CvC,EAAW7S,EAAQ6S,SAEvB,GAAiB,SAAbA,GAAoC,SAAbA,EAM3B,OAAO7S,EAAQmV,GALb,IAAIE,EAAOrV,EAAQyS,cAAc9P,gBAEjC,OADuB3C,EAAQyS,cAAc6C,kBAAoBD,GACzCF,GAsC5B,SAASI,GAAeC,EAAQC,GAC9B,IAAIC,EAAiB,MAATD,EAAe,OAAS,MAChCE,EAAkB,QAAVD,EAAmB,QAAU,SAEzC,OAAO9U,WAAW4U,EAAO,SAAWE,EAAQ,SAAU,IAAM9U,WAAW4U,EAAO,SAAWG,EAAQ,SAAU,IAG7G,SAASC,GAAQH,EAAMzC,EAAMqC,EAAMQ,GACjC,OAAOlW,KAAKmW,IAAI9C,EAAK,SAAWyC,GAAOzC,EAAK,SAAWyC,GAAOJ,EAAK,SAAWI,GAAOJ,EAAK,SAAWI,GAAOJ,EAAK,SAAWI,GAAO7B,GAAK,IAAMjF,SAAS0G,EAAK,SAAWI,IAAS9G,SAASkH,EAAc,UAAqB,WAATJ,EAAoB,MAAQ,UAAY9G,SAASkH,EAAc,UAAqB,WAATJ,EAAoB,SAAW,WAAa,GAG5U,SAASM,GAAelW,GACtB,IAAImT,EAAOnT,EAASmT,KAChBqC,EAAOxV,EAAS8C,gBAChBkT,EAAgBjC,GAAK,KAAOjB,iBAAiB0C,GAEjD,MAAO,CACLW,OAAQJ,GAAQ,SAAU5C,EAAMqC,EAAMQ,GACtCI,MAAOL,GAAQ,QAAS5C,EAAMqC,EAAMQ,IAIxC,IAMIK,GAWK,SAAU7P,EAAa8P,EAAYC,GAGxC,OAFID,GAAYE,GAAiBhQ,EAAYxE,UAAWsU,GACpDC,GAAaC,GAAiBhQ,EAAa+P,GACxC/P,GAbT,SAASgQ,GAAiBzS,EAAQ0S,GAChC,IAAK,IAAItO,EAAI,EAAGA,EAAIsO,EAAMpO,OAAQF,IAAK,CACrC,IAAIuO,EAAaD,EAAMtO,GACvBuO,EAAWC,WAAaD,EAAWC,aAAc,EACjDD,EAAWE,cAAe,EACtB,UAAWF,IAAYA,EAAWG,UAAW,GACjD9U,OAAO+U,eAAe/S,EAAQ2S,EAAWK,IAAKL,IAe/B,SAAjBI,GAA2BtV,EAAKuV,EAAK3U,GAYvC,OAXI2U,KAAOvV,EACTO,OAAO+U,eAAetV,EAAKuV,EAAK,CAC9B3U,MAAOA,EACPuU,YAAY,EACZC,cAAc,EACdC,UAAU,IAGZrV,EAAIuV,GAAO3U,EAGNZ,EAZT,IAeIwV,GAAWjV,OAAOkV,QAAU,SAAUlT,GACxC,IAAK,IAAIoE,EAAI,EAAGA,EAAI/D,UAAUiE,OAAQF,IAAK,CACzC,IAAI+O,EAAS9S,UAAU+D,GAEvB,IAAK,IAAI4O,KAAOG,EACVnV,OAAOC,UAAUC,eAAeC,KAAKgV,EAAQH,KAC/ChT,EAAOgT,GAAOG,EAAOH,IAK3B,OAAOhT,GAUT,SAASoT,GAAcC,GACrB,OAAOJ,GAAS,GAAII,EAAS,CAC3BC,MAAOD,EAAQE,KAAOF,EAAQhB,MAC9BmB,OAAQH,EAAQI,IAAMJ,EAAQjB,SAWlC,SAAS9E,GAAsBlR,GAC7B,IAAIsX,EAAO,GAKX,IACE,GAAI1D,GAAK,IAAK,CACZ0D,EAAOtX,EAAQkR,wBACf,IAAIqG,EAAYrC,GAAUlV,EAAS,OAC/BwX,EAAatC,GAAUlV,EAAS,QACpCsX,EAAKD,KAAOE,EACZD,EAAKH,MAAQK,EACbF,EAAKF,QAAUG,EACfD,EAAKJ,OAASM,OAEdF,EAAOtX,EAAQkR,wBAEjB,MAAOrE,IAET,IAAI4K,EAAS,CACXN,KAAMG,EAAKH,KACXE,IAAKC,EAAKD,IACVpB,MAAOqB,EAAKJ,MAAQI,EAAKH,KACzBnB,OAAQsB,EAAKF,OAASE,EAAKD,KAIzBK,EAA6B,SAArB1X,EAAQ6S,SAAsBkD,GAAe/V,EAAQyS,eAAiB,GAC9EwD,EAAQyB,EAAMzB,OAASjW,EAAQ2X,aAAeF,EAAOxB,MACrDD,EAAS0B,EAAM1B,QAAUhW,EAAQ4X,cAAgBH,EAAOzB,OAExD6B,EAAiB7X,EAAQ8X,YAAc7B,EACvC8B,EAAgB/X,EAAQgB,aAAegV,EAI3C,GAAI6B,GAAkBE,EAAe,CACnC,IAAIvC,EAAShD,GAAyBxS,GACtC6X,GAAkBtC,GAAeC,EAAQ,KACzCuC,GAAiBxC,GAAeC,EAAQ,KAExCiC,EAAOxB,OAAS4B,EAChBJ,EAAOzB,QAAU+B,EAGnB,OAAOf,GAAcS,GAGvB,SAASO,GAAqChK,EAAU3I,EAAxD,GACE,IAAI4S,EAAmC,EAAnBhU,UAAUiE,aAA+BkN,IAD/D,GAAA,EAGMzB,EAASC,GAAK,IACdsE,EAA6B,SAApB7S,EAAOwN,SAChBsF,EAAejH,GAAsBlD,GACrCoK,EAAalH,GAAsB7L,GACnCgT,EAAetF,GAAgB/E,GAE/BwH,EAAShD,GAAyBnN,GAClCiT,EAAiB1X,WAAW4U,EAAO8C,eAAgB,IACnDC,EAAkB3X,WAAW4U,EAAO+C,gBAAiB,IAGrDN,GAAiBC,IACnBE,EAAWf,IAAM1X,KAAKmW,IAAIsC,EAAWf,IAAK,GAC1Ce,EAAWjB,KAAOxX,KAAKmW,IAAIsC,EAAWjB,KAAM,IAE9C,IAAIF,EAAUD,GAAc,CAC1BK,IAAKc,EAAad,IAAMe,EAAWf,IAAMiB,EACzCnB,KAAMgB,EAAahB,KAAOiB,EAAWjB,KAAOoB,EAC5CtC,MAAOkC,EAAalC,MACpBD,OAAQmC,EAAanC,SASvB,GAPAiB,EAAQuB,UAAY,EACpBvB,EAAQwB,WAAa,GAMhB9E,GAAUuE,EAAQ,CACrB,IAAIM,EAAY5X,WAAW4U,EAAOgD,UAAW,IACzCC,EAAa7X,WAAW4U,EAAOiD,WAAY,IAE/CxB,EAAQI,KAAOiB,EAAiBE,EAChCvB,EAAQG,QAAUkB,EAAiBE,EACnCvB,EAAQE,MAAQoB,EAAkBE,EAClCxB,EAAQC,OAASqB,EAAkBE,EAGnCxB,EAAQuB,UAAYA,EACpBvB,EAAQwB,WAAaA,EAOvB,OAJI9E,IAAWsE,EAAgB5S,EAAO8B,SAASkR,GAAgBhT,IAAWgT,GAA0C,SAA1BA,EAAaxF,YACrGoE,EA1NJ,SAAuBK,EAAMtX,EAA7B,GACE,IAAI0Y,EAA8B,EAAnBzU,UAAUiE,aAA+BkN,IAD1D,GAAA,EAGMmC,EAAYrC,GAAUlV,EAAS,OAC/BwX,EAAatC,GAAUlV,EAAS,QAChC2Y,EAAWD,GAAY,EAAI,EAK/B,OAJApB,EAAKD,KAAOE,EAAYoB,EACxBrB,EAAKF,QAAUG,EAAYoB,EAC3BrB,EAAKH,MAAQK,EAAamB,EAC1BrB,EAAKJ,OAASM,EAAamB,EACpBrB,EAgNKsB,CAAc3B,EAAS5R,IAG5B4R,EAuDT,SAAS4B,GAA6B7Y,GAEpC,IAAKA,IAAYA,EAAQ8Y,eAAiBlF,KACxC,OAAO/T,SAAS8C,gBAGlB,IADA,IAAIoW,EAAK/Y,EAAQ8Y,cACVC,GAAoD,SAA9CvG,GAAyBuG,EAAI,cACxCA,EAAKA,EAAGD,cAEV,OAAOC,GAAMlZ,SAAS8C,gBAcxB,SAASqW,GAAcC,EAAQ3F,EAAW4F,EAASC,EAAnD,GACE,IAAIlB,EAAmC,EAAnBhU,UAAUiE,aAA+BkN,IAD/D,GAAA,EAKMgE,EAAa,CAAE/B,IAAK,EAAGF,KAAM,GAC7BpD,EAAekE,EAAgBY,GAA6BI,GAAU9E,GAAuB8E,EAAQ5F,GAAiBC,IAG1H,GAA0B,aAAtB6F,EACFC,EArFJ,SAAuDpZ,EAAvD,GACE,IAAIqZ,EAAmC,EAAnBpV,UAAUiE,aAA+BkN,IAD/D,GAAA,EAGMC,EAAOrV,EAAQyS,cAAc9P,gBAC7B2W,EAAiBtB,GAAqChY,EAASqV,GAC/DY,EAAQtW,KAAKmW,IAAIT,EAAKsC,YAAa/P,OAAO2R,YAAc,GACxDvD,EAASrW,KAAKmW,IAAIT,EAAKuC,aAAchQ,OAAO4R,aAAe,GAE3DjC,EAAa8B,EAAkC,EAAlBnE,GAAUG,GACvCmC,EAAc6B,EAA0C,EAA1BnE,GAAUG,EAAM,QASlD,OAAO2B,GAPM,CACXK,IAAKE,EAAY+B,EAAejC,IAAMiC,EAAed,UACrDrB,KAAMK,EAAa8B,EAAenC,KAAOmC,EAAeb,WACxDxC,MAAOA,EACPD,OAAQA,IAsEKyD,CAA8C1F,EAAckE,OACpE,CAEL,IAAIyB,OAAiB,EACK,iBAAtBP,EAE8B,UADhCO,EAAiB3G,GAAgBH,GAAcU,KAC5BT,WACjB6G,EAAiBT,EAAOxG,cAAc9P,iBAGxC+W,EAD+B,WAAtBP,EACQF,EAAOxG,cAAc9P,gBAErBwW,EAGnB,IAAIlC,EAAUe,GAAqC0B,EAAgB3F,EAAckE,GAGjF,GAAgC,SAA5ByB,EAAe7G,UA1EvB,SAAS8G,EAAQ3Z,GACf,IAAI6S,EAAW7S,EAAQ6S,SACvB,GAAiB,SAAbA,GAAoC,SAAbA,EACzB,OAAO,EAET,GAAsD,UAAlDL,GAAyBxS,EAAS,YACpC,OAAO,EAET,IAAI+C,EAAa6P,GAAc5S,GAC/B,QAAK+C,GAGE4W,EAAQ5W,GA8D8B4W,CAAQ5F,GAWjDqF,EAAanC,MAXmD,CAChE,IAAI2C,EAAkB7D,GAAekD,EAAOxG,eACxCuD,EAAS4D,EAAgB5D,OACzBC,EAAQ2D,EAAgB3D,MAE5BmD,EAAW/B,KAAOJ,EAAQI,IAAMJ,EAAQuB,UACxCY,EAAWhC,OAASpB,EAASiB,EAAQI,IACrC+B,EAAWjC,MAAQF,EAAQE,KAAOF,EAAQwB,WAC1CW,EAAWlC,MAAQjB,EAAQgB,EAAQE,MASvC,IAAI0C,EAAqC,iBADzCX,EAAUA,GAAW,GAOrB,OALAE,EAAWjC,MAAQ0C,EAAkBX,EAAUA,EAAQ/B,MAAQ,EAC/DiC,EAAW/B,KAAOwC,EAAkBX,EAAUA,EAAQ7B,KAAO,EAC7D+B,EAAWlC,OAAS2C,EAAkBX,EAAUA,EAAQhC,OAAS,EACjEkC,EAAWhC,QAAUyC,EAAkBX,EAAUA,EAAQ9B,QAAU,EAE5DgC,EAmBT,SAASU,GAAqBC,EAAWC,EAASf,EAAQ3F,EAAW6F,EAArE,GACE,IAAID,EAA6B,EAAnBjV,UAAUiE,aAA+BkN,IADzD,EAAA,EACoF,EAElF,IAAmC,IAA/B2E,EAAU/M,QAAQ,QACpB,OAAO+M,EAGT,IAAIX,EAAaJ,GAAcC,EAAQ3F,EAAW4F,EAASC,GAEvDc,EAAQ,CACV5C,IAAK,CACHpB,MAAOmD,EAAWnD,MAClBD,OAAQgE,EAAQ3C,IAAM+B,EAAW/B,KAEnCH,MAAO,CACLjB,MAAOmD,EAAWlC,MAAQ8C,EAAQ9C,MAClClB,OAAQoD,EAAWpD,QAErBoB,OAAQ,CACNnB,MAAOmD,EAAWnD,MAClBD,OAAQoD,EAAWhC,OAAS4C,EAAQ5C,QAEtCD,KAAM,CACJlB,MAAO+D,EAAQ7C,KAAOiC,EAAWjC,KACjCnB,OAAQoD,EAAWpD,SAInBkE,EAActY,OAAOuY,KAAKF,GAAOG,IAAI,SAAUxD,GACjD,OAAOC,GAAS,CACdD,IAAKA,GACJqD,EAAMrD,GAAM,CACbyD,KAhDN,SAAiBC,GAIf,OAHYA,EAAKrE,MACJqE,EAAKtE,OA8CRuE,CAAQN,EAAMrD,QAErB4D,KAAK,SAAUC,EAAGC,GACnB,OAAOA,EAAEL,KAAOI,EAAEJ,OAGhBM,EAAgBT,EAAYnK,OAAO,SAAU6K,GAC/C,IAAI3E,EAAQ2E,EAAM3E,MACdD,EAAS4E,EAAM5E,OACnB,OAAOC,GAASgD,EAAOtB,aAAe3B,GAAUiD,EAAOrB,eAGrDiD,EAA2C,EAAvBF,EAAczS,OAAayS,EAAc,GAAG/D,IAAMsD,EAAY,GAAGtD,IAErFkE,EAAYf,EAAUjZ,MAAM,KAAK,GAErC,OAAO+Z,GAAqBC,EAAY,IAAMA,EAAY,IAa5D,SAASC,GAAoBC,EAAO/B,EAAQ3F,EAA5C,GACE,IAAI2E,EAAmC,EAAnBhU,UAAUiE,aAA+BkN,IAD/D,EAAA,EAC0F,KAGxF,OAAO4C,GAAqC1E,EADnB2E,EAAgBY,GAA6BI,GAAU9E,GAAuB8E,EAAQ5F,GAAiBC,IACrD2E,GAU7E,SAASgD,GAAcjb,GACrB,IACIwV,EADSxV,EAAQyS,cAAcC,YACfC,iBAAiB3S,GACjCkb,EAAIta,WAAW4U,EAAOgD,WAAa,GAAK5X,WAAW4U,EAAO2F,cAAgB,GAC1EC,EAAIxa,WAAW4U,EAAOiD,YAAc,GAAK7X,WAAW4U,EAAO6F,aAAe,GAK9E,MAJa,CACXpF,MAAOjW,EAAQ8X,YAAcsD,EAC7BpF,OAAQhW,EAAQgB,aAAeka,GAYnC,SAASI,GAAqBvB,GAC5B,IAAIwB,EAAO,CAAEpE,KAAM,QAASD,MAAO,OAAQE,OAAQ,MAAOC,IAAK,UAC/D,OAAO0C,EAAUyB,QAAQ,yBAA0B,SAAUC,GAC3D,OAAOF,EAAKE,KAchB,SAASC,GAAiBzC,EAAQ0C,EAAkB5B,GAClDA,EAAYA,EAAUjZ,MAAM,KAAK,GAGjC,IAAI8a,EAAaX,GAAchC,GAG3B4C,EAAgB,CAClB5F,MAAO2F,EAAW3F,MAClBD,OAAQ4F,EAAW5F,QAIjB8F,GAAoD,IAA1C,CAAC,QAAS,QAAQ9O,QAAQ+M,GACpCgC,EAAWD,EAAU,MAAQ,OAC7BE,EAAgBF,EAAU,OAAS,MACnCG,EAAcH,EAAU,SAAW,QACnCI,EAAwBJ,EAAqB,QAAX,SAStC,OAPAD,EAAcE,GAAYJ,EAAiBI,GAAYJ,EAAiBM,GAAe,EAAIL,EAAWK,GAAe,EAEnHJ,EAAcG,GADZjC,IAAciC,EACeL,EAAiBK,GAAiBJ,EAAWM,GAE7CP,EAAiBL,GAAqBU,IAGhEH,EAYT,SAASM,GAAKC,EAAKC,GAEjB,OAAIC,MAAMza,UAAUsa,KACXC,EAAID,KAAKE,GAIXD,EAAIrM,OAAOsM,GAAO,GAqC3B,SAASE,GAAaC,EAAWxW,EAAMyW,GAoBrC,YAnB8BrH,IAATqH,EAAqBD,EAAYA,EAAU1U,MAAM,EA1BxE,SAAmBsU,EAAKM,EAAMza,GAE5B,GAAIqa,MAAMza,UAAU8a,UAClB,OAAOP,EAAIO,UAAU,SAAUC,GAC7B,OAAOA,EAAIF,KAAUza,IAKzB,IAAIG,EAAQ+Z,GAAKC,EAAK,SAAU/a,GAC9B,OAAOA,EAAIqb,KAAUza,IAEvB,OAAOma,EAAIpP,QAAQ5K,GAcsDua,CAAUH,EAAW,OAAQC,KAEvFI,QAAQ,SAAUlE,GAC3BA,EAAmB,UAErBmE,QAAQC,KAAK,yDAEf,IAAI3Z,EAAKuV,EAAmB,UAAKA,EAASvV,GACtCuV,EAASqE,SAAW1K,GAAWlP,KAIjC4C,EAAKiR,QAAQgC,OAASjC,GAAchR,EAAKiR,QAAQgC,QACjDjT,EAAKiR,QAAQ3D,UAAY0D,GAAchR,EAAKiR,QAAQ3D,WAEpDtN,EAAO5C,EAAG4C,EAAM2S,MAIb3S,EA8DT,SAASiX,GAAkBT,EAAWU,GACpC,OAAOV,EAAUW,KAAK,SAAU7C,GAC9B,IAAI8C,EAAO9C,EAAK8C,KAEhB,OADc9C,EAAK0C,SACDI,IAASF,IAW/B,SAASG,GAAyB1b,GAIhC,IAHA,IAAI2b,EAAW,EAAC,EAAO,KAAM,SAAU,MAAO,KAC1CC,EAAY5b,EAAS6b,OAAO,GAAG/a,cAAgBd,EAASmG,MAAM,GAEzDE,EAAI,EAAGA,EAAIsV,EAASpV,OAAQF,IAAK,CACxC,IAAItI,EAAS4d,EAAStV,GAClByV,EAAU/d,EAAS,GAAKA,EAAS6d,EAAY5b,EACjD,GAA4C,oBAAjC9B,SAASmT,KAAKlC,MAAM2M,GAC7B,OAAOA,EAGX,OAAO,KAsCT,SAASC,GAAU1d,GACjB,IAAIyS,EAAgBzS,EAAQyS,cAC5B,OAAOA,EAAgBA,EAAcC,YAAc9K,OAoBrD,SAAS+V,GAAoBrK,EAAWsK,EAAS5C,EAAO6C,GAEtD7C,EAAM6C,YAAcA,EACpBH,GAAUpK,GAAWwK,iBAAiB,SAAU9C,EAAM6C,YAAa,CAAEE,SAAS,IAG9E,IAAIC,EAAgBjL,GAAgBO,GAKpC,OA5BF,SAAS2K,EAAsB5F,EAAc9U,EAAO2a,EAAUC,GAC5D,IAAIC,EAAmC,SAA1B/F,EAAaxF,SACtBjP,EAASwa,EAAS/F,EAAa5F,cAAcC,YAAc2F,EAC/DzU,EAAOka,iBAAiBva,EAAO2a,EAAU,CAAEH,SAAS,IAE/CK,GACHH,EAAsBlL,GAAgBnP,EAAOb,YAAaQ,EAAO2a,EAAUC,GAE7EA,EAAcjO,KAAKtM,GAgBnBqa,CAAsBD,EAAe,SAAUhD,EAAM6C,YAAa7C,EAAMmD,eACxEnD,EAAMgD,cAAgBA,EACtBhD,EAAMqD,eAAgB,EAEfrD,EA6CT,SAASsD,KACHpf,KAAK8b,MAAMqD,gBACbE,qBAAqBrf,KAAKsf,gBAC1Btf,KAAK8b,MA3BT,SAA8B1H,EAAW0H,GAcvC,OAZA0C,GAAUpK,GAAWmL,oBAAoB,SAAUzD,EAAM6C,aAGzD7C,EAAMmD,cAActB,QAAQ,SAAUjZ,GACpCA,EAAO6a,oBAAoB,SAAUzD,EAAM6C,eAI7C7C,EAAM6C,YAAc,KACpB7C,EAAMmD,cAAgB,GACtBnD,EAAMgD,cAAgB,KACtBhD,EAAMqD,eAAgB,EACfrD,EAaQ0D,CAAqBxf,KAAKoU,UAAWpU,KAAK8b,QAW3D,SAAS2D,GAAUC,GACjB,MAAa,KAANA,IAAaC,MAAMje,WAAWge,KAAOE,SAASF,GAWvD,SAASG,GAAU/e,EAASwV,GAC1B5T,OAAOuY,KAAK3E,GAAQqH,QAAQ,SAAUH,GACpC,IAAIsC,EAAO,IAEkE,IAAzE,CAAC,QAAS,SAAU,MAAO,QAAS,SAAU,QAAQhS,QAAQ0P,IAAgBiC,GAAUnJ,EAAOkH,MACjGsC,EAAO,MAEThf,EAAQ8Q,MAAM4L,GAAQlH,EAAOkH,GAAQsC,IAkGzC,SAASC,GAAkBjZ,EAAMkZ,GAOjB,SAAVC,EAA2BC,GAC7B,OAAOA,EAPT,IAAIC,EAAgBrZ,EAAKiR,QACrBgC,EAASoG,EAAcpG,OACvB3F,EAAY+L,EAAc/L,UAC1BgM,EAAQ3f,KAAK2f,MACbC,EAAQ5f,KAAK4f,MAMbC,EAAiBF,EAAMhM,EAAU2C,OACjCwJ,EAAcH,EAAMrG,EAAOhD,OAE3ByJ,GAA4D,IAA/C,CAAC,OAAQ,SAAS1S,QAAQhH,EAAK+T,WAC5C4F,GAA+C,IAAjC3Z,EAAK+T,UAAU/M,QAAQ,KAIrC4S,EAAuBV,EAAwBQ,GAAcC,GAH3CH,EAAiB,GAAMC,EAAc,EAGuCH,EAAQC,EAAjEJ,EACrCU,EAAqBX,EAAwBI,EAAVH,EAEvC,MAAO,CACLhI,KAAMyI,EANWJ,EAAiB,GAAM,GAAKC,EAAc,GAAM,IAMtBE,GAAeT,EAAcjG,EAAO9B,KAAO,EAAI8B,EAAO9B,MACjGE,IAAKwI,EAAkB5G,EAAO5B,KAC9BD,OAAQyI,EAAkB5G,EAAO7B,QACjCF,MAAO0I,EAAoB3G,EAAO/B,QAItC,IAAI4I,GAAYjO,IAAa,WAAWtP,KAAKiI,UAAUwH,WA8GvD,SAAS+N,GAAmBvD,EAAWwD,EAAgBC,GACrD,IAAIC,EAAa/D,GAAKK,EAAW,SAAUlC,GAEzC,OADWA,EAAK8C,OACA4C,IAGdG,IAAeD,GAAc1D,EAAUW,KAAK,SAAUxE,GACxD,OAAOA,EAASyE,OAAS6C,GAAiBtH,EAASqE,SAAWrE,EAASrE,MAAQ4L,EAAW5L,QAG5F,IAAK6L,EAAY,CACf,IAAIC,EAAc,IAAMJ,EAAiB,IACrCK,EAAY,IAAMJ,EAAgB,IACtCnD,QAAQC,KAAKsD,EAAY,4BAA8BD,EAAc,4DAA8DA,EAAc,KAEnJ,OAAOD,EAoIT,IAAIG,GAAa,CAAC,aAAc,OAAQ,WAAY,YAAa,MAAO,UAAW,cAAe,QAAS,YAAa,aAAc,SAAU,eAAgB,WAAY,OAAQ,cAGhLC,GAAkBD,GAAWxY,MAAM,GAYvC,SAAS0Y,GAAUzG,EAAnB,GACE,IAAI0G,EAA6B,EAAnBxc,UAAUiE,aAA+BkN,IADzD,GAAA,EAGM3J,EAAQ8U,GAAgBvT,QAAQ+M,GAChCqC,EAAMmE,GAAgBzY,MAAM2D,EAAQ,GAAGiV,OAAOH,GAAgBzY,MAAM,EAAG2D,IAC3E,OAAOgV,EAAUrE,EAAIuE,UAAYvE,EAGnC,IAAIwE,GACI,OADJA,GAES,YAFTA,GAGgB,mBAiMpB,SAASC,GAAYC,EAAQjF,EAAeF,EAAkBoF,GAC5D,IAAI9J,EAAU,CAAC,EAAG,GAKd+J,GAA0D,IAA9C,CAAC,QAAS,QAAQhU,QAAQ+T,GAItCE,EAAYH,EAAOhgB,MAAM,WAAWsZ,IAAI,SAAU8G,GACpD,OAAOA,EAAK9gB,SAKV+gB,EAAUF,EAAUjU,QAAQmP,GAAK8E,EAAW,SAAUC,GACxD,OAAgC,IAAzBA,EAAKE,OAAO,WAGjBH,EAAUE,KAAiD,IAArCF,EAAUE,GAASnU,QAAQ,MACnD8P,QAAQC,KAAK,gFAKf,IAAIsE,EAAa,cACbC,GAAmB,IAAbH,EAAiB,CAACF,EAAUnZ,MAAM,EAAGqZ,GAAST,OAAO,CAACO,EAAUE,GAASrgB,MAAMugB,GAAY,KAAM,CAACJ,EAAUE,GAASrgB,MAAMugB,GAAY,IAAIX,OAAOO,EAAUnZ,MAAMqZ,EAAU,KAAO,CAACF,GAqC9L,OAlCAK,EAAMA,EAAIlH,IAAI,SAAUmH,EAAI9V,GAE1B,IAAIwQ,GAAyB,IAAVxQ,GAAeuV,EAAYA,GAAa,SAAW,QAClEQ,GAAoB,EACxB,OAAOD,EAGNE,OAAO,SAAUhH,EAAGC,GACnB,MAAwB,KAApBD,EAAEA,EAAEvS,OAAS,KAAwC,IAA3B,CAAC,IAAK,KAAK8E,QAAQ0N,IAC/CD,EAAEA,EAAEvS,OAAS,GAAKwS,EAClB8G,GAAoB,EACb/G,GACE+G,GACT/G,EAAEA,EAAEvS,OAAS,IAAMwS,EACnB8G,GAAoB,EACb/G,GAEAA,EAAEiG,OAAOhG,IAEjB,IAEFN,IAAI,SAAUsH,GACb,OAxGN,SAAiBA,EAAKzF,EAAaJ,EAAeF,GAEhD,IAAI7a,EAAQ4gB,EAAItf,MAAM,6BAClBH,GAASnB,EAAM,GACfke,EAAOle,EAAM,GAGjB,IAAKmB,EACH,OAAOyf,EAGT,GAA0B,IAAtB1C,EAAKhS,QAAQ,KAcV,MAAa,OAATgS,GAA0B,OAATA,EAYnB/c,GATM,OAAT+c,EACKrf,KAAKmW,IAAIjW,SAAS8C,gBAAgBiV,aAAchQ,OAAO4R,aAAe,GAEtE7Z,KAAKmW,IAAIjW,SAAS8C,gBAAgBgV,YAAa/P,OAAO2R,YAAc,IAE/D,IAAMtX,EArBpB,IAAIjC,OAAU,EACd,OAAQgf,GACN,IAAK,KACHhf,EAAU6b,EACV,MACF,IAAK,IACL,IAAK,KACL,QACE7b,EAAU2b,EAId,OADW3E,GAAchX,GACbic,GAAe,IAAMha,EAgFxB0f,CAAQD,EAAKzF,EAAaJ,EAAeF,QAKhDkB,QAAQ,SAAU0E,EAAI9V,GACxB8V,EAAG1E,QAAQ,SAAUqE,EAAMU,GACrBjD,GAAUuC,KACZjK,EAAQxL,IAAUyV,GAA2B,MAAnBK,EAAGK,EAAS,IAAc,EAAI,QAIvD3K,EA2OT,IAkWI4K,GAAW,CAKb9H,UAAW,SAMX+H,eAAe,EAMfzD,eAAe,EAOf0D,iBAAiB,EAQjBC,SAAU,aAUVC,SAAU,aAOVzF,UAnZc,CASd0F,MAAO,CAEL5N,MAAO,IAEP0I,SAAS,EAET5Z,GA9HJ,SAAe4C,GACb,IAAI+T,EAAY/T,EAAK+T,UACjBgH,EAAgBhH,EAAUjZ,MAAM,KAAK,GACrCqhB,EAAiBpI,EAAUjZ,MAAM,KAAK,GAG1C,GAAIqhB,EAAgB,CAClB,IAAI9C,EAAgBrZ,EAAKiR,QACrB3D,EAAY+L,EAAc/L,UAC1B2F,EAASoG,EAAcpG,OAEvByG,GAA2D,IAA9C,CAAC,SAAU,OAAO1S,QAAQ+T,GACvCqB,EAAO1C,EAAa,OAAS,MAC7BzD,EAAcyD,EAAa,QAAU,SAErC2C,EAAe,CACjB/V,MAAOqK,GAAe,GAAIyL,EAAM9O,EAAU8O,IAC1CzV,IAAKgK,GAAe,GAAIyL,EAAM9O,EAAU8O,GAAQ9O,EAAU2I,GAAehD,EAAOgD,KAGlFjW,EAAKiR,QAAQgC,OAASpC,GAAS,GAAIoC,EAAQoJ,EAAaF,IAG1D,OAAOnc,IAgJP8a,OAAQ,CAENxM,MAAO,IAEP0I,SAAS,EAET5Z,GA7RJ,SAAgB4C,EAAMsU,GACpB,IAAIwG,EAASxG,EAAKwG,OACd/G,EAAY/T,EAAK+T,UACjBsF,EAAgBrZ,EAAKiR,QACrBgC,EAASoG,EAAcpG,OACvB3F,EAAY+L,EAAc/L,UAE1ByN,EAAgBhH,EAAUjZ,MAAM,KAAK,GAErCmW,OAAU,EAsBd,OApBEA,EADE0H,IAAWmC,GACH,EAAEA,EAAQ,GAEVD,GAAYC,EAAQ7H,EAAQ3F,EAAWyN,GAG7B,SAAlBA,GACF9H,EAAO5B,KAAOJ,EAAQ,GACtBgC,EAAO9B,MAAQF,EAAQ,IACI,UAAlB8J,GACT9H,EAAO5B,KAAOJ,EAAQ,GACtBgC,EAAO9B,MAAQF,EAAQ,IACI,QAAlB8J,GACT9H,EAAO9B,MAAQF,EAAQ,GACvBgC,EAAO5B,KAAOJ,EAAQ,IACK,WAAlB8J,IACT9H,EAAO9B,MAAQF,EAAQ,GACvBgC,EAAO5B,KAAOJ,EAAQ,IAGxBjR,EAAKiT,OAASA,EACPjT,GAkQL8a,OAAQ,GAoBVwB,gBAAiB,CAEfhO,MAAO,IAEP0I,SAAS,EAET5Z,GAlRJ,SAAyB4C,EAAM4X,GAC7B,IAAIzE,EAAoByE,EAAQzE,mBAAqBtF,GAAgB7N,EAAKuc,SAAStJ,QAK/EjT,EAAKuc,SAASjP,YAAc6F,IAC9BA,EAAoBtF,GAAgBsF,IAMtC,IAAIqJ,EAAgBnF,GAAyB,aACzCoF,EAAezc,EAAKuc,SAAStJ,OAAOnI,MACpCuG,EAAMoL,EAAapL,IACnBF,EAAOsL,EAAatL,KACpBuL,EAAYD,EAAaD,GAE7BC,EAAapL,IAAM,GACnBoL,EAAatL,KAAO,GACpBsL,EAAaD,GAAiB,GAE9B,IAAIpJ,EAAaJ,GAAchT,EAAKuc,SAAStJ,OAAQjT,EAAKuc,SAASjP,UAAWsK,EAAQ1E,QAASC,EAAmBnT,EAAK8b,eAIvHW,EAAapL,IAAMA,EACnBoL,EAAatL,KAAOA,EACpBsL,EAAaD,GAAiBE,EAE9B9E,EAAQxE,WAAaA,EAErB,IAAI9E,EAAQsJ,EAAQ+E,SAChB1J,EAASjT,EAAKiR,QAAQgC,OAEtBoD,EAAQ,CACVuG,QAAS,SAAiB7I,GACxB,IAAI9X,EAAQgX,EAAOc,GAInB,OAHId,EAAOc,GAAaX,EAAWW,KAAe6D,EAAQiF,sBACxD5gB,EAAQtC,KAAKmW,IAAImD,EAAOc,GAAYX,EAAWW,KAE1CpD,GAAe,GAAIoD,EAAW9X,IAEvC6gB,UAAW,SAAmB/I,GAC5B,IAAIgC,EAAyB,UAAdhC,EAAwB,OAAS,MAC5C9X,EAAQgX,EAAO8C,GAInB,OAHI9C,EAAOc,GAAaX,EAAWW,KAAe6D,EAAQiF,sBACxD5gB,EAAQtC,KAAKojB,IAAI9J,EAAO8C,GAAW3C,EAAWW,IAA4B,UAAdA,EAAwBd,EAAOhD,MAAQgD,EAAOjD,UAErGW,GAAe,GAAIoF,EAAU9Z,KAWxC,OAPAqS,EAAMuI,QAAQ,SAAU9C,GACtB,IAAIqI,GAA+C,IAAxC,CAAC,OAAQ,OAAOpV,QAAQ+M,GAAoB,UAAY,YACnEd,EAASpC,GAAS,GAAIoC,EAAQoD,EAAM+F,GAAMrI,MAG5C/T,EAAKiR,QAAQgC,OAASA,EAEfjT,GA2NL2c,SAAU,CAAC,OAAQ,QAAS,MAAO,UAOnCzJ,QAAS,EAMTC,kBAAmB,gBAYrB6J,aAAc,CAEZ1O,MAAO,IAEP0I,SAAS,EAET5Z,GAlgBJ,SAAsB4C,GACpB,IAAIqZ,EAAgBrZ,EAAKiR,QACrBgC,EAASoG,EAAcpG,OACvB3F,EAAY+L,EAAc/L,UAE1ByG,EAAY/T,EAAK+T,UAAUjZ,MAAM,KAAK,GACtCye,EAAQ5f,KAAK4f,MACbG,GAAuD,IAA1C,CAAC,MAAO,UAAU1S,QAAQ+M,GACvCqI,EAAO1C,EAAa,QAAU,SAC9BuD,EAASvD,EAAa,OAAS,MAC/BzD,EAAcyD,EAAa,QAAU,SASzC,OAPIzG,EAAOmJ,GAAQ7C,EAAMjM,EAAU2P,MACjCjd,EAAKiR,QAAQgC,OAAOgK,GAAU1D,EAAMjM,EAAU2P,IAAWhK,EAAOgD,IAE9DhD,EAAOgK,GAAU1D,EAAMjM,EAAU8O,MACnCpc,EAAKiR,QAAQgC,OAAOgK,GAAU1D,EAAMjM,EAAU8O,KAGzCpc,IA4fPkd,MAAO,CAEL5O,MAAO,IAEP0I,SAAS,EAET5Z,GApxBJ,SAAe4C,EAAM4X,GACnB,IAAIuF,EAGJ,IAAKpD,GAAmB/Z,EAAKuc,SAAS/F,UAAW,QAAS,gBACxD,OAAOxW,EAGT,IAAIod,EAAexF,EAAQ5d,QAG3B,GAA4B,iBAAjBojB,GAIT,KAHAA,EAAepd,EAAKuc,SAAStJ,OAAO5Y,cAAc+iB,IAIhD,OAAOpd,OAKT,IAAKA,EAAKuc,SAAStJ,OAAO9R,SAASic,GAEjC,OADAtG,QAAQC,KAAK,iEACN/W,EAIX,IAAI+T,EAAY/T,EAAK+T,UAAUjZ,MAAM,KAAK,GACtCue,EAAgBrZ,EAAKiR,QACrBgC,EAASoG,EAAcpG,OACvB3F,EAAY+L,EAAc/L,UAE1BoM,GAAuD,IAA1C,CAAC,OAAQ,SAAS1S,QAAQ+M,GAEvC9R,EAAMyX,EAAa,SAAW,QAC9B2D,EAAkB3D,EAAa,MAAQ,OACvC0C,EAAOiB,EAAgBhhB,cACvBihB,EAAU5D,EAAa,OAAS,MAChCuD,EAASvD,EAAa,SAAW,QACjC6D,EAAmBtI,GAAcmI,GAAcnb,GAQ/CqL,EAAU2P,GAAUM,EAAmBtK,EAAOmJ,KAChDpc,EAAKiR,QAAQgC,OAAOmJ,IAASnJ,EAAOmJ,IAAS9O,EAAU2P,GAAUM,IAG/DjQ,EAAU8O,GAAQmB,EAAmBtK,EAAOgK,KAC9Cjd,EAAKiR,QAAQgC,OAAOmJ,IAAS9O,EAAU8O,GAAQmB,EAAmBtK,EAAOgK,IAE3Ejd,EAAKiR,QAAQgC,OAASjC,GAAchR,EAAKiR,QAAQgC,QAGjD,IAAIuK,EAASlQ,EAAU8O,GAAQ9O,EAAUrL,GAAO,EAAIsb,EAAmB,EAInE9iB,EAAM+R,GAAyBxM,EAAKuc,SAAStJ,QAC7CwK,EAAmB7iB,WAAWH,EAAI,SAAW4iB,GAAkB,IAC/DK,EAAmB9iB,WAAWH,EAAI,SAAW4iB,EAAkB,SAAU,IACzEM,EAAYH,EAASxd,EAAKiR,QAAQgC,OAAOmJ,GAAQqB,EAAmBC,EAQxE,OALAC,EAAYhkB,KAAKmW,IAAInW,KAAKojB,IAAI9J,EAAOhR,GAAOsb,EAAkBI,GAAY,GAE1E3d,EAAKod,aAAeA,EACpBpd,EAAKiR,QAAQiM,OAAmCvM,GAA1BwM,EAAsB,GAAwCf,EAAMziB,KAAK2f,MAAMqE,IAAahN,GAAewM,EAAqBG,EAAS,IAAKH,GAE7Jnd,GA8sBLhG,QAAS,aAcX4jB,KAAM,CAEJtP,MAAO,IAEP0I,SAAS,EAET5Z,GA5oBJ,SAAc4C,EAAM4X,GAElB,GAAIX,GAAkBjX,EAAKuc,SAAS/F,UAAW,SAC7C,OAAOxW,EAGT,GAAIA,EAAK6d,SAAW7d,EAAK+T,YAAc/T,EAAK8d,kBAE1C,OAAO9d,EAGT,IAAIoT,EAAaJ,GAAchT,EAAKuc,SAAStJ,OAAQjT,EAAKuc,SAASjP,UAAWsK,EAAQ1E,QAAS0E,EAAQzE,kBAAmBnT,EAAK8b,eAE3H/H,EAAY/T,EAAK+T,UAAUjZ,MAAM,KAAK,GACtCijB,EAAoBzI,GAAqBvB,GACzCe,EAAY9U,EAAK+T,UAAUjZ,MAAM,KAAK,IAAM,GAE5CkjB,EAAY,GAEhB,OAAQpG,EAAQqG,UACd,KAAKrD,GACHoD,EAAY,CAACjK,EAAWgK,GACxB,MACF,KAAKnD,GACHoD,EAAYxD,GAAUzG,GACtB,MACF,KAAK6G,GACHoD,EAAYxD,GAAUzG,GAAW,GACjC,MACF,QACEiK,EAAYpG,EAAQqG,SAyDxB,OAtDAD,EAAUnH,QAAQ,SAAUqH,EAAMzY,GAChC,GAAIsO,IAAcmK,GAAQF,EAAU9b,SAAWuD,EAAQ,EACrD,OAAOzF,EAGT+T,EAAY/T,EAAK+T,UAAUjZ,MAAM,KAAK,GACtCijB,EAAoBzI,GAAqBvB,GAEzC,IAAI8B,EAAgB7V,EAAKiR,QAAQgC,OAC7BkL,EAAane,EAAKiR,QAAQ3D,UAG1BiM,EAAQ5f,KAAK4f,MACb6E,EAA4B,SAAdrK,GAAwBwF,EAAM1D,EAAc3E,OAASqI,EAAM4E,EAAWhN,OAAuB,UAAd4C,GAAyBwF,EAAM1D,EAAc1E,MAAQoI,EAAM4E,EAAWjN,QAAwB,QAAd6C,GAAuBwF,EAAM1D,EAAczE,QAAUmI,EAAM4E,EAAW9M,MAAsB,WAAd0C,GAA0BwF,EAAM1D,EAAcxE,KAAOkI,EAAM4E,EAAW/M,QAEjUiN,EAAgB9E,EAAM1D,EAAc1E,MAAQoI,EAAMnG,EAAWjC,MAC7DmN,EAAiB/E,EAAM1D,EAAc3E,OAASqI,EAAMnG,EAAWlC,OAC/DqN,EAAehF,EAAM1D,EAAcxE,KAAOkI,EAAMnG,EAAW/B,KAC3DmN,EAAkBjF,EAAM1D,EAAczE,QAAUmI,EAAMnG,EAAWhC,QAEjEqN,EAAoC,SAAd1K,GAAwBsK,GAA+B,UAAdtK,GAAyBuK,GAAgC,QAAdvK,GAAuBwK,GAA8B,WAAdxK,GAA0ByK,EAG3K9E,GAAuD,IAA1C,CAAC,MAAO,UAAU1S,QAAQ+M,GAGvC2K,IAA0B9G,EAAQ+G,iBAAmBjF,GAA4B,UAAd5E,GAAyBuJ,GAAiB3E,GAA4B,QAAd5E,GAAuBwJ,IAAmB5E,GAA4B,UAAd5E,GAAyByJ,IAAiB7E,GAA4B,QAAd5E,GAAuB0J,GAGlQI,IAA8BhH,EAAQiH,0BAA4BnF,GAA4B,UAAd5E,GAAyBwJ,GAAkB5E,GAA4B,QAAd5E,GAAuBuJ,IAAkB3E,GAA4B,UAAd5E,GAAyB0J,IAAoB9E,GAA4B,QAAd5E,GAAuByJ,GAElRO,EAAmBJ,GAAyBE,GAE5CR,GAAeK,GAAuBK,KAExC9e,EAAK6d,SAAU,GAEXO,GAAeK,KACjB1K,EAAYiK,EAAUvY,EAAQ,IAG5BqZ,IACFhK,EAvJR,SAA8BA,GAC5B,MAAkB,QAAdA,EACK,QACgB,UAAdA,EACF,MAEFA,EAiJWiK,CAAqBjK,IAGnC9U,EAAK+T,UAAYA,GAAae,EAAY,IAAMA,EAAY,IAI5D9U,EAAKiR,QAAQgC,OAASpC,GAAS,GAAI7Q,EAAKiR,QAAQgC,OAAQyC,GAAiB1V,EAAKuc,SAAStJ,OAAQjT,EAAKiR,QAAQ3D,UAAWtN,EAAK+T,YAE5H/T,EAAOuW,GAAavW,EAAKuc,SAAS/F,UAAWxW,EAAM,WAGhDA,GA4jBLie,SAAU,OAKV/K,QAAS,EAOTC,kBAAmB,WAQnBwL,gBAAgB,EAQhBE,yBAAyB,GAU3BG,MAAO,CAEL1Q,MAAO,IAEP0I,SAAS,EAET5Z,GArQJ,SAAe4C,GACb,IAAI+T,EAAY/T,EAAK+T,UACjBgH,EAAgBhH,EAAUjZ,MAAM,KAAK,GACrCue,EAAgBrZ,EAAKiR,QACrBgC,EAASoG,EAAcpG,OACvB3F,EAAY+L,EAAc/L,UAE1BwI,GAAwD,IAA9C,CAAC,OAAQ,SAAS9O,QAAQ+T,GAEpCkE,GAA6D,IAA5C,CAAC,MAAO,QAAQjY,QAAQ+T,GAO7C,OALA9H,EAAO6C,EAAU,OAAS,OAASxI,EAAUyN,IAAkBkE,EAAiBhM,EAAO6C,EAAU,QAAU,UAAY,GAEvH9V,EAAK+T,UAAYuB,GAAqBvB,GACtC/T,EAAKiR,QAAQgC,OAASjC,GAAciC,GAE7BjT,IAkQPsK,KAAM,CAEJgE,MAAO,IAEP0I,SAAS,EAET5Z,GA9TJ,SAAc4C,GACZ,IAAK+Z,GAAmB/Z,EAAKuc,SAAS/F,UAAW,OAAQ,mBACvD,OAAOxW,EAGT,IAAIgU,EAAUhU,EAAKiR,QAAQ3D,UACvB4R,EAAQ/I,GAAKnW,EAAKuc,SAAS/F,UAAW,SAAU7D,GAClD,MAAyB,oBAAlBA,EAASyE,OACfhE,WAEH,GAAIY,EAAQ5C,OAAS8N,EAAM7N,KAAO2C,EAAQ7C,KAAO+N,EAAMhO,OAAS8C,EAAQ3C,IAAM6N,EAAM9N,QAAU4C,EAAQ9C,MAAQgO,EAAM/N,KAAM,CAExH,IAAkB,IAAdnR,EAAKsK,KACP,OAAOtK,EAGTA,EAAKsK,MAAO,EACZtK,EAAKmf,WAAW,uBAAyB,OACpC,CAEL,IAAkB,IAAdnf,EAAKsK,KACP,OAAOtK,EAGTA,EAAKsK,MAAO,EACZtK,EAAKmf,WAAW,wBAAyB,EAG3C,OAAOnf,IAoTPof,aAAc,CAEZ9Q,MAAO,IAEP0I,SAAS,EAET5Z,GAtgCJ,SAAsB4C,EAAM4X,GAC1B,IAAI1C,EAAI0C,EAAQ1C,EACZE,EAAIwC,EAAQxC,EACZnC,EAASjT,EAAKiR,QAAQgC,OAItBoM,EAA8BlJ,GAAKnW,EAAKuc,SAAS/F,UAAW,SAAU7D,GACxE,MAAyB,eAAlBA,EAASyE,OACfkI,qBACiClQ,IAAhCiQ,GACFvI,QAAQC,KAAK,iIAEf,IAAIuI,OAAkDlQ,IAAhCiQ,EAA4CA,EAA8BzH,EAAQ0H,gBAEpGvR,EAAeF,GAAgB7N,EAAKuc,SAAStJ,QAC7CsM,EAAmBrU,GAAsB6C,GAGzCyB,EAAS,CACXgQ,SAAUvM,EAAOuM,UAGfvO,EAAUgI,GAAkBjZ,EAAM4B,OAAO6d,iBAAmB,IAAM3F,IAElEpK,EAAc,WAANwF,EAAiB,MAAQ,SACjCvF,EAAc,UAANyF,EAAgB,OAAS,QAKjCsK,EAAmBrI,GAAyB,aAW5ClG,OAAO,EACPE,OAAM,EAqBV,GAhBIA,EAJU,UAAV3B,EAG4B,SAA1B3B,EAAalB,UACRkB,EAAa6D,aAAeX,EAAQG,QAEpCmO,EAAiBvP,OAASiB,EAAQG,OAGrCH,EAAQI,IAIZF,EAFU,SAAVxB,EAC4B,SAA1B5B,EAAalB,UACPkB,EAAa4D,YAAcV,EAAQC,OAEnCqO,EAAiBtP,MAAQgB,EAAQC,MAGpCD,EAAQE,KAEbmO,GAAmBI,EACrBlQ,EAAOkQ,GAAoB,eAAiBvO,EAAO,OAASE,EAAM,SAClE7B,EAAOE,GAAS,EAChBF,EAAOG,GAAS,EAChBH,EAAOmQ,WAAa,gBACf,CAEL,IAAIC,EAAsB,UAAVlQ,GAAsB,EAAI,EACtCmQ,EAAuB,SAAVlQ,GAAqB,EAAI,EAC1CH,EAAOE,GAAS2B,EAAMuO,EACtBpQ,EAAOG,GAASwB,EAAO0O,EACvBrQ,EAAOmQ,WAAajQ,EAAQ,KAAOC,EAIrC,IAAIwP,EAAa,CACfW,cAAe9f,EAAK+T,WAQtB,OAJA/T,EAAKmf,WAAatO,GAAS,GAAIsO,EAAYnf,EAAKmf,YAChDnf,EAAKwP,OAASqB,GAAS,GAAIrB,EAAQxP,EAAKwP,QACxCxP,EAAK+f,YAAclP,GAAS,GAAI7Q,EAAKiR,QAAQiM,MAAOld,EAAK+f,aAElD/f,GAo7BLsf,iBAAiB,EAMjBpK,EAAG,SAMHE,EAAG,SAkBL4K,WAAY,CAEV1R,MAAO,IAEP0I,SAAS,EAET5Z,GAzpCJ,SAAoB4C,GAgBlB,OAXA+Y,GAAU/Y,EAAKuc,SAAStJ,OAAQjT,EAAKwP,QAzBvC,SAAuBxV,EAASmlB,GAC9BvjB,OAAOuY,KAAKgL,GAAYtI,QAAQ,SAAUH,IAE1B,IADFyI,EAAWzI,GAErB1c,EAAQwH,aAAakV,EAAMyI,EAAWzI,IAEtC1c,EAAQimB,gBAAgBvJ,KAuB5BwJ,CAAclgB,EAAKuc,SAAStJ,OAAQjT,EAAKmf,YAGrCnf,EAAKod,cAAgBxhB,OAAOuY,KAAKnU,EAAK+f,aAAa7d,QACrD6W,GAAU/Y,EAAKod,aAAcpd,EAAK+f,aAG7B/f,GA2oCLmgB,OA9nCJ,SAA0B7S,EAAW2F,EAAQ2E,EAASwI,EAAiBpL,GAErE,IAAIW,EAAmBZ,GAAoBC,EAAO/B,EAAQ3F,EAAWsK,EAAQkE,eAKzE/H,EAAYD,GAAqB8D,EAAQ7D,UAAW4B,EAAkB1C,EAAQ3F,EAAWsK,EAAQpB,UAAUoH,KAAKzK,kBAAmByE,EAAQpB,UAAUoH,KAAK1K,SAQ9J,OANAD,EAAOzR,aAAa,cAAeuS,GAInCgF,GAAU9F,EAAQ,CAAEuM,SAAU5H,EAAQkE,cAAgB,QAAU,aAEzDlE,GAsnCL0H,qBAAiBlQ,KAuGjBiR,IA+EFnQ,GAAYmQ,GAAQ,CAAC,CACnBzP,IAAK,SACL3U,MAAO,WACL,OA9lDN,WAEE,IAAI/C,KAAK8b,MAAMsL,YAAf,CAIA,IAAItgB,EAAO,CACTuc,SAAUrjB,KACVsW,OAAQ,GACRuQ,YAAa,GACbZ,WAAY,GACZtB,SAAS,EACT5M,QAAS,IAIXjR,EAAKiR,QAAQ3D,UAAYyH,GAAoB7b,KAAK8b,MAAO9b,KAAK+Z,OAAQ/Z,KAAKoU,UAAWpU,KAAK0e,QAAQkE,eAKnG9b,EAAK+T,UAAYD,GAAqB5a,KAAK0e,QAAQ7D,UAAW/T,EAAKiR,QAAQ3D,UAAWpU,KAAK+Z,OAAQ/Z,KAAKoU,UAAWpU,KAAK0e,QAAQpB,UAAUoH,KAAKzK,kBAAmBja,KAAK0e,QAAQpB,UAAUoH,KAAK1K,SAG9LlT,EAAK8d,kBAAoB9d,EAAK+T,UAE9B/T,EAAK8b,cAAgB5iB,KAAK0e,QAAQkE,cAGlC9b,EAAKiR,QAAQgC,OAASyC,GAAiBxc,KAAK+Z,OAAQjT,EAAKiR,QAAQ3D,UAAWtN,EAAK+T,WAEjF/T,EAAKiR,QAAQgC,OAAOuM,SAAWtmB,KAAK0e,QAAQkE,cAAgB,QAAU,WAGtE9b,EAAOuW,GAAard,KAAKsd,UAAWxW,GAI/B9G,KAAK8b,MAAMuL,UAIdrnB,KAAK0e,QAAQqE,SAASjc,IAHtB9G,KAAK8b,MAAMuL,WAAY,EACvBrnB,KAAK0e,QAAQoE,SAAShc,MAsjDNjE,KAAK7C,QAEpB,CACD0X,IAAK,UACL3U,MAAO,WACL,OA7gDN,WAsBE,OArBA/C,KAAK8b,MAAMsL,aAAc,EAGrBrJ,GAAkB/d,KAAKsd,UAAW,gBACpCtd,KAAK+Z,OAAOgN,gBAAgB,eAC5B/mB,KAAK+Z,OAAOnI,MAAM0U,SAAW,GAC7BtmB,KAAK+Z,OAAOnI,MAAMuG,IAAM,GACxBnY,KAAK+Z,OAAOnI,MAAMqG,KAAO,GACzBjY,KAAK+Z,OAAOnI,MAAMoG,MAAQ,GAC1BhY,KAAK+Z,OAAOnI,MAAMsG,OAAS,GAC3BlY,KAAK+Z,OAAOnI,MAAM6U,WAAa,GAC/BzmB,KAAK+Z,OAAOnI,MAAMuM,GAAyB,cAAgB,IAG7Dne,KAAKof,wBAIDpf,KAAK0e,QAAQmE,iBACf7iB,KAAK+Z,OAAOlW,WAAWyjB,YAAYtnB,KAAK+Z,QAEnC/Z,MAu/CY6C,KAAK7C,QAErB,CACD0X,IAAK,uBACL3U,MAAO,WACL,OA18CN,WACO/C,KAAK8b,MAAMqD,gBACdnf,KAAK8b,MAAQ2C,GAAoBze,KAAKoU,UAAWpU,KAAK0e,QAAS1e,KAAK8b,MAAO9b,KAAKsf,kBAw8ClDzc,KAAK7C,QAElC,CACD0X,IAAK,wBACL3U,MAAO,WACL,OAAOqc,GAAsBvc,KAAK7C,UA4B/BmnB,IApHP,SAASA,GAAO/S,EAAW2F,GACzB,IAAIha,EAAQC,KAER0e,EAA6B,EAAnB3Z,UAAUiE,aAA+BkN,IAAjBnR,UAAU,GAAmBA,UAAU,GAAK,IA3jEjE,SAAUse,EAAUlc,GACvC,KAAMkc,aAAoBlc,GACxB,MAAM,IAAInD,UAAU,qCA0jEpBujB,CAAevnB,KAAMmnB,IAErBnnB,KAAKsf,eAAiB,WACpB,OAAOkI,sBAAsBznB,EAAM0nB,SAIrCznB,KAAKynB,OAAS1U,GAAS/S,KAAKynB,OAAOpb,KAAKrM,OAGxCA,KAAK0e,QAAU/G,GAAS,GAAIwP,GAAOxE,SAAUjE,GAG7C1e,KAAK8b,MAAQ,CACXsL,aAAa,EACbC,WAAW,EACXpI,cAAe,IAIjBjf,KAAKoU,UAAYA,GAAaA,EAAUjQ,OAASiQ,EAAU,GAAKA,EAChEpU,KAAK+Z,OAASA,GAAUA,EAAO5V,OAAS4V,EAAO,GAAKA,EAGpD/Z,KAAK0e,QAAQpB,UAAY,GACzB5a,OAAOuY,KAAKtD,GAAS,GAAIwP,GAAOxE,SAASrF,UAAWoB,EAAQpB,YAAYK,QAAQ,SAAUO,GACxFne,EAAM2e,QAAQpB,UAAUY,GAAQvG,GAAS,GAAIwP,GAAOxE,SAASrF,UAAUY,IAAS,GAAIQ,EAAQpB,UAAYoB,EAAQpB,UAAUY,GAAQ,MAIpIle,KAAKsd,UAAY5a,OAAOuY,KAAKjb,KAAK0e,QAAQpB,WAAWpC,IAAI,SAAUgD,GACjE,OAAOvG,GAAS,CACduG,KAAMA,GACLne,EAAM2e,QAAQpB,UAAUY,MAG5B5C,KAAK,SAAUC,EAAGC,GACjB,OAAOD,EAAEnG,MAAQoG,EAAEpG,QAOrBpV,KAAKsd,UAAUK,QAAQ,SAAUuJ,GAC3BA,EAAgBpJ,SAAW1K,GAAW8T,EAAgBD,SACxDC,EAAgBD,OAAOlnB,EAAMqU,UAAWrU,EAAMga,OAAQha,EAAM2e,QAASwI,EAAiBnnB,EAAM+b,SAKhG9b,KAAKynB,SAEL,IAAItI,EAAgBnf,KAAK0e,QAAQS,cAC7BA,GAEFnf,KAAK0nB,uBAGP1nB,KAAK8b,MAAMqD,cAAgBA,EA6E/BgI,GAAOQ,OAA2B,oBAAXjf,OAAyBA,OAASkf,QAAQC,YACjEV,GAAO/F,WAAaA,GACpB+F,GAAOxE,SAAWA,GCniFlB,IAAM3d,GAA2B,WAE3BC,GAA2B,cAC3BC,GAAS,IAAsBD,GAC/BoC,GAA2B,YAC3BlC,GAA2BjF,EAAEgE,GAAGc,IAOhC8iB,GAA2B,IAAI1kB,OAAU2kB,YAEzC3iB,GAAQ,CACZ+K,KAAI,OAAsBjL,GAC1BkL,OAAM,SAAsBlL,GAC5B+K,KAAI,OAAsB/K,GAC1BgL,MAAK,QAAsBhL,GAC3B8iB,MAAK,QAAsB9iB,GAC3BK,eAAc,QAAaL,GAAYmC,GACvC4gB,iBAAgB,UAAa/iB,GAAYmC,GACzC6gB,eAAc,QAAahjB,GAAYmC,IAGnC7B,GACc,WADdA,GAEc,OAFdA,GAGc,SAHdA,GAIc,YAJdA,GAKc,WALdA,GAMc,sBANdA,GAQc,kBAGd8B,GACY,2BADZA,GAEY,iBAFZA,GAGY,iBAHZA,GAIY,cAJZA,GAKY,8DAGZ6gB,GACQ,YADRA,GAEQ,UAFRA,GAGQ,eAHRA,GAIQ,aAJRA,GAKQ,cALRA,GAOQ,aAIRjf,GAAU,CACd0Y,OAAe,EACf8C,MAAe,EACf0D,SAAe,eACfhU,UAAe,SACfiU,QAAe,UACfC,aAAe,MAGX7e,GAAc,CAClBmY,OAAe,2BACf8C,KAAe,UACf0D,SAAe,mBACfhU,UAAe,mBACfiU,QAAe,SACfC,aAAe,iBASXC,cACJ,SAAAA,EAAYznB,EAASyB,GACnBvC,KAAK0F,SAAY5E,EACjBd,KAAKwoB,QAAY,KACjBxoB,KAAKkL,QAAYlL,KAAKmL,WAAW5I,GACjCvC,KAAKyoB,MAAYzoB,KAAK0oB,kBACtB1oB,KAAK2oB,UAAY3oB,KAAK4oB,gBAEtB5oB,KAAK2L,gDAmBPjE,OAAA,WACE,IAAI1H,KAAK0F,SAASmjB,WAAY3oB,EAAEF,KAAK0F,UAAUa,SAASf,IAAxD,CAIA,IAAMsjB,EAAW5oB,EAAEF,KAAKyoB,OAAOliB,SAASf,IAExC+iB,EAASQ,cAELD,GAIJ9oB,KAAKqR,MAAK,OAGZA,KAAA,SAAK2X,GACH,QADsB,IAAnBA,IAAAA,GAAY,KACXhpB,KAAK0F,SAASmjB,UAAY3oB,EAAEF,KAAK0F,UAAUa,SAASf,KAAuBtF,EAAEF,KAAKyoB,OAAOliB,SAASf,KAAtG,CAIA,IAAM6I,EAAgB,CACpBA,cAAerO,KAAK0F,UAEhBujB,EAAY/oB,EAAEkF,MAAMA,GAAM6K,KAAM5B,GAChClI,EAASoiB,EAASW,sBAAsBlpB,KAAK0F,UAInD,GAFAxF,EAAEiG,GAAQpE,QAAQknB,IAEdA,EAAUljB,qBAAd,CAKA,IAAK/F,KAAK2oB,WAAaK,EAAW,CAKhC,GAAsB,oBAAX7B,GACT,MAAM,IAAInjB,UAAU,oEAGtB,IAAImlB,EAAmBnpB,KAAK0F,SAEG,WAA3B1F,KAAKkL,QAAQkJ,UACf+U,EAAmBhjB,EACV/F,EAAK8B,UAAUlC,KAAKkL,QAAQkJ,aACrC+U,EAAmBnpB,KAAKkL,QAAQkJ,UAGa,oBAAlCpU,KAAKkL,QAAQkJ,UAAUjQ,SAChCglB,EAAmBnpB,KAAKkL,QAAQkJ,UAAU,KAOhB,iBAA1BpU,KAAKkL,QAAQkd,UACfloB,EAAEiG,GAAQ4I,SAASvJ,IAErBxF,KAAKwoB,QAAU,IAAIrB,GAAOgC,EAAkBnpB,KAAKyoB,MAAOzoB,KAAKopB,oBAO3D,iBAAkBzoB,SAAS8C,iBACuB,IAAlDvD,EAAEiG,GAAQC,QAAQkB,IAAqB0B,QACzC9I,EAAES,SAASmT,MAAMhF,WAAW5H,GAAG,YAAa,KAAMhH,EAAEmpB,MAGtDrpB,KAAK0F,SAAS0C,QACdpI,KAAK0F,SAAS4C,aAAa,iBAAiB,GAE5CpI,EAAEF,KAAKyoB,OAAOlgB,YAAY/C,IAC1BtF,EAAEiG,GACCoC,YAAY/C,IACZzD,QAAQ7B,EAAEkF,MAAMA,GAAM8K,MAAO7B,SAGlC+C,KAAA,WACE,IAAIpR,KAAK0F,SAASmjB,WAAY3oB,EAAEF,KAAK0F,UAAUa,SAASf,KAAwBtF,EAAEF,KAAKyoB,OAAOliB,SAASf,IAAvG,CAIA,IAAM6I,EAAgB,CACpBA,cAAerO,KAAK0F,UAEhB4jB,EAAYppB,EAAEkF,MAAMA,GAAM+K,KAAM9B,GAChClI,EAASoiB,EAASW,sBAAsBlpB,KAAK0F,UAEnDxF,EAAEiG,GAAQpE,QAAQunB,GAEdA,EAAUvjB,uBAIV/F,KAAKwoB,SACPxoB,KAAKwoB,QAAQe,UAGfrpB,EAAEF,KAAKyoB,OAAOlgB,YAAY/C,IAC1BtF,EAAEiG,GACCoC,YAAY/C,IACZzD,QAAQ7B,EAAEkF,MAAMA,GAAMgL,OAAQ/B,SAGnCpI,QAAA,WACE/F,EAAEgG,WAAWlG,KAAK0F,SAAUT,IAC5B/E,EAAEF,KAAK0F,UAAUiH,IAAIzH,IACrBlF,KAAK0F,SAAW,MAChB1F,KAAKyoB,MAAQ,QACTzoB,KAAKwoB,UACPxoB,KAAKwoB,QAAQe,UACbvpB,KAAKwoB,QAAU,SAInBf,OAAA,WACEznB,KAAK2oB,UAAY3oB,KAAK4oB,gBACD,OAAjB5oB,KAAKwoB,SACPxoB,KAAKwoB,QAAQlJ,oBAMjB3T,mBAAA,WAAqB,IAAA5L,EAAAC,KACnBE,EAAEF,KAAK0F,UAAUwB,GAAG9B,GAAM4iB,MAAO,SAAC3jB,GAChCA,EAAM4C,iBACN5C,EAAMmlB,kBACNzpB,EAAK2H,cAITyD,WAAA,SAAW5I,GAaT,OAZAA,EAAMqK,EAAA,GACD5M,KAAKypB,YAAYvgB,QADhB,GAEDhJ,EAAEF,KAAK0F,UAAUoB,OAFhB,GAGDvE,GAGLnC,EAAKiC,gBACH2C,GACAzC,EACAvC,KAAKypB,YAAYhgB,aAGZlH,KAGTmmB,gBAAA,WACE,IAAK1oB,KAAKyoB,MAAO,CACf,IAAMtiB,EAASoiB,EAASW,sBAAsBlpB,KAAK0F,UAE/CS,IACFnG,KAAKyoB,MAAQtiB,EAAOhF,cAAcmG,KAGtC,OAAOtH,KAAKyoB,SAGdiB,cAAA,WACE,IAAMC,EAAkBzpB,EAAEF,KAAK0F,SAAS7B,YACpCgX,EAAYsN,GAehB,OAZIwB,EAAgBpjB,SAASf,KAC3BqV,EAAYsN,GACRjoB,EAAEF,KAAKyoB,OAAOliB,SAASf,MACzBqV,EAAYsN,KAELwB,EAAgBpjB,SAASf,IAClCqV,EAAYsN,GACHwB,EAAgBpjB,SAASf,IAClCqV,EAAYsN,GACHjoB,EAAEF,KAAKyoB,OAAOliB,SAASf,MAChCqV,EAAYsN,IAEPtN,KAGT+N,cAAA,WACE,OAAoD,EAA7C1oB,EAAEF,KAAK0F,UAAUU,QAAQ,WAAW4C,UAG7C4gB,WAAA,WAAa,IAAA5c,EAAAhN,KACL4hB,EAAS,GAef,MAbmC,mBAAxB5hB,KAAKkL,QAAQ0W,OACtBA,EAAO1d,GAAK,SAAC4C,GAMX,OALAA,EAAKiR,QAALnL,EAAA,GACK9F,EAAKiR,QADV,GAEK/K,EAAK9B,QAAQ0W,OAAO9a,EAAKiR,QAAS/K,EAAKtH,WAAa,IAGlDoB,GAGT8a,EAAOA,OAAS5hB,KAAKkL,QAAQ0W,OAGxBA,KAGTwH,iBAAA,WACE,IAAMd,EAAe,CACnBzN,UAAW7a,KAAK0pB,gBAChBpM,UAAW,CACTsE,OAAQ5hB,KAAK4pB,aACblF,KAAM,CACJ5G,QAAS9d,KAAKkL,QAAQwZ,MAExBtB,gBAAiB,CACfnJ,kBAAmBja,KAAKkL,QAAQkd,YAYtC,MAN6B,WAAzBpoB,KAAKkL,QAAQmd,UACfC,EAAahL,UAAUwJ,WAAa,CAClChJ,SAAS,IAIblR,EAAA,GACK0b,EADL,GAEKtoB,KAAKkL,QAAQod,iBAMb3hB,iBAAP,SAAwBpE,GACtB,OAAOvC,KAAK4G,KAAK,WACf,IAAIE,EAAO5G,EAAEF,MAAM8G,KAAK7B,IAQxB,GALK6B,IACHA,EAAO,IAAIyhB,EAASvoB,KAHY,iBAAXuC,EAAsBA,EAAS,MAIpDrC,EAAEF,MAAM8G,KAAK7B,GAAU6B,IAGH,iBAAXvE,EAAqB,CAC9B,GAA4B,oBAAjBuE,EAAKvE,GACd,MAAM,IAAIyB,UAAJ,oBAAkCzB,EAAlC,KAERuE,EAAKvE,WAKJwmB,YAAP,SAAmB1kB,GACjB,IAAIA,GAhWyB,IAgWfA,EAAMwJ,QACH,UAAfxJ,EAAMyD,MApWqB,IAoWDzD,EAAMwJ,OAMlC,IAFA,IAAMgc,EAAU,GAAGjhB,MAAM/F,KAAKlC,SAASkI,iBAAiBvB,KAE/CwB,EAAI,EAAGC,EAAM8gB,EAAQ7gB,OAAQF,EAAIC,EAAKD,IAAK,CAClD,IAAM3C,EAASoiB,EAASW,sBAAsBW,EAAQ/gB,IAChDghB,EAAU5pB,EAAE2pB,EAAQ/gB,IAAIhC,KAAK7B,IAC7BoJ,EAAgB,CACpBA,cAAewb,EAAQ/gB,IAOzB,GAJIzE,GAAwB,UAAfA,EAAMyD,OACjBuG,EAAc0b,WAAa1lB,GAGxBylB,EAAL,CAIA,IAAME,EAAeF,EAAQrB,MAC7B,GAAKvoB,EAAEiG,GAAQI,SAASf,OAIpBnB,IAAyB,UAAfA,EAAMyD,MAChB,kBAAkBzE,KAAKgB,EAAMK,OAAOyD,UAA2B,UAAf9D,EAAMyD,MA/X/B,IA+XmDzD,EAAMwJ,QAChF3N,EAAE+H,SAAS9B,EAAQ9B,EAAMK,SAF7B,CAMA,IAAM4kB,EAAYppB,EAAEkF,MAAMA,GAAM+K,KAAM9B,GACtCnO,EAAEiG,GAAQpE,QAAQunB,GACdA,EAAUvjB,uBAMV,iBAAkBpF,SAAS8C,iBAC7BvD,EAAES,SAASmT,MAAMhF,WAAWnC,IAAI,YAAa,KAAMzM,EAAEmpB,MAGvDQ,EAAQ/gB,GAAGR,aAAa,gBAAiB,SAErCwhB,EAAQtB,SACVsB,EAAQtB,QAAQe,UAGlBrpB,EAAE8pB,GAAc1jB,YAAYd,IAC5BtF,EAAEiG,GACCG,YAAYd,IACZzD,QAAQ7B,EAAEkF,MAAMA,GAAMgL,OAAQ/B,WAI9B6a,sBAAP,SAA6BpoB,GAC3B,IAAIqF,EACEpF,EAAWX,EAAKS,uBAAuBC,GAM7C,OAJIC,IACFoF,EAASxF,SAASQ,cAAcJ,IAG3BoF,GAAUrF,EAAQ+C,cAIpBomB,uBAAP,SAA8B5lB,GAQ5B,IAAI,kBAAkBhB,KAAKgB,EAAMK,OAAOyD,WAlbX,KAmbzB9D,EAAMwJ,OApbmB,KAobQxJ,EAAMwJ,QAhbd,KAib1BxJ,EAAMwJ,OAlboB,KAkbYxJ,EAAMwJ,OAC3C3N,EAAEmE,EAAMK,QAAQ0B,QAAQkB,IAAe0B,SAAW8e,GAAezkB,KAAKgB,EAAMwJ,UAIhFxJ,EAAM4C,iBACN5C,EAAMmlB,mBAEFxpB,KAAK6oB,WAAY3oB,EAAEF,MAAMuG,SAASf,KAAtC,CAIA,IAAMW,EAAWoiB,EAASW,sBAAsBlpB,MAC1C8oB,EAAW5oB,EAAEiG,GAAQI,SAASf,IAEpC,GAAKsjB,GApcwB,KAocZzkB,EAAMwJ,MAIvB,GAAKib,KAAYA,GAxcY,KAwcCzkB,EAAMwJ,OAvcP,KAucmCxJ,EAAMwJ,OAAtE,CAUA,IAAMqc,EAAQ,GAAGthB,MAAM/F,KAAKsD,EAAO0C,iBAAiBvB,KACjDuJ,OAAO,SAACsZ,GAAD,OAAUjqB,EAAEiqB,GAAMxlB,GAAG,cAE/B,GAAqB,IAAjBulB,EAAMlhB,OAAV,CAIA,IAAIuD,EAAQ2d,EAAMpc,QAAQzJ,EAAMK,QAtdH,KAwdzBL,EAAMwJ,OAAsC,EAARtB,GACtCA,IAxd2B,KA2dzBlI,EAAMwJ,OAAgCtB,EAAQ2d,EAAMlhB,OAAS,GAC/DuD,IAGEA,EAAQ,IACVA,EAAQ,GAGV2d,EAAM3d,GAAOnE,aA/Bb,CACE,GAzc2B,KAycvB/D,EAAMwJ,MAA0B,CAClC,IAAMnG,EAASvB,EAAOhF,cAAcmG,IACpCpH,EAAEwH,GAAQ3F,QAAQ,SAGpB7B,EAAEF,MAAM+B,QAAQ,oDAvXlB,MA5F6B,wCAgG7B,OAAOmH,uCAIP,OAAOO,YAkZXvJ,EAAES,UACCuG,GAAG9B,GAAM6iB,iBAAkB3gB,GAAsBihB,GAAS0B,wBAC1D/iB,GAAG9B,GAAM6iB,iBAAkB3gB,GAAeihB,GAAS0B,wBACnD/iB,GAAM9B,GAAMG,eAHf,IAGiCH,GAAM8iB,eAAkBK,GAASQ,aAC/D7hB,GAAG9B,GAAMG,eAAgB+B,GAAsB,SAAUjD,GACxDA,EAAM4C,iBACN5C,EAAMmlB,kBACNjB,GAAS5hB,iBAAiB9D,KAAK3C,EAAEF,MAAO,YAEzCkH,GAAG9B,GAAMG,eAAgB+B,GAAqB,SAACqG,GAC9CA,EAAE6b,oBASNtpB,EAAEgE,GAAGc,IAAQujB,GAAS5hB,iBACtBzG,EAAEgE,GAAGc,IAAMmC,YAAcohB,GACzBroB,EAAEgE,GAAGc,IAAMoC,WAAa,WAEtB,OADAlH,EAAEgE,GAAGc,IAAQG,GACNojB,GAAS5hB,kBC/gBlB,IAAM3B,GAAqB,QAErBC,GAAqB,WACrBC,GAAS,IAAgBD,GAEzBE,GAAqBjF,EAAEgE,GAAGc,IAG1BkE,GAAU,CACdkhB,UAAW,EACXhhB,UAAW,EACXhB,OAAW,EACXiJ,MAAW,GAGP5H,GAAc,CAClB2gB,SAAW,mBACXhhB,SAAW,UACXhB,MAAW,UACXiJ,KAAW,WAGPjM,GAAQ,CACZ+K,KAAI,OAAuBjL,GAC3BmlB,eAAc,gBAAsBnlB,GACpCkL,OAAM,SAAuBlL,GAC7B+K,KAAI,OAAuB/K,GAC3BgL,MAAK,QAAuBhL,GAC5BolB,QAAO,UAAuBplB,GAC9BqlB,OAAM,SAAuBrlB,GAC7BslB,cAAa,gBAAuBtlB,GACpCulB,gBAAe,kBAAuBvlB,GACtCwlB,gBAAe,kBAAuBxlB,GACtCylB,kBAAiB,oBAAuBzlB,GACxCK,eAAc,QAAcL,GA9BH,aAiCrBM,GACiB,0BADjBA,GAEiB,0BAFjBA,GAGiB,iBAHjBA,GAIiB,aAJjBA,GAKiB,OALjBA,GAMiB,OANjBA,GAOiB,eAGjB8B,GACa,gBADbA,GAEa,cAFbA,GAGa,wBAHbA,GAIa,yBAJbA,GAKa,oDALbA,GAMa,cASbsjB,cACJ,SAAAA,EAAY9pB,EAASyB,GACnBvC,KAAKkL,QAAuBlL,KAAKmL,WAAW5I,GAC5CvC,KAAK0F,SAAuB5E,EAC5Bd,KAAK6qB,QAAuB/pB,EAAQK,cAAcmG,IAClDtH,KAAK8qB,UAAuB,KAC5B9qB,KAAK+qB,UAAuB,EAC5B/qB,KAAKgrB,oBAAuB,EAC5BhrB,KAAKirB,sBAAuB,EAC5BjrB,KAAKuQ,kBAAuB,EAC5BvQ,KAAKkrB,gBAAuB,6BAe9BxjB,OAAA,SAAO2G,GACL,OAAOrO,KAAK+qB,SAAW/qB,KAAKoR,OAASpR,KAAKqR,KAAKhD,MAGjDgD,KAAA,SAAKhD,GAAe,IAAAtO,EAAAC,KAClB,IAAIA,KAAK+qB,WAAY/qB,KAAKuQ,iBAA1B,CAIIrQ,EAAEF,KAAK0F,UAAUa,SAASf,MAC5BxF,KAAKuQ,kBAAmB,GAG1B,IAAM0Y,EAAY/oB,EAAEkF,MAAMA,GAAM6K,KAAM,CACpC5B,cAAAA,IAGFnO,EAAEF,KAAK0F,UAAU3D,QAAQknB,GAErBjpB,KAAK+qB,UAAY9B,EAAUljB,uBAI/B/F,KAAK+qB,UAAW,EAEhB/qB,KAAKmrB,kBACLnrB,KAAKorB,gBAELprB,KAAKqrB,gBAELrrB,KAAKsrB,kBACLtrB,KAAKurB,kBAELrrB,EAAEF,KAAK0F,UAAUwB,GACf9B,GAAMolB,cACNljB,GACA,SAACjD,GAAD,OAAWtE,EAAKqR,KAAK/M,KAGvBnE,EAAEF,KAAK6qB,SAAS3jB,GAAG9B,GAAMulB,kBAAmB,WAC1CzqB,EAAEH,EAAK2F,UAAUvF,IAAIiF,GAAMslB,gBAAiB,SAACrmB,GACvCnE,EAAEmE,EAAMK,QAAQC,GAAG5E,EAAK2F,YAC1B3F,EAAKkrB,sBAAuB,OAKlCjrB,KAAKwrB,cAAc,WAAA,OAAMzrB,EAAK0rB,aAAapd,UAG7C+C,KAAA,SAAK/M,GAAO,IAAA2I,EAAAhN,KAKV,GAJIqE,GACFA,EAAM4C,iBAGHjH,KAAK+qB,WAAY/qB,KAAKuQ,iBAA3B,CAIA,IAAM+Y,EAAYppB,EAAEkF,MAAMA,GAAM+K,MAIhC,GAFAjQ,EAAEF,KAAK0F,UAAU3D,QAAQunB,GAEpBtpB,KAAK+qB,WAAYzB,EAAUvjB,qBAAhC,CAIA/F,KAAK+qB,UAAW,EAChB,IAAMW,EAAaxrB,EAAEF,KAAK0F,UAAUa,SAASf,IAiB7C,GAfIkmB,IACF1rB,KAAKuQ,kBAAmB,GAG1BvQ,KAAKsrB,kBACLtrB,KAAKurB,kBAELrrB,EAAES,UAAUgM,IAAIvH,GAAMklB,SAEtBpqB,EAAEF,KAAK0F,UAAUY,YAAYd,IAE7BtF,EAAEF,KAAK0F,UAAUiH,IAAIvH,GAAMolB,eAC3BtqB,EAAEF,KAAK6qB,SAASle,IAAIvH,GAAMulB,mBAGtBe,EAAY,CACd,IAAMpqB,EAAsBlB,EAAKiB,iCAAiCrB,KAAK0F,UAEvExF,EAAEF,KAAK0F,UACJvF,IAAIC,EAAKR,eAAgB,SAACyE,GAAD,OAAW2I,EAAK2e,WAAWtnB,KACpDD,qBAAqB9C,QAExBtB,KAAK2rB,kBAIT1lB,QAAA,WACE,CAACyC,OAAQ1I,KAAK0F,SAAU1F,KAAK6qB,SAC1BlN,QAAQ,SAACiO,GAAD,OAAiB1rB,EAAE0rB,GAAajf,IAAIzH,MAO/ChF,EAAES,UAAUgM,IAAIvH,GAAMklB,SAEtBpqB,EAAEgG,WAAWlG,KAAK0F,SAAUT,IAE5BjF,KAAKkL,QAAuB,KAC5BlL,KAAK0F,SAAuB,KAC5B1F,KAAK6qB,QAAuB,KAC5B7qB,KAAK8qB,UAAuB,KAC5B9qB,KAAK+qB,SAAuB,KAC5B/qB,KAAKgrB,mBAAuB,KAC5BhrB,KAAKirB,qBAAuB,KAC5BjrB,KAAKuQ,iBAAuB,KAC5BvQ,KAAKkrB,gBAAuB,QAG9BW,aAAA,WACE7rB,KAAKqrB,mBAKPlgB,WAAA,SAAW5I,GAMT,OALAA,EAAMqK,EAAA,GACD1D,GADC,GAED3G,GAELnC,EAAKiC,gBAAgB2C,GAAMzC,EAAQkH,IAC5BlH,KAGTupB,2BAAA,WAA6B,IAAA3e,EAAAnN,KAC3B,GAA8B,WAA1BA,KAAKkL,QAAQkf,SAAuB,CACtC,IAAM2B,EAAqB7rB,EAAEkF,MAAMA,GAAMilB,gBAGzC,GADAnqB,EAAEF,KAAK0F,UAAU3D,QAAQgqB,GACrBA,EAAmBC,iBACrB,OAGFhsB,KAAK0F,SAASsC,UAAUiB,IAAIzD,IAE5B,IAAMymB,EAA0B7rB,EAAKiB,iCAAiCrB,KAAK0F,UAE3ExF,EAAEF,KAAK0F,UAAUvF,IAAIC,EAAKR,eAAgB,WACxCuN,EAAKzH,SAASsC,UAAUtB,OAAOlB,MAE9BpB,qBAAqB6nB,GACxBjsB,KAAK0F,SAAS0C,aAEdpI,KAAKoR,UAITqa,aAAA,SAAapd,GAAe,IAAAa,EAAAlP,KACpB0rB,EAAaxrB,EAAEF,KAAK0F,UAAUa,SAASf,IACvC0mB,EAAYlsB,KAAK6qB,QAAU7qB,KAAK6qB,QAAQ1pB,cAAcmG,IAAuB,KAE9EtH,KAAK0F,SAAS7B,YACf7D,KAAK0F,SAAS7B,WAAWzB,WAAakT,KAAK6W,cAE7CxrB,SAASmT,KAAKsY,YAAYpsB,KAAK0F,UAGjC1F,KAAK0F,SAASkM,MAAMyW,QAAU,QAC9BroB,KAAK0F,SAASqhB,gBAAgB,eAC9B/mB,KAAK0F,SAAS4C,aAAa,cAAc,GAErCpI,EAAEF,KAAK6qB,SAAStkB,SAASf,KAAyB0mB,EACpDA,EAAU7T,UAAY,EAEtBrY,KAAK0F,SAAS2S,UAAY,EAGxBqT,GACFtrB,EAAKyB,OAAO7B,KAAK0F,UAGnBxF,EAAEF,KAAK0F,UAAUqJ,SAASvJ,IAEtBxF,KAAKkL,QAAQ9C,OACfpI,KAAKqsB,gBAOoB,SAArBC,IACApd,EAAKhE,QAAQ9C,OACf8G,EAAKxJ,SAAS0C,QAEhB8G,EAAKqB,kBAAmB,EACxBrQ,EAAEgP,EAAKxJ,UAAU3D,QAAQwqB,GAT3B,IAAMA,EAAarsB,EAAEkF,MAAMA,GAAM8K,MAAO,CACtC7B,cAAAA,IAWF,GAAIqd,EAAY,CACd,IAAMpqB,EAAsBlB,EAAKiB,iCAAiCrB,KAAK6qB,SAEvE3qB,EAAEF,KAAK6qB,SACJ1qB,IAAIC,EAAKR,eAAgB0sB,GACzBloB,qBAAqB9C,QAExBgrB,OAIJD,cAAA,WAAgB,IAAAG,EAAAxsB,KACdE,EAAES,UACCgM,IAAIvH,GAAMklB,SACVpjB,GAAG9B,GAAMklB,QAAS,SAACjmB,GACd1D,WAAa0D,EAAMK,QACnB8nB,EAAK9mB,WAAarB,EAAMK,QACsB,IAA9CxE,EAAEssB,EAAK9mB,UAAU+mB,IAAIpoB,EAAMK,QAAQsE,QACrCwjB,EAAK9mB,SAAS0C,aAKtBkjB,gBAAA,WAAkB,IAAAoB,EAAA1sB,KACZA,KAAK+qB,UAAY/qB,KAAKkL,QAAQ9B,SAChClJ,EAAEF,KAAK0F,UAAUwB,GAAG9B,GAAMqlB,gBAAiB,SAACpmB,GAlTvB,KAmTfA,EAAMwJ,OACR6e,EAAKZ,+BAGC9rB,KAAK+qB,UACf7qB,EAAEF,KAAK0F,UAAUiH,IAAIvH,GAAMqlB,oBAI/Bc,gBAAA,WAAkB,IAAAoB,EAAA3sB,KACZA,KAAK+qB,SACP7qB,EAAEwI,QAAQxB,GAAG9B,GAAMmlB,OAAQ,SAAClmB,GAAD,OAAWsoB,EAAKd,aAAaxnB,KAExDnE,EAAEwI,QAAQiE,IAAIvH,GAAMmlB,WAIxBoB,WAAA,WAAa,IAAAiB,EAAA5sB,KACXA,KAAK0F,SAASkM,MAAMyW,QAAU,OAC9BroB,KAAK0F,SAAS4C,aAAa,eAAe,GAC1CtI,KAAK0F,SAASqhB,gBAAgB,cAC9B/mB,KAAKuQ,kBAAmB,EACxBvQ,KAAKwrB,cAAc,WACjBtrB,EAAES,SAASmT,MAAMxN,YAAYd,IAC7BonB,EAAKC,oBACLD,EAAKE,kBACL5sB,EAAE0sB,EAAKlnB,UAAU3D,QAAQqD,GAAMgL,aAInC2c,gBAAA,WACM/sB,KAAK8qB,YACP5qB,EAAEF,KAAK8qB,WAAWpkB,SAClB1G,KAAK8qB,UAAY,SAIrBU,cAAA,SAAcxM,GAAU,IAAAgO,EAAAhtB,KAChBitB,EAAU/sB,EAAEF,KAAK0F,UAAUa,SAASf,IACtCA,GAAiB,GAErB,GAAIxF,KAAK+qB,UAAY/qB,KAAKkL,QAAQkf,SAAU,CA4B1C,GA3BApqB,KAAK8qB,UAAYnqB,SAASusB,cAAc,OACxCltB,KAAK8qB,UAAUqC,UAAY3nB,GAEvBynB,GACFjtB,KAAK8qB,UAAU9iB,UAAUiB,IAAIgkB,GAG/B/sB,EAAEF,KAAK8qB,WAAWsC,SAASzsB,SAASmT,MAEpC5T,EAAEF,KAAK0F,UAAUwB,GAAG9B,GAAMolB,cAAe,SAACnmB,GACpC2oB,EAAK/B,qBACP+B,EAAK/B,sBAAuB,EAG1B5mB,EAAMK,SAAWL,EAAMkO,eAI3Bya,EAAKlB,+BAGHmB,GACF7sB,EAAKyB,OAAO7B,KAAK8qB,WAGnB5qB,EAAEF,KAAK8qB,WAAW/b,SAASvJ,KAEtBwZ,EACH,OAGF,IAAKiO,EAEH,YADAjO,IAIF,IAAMqO,EAA6BjtB,EAAKiB,iCAAiCrB,KAAK8qB,WAE9E5qB,EAAEF,KAAK8qB,WACJ3qB,IAAIC,EAAKR,eAAgBof,GACzB5a,qBAAqBipB,QACnB,IAAKrtB,KAAK+qB,UAAY/qB,KAAK8qB,UAAW,CAC3C5qB,EAAEF,KAAK8qB,WAAWxkB,YAAYd,IAE9B,IAAM8nB,EAAiB,WACrBN,EAAKD,kBACD/N,GACFA,KAIJ,GAAI9e,EAAEF,KAAK0F,UAAUa,SAASf,IAAiB,CAC7C,IAAM6nB,EAA6BjtB,EAAKiB,iCAAiCrB,KAAK8qB,WAE9E5qB,EAAEF,KAAK8qB,WACJ3qB,IAAIC,EAAKR,eAAgB0tB,GACzBlpB,qBAAqBipB,QAExBC,SAEOtO,GACTA,OASJqM,cAAA,WACE,IAAMkC,EACJvtB,KAAK0F,SAAS8nB,aAAe7sB,SAAS8C,gBAAgBiV,cAEnD1Y,KAAKgrB,oBAAsBuC,IAC9BvtB,KAAK0F,SAASkM,MAAM6b,YAAiBztB,KAAKkrB,gBAA1C,MAGElrB,KAAKgrB,qBAAuBuC,IAC9BvtB,KAAK0F,SAASkM,MAAM8b,aAAkB1tB,KAAKkrB,gBAA3C,SAIJ2B,kBAAA,WACE7sB,KAAK0F,SAASkM,MAAM6b,YAAc,GAClCztB,KAAK0F,SAASkM,MAAM8b,aAAe,MAGrCvC,gBAAA,WACE,IAAM/S,EAAOzX,SAASmT,KAAK9B,wBAC3BhS,KAAKgrB,mBAAqB5S,EAAKH,KAAOG,EAAKJ,MAAQtP,OAAO2R,WAC1Dra,KAAKkrB,gBAAkBlrB,KAAK2tB,wBAG9BvC,cAAA,WAAgB,IAAAwC,EAAA5tB,KACd,GAAIA,KAAKgrB,mBAAoB,CAG3B,IAAM6C,EAAe,GAAGjlB,MAAM/F,KAAKlC,SAASkI,iBAAiBvB,KACvDwmB,EAAgB,GAAGllB,MAAM/F,KAAKlC,SAASkI,iBAAiBvB,KAG9DpH,EAAE2tB,GAAcjnB,KAAK,SAAC2F,EAAOzL,GAC3B,IAAMitB,EAAgBjtB,EAAQ8Q,MAAM8b,aAC9BM,EAAoB9tB,EAAEY,GAASS,IAAI,iBACzCrB,EAAEY,GACCgG,KAAK,gBAAiBinB,GACtBxsB,IAAI,gBAAoBG,WAAWssB,GAAqBJ,EAAK1C,gBAFhE,QAMFhrB,EAAE4tB,GAAelnB,KAAK,SAAC2F,EAAOzL,GAC5B,IAAMmtB,EAAentB,EAAQ8Q,MAAMuK,YAC7B+R,EAAmBhuB,EAAEY,GAASS,IAAI,gBACxCrB,EAAEY,GACCgG,KAAK,eAAgBmnB,GACrB1sB,IAAI,eAAmBG,WAAWwsB,GAAoBN,EAAK1C,gBAF9D,QAMF,IAAM6C,EAAgBptB,SAASmT,KAAKlC,MAAM8b,aACpCM,EAAoB9tB,EAAES,SAASmT,MAAMvS,IAAI,iBAC/CrB,EAAES,SAASmT,MACRhN,KAAK,gBAAiBinB,GACtBxsB,IAAI,gBAAoBG,WAAWssB,GAAqBhuB,KAAKkrB,gBAFhE,MAKFhrB,EAAES,SAASmT,MAAM/E,SAASvJ,OAG5BsnB,gBAAA,WAEE,IAAMe,EAAe,GAAGjlB,MAAM/F,KAAKlC,SAASkI,iBAAiBvB,KAC7DpH,EAAE2tB,GAAcjnB,KAAK,SAAC2F,EAAOzL,GAC3B,IAAMkZ,EAAU9Z,EAAEY,GAASgG,KAAK,iBAChC5G,EAAEY,GAASoF,WAAW,iBACtBpF,EAAQ8Q,MAAM8b,aAAe1T,GAAoB,KAInD,IAAMmU,EAAW,GAAGvlB,MAAM/F,KAAKlC,SAASkI,iBAAT,GAA6BvB,KAC5DpH,EAAEiuB,GAAUvnB,KAAK,SAAC2F,EAAOzL,GACvB,IAAMstB,EAASluB,EAAEY,GAASgG,KAAK,gBACT,oBAAXsnB,GACTluB,EAAEY,GAASS,IAAI,eAAgB6sB,GAAQloB,WAAW,kBAKtD,IAAM8T,EAAU9Z,EAAES,SAASmT,MAAMhN,KAAK,iBACtC5G,EAAES,SAASmT,MAAM5N,WAAW,iBAC5BvF,SAASmT,KAAKlC,MAAM8b,aAAe1T,GAAoB,MAGzD2T,mBAAA,WACE,IAAMU,EAAY1tB,SAASusB,cAAc,OACzCmB,EAAUlB,UAAY3nB,GACtB7E,SAASmT,KAAKsY,YAAYiC,GAC1B,IAAMC,EAAiBD,EAAUrc,wBAAwB+E,MAAQsX,EAAU5V,YAE3E,OADA9X,SAASmT,KAAKwT,YAAY+G,GACnBC,KAKF3nB,iBAAP,SAAwBpE,EAAQ8L,GAC9B,OAAOrO,KAAK4G,KAAK,WACf,IAAIE,EAAO5G,EAAEF,MAAM8G,KAAK7B,IAClBiG,EAAO0B,EAAA,GACR1D,GADQ,GAERhJ,EAAEF,MAAM8G,OAFA,GAGU,iBAAXvE,GAAuBA,EAASA,EAAS,IAQrD,GALKuE,IACHA,EAAO,IAAI8jB,EAAM5qB,KAAMkL,GACvBhL,EAAEF,MAAM8G,KAAK7B,GAAU6B,IAGH,iBAAXvE,EAAqB,CAC9B,GAA4B,oBAAjBuE,EAAKvE,GACd,MAAM,IAAIyB,UAAJ,oBAAkCzB,EAAlC,KAERuE,EAAKvE,GAAQ8L,QACJnD,EAAQmG,MACjBvK,EAAKuK,KAAKhD,8CA9cd,MA7EuB,wCAiFvB,OAAOnF,YAsdXhJ,EAAES,UAAUuG,GAAG9B,GAAMG,eAAgB+B,GAAsB,SAAUjD,GAAO,IACtEK,EADsE6pB,EAAAvuB,KAEpEe,EAAWX,EAAKS,uBAAuBb,MAEzCe,IACF2D,EAAS/D,SAASQ,cAAcJ,IAGlC,IAAMwB,EAASrC,EAAEwE,GAAQoC,KAAK7B,IAC1B,SADW2H,EAAA,GAER1M,EAAEwE,GAAQoC,OAFF,GAGR5G,EAAEF,MAAM8G,QAGM,MAAjB9G,KAAKmI,SAAoC,SAAjBnI,KAAKmI,SAC/B9D,EAAM4C,iBAGR,IAAMyL,EAAUxS,EAAEwE,GAAQvE,IAAIiF,GAAM6K,KAAM,SAACgZ,GACrCA,EAAUljB,sBAKd2M,EAAQvS,IAAIiF,GAAMgL,OAAQ,WACpBlQ,EAAEquB,GAAM5pB,GAAG,aACb4pB,EAAKnmB,YAKXwiB,GAAMjkB,iBAAiB9D,KAAK3C,EAAEwE,GAASnC,EAAQvC,QASjDE,EAAEgE,GAAGc,IAAQ4lB,GAAMjkB,iBACnBzG,EAAEgE,GAAGc,IAAMmC,YAAcyjB,GACzB1qB,EAAEgE,GAAGc,IAAMoC,WAAa,WAEtB,OADAlH,EAAEgE,GAAGc,IAAQG,GACNylB,GAAMjkB,kBC7lBf,IAAM6nB,GAAW,CACf,aACA,OACA,OACA,WACA,WACA,SACA,MACA,cAKWC,GAAmB,CAE9BC,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAJP,kBAK7BnT,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BJ,KAAM,GACNK,EAAG,GACHmT,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJxmB,EAAG,GACHymB,IAAK,CAAC,MAAO,MAAO,QAAS,QAAS,UACtCC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,IAQAC,GAAmB,8DAOnBC,GAAmB,sIAyBlB,SAASC,GAAaC,EAAYC,EAAWC,GAClD,GAA0B,IAAtBF,EAAWvnB,OACb,OAAOunB,EAGT,GAAIE,GAAoC,mBAAfA,EACvB,OAAOA,EAAWF,GAQpB,IALA,IACMG,GADY,IAAIhoB,OAAOioB,WACKC,gBAAgBL,EAAY,aACxDM,EAAgBnuB,OAAOuY,KAAKuV,GAC5BrC,EAAW,GAAGvlB,MAAM/F,KAAK6tB,EAAgB5c,KAAKjL,iBAAiB,MAZPioB,EAAA,SAcrDhoB,GACP,IAAM+Q,EAAKsU,EAASrlB,GACdioB,EAASlX,EAAGlG,SAASxQ,cAE3B,IAA0D,IAAtD0tB,EAAc/iB,QAAQ+L,EAAGlG,SAASxQ,eAGpC,OAFA0W,EAAGhW,WAAWyjB,YAAYzN,GAE1B,WAGF,IAAMmX,EAAgB,GAAGpoB,MAAM/F,KAAKgX,EAAGoM,YACjCgL,EAAwB,GAAGzP,OAAOgP,EAAU,MAAQ,GAAIA,EAAUO,IAAW,IAEnFC,EAAcrT,QAAQ,SAAC9L,IAlD3B,SAA0BA,EAAMqf,GAC9B,IAAMC,EAAWtf,EAAK8B,SAASxQ,cAE/B,IAAgD,IAA5C+tB,EAAqBpjB,QAAQqjB,GAC/B,OAAoC,IAAhC3C,GAAS1gB,QAAQqjB,IACZlvB,QAAQ4P,EAAKuf,UAAUluB,MAAMktB,KAAqBve,EAAKuf,UAAUluB,MAAMmtB,KASlF,IAHA,IAAMgB,EAASH,EAAqBrgB,OAAO,SAACygB,GAAD,OAAeA,aAAqBluB,SAGtE0F,EAAI,EAAGyoB,EAAIF,EAAOroB,OAAQF,EAAIyoB,EAAGzoB,IACxC,GAAIqoB,EAASjuB,MAAMmuB,EAAOvoB,IACxB,OAAO,EAIX,OAAO,EA+BE0oB,CAAiB3f,EAAMof,IAC1BpX,EAAGkN,gBAAgBlV,EAAK8B,aAfrB7K,EAAI,EAAGC,EAAMolB,EAASnlB,OAAQF,EAAIC,EAAKD,IAAKgoB,EAA5ChoB,GAoBT,OAAO4nB,EAAgB5c,KAAK2d,UCxG9B,IAAMzsB,GAAwB,UAExBC,GAAwB,aACxBC,GAAS,IAAmBD,GAC5BE,GAAwBjF,EAAEgE,GAAGc,IAC7B0sB,GAAwB,aACxBC,GAAwB,IAAIvuB,OAAJ,UAAqBsuB,GAArB,OAAyC,KACjEE,GAAwB,CAAC,WAAY,YAAa,cAElDnoB,GAAc,CAClBooB,UAAoB,UACpBC,SAAoB,SACpBC,MAAoB,4BACpBhwB,QAAoB,SACpBiwB,MAAoB,kBACpB7b,KAAoB,UACpBpV,SAAoB,mBACpB8Z,UAAoB,oBACpB+G,OAAoB,2BACpBqQ,UAAoB,2BACpBC,kBAAoB,iBACpB9J,SAAoB,mBACpB+J,SAAoB,UACpB1B,WAAoB,kBACpBD,UAAoB,SACpBlI,aAAoB,iBAGhBH,GAAgB,CACpBiK,KAAS,OACTC,IAAS,MACTC,MAAS,QACTC,OAAS,SACTC,KAAS,QAGLtpB,GAAU,CACd2oB,WAAoB,EACpBC,SAAoB,uGAGpB/vB,QAAoB,cACpBgwB,MAAoB,GACpBC,MAAoB,EACpB7b,MAAoB,EACpBpV,UAAoB,EACpB8Z,UAAoB,MACpB+G,OAAoB,EACpBqQ,WAAoB,EACpBC,kBAAoB,OACpB9J,SAAoB,eACpB+J,UAAoB,EACpB1B,WAAoB,KACpBD,UAAoB/B,GACpBnG,aAAoB,MAGhBmK,GACG,OADHA,GAEG,MAGHrtB,GAAQ,CACZ+K,KAAI,OAAgBjL,GACpBkL,OAAM,SAAgBlL,GACtB+K,KAAI,OAAgB/K,GACpBgL,MAAK,QAAgBhL,GACrBwtB,SAAQ,WAAgBxtB,GACxB8iB,MAAK,QAAgB9iB,GACrBolB,QAAO,UAAgBplB,GACvBytB,SAAQ,WAAgBztB,GACxB4E,WAAU,aAAgB5E,GAC1B6E,WAAU,aAAgB7E,IAGtBM,GACG,OADHA,GAEG,OAGH8B,GAEY,iBAFZA,GAGY,SAGZsrB,GACK,QADLA,GAEK,QAFLA,GAGK,QAHLA,GAIK,SAULC,cACJ,SAAAA,EAAY/xB,EAASyB,GACnB,GAAsB,oBAAX4kB,GACT,MAAM,IAAInjB,UAAU,mEAItBhE,KAAK8yB,YAAiB,EACtB9yB,KAAK+yB,SAAiB,EACtB/yB,KAAKgzB,YAAiB,GACtBhzB,KAAKizB,eAAiB,GACtBjzB,KAAKwoB,QAAiB,KAGtBxoB,KAAKc,QAAUA,EACfd,KAAKuC,OAAUvC,KAAKmL,WAAW5I,GAC/BvC,KAAKkzB,IAAU,KAEflzB,KAAKmzB,2CAmCPC,OAAA,WACEpzB,KAAK8yB,YAAa,KAGpBO,QAAA,WACErzB,KAAK8yB,YAAa,KAGpBQ,cAAA,WACEtzB,KAAK8yB,YAAc9yB,KAAK8yB,cAG1BprB,OAAA,SAAOrD,GACL,GAAKrE,KAAK8yB,WAIV,GAAIzuB,EAAO,CACT,IAAMkvB,EAAUvzB,KAAKypB,YAAYxkB,SAC7B6kB,EAAU5pB,EAAEmE,EAAMkO,eAAezL,KAAKysB,GAErCzJ,IACHA,EAAU,IAAI9pB,KAAKypB,YACjBplB,EAAMkO,cACNvS,KAAKwzB,sBAEPtzB,EAAEmE,EAAMkO,eAAezL,KAAKysB,EAASzJ,IAGvCA,EAAQmJ,eAAeQ,OAAS3J,EAAQmJ,eAAeQ,MAEnD3J,EAAQ4J,uBACV5J,EAAQ6J,OAAO,KAAM7J,GAErBA,EAAQ8J,OAAO,KAAM9J,OAElB,CACL,GAAI5pB,EAAEF,KAAK6zB,iBAAiBttB,SAASf,IAEnC,YADAxF,KAAK4zB,OAAO,KAAM5zB,MAIpBA,KAAK2zB,OAAO,KAAM3zB,UAItBiG,QAAA,WACEyH,aAAa1N,KAAK+yB,UAElB7yB,EAAEgG,WAAWlG,KAAKc,QAASd,KAAKypB,YAAYxkB,UAE5C/E,EAAEF,KAAKc,SAAS6L,IAAI3M,KAAKypB,YAAYvkB,WACrChF,EAAEF,KAAKc,SAASsF,QAAQ,UAAUuG,IAAI,gBAAiB3M,KAAK8zB,mBAExD9zB,KAAKkzB,KACPhzB,EAAEF,KAAKkzB,KAAKxsB,SAGd1G,KAAK8yB,WAAiB,KACtB9yB,KAAK+yB,SAAiB,KACtB/yB,KAAKgzB,YAAiB,KACtBhzB,KAAKizB,eAAiB,KAClBjzB,KAAKwoB,SACPxoB,KAAKwoB,QAAQe,UAGfvpB,KAAKwoB,QAAU,KACfxoB,KAAKc,QAAU,KACfd,KAAKuC,OAAU,KACfvC,KAAKkzB,IAAU,QAGjB7hB,KAAA,WAAO,IAAAtR,EAAAC,KACL,GAAuC,SAAnCE,EAAEF,KAAKc,SAASS,IAAI,WACtB,MAAM,IAAI+B,MAAM,uCAGlB,IAAM2lB,EAAY/oB,EAAEkF,MAAMpF,KAAKypB,YAAYrkB,MAAM6K,MACjD,GAAIjQ,KAAK+zB,iBAAmB/zB,KAAK8yB,WAAY,CAC3C5yB,EAAEF,KAAKc,SAASiB,QAAQknB,GAExB,IAAM+K,EAAa5zB,EAAKoD,eAAexD,KAAKc,SACtCmzB,EAAa/zB,EAAE+H,SACJ,OAAf+rB,EAAsBA,EAAah0B,KAAKc,QAAQyS,cAAc9P,gBAC9DzD,KAAKc,SAGP,GAAImoB,EAAUljB,uBAAyBkuB,EACrC,OAGF,IAAMf,EAAQlzB,KAAK6zB,gBACbK,EAAQ9zB,EAAKG,OAAOP,KAAKypB,YAAYzkB,MAE3CkuB,EAAI5qB,aAAa,KAAM4rB,GACvBl0B,KAAKc,QAAQwH,aAAa,mBAAoB4rB,GAE9Cl0B,KAAKm0B,aAEDn0B,KAAKuC,OAAOsvB,WACd3xB,EAAEgzB,GAAKnkB,SAASvJ,IAGlB,IAAMqV,EAA8C,mBAA1B7a,KAAKuC,OAAOsY,UAClC7a,KAAKuC,OAAOsY,UAAUhY,KAAK7C,KAAMkzB,EAAKlzB,KAAKc,SAC3Cd,KAAKuC,OAAOsY,UAEVuZ,EAAap0B,KAAKq0B,eAAexZ,GACvC7a,KAAKs0B,mBAAmBF,GAExB,IAAMnC,EAAYjyB,KAAKu0B,gBACvBr0B,EAAEgzB,GAAKpsB,KAAK9G,KAAKypB,YAAYxkB,SAAUjF,MAElCE,EAAE+H,SAASjI,KAAKc,QAAQyS,cAAc9P,gBAAiBzD,KAAKkzB,MAC/DhzB,EAAEgzB,GAAK9F,SAAS6E,GAGlB/xB,EAAEF,KAAKc,SAASiB,QAAQ/B,KAAKypB,YAAYrkB,MAAMstB,UAE/C1yB,KAAKwoB,QAAU,IAAIrB,GAAOnnB,KAAKc,QAASoyB,EAAKlzB,KAAKopB,iBAAiBgL,IAEnEl0B,EAAEgzB,GAAKnkB,SAASvJ,IAMZ,iBAAkB7E,SAAS8C,iBAC7BvD,EAAES,SAASmT,MAAMhF,WAAW5H,GAAG,YAAa,KAAMhH,EAAEmpB,MAGtD,IAAMmL,EAAW,WACXz0B,EAAKwC,OAAOsvB,WACd9xB,EAAK00B,iBAEP,IAAMC,EAAiB30B,EAAKizB,YAC5BjzB,EAAKizB,YAAkB,KAEvB9yB,EAAEH,EAAKe,SAASiB,QAAQhC,EAAK0pB,YAAYrkB,MAAM8K,OAE3CwkB,IAAmBjC,IACrB1yB,EAAK6zB,OAAO,KAAM7zB,IAItB,GAAIG,EAAEF,KAAKkzB,KAAK3sB,SAASf,IAAiB,CACxC,IAAMlE,EAAqBlB,EAAKiB,iCAAiCrB,KAAKkzB,KAEtEhzB,EAAEF,KAAKkzB,KACJ/yB,IAAIC,EAAKR,eAAgB40B,GACzBpwB,qBAAqB9C,QAExBkzB,QAKNpjB,KAAA,SAAK4N,GAGc,SAAXwV,IACAxnB,EAAKgmB,cAAgBP,IAAmBS,EAAIrvB,YAC9CqvB,EAAIrvB,WAAWyjB,YAAY4L,GAG7BlmB,EAAK2nB,iBACL3nB,EAAKlM,QAAQimB,gBAAgB,oBAC7B7mB,EAAE8M,EAAKlM,SAASiB,QAAQiL,EAAKyc,YAAYrkB,MAAMgL,QAC1B,OAAjBpD,EAAKwb,SACPxb,EAAKwb,QAAQe,UAGXvK,GACFA,IAhBS,IAAAhS,EAAAhN,KACPkzB,EAAYlzB,KAAK6zB,gBACjBvK,EAAYppB,EAAEkF,MAAMpF,KAAKypB,YAAYrkB,MAAM+K,MAoBjD,GAFAjQ,EAAEF,KAAKc,SAASiB,QAAQunB,IAEpBA,EAAUvjB,qBAAd,CAgBA,GAZA7F,EAAEgzB,GAAK5sB,YAAYd,IAIf,iBAAkB7E,SAAS8C,iBAC7BvD,EAAES,SAASmT,MAAMhF,WAAWnC,IAAI,YAAa,KAAMzM,EAAEmpB,MAGvDrpB,KAAKizB,eAAeL,KAAiB,EACrC5yB,KAAKizB,eAAeL,KAAiB,EACrC5yB,KAAKizB,eAAeL,KAAiB,EAEjC1yB,EAAEF,KAAKkzB,KAAK3sB,SAASf,IAAiB,CACxC,IAAMlE,EAAqBlB,EAAKiB,iCAAiC6xB,GAEjEhzB,EAAEgzB,GACC/yB,IAAIC,EAAKR,eAAgB40B,GACzBpwB,qBAAqB9C,QAExBkzB,IAGFx0B,KAAKgzB,YAAc,OAGrBvL,OAAA,WACuB,OAAjBznB,KAAKwoB,SACPxoB,KAAKwoB,QAAQlJ,oBAMjByU,cAAA,WACE,OAAO9xB,QAAQjC,KAAK40B,eAGtBN,mBAAA,SAAmBF,GACjBl0B,EAAEF,KAAK6zB,iBAAiB9kB,SAAY2iB,GAApC,IAAoD0C,MAGtDP,cAAA,WAEE,OADA7zB,KAAKkzB,IAAMlzB,KAAKkzB,KAAOhzB,EAAEF,KAAKuC,OAAOuvB,UAAU,GACxC9xB,KAAKkzB,OAGdiB,WAAA,WACE,IAAMjB,EAAMlzB,KAAK6zB,gBACjB7zB,KAAK60B,kBAAkB30B,EAAEgzB,EAAIrqB,iBAAiBvB,KAA0BtH,KAAK40B,YAC7E10B,EAAEgzB,GAAK5sB,YAAed,GAAtB,IAAwCA,OAG1CqvB,kBAAA,SAAkBhuB,EAAUiuB,GACH,iBAAZA,IAAyBA,EAAQ1yB,WAAY0yB,EAAQ3wB,OAa5DnE,KAAKuC,OAAO4T,MACVnW,KAAKuC,OAAO4vB,WACd2C,EAAUxE,GAAawE,EAAS90B,KAAKuC,OAAOiuB,UAAWxwB,KAAKuC,OAAOkuB,aAGrE5pB,EAASsP,KAAK2e,IAEdjuB,EAASkuB,KAAKD,GAlBV90B,KAAKuC,OAAO4T,KACTjW,EAAE40B,GAAS3uB,SAASxB,GAAGkC,IAC1BA,EAASmuB,QAAQC,OAAOH,GAG1BjuB,EAASkuB,KAAK70B,EAAE40B,GAASC,WAiB/BH,SAAA,WACE,IAAI7C,EAAQ/xB,KAAKc,QAAQE,aAAa,uBAQtC,OALE+wB,EADGA,IACkC,mBAAtB/xB,KAAKuC,OAAOwvB,MACvB/xB,KAAKuC,OAAOwvB,MAAMlvB,KAAK7C,KAAKc,SAC5Bd,KAAKuC,OAAOwvB,UAQpB3I,iBAAA,SAAiBgL,GAAY,IAAAjnB,EAAAnN,KAuB3B,OAAA4M,EAAA,GAtBwB,CACtBiO,UAAWuZ,EACX9W,UAAW,CACTsE,OAAQ5hB,KAAK4pB,aACblF,KAAM,CACJK,SAAU/kB,KAAKuC,OAAO2vB,mBAExBlO,MAAO,CACLljB,QAASwG,IAEX8b,gBAAiB,CACfnJ,kBAAmBja,KAAKuC,OAAO6lB,WAGnCtF,SAAU,SAAChc,GACLA,EAAK8d,oBAAsB9d,EAAK+T,WAClC1N,EAAK+nB,6BAA6BpuB,IAGtCic,SAAU,SAACjc,GAAD,OAAUqG,EAAK+nB,6BAA6BpuB,KAGxD,GAEK9G,KAAKuC,OAAO+lB,iBAInBsB,WAAA,WAAa,IAAA1a,EAAAlP,KACL4hB,EAAS,GAef,MAbkC,mBAAvB5hB,KAAKuC,OAAOqf,OACrBA,EAAO1d,GAAK,SAAC4C,GAMX,OALAA,EAAKiR,QAALnL,EAAA,GACK9F,EAAKiR,QADV,GAEK7I,EAAK3M,OAAOqf,OAAO9a,EAAKiR,QAAS7I,EAAKpO,UAAY,IAGhDgG,GAGT8a,EAAOA,OAAS5hB,KAAKuC,OAAOqf,OAGvBA,KAGT2S,cAAA,WACE,OAA8B,IAA1Bv0B,KAAKuC,OAAO0vB,UACPtxB,SAASmT,KAGd1T,EAAK8B,UAAUlC,KAAKuC,OAAO0vB,WACtB/xB,EAAEF,KAAKuC,OAAO0vB,WAGhB/xB,EAAES,UAAUsc,KAAKjd,KAAKuC,OAAO0vB,cAGtCoC,eAAA,SAAexZ,GACb,OAAOsN,GAActN,EAAUtX,kBAGjC4vB,cAAA,WAAgB,IAAA3G,EAAAxsB,KACGA,KAAKuC,OAAOR,QAAQH,MAAM,KAElC+b,QAAQ,SAAC5b,GAChB,GAAgB,UAAZA,EACF7B,EAAEssB,EAAK1rB,SAASoG,GACdslB,EAAK/C,YAAYrkB,MAAM4iB,MACvBwE,EAAKjqB,OAAOxB,SACZ,SAACsD,GAAD,OAAWmoB,EAAK9kB,OAAOrD,UAEpB,GAAItC,IAAY6wB,GAAgB,CACrC,IAAMuC,EAAUpzB,IAAY6wB,GACxBpG,EAAK/C,YAAYrkB,MAAM0E,WACvB0iB,EAAK/C,YAAYrkB,MAAMklB,QACrB8K,EAAWrzB,IAAY6wB,GACzBpG,EAAK/C,YAAYrkB,MAAM2E,WACvByiB,EAAK/C,YAAYrkB,MAAMutB,SAE3BzyB,EAAEssB,EAAK1rB,SACJoG,GACCiuB,EACA3I,EAAKjqB,OAAOxB,SACZ,SAACsD,GAAD,OAAWmoB,EAAKmH,OAAOtvB,KAExB6C,GACCkuB,EACA5I,EAAKjqB,OAAOxB,SACZ,SAACsD,GAAD,OAAWmoB,EAAKoH,OAAOvvB,QAK/BrE,KAAK8zB,kBAAoB,WACnBtH,EAAK1rB,SACP0rB,EAAKpb,QAITlR,EAAEF,KAAKc,SAASsF,QAAQ,UAAUc,GAChC,gBACAlH,KAAK8zB,mBAGH9zB,KAAKuC,OAAOxB,SACdf,KAAKuC,OAALqK,EAAA,GACK5M,KAAKuC,OADV,CAEER,QAAS,SACThB,SAAU,KAGZf,KAAKq1B,eAITA,UAAA,WACE,IAAMC,SAAmBt1B,KAAKc,QAAQE,aAAa,wBAE/ChB,KAAKc,QAAQE,aAAa,UAA0B,UAAds0B,IACxCt1B,KAAKc,QAAQwH,aACX,sBACAtI,KAAKc,QAAQE,aAAa,UAAY,IAGxChB,KAAKc,QAAQwH,aAAa,QAAS,QAIvCqrB,OAAA,SAAOtvB,EAAOylB,GACZ,IAAMyJ,EAAUvzB,KAAKypB,YAAYxkB,UACjC6kB,EAAUA,GAAW5pB,EAAEmE,EAAMkO,eAAezL,KAAKysB,MAG/CzJ,EAAU,IAAI9pB,KAAKypB,YACjBplB,EAAMkO,cACNvS,KAAKwzB,sBAEPtzB,EAAEmE,EAAMkO,eAAezL,KAAKysB,EAASzJ,IAGnCzlB,IACFylB,EAAQmJ,eACS,YAAf5uB,EAAMyD,KAAqB8qB,GAAgBA,KACzC,GAGF1yB,EAAE4pB,EAAQ+J,iBAAiBttB,SAASf,KAAmBskB,EAAQkJ,cAAgBP,GACjF3I,EAAQkJ,YAAcP,IAIxB/kB,aAAaoc,EAAQiJ,UAErBjJ,EAAQkJ,YAAcP,GAEjB3I,EAAQvnB,OAAOyvB,OAAUlI,EAAQvnB,OAAOyvB,MAAM3gB,KAKnDyY,EAAQiJ,SAAW1yB,WAAW,WACxBypB,EAAQkJ,cAAgBP,IAC1B3I,EAAQzY,QAETyY,EAAQvnB,OAAOyvB,MAAM3gB,MARtByY,EAAQzY,WAWZuiB,OAAA,SAAOvvB,EAAOylB,GACZ,IAAMyJ,EAAUvzB,KAAKypB,YAAYxkB,UACjC6kB,EAAUA,GAAW5pB,EAAEmE,EAAMkO,eAAezL,KAAKysB,MAG/CzJ,EAAU,IAAI9pB,KAAKypB,YACjBplB,EAAMkO,cACNvS,KAAKwzB,sBAEPtzB,EAAEmE,EAAMkO,eAAezL,KAAKysB,EAASzJ,IAGnCzlB,IACFylB,EAAQmJ,eACS,aAAf5uB,EAAMyD,KAAsB8qB,GAAgBA,KAC1C,GAGF9I,EAAQ4J,yBAIZhmB,aAAaoc,EAAQiJ,UAErBjJ,EAAQkJ,YAAcP,GAEjB3I,EAAQvnB,OAAOyvB,OAAUlI,EAAQvnB,OAAOyvB,MAAM5gB,KAKnD0Y,EAAQiJ,SAAW1yB,WAAW,WACxBypB,EAAQkJ,cAAgBP,IAC1B3I,EAAQ1Y,QAET0Y,EAAQvnB,OAAOyvB,MAAM5gB,MARtB0Y,EAAQ1Y,WAWZsiB,qBAAA,WACE,IAAK,IAAM3xB,KAAW/B,KAAKizB,eACzB,GAAIjzB,KAAKizB,eAAelxB,GACtB,OAAO,EAIX,OAAO,KAGToJ,WAAA,SAAW5I,GACT,IAAMgzB,EAAiBr1B,EAAEF,KAAKc,SAASgG,OAwCvC,OAtCApE,OAAOuY,KAAKsa,GACT5X,QAAQ,SAAC6X,IACyC,IAA7C5D,GAAsB9jB,QAAQ0nB,WACzBD,EAAeC,KAUA,iBAN5BjzB,EAAMqK,EAAA,GACD5M,KAAKypB,YAAYvgB,QADhB,GAEDqsB,EAFC,GAGiB,iBAAXhzB,GAAuBA,EAASA,EAAS,KAGnCyvB,QAChBzvB,EAAOyvB,MAAQ,CACb3gB,KAAM9O,EAAOyvB,MACb5gB,KAAM7O,EAAOyvB,QAIW,iBAAjBzvB,EAAOwvB,QAChBxvB,EAAOwvB,MAAQxvB,EAAOwvB,MAAM9uB,YAGA,iBAAnBV,EAAOuyB,UAChBvyB,EAAOuyB,QAAUvyB,EAAOuyB,QAAQ7xB,YAGlC7C,EAAKiC,gBACH2C,GACAzC,EACAvC,KAAKypB,YAAYhgB,aAGflH,EAAO4vB,WACT5vB,EAAOuvB,SAAWxB,GAAa/tB,EAAOuvB,SAAUvvB,EAAOiuB,UAAWjuB,EAAOkuB,aAGpEluB,KAGTixB,mBAAA,WACE,IAAMjxB,EAAS,GAEf,GAAIvC,KAAKuC,OACP,IAAK,IAAMmV,KAAO1X,KAAKuC,OACjBvC,KAAKypB,YAAYvgB,QAAQwO,KAAS1X,KAAKuC,OAAOmV,KAChDnV,EAAOmV,GAAO1X,KAAKuC,OAAOmV,IAKhC,OAAOnV,KAGToyB,eAAA,WACE,IAAMc,EAAOv1B,EAAEF,KAAK6zB,iBACd6B,EAAWD,EAAK5jB,KAAK,SAAS3O,MAAMyuB,IACzB,OAAb+D,GAAqBA,EAAS1sB,QAChCysB,EAAKnvB,YAAYovB,EAASC,KAAK,QAInCT,6BAAA,SAA6BU,GAC3B,IAAMC,EAAiBD,EAAWvS,SAClCrjB,KAAKkzB,IAAM2C,EAAe9b,OAC1B/Z,KAAK20B,iBACL30B,KAAKs0B,mBAAmBt0B,KAAKq0B,eAAeuB,EAAW/a,eAGzD4Z,eAAA,WACE,IAAMvB,EAAMlzB,KAAK6zB,gBACXiC,EAAsB91B,KAAKuC,OAAOsvB,UAEA,OAApCqB,EAAIlyB,aAAa,iBAIrBd,EAAEgzB,GAAK5sB,YAAYd,IACnBxF,KAAKuC,OAAOsvB,WAAY,EACxB7xB,KAAKoR,OACLpR,KAAKqR,OACLrR,KAAKuC,OAAOsvB,UAAYiE,MAKnBnvB,iBAAP,SAAwBpE,GACtB,OAAOvC,KAAK4G,KAAK,WACf,IAAIE,EAAO5G,EAAEF,MAAM8G,KAAK7B,IAClBiG,EAA4B,iBAAX3I,GAAuBA,EAE9C,IAAKuE,IAAQ,eAAezD,KAAKd,MAI5BuE,IACHA,EAAO,IAAI+rB,EAAQ7yB,KAAMkL,GACzBhL,EAAEF,MAAM8G,KAAK7B,GAAU6B,IAGH,iBAAXvE,GAAqB,CAC9B,GAA4B,oBAAjBuE,EAAKvE,GACd,MAAM,IAAIyB,UAAJ,oBAAkCzB,EAAlC,KAERuE,EAAKvE,iDArnBT,MA3H0B,wCA+H1B,OAAO2G,gCAIP,OAAOlE,oCAIP,OAAOC,iCAIP,OAAOG,qCAIP,OAAOF,uCAIP,OAAOuE,YAymBXvJ,EAAEgE,GAAGc,IAAQ6tB,GAAQlsB,iBACrBzG,EAAEgE,GAAGc,IAAMmC,YAAc0rB,GACzB3yB,EAAEgE,GAAGc,IAAMoC,WAAa,WAEtB,OADAlH,EAAEgE,GAAGc,IAAQG,GACN0tB,GAAQlsB,kBCtwBjB,IAAM3B,GAAsB,UAEtBC,GAAsB,aACtBC,GAAS,IAAiBD,GAC1BE,GAAsBjF,EAAEgE,GAAGc,IAC3B0sB,GAAsB,aACtBC,GAAsB,IAAIvuB,OAAJ,UAAqBsuB,GAArB,OAAyC,KAE/DxoB,GAAO0D,EAAA,GACRimB,GAAQ3pB,QADA,CAEX2R,UAAY,QACZ9Y,QAAY,QACZ+yB,QAAY,GACZhD,SAAY,wIAMRroB,GAAWmD,EAAA,GACZimB,GAAQppB,YADI,CAEfqrB,QAAU,8BAGNtvB,GACG,OADHA,GAEG,OAGH8B,GACM,kBADNA,GAEM,gBAGNlC,GAAQ,CACZ+K,KAAI,OAAgBjL,GACpBkL,OAAM,SAAgBlL,GACtB+K,KAAI,OAAgB/K,GACpBgL,MAAK,QAAgBhL,GACrBwtB,SAAQ,WAAgBxtB,GACxB8iB,MAAK,QAAgB9iB,GACrBolB,QAAO,UAAgBplB,GACvBytB,SAAQ,WAAgBztB,GACxB4E,WAAU,aAAgB5E,GAC1B6E,WAAU,aAAgB7E,IAStB6wB,gMAiCJhC,cAAA,WACE,OAAO/zB,KAAK40B,YAAc50B,KAAKg2B,iBAGjC1B,mBAAA,SAAmBF,GACjBl0B,EAAEF,KAAK6zB,iBAAiB9kB,SAAY2iB,GAApC,IAAoD0C,MAGtDP,cAAA,WAEE,OADA7zB,KAAKkzB,IAAMlzB,KAAKkzB,KAAOhzB,EAAEF,KAAKuC,OAAOuvB,UAAU,GACxC9xB,KAAKkzB,OAGdiB,WAAA,WACE,IAAMsB,EAAOv1B,EAAEF,KAAK6zB,iBAGpB7zB,KAAK60B,kBAAkBY,EAAKxY,KAAK3V,IAAiBtH,KAAK40B,YACvD,IAAIE,EAAU90B,KAAKg2B,cACI,mBAAZlB,IACTA,EAAUA,EAAQjyB,KAAK7C,KAAKc,UAE9Bd,KAAK60B,kBAAkBY,EAAKxY,KAAK3V,IAAmBwtB,GAEpDW,EAAKnvB,YAAed,GAApB,IAAsCA,OAKxCwwB,YAAA,WACE,OAAOh2B,KAAKc,QAAQE,aAAa,iBAC/BhB,KAAKuC,OAAOuyB,WAGhBH,eAAA,WACE,IAAMc,EAAOv1B,EAAEF,KAAK6zB,iBACd6B,EAAWD,EAAK5jB,KAAK,SAAS3O,MAAMyuB,IACzB,OAAb+D,GAAuC,EAAlBA,EAAS1sB,QAChCysB,EAAKnvB,YAAYovB,EAASC,KAAK,QAM5BhvB,iBAAP,SAAwBpE,GACtB,OAAOvC,KAAK4G,KAAK,WACf,IAAIE,EAAO5G,EAAEF,MAAM8G,KAAK7B,IAClBiG,EAA4B,iBAAX3I,EAAsBA,EAAS,KAEtD,IAAKuE,IAAQ,eAAezD,KAAKd,MAI5BuE,IACHA,EAAO,IAAIivB,EAAQ/1B,KAAMkL,GACzBhL,EAAEF,MAAM8G,KAAK7B,GAAU6B,IAGH,iBAAXvE,GAAqB,CAC9B,GAA4B,oBAAjBuE,EAAKvE,GACd,MAAM,IAAIyB,UAAJ,oBAAkCzB,EAAlC,KAERuE,EAAKvE,iDA3FT,MAxDwB,wCA4DxB,OAAO2G,gCAIP,OAAOlE,oCAIP,OAAOC,iCAIP,OAAOG,qCAIP,OAAOF,uCAIP,OAAOuE,UA5BWopB,IA2GtB3yB,EAAEgE,GAAGc,IAAQ+wB,GAAQpvB,iBACrBzG,EAAEgE,GAAGc,IAAMmC,YAAc4uB,GACzB71B,EAAEgE,GAAGc,IAAMoC,WAAa,WAEtB,OADAlH,EAAEgE,GAAGc,IAAQG,GACN4wB,GAAQpvB,kBCpKjB,IAAM3B,GAAqB,YAErBC,GAAqB,eACrBC,GAAS,IAAgBD,GAEzBE,GAAqBjF,EAAEgE,GAAGc,IAE1BkE,GAAU,CACd0Y,OAAS,GACTqU,OAAS,OACTvxB,OAAS,IAGL+E,GAAc,CAClBmY,OAAS,SACTqU,OAAS,SACTvxB,OAAS,oBAGLU,GAAQ,CACZ8wB,SAAQ,WAAmBhxB,GAC3BixB,OAAM,SAAmBjxB,GACzBsC,cAAa,OAAUtC,GAlBE,aAqBrBM,GACY,gBADZA,GAGY,SAGZ8B,GACc,sBADdA,GAGc,oBAHdA,GAIc,YAJdA,GAKc,YALdA,GAMc,mBANdA,GAOc,YAPdA,GAQc,iBARdA,GASc,mBAGd8uB,GACO,SADPA,GAEO,WASPC,cACJ,SAAAA,EAAYv1B,EAASyB,GAAQ,IAAAxC,EAAAC,KAC3BA,KAAK0F,SAAiB5E,EACtBd,KAAKs2B,eAAqC,SAApBx1B,EAAQqH,QAAqBO,OAAS5H,EAC5Dd,KAAKkL,QAAiBlL,KAAKmL,WAAW5I,GACtCvC,KAAK+Q,UAAoB/Q,KAAKkL,QAAQxG,OAAhB,IAA0B4C,GAA1B,IACGtH,KAAKkL,QAAQxG,OADhB,IAC0B4C,GAD1B,IAEGtH,KAAKkL,QAAQxG,OAFhB,IAE0B4C,GAChDtH,KAAKu2B,SAAiB,GACtBv2B,KAAKw2B,SAAiB,GACtBx2B,KAAKy2B,cAAiB,KACtBz2B,KAAK02B,cAAiB,EAEtBx2B,EAAEF,KAAKs2B,gBAAgBpvB,GAAG9B,GAAM+wB,OAAQ,SAAC9xB,GAAD,OAAWtE,EAAK42B,SAAStyB,KAEjErE,KAAK42B,UACL52B,KAAK22B,sCAePC,QAAA,WAAU,IAAA5pB,EAAAhN,KACF62B,EAAa72B,KAAKs2B,iBAAmBt2B,KAAKs2B,eAAe5tB,OAC3D0tB,GAAsBA,GAEpBU,EAAuC,SAAxB92B,KAAKkL,QAAQ+qB,OAC9BY,EAAa72B,KAAKkL,QAAQ+qB,OAExBc,EAAaD,IAAiBV,GAChCp2B,KAAKg3B,gBAAkB,EAE3Bh3B,KAAKu2B,SAAW,GAChBv2B,KAAKw2B,SAAW,GAEhBx2B,KAAK02B,cAAgB12B,KAAKi3B,mBAEV,GAAGruB,MAAM/F,KAAKlC,SAASkI,iBAAiB7I,KAAK+Q,YAG1DmK,IAAI,SAACpa,GACJ,IAAI4D,EACEwyB,EAAiB92B,EAAKS,uBAAuBC,GAMnD,GAJIo2B,IACFxyB,EAAS/D,SAASQ,cAAc+1B,IAG9BxyB,EAAQ,CACV,IAAMyyB,EAAYzyB,EAAOsN,wBACzB,GAAImlB,EAAUpgB,OAASogB,EAAUrgB,OAE/B,MAAO,CACL5W,EAAEwE,GAAQoyB,KAAgB3e,IAAM4e,EAChCG,GAIN,OAAO,OAERrmB,OAAO,SAACsZ,GAAD,OAAUA,IACjB7O,KAAK,SAACC,EAAGC,GAAJ,OAAUD,EAAE,GAAKC,EAAE,KACxBmC,QAAQ,SAACwM,GACRnd,EAAKupB,SAASvlB,KAAKmZ,EAAK,IACxBnd,EAAKwpB,SAASxlB,KAAKmZ,EAAK,SAI9BlkB,QAAA,WACE/F,EAAEgG,WAAWlG,KAAK0F,SAAUT,IAC5B/E,EAAEF,KAAKs2B,gBAAgB3pB,IAAIzH,IAE3BlF,KAAK0F,SAAiB,KACtB1F,KAAKs2B,eAAiB,KACtBt2B,KAAKkL,QAAiB,KACtBlL,KAAK+Q,UAAiB,KACtB/Q,KAAKu2B,SAAiB,KACtBv2B,KAAKw2B,SAAiB,KACtBx2B,KAAKy2B,cAAiB,KACtBz2B,KAAK02B,cAAiB,QAKxBvrB,WAAA,SAAW5I,GAMT,GAA6B,iBAL7BA,EAAMqK,EAAA,GACD1D,GADC,GAEiB,iBAAX3G,GAAuBA,EAASA,EAAS,KAGnCmC,OAAqB,CACrC,IAAI+L,EAAKvQ,EAAEqC,EAAOmC,QAAQmN,KAAK,MAC1BpB,IACHA,EAAKrQ,EAAKG,OAAOyE,IACjB9E,EAAEqC,EAAOmC,QAAQmN,KAAK,KAAMpB,IAE9BlO,EAAOmC,OAAP,IAAoB+L,EAKtB,OAFArQ,EAAKiC,gBAAgB2C,GAAMzC,EAAQkH,IAE5BlH,KAGTy0B,cAAA,WACE,OAAOh3B,KAAKs2B,iBAAmB5tB,OAC3B1I,KAAKs2B,eAAec,YAAcp3B,KAAKs2B,eAAeje,aAG5D4e,iBAAA,WACE,OAAOj3B,KAAKs2B,eAAe9I,cAAgB/sB,KAAKmW,IAC9CjW,SAASmT,KAAK0Z,aACd7sB,SAAS8C,gBAAgB+pB,iBAI7B6J,iBAAA,WACE,OAAOr3B,KAAKs2B,iBAAmB5tB,OAC3BA,OAAO4R,YAActa,KAAKs2B,eAAetkB,wBAAwB8E,UAGvE6f,SAAA,WACE,IAAMte,EAAerY,KAAKg3B,gBAAkBh3B,KAAKkL,QAAQ0W,OACnD4L,EAAextB,KAAKi3B,mBACpBK,EAAet3B,KAAKkL,QAAQ0W,OAChC4L,EACAxtB,KAAKq3B,mBAMP,GAJIr3B,KAAK02B,gBAAkBlJ,GACzBxtB,KAAK42B,UAGUU,GAAbjf,EAAJ,CACE,IAAM3T,EAAS1E,KAAKw2B,SAASx2B,KAAKw2B,SAASxtB,OAAS,GAEhDhJ,KAAKy2B,gBAAkB/xB,GACzB1E,KAAKu3B,UAAU7yB,OAJnB,CASA,GAAI1E,KAAKy2B,eAAiBpe,EAAYrY,KAAKu2B,SAAS,IAAyB,EAAnBv2B,KAAKu2B,SAAS,GAGtE,OAFAv2B,KAAKy2B,cAAgB,UACrBz2B,KAAKw3B,SAKP,IADA,IACS1uB,EADY9I,KAAKu2B,SAASvtB,OACRF,KAAM,CACR9I,KAAKy2B,gBAAkBz2B,KAAKw2B,SAAS1tB,IACxDuP,GAAarY,KAAKu2B,SAASztB,KACM,oBAAzB9I,KAAKu2B,SAASztB,EAAI,IACtBuP,EAAYrY,KAAKu2B,SAASztB,EAAI,KAGpC9I,KAAKu3B,UAAUv3B,KAAKw2B,SAAS1tB,SAKnCyuB,UAAA,SAAU7yB,GACR1E,KAAKy2B,cAAgB/xB,EAErB1E,KAAKw3B,SAEL,IAAMC,EAAUz3B,KAAK+Q,UAClBnP,MAAM,KACNsZ,IAAI,SAACna,GAAD,OAAiBA,EAAjB,iBAA0C2D,EAA1C,MAAsD3D,EAAtD,UAAwE2D,EAAxE,OAEDgzB,EAAQx3B,EAAE,GAAG0I,MAAM/F,KAAKlC,SAASkI,iBAAiB4uB,EAAQ9B,KAAK,QAEjE+B,EAAMnxB,SAASf,KACjBkyB,EAAMtxB,QAAQkB,IAAmB2V,KAAK3V,IAA0ByH,SAASvJ,IACzEkyB,EAAM3oB,SAASvJ,MAGfkyB,EAAM3oB,SAASvJ,IAGfkyB,EAAMC,QAAQrwB,IAAyB0E,KAAQ1E,GAA/C,KAAsEA,IAAuByH,SAASvJ,IAEtGkyB,EAAMC,QAAQrwB,IAAyB0E,KAAK1E,IAAoBwH,SAASxH,IAAoByH,SAASvJ,KAGxGtF,EAAEF,KAAKs2B,gBAAgBv0B,QAAQqD,GAAM8wB,SAAU,CAC7C7nB,cAAe3J,OAInB8yB,OAAA,WACE,GAAG5uB,MAAM/F,KAAKlC,SAASkI,iBAAiB7I,KAAK+Q,YAC1CF,OAAO,SAACmE,GAAD,OAAUA,EAAKhN,UAAUC,SAASzC,MACzCmY,QAAQ,SAAC3I,GAAD,OAAUA,EAAKhN,UAAUtB,OAAOlB,SAKtCmB,iBAAP,SAAwBpE,GACtB,OAAOvC,KAAK4G,KAAK,WACf,IAAIE,EAAO5G,EAAEF,MAAM8G,KAAK7B,IAQxB,GALK6B,IACHA,EAAO,IAAIuvB,EAAUr2B,KAHW,iBAAXuC,GAAuBA,GAI5CrC,EAAEF,MAAM8G,KAAK7B,GAAU6B,IAGH,iBAAXvE,EAAqB,CAC9B,GAA4B,oBAAjBuE,EAAKvE,GACd,MAAM,IAAIyB,UAAJ,oBAAkCzB,EAAlC,KAERuE,EAAKvE,iDAtMT,MA3EuB,wCA+EvB,OAAO2G,YA8MXhJ,EAAEwI,QAAQxB,GAAG9B,GAAMoC,cAAe,WAIhC,IAHA,IAAMowB,EAAa,GAAGhvB,MAAM/F,KAAKlC,SAASkI,iBAAiBvB,KAGlDwB,EAFgB8uB,EAAW5uB,OAELF,KAAM,CACnC,IAAM+uB,EAAO33B,EAAE03B,EAAW9uB,IAC1ButB,GAAU1vB,iBAAiB9D,KAAKg1B,EAAMA,EAAK/wB,WAU/C5G,EAAEgE,GAAGc,IAAQqxB,GAAU1vB,iBACvBzG,EAAEgE,GAAGc,IAAMmC,YAAckvB,GACzBn2B,EAAEgE,GAAGc,IAAMoC,WAAa,WAEtB,OADAlH,EAAEgE,GAAGc,IAAQG,GACNkxB,GAAU1vB,kBClTnB,IAEM1B,GAAqB,SACrBC,GAAS,IAAgBD,GAEzBE,GAAqBjF,EAAEgE,GAAF,IAErBkB,GAAQ,CACZ+K,KAAI,OAAoBjL,GACxBkL,OAAM,SAAoBlL,GAC1B+K,KAAI,OAAoB/K,GACxBgL,MAAK,QAAoBhL,GACzBK,eAAc,QAAWL,GARA,aAWrBM,GACY,gBADZA,GAEY,SAFZA,GAGY,WAHZA,GAIY,OAJZA,GAKY,OAGZ8B,GACoB,YADpBA,GAEoB,oBAFpBA,GAGoB,UAHpBA,GAIoB,iBAJpBA,GAKoB,kEALpBA,GAMoB,mBANpBA,GAOoB,2BASpBwwB,cACJ,SAAAA,EAAYh3B,GACVd,KAAK0F,SAAW5E,6BAWlBuQ,KAAA,WAAO,IAAAtR,EAAAC,KACL,KAAIA,KAAK0F,SAAS7B,YACd7D,KAAK0F,SAAS7B,WAAWzB,WAAakT,KAAK6W,cAC3CjsB,EAAEF,KAAK0F,UAAUa,SAASf,KAC1BtF,EAAEF,KAAK0F,UAAUa,SAASf,KAH9B,CAOA,IAAId,EACAqzB,EACEC,EAAc93B,EAAEF,KAAK0F,UAAUU,QAAQkB,IAAyB,GAChEvG,EAAWX,EAAKS,uBAAuBb,KAAK0F,UAElD,GAAIsyB,EAAa,CACf,IAAMC,EAAwC,OAAzBD,EAAYrkB,UAA8C,OAAzBqkB,EAAYrkB,SAAoBrM,GAAqBA,GAE3GywB,GADAA,EAAW73B,EAAEg4B,UAAUh4B,EAAE83B,GAAa/a,KAAKgb,KACvBF,EAAS/uB,OAAS,GAGxC,IAAMsgB,EAAYppB,EAAEkF,MAAMA,GAAM+K,KAAM,CACpC9B,cAAerO,KAAK0F,WAGhBujB,EAAY/oB,EAAEkF,MAAMA,GAAM6K,KAAM,CACpC5B,cAAe0pB,IASjB,GANIA,GACF73B,EAAE63B,GAAUh2B,QAAQunB,GAGtBppB,EAAEF,KAAK0F,UAAU3D,QAAQknB,IAErBA,EAAUljB,uBACVujB,EAAUvjB,qBADd,CAKIhF,IACF2D,EAAS/D,SAASQ,cAAcJ,IAGlCf,KAAKu3B,UACHv3B,KAAK0F,SACLsyB,GAGF,IAAMxD,EAAW,WACf,IAAM2D,EAAcj4B,EAAEkF,MAAMA,GAAMgL,OAAQ,CACxC/B,cAAetO,EAAK2F,WAGhB6mB,EAAarsB,EAAEkF,MAAMA,GAAM8K,MAAO,CACtC7B,cAAe0pB,IAGjB73B,EAAE63B,GAAUh2B,QAAQo2B,GACpBj4B,EAAEH,EAAK2F,UAAU3D,QAAQwqB,IAGvB7nB,EACF1E,KAAKu3B,UAAU7yB,EAAQA,EAAOb,WAAY2wB,GAE1CA,SAIJvuB,QAAA,WACE/F,EAAEgG,WAAWlG,KAAK0F,SAAUT,IAC5BjF,KAAK0F,SAAW,QAKlB6xB,UAAA,SAAUz2B,EAASmxB,EAAWjT,GAOX,SAAXwV,IAAW,OAAMxnB,EAAKorB,oBAC1Bt3B,EACAu3B,EACArZ,GAVoC,IAAAhS,EAAAhN,KAKhCq4B,IAJiBpG,GAAqC,OAAvBA,EAAUte,UAA4C,OAAvBse,EAAUte,SAE1EzT,EAAE+xB,GAAWnjB,SAASxH,IADtBpH,EAAE+xB,GAAWhV,KAAK3V,KAGQ,GACxB4K,EAAkB8M,GAAaqZ,GAAUn4B,EAAEm4B,GAAQ9xB,SAASf,IAOlE,GAAI6yB,GAAUnmB,EAAiB,CAC7B,IAAM5Q,EAAqBlB,EAAKiB,iCAAiCg3B,GAEjEn4B,EAAEm4B,GACC/xB,YAAYd,IACZrF,IAAIC,EAAKR,eAAgB40B,GACzBpwB,qBAAqB9C,QAExBkzB,OAIJ4D,oBAAA,SAAoBt3B,EAASu3B,EAAQrZ,GACnC,GAAIqZ,EAAQ,CACVn4B,EAAEm4B,GAAQ/xB,YAAYd,IAEtB,IAAM8yB,EAAgBp4B,EAAEm4B,EAAOx0B,YAAYoZ,KACzC3V,IACA,GAEEgxB,GACFp4B,EAAEo4B,GAAehyB,YAAYd,IAGK,QAAhC6yB,EAAOr3B,aAAa,SACtBq3B,EAAO/vB,aAAa,iBAAiB,GAezC,GAXApI,EAAEY,GAASiO,SAASvJ,IACiB,QAAjC1E,EAAQE,aAAa,SACvBF,EAAQwH,aAAa,iBAAiB,GAGxClI,EAAKyB,OAAOf,GAERA,EAAQkH,UAAUC,SAASzC,KAC7B1E,EAAQkH,UAAUiB,IAAIzD,IAGpB1E,EAAQ+C,YAAc3D,EAAEY,EAAQ+C,YAAY0C,SAASf,IAA0B,CACjF,IAAM+yB,EAAkBr4B,EAAEY,GAASsF,QAAQkB,IAAmB,GAE9D,GAAIixB,EAAiB,CACnB,IAAMC,EAAqB,GAAG5vB,MAAM/F,KAAK01B,EAAgB1vB,iBAAiBvB,KAE1EpH,EAAEs4B,GAAoBzpB,SAASvJ,IAGjC1E,EAAQwH,aAAa,iBAAiB,GAGpC0W,GACFA,OAMGrY,iBAAP,SAAwBpE,GACtB,OAAOvC,KAAK4G,KAAK,WACf,IAAM0L,EAAQpS,EAAEF,MACZ8G,EAAOwL,EAAMxL,KAAK7B,IAOtB,GALK6B,IACHA,EAAO,IAAIgxB,EAAI93B,MACfsS,EAAMxL,KAAK7B,GAAU6B,IAGD,iBAAXvE,EAAqB,CAC9B,GAA4B,oBAAjBuE,EAAKvE,GACd,MAAM,IAAIyB,UAAJ,oBAAkCzB,EAAlC,KAERuE,EAAKvE,iDArKT,MA9CuB,iBA+N3BrC,EAAES,UACCuG,GAAG9B,GAAMG,eAAgB+B,GAAsB,SAAUjD,GACxDA,EAAM4C,iBACN6wB,GAAInxB,iBAAiB9D,KAAK3C,EAAEF,MAAO,UASvCE,EAAEgE,GAAF,IAAa4zB,GAAInxB,iBACjBzG,EAAEgE,GAAF,IAAWiD,YAAc2wB,GACzB53B,EAAEgE,GAAF,IAAWkD,WAAa,WAEtB,OADAlH,EAAEgE,GAAF,IAAaiB,GACN2yB,GAAInxB,kBChPb,IAAM3B,GAAqB,QAErBC,GAAqB,WACrBC,GAAS,IAAgBD,GACzBE,GAAqBjF,EAAEgE,GAAGc,IAE1BI,GAAQ,CACZolB,cAAa,gBAAmBtlB,GAChCiL,KAAI,OAAmBjL,GACvBkL,OAAM,SAAmBlL,GACzB+K,KAAI,OAAmB/K,GACvBgL,MAAK,QAAmBhL,IAGpBM,GACM,OADNA,GAEM,OAFNA,GAGM,OAHNA,GAIM,UAGNiE,GAAc,CAClBooB,UAAY,UACZ4G,SAAY,UACZzG,MAAY,UAGR9oB,GAAU,CACd2oB,WAAY,EACZ4G,UAAY,EACZzG,MAAY,KAGR1qB,GACW,yBASXoxB,cACJ,SAAAA,EAAY53B,EAASyB,GACnBvC,KAAK0F,SAAW5E,EAChBd,KAAKkL,QAAWlL,KAAKmL,WAAW5I,GAChCvC,KAAK+yB,SAAW,KAChB/yB,KAAKmzB,2CAmBP9hB,KAAA,WAAO,IAAAtR,EAAAC,KACCipB,EAAY/oB,EAAEkF,MAAMA,GAAM6K,MAGhC,GADA/P,EAAEF,KAAK0F,UAAU3D,QAAQknB,IACrBA,EAAUljB,qBAAd,CAII/F,KAAKkL,QAAQ2mB,WACf7xB,KAAK0F,SAASsC,UAAUiB,IAAIzD,IAG9B,IAAMgvB,EAAW,WACfz0B,EAAK2F,SAASsC,UAAUtB,OAAOlB,IAC/BzF,EAAK2F,SAASsC,UAAUiB,IAAIzD,IAE5BtF,EAAEH,EAAK2F,UAAU3D,QAAQqD,GAAM8K,OAE3BnQ,EAAKmL,QAAQutB,WACf14B,EAAKgzB,SAAW1yB,WAAW,WACzBN,EAAKqR,QACJrR,EAAKmL,QAAQ8mB,SAOpB,GAHAhyB,KAAK0F,SAASsC,UAAUtB,OAAOlB,IAC/BpF,EAAKyB,OAAO7B,KAAK0F,UACjB1F,KAAK0F,SAASsC,UAAUiB,IAAIzD,IACxBxF,KAAKkL,QAAQ2mB,UAAW,CAC1B,IAAMvwB,EAAqBlB,EAAKiB,iCAAiCrB,KAAK0F,UAEtExF,EAAEF,KAAK0F,UACJvF,IAAIC,EAAKR,eAAgB40B,GACzBpwB,qBAAqB9C,QAExBkzB,QAIJpjB,KAAA,WACE,GAAKpR,KAAK0F,SAASsC,UAAUC,SAASzC,IAAtC,CAIA,IAAM8jB,EAAYppB,EAAEkF,MAAMA,GAAM+K,MAEhCjQ,EAAEF,KAAK0F,UAAU3D,QAAQunB,GACrBA,EAAUvjB,sBAId/F,KAAK24B,aAGP1yB,QAAA,WACEyH,aAAa1N,KAAK+yB,UAClB/yB,KAAK+yB,SAAW,KAEZ/yB,KAAK0F,SAASsC,UAAUC,SAASzC,KACnCxF,KAAK0F,SAASsC,UAAUtB,OAAOlB,IAGjCtF,EAAEF,KAAK0F,UAAUiH,IAAIvH,GAAMolB,eAE3BtqB,EAAEgG,WAAWlG,KAAK0F,SAAUT,IAC5BjF,KAAK0F,SAAW,KAChB1F,KAAKkL,QAAW,QAKlBC,WAAA,SAAW5I,GAaT,OAZAA,EAAMqK,EAAA,GACD1D,GADC,GAEDhJ,EAAEF,KAAK0F,UAAUoB,OAFhB,GAGiB,iBAAXvE,GAAuBA,EAASA,EAAS,IAGrDnC,EAAKiC,gBACH2C,GACAzC,EACAvC,KAAKypB,YAAYhgB,aAGZlH,KAGT4wB,cAAA,WAAgB,IAAAnmB,EAAAhN,KACdE,EAAEF,KAAK0F,UAAUwB,GACf9B,GAAMolB,cACNljB,GACA,WAAA,OAAM0F,EAAKoE,YAIfunB,OAAA,WACmB,SAAXnE,IACJrnB,EAAKzH,SAASsC,UAAUiB,IAAIzD,IAC5BtF,EAAEiN,EAAKzH,UAAU3D,QAAQqD,GAAMgL,QAH1B,IAAAjD,EAAAnN,KAOP,GADAA,KAAK0F,SAASsC,UAAUtB,OAAOlB,IAC3BxF,KAAKkL,QAAQ2mB,UAAW,CAC1B,IAAMvwB,EAAqBlB,EAAKiB,iCAAiCrB,KAAK0F,UAEtExF,EAAEF,KAAK0F,UACJvF,IAAIC,EAAKR,eAAgB40B,GACzBpwB,qBAAqB9C,QAExBkzB,OAMG7tB,iBAAP,SAAwBpE,GACtB,OAAOvC,KAAK4G,KAAK,WACf,IAAMC,EAAW3G,EAAEF,MACf8G,EAAaD,EAASC,KAAK7B,IAQ/B,GALK6B,IACHA,EAAO,IAAI4xB,EAAM14B,KAHgB,iBAAXuC,GAAuBA,GAI7CsE,EAASC,KAAK7B,GAAU6B,IAGJ,iBAAXvE,EAAqB,CAC9B,GAA4B,oBAAjBuE,EAAKvE,GACd,MAAM,IAAIyB,UAAJ,oBAAkCzB,EAAlC,KAGRuE,EAAKvE,GAAQvC,kDAhJjB,MArDuB,4CAyDvB,OAAOyJ,mCAIP,OAAOP,YAoJXhJ,EAAEgE,GAAGc,IAAoB0zB,GAAM/xB,iBAC/BzG,EAAEgE,GAAGc,IAAMmC,YAAcuxB,GACzBx4B,EAAEgE,GAAGc,IAAMoC,WAAc,WAEvB,OADAlH,EAAEgE,GAAGc,IAAQG,GACNuzB,GAAM/xB", "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.4.1): util.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\n\n/**\n * ------------------------------------------------------------------------\n * Private TransitionEnd Helpers\n * ------------------------------------------------------------------------\n */\n\nconst TRANSITION_END = 'transitionend'\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nfunction toType(obj) {\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\nfunction getSpecialTransitionEndEvent() {\n  return {\n    bindType: TRANSITION_END,\n    delegateType: TRANSITION_END,\n    handle(event) {\n      if ($(event.target).is(this)) {\n        return event.handleObj.handler.apply(this, arguments) // eslint-disable-line prefer-rest-params\n      }\n      return undefined // eslint-disable-line no-undefined\n    }\n  }\n}\n\nfunction transitionEndEmulator(duration) {\n  let called = false\n\n  $(this).one(Util.TRANSITION_END, () => {\n    called = true\n  })\n\n  setTimeout(() => {\n    if (!called) {\n      Util.triggerTransitionEnd(this)\n    }\n  }, duration)\n\n  return this\n}\n\nfunction setTransitionEndSupport() {\n  $.fn.emulateTransitionEnd = transitionEndEmulator\n  $.event.special[Util.TRANSITION_END] = getSpecialTransitionEndEvent()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst Util = {\n\n  TRANSITION_END: 'bsTransitionEnd',\n\n  getUID(prefix) {\n    do {\n      // eslint-disable-next-line no-bitwise\n      prefix += ~~(Math.random() * MAX_UID) // \"~~\" acts like a faster Math.floor() here\n    } while (document.getElementById(prefix))\n    return prefix\n  },\n\n  getSelectorFromElement(element) {\n    let selector = element.getAttribute('data-target')\n\n    if (!selector || selector === '#') {\n      const hrefAttr = element.getAttribute('href')\n      selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : ''\n    }\n\n    try {\n      return document.querySelector(selector) ? selector : null\n    } catch (err) {\n      return null\n    }\n  },\n\n  getTransitionDurationFromElement(element) {\n    if (!element) {\n      return 0\n    }\n\n    // Get transition-duration of the element\n    let transitionDuration = $(element).css('transition-duration')\n    let transitionDelay = $(element).css('transition-delay')\n\n    const floatTransitionDuration = parseFloat(transitionDuration)\n    const floatTransitionDelay = parseFloat(transitionDelay)\n\n    // Return 0 if element or transition duration is not found\n    if (!floatTransitionDuration && !floatTransitionDelay) {\n      return 0\n    }\n\n    // If multiple durations are defined, take the first\n    transitionDuration = transitionDuration.split(',')[0]\n    transitionDelay = transitionDelay.split(',')[0]\n\n    return (parseFloat(transitionDuration) + parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n  },\n\n  reflow(element) {\n    return element.offsetHeight\n  },\n\n  triggerTransitionEnd(element) {\n    $(element).trigger(TRANSITION_END)\n  },\n\n  // TODO: Remove in v5\n  supportsTransitionEnd() {\n    return Boolean(TRANSITION_END)\n  },\n\n  isElement(obj) {\n    return (obj[0] || obj).nodeType\n  },\n\n  typeCheckConfig(componentName, config, configTypes) {\n    for (const property in configTypes) {\n      if (Object.prototype.hasOwnProperty.call(configTypes, property)) {\n        const expectedTypes = configTypes[property]\n        const value         = config[property]\n        const valueType     = value && Util.isElement(value)\n          ? 'element' : toType(value)\n\n        if (!new RegExp(expectedTypes).test(valueType)) {\n          throw new Error(\n            `${componentName.toUpperCase()}: ` +\n            `Option \"${property}\" provided type \"${valueType}\" ` +\n            `but expected type \"${expectedTypes}\".`)\n        }\n      }\n    }\n  },\n\n  findShadowRoot(element) {\n    if (!document.documentElement.attachShadow) {\n      return null\n    }\n\n    // Can find the shadow root otherwise it'll return the document\n    if (typeof element.getRootNode === 'function') {\n      const root = element.getRootNode()\n      return root instanceof ShadowRoot ? root : null\n    }\n\n    if (element instanceof ShadowRoot) {\n      return element\n    }\n\n    // when we don't find a shadow root\n    if (!element.parentNode) {\n      return null\n    }\n\n    return Util.findShadowRoot(element.parentNode)\n  },\n\n  jQueryDetection() {\n    if (typeof $ === 'undefined') {\n      throw new TypeError('Bootstrap\\'s JavaScript requires jQuery. jQuery must be included before Bootstrap\\'s JavaScript.')\n    }\n\n    const version = $.fn.jquery.split(' ')[0].split('.')\n    const minMajor = 1\n    const ltMajor = 2\n    const minMinor = 9\n    const minPatch = 1\n    const maxMajor = 4\n\n    if (version[0] < ltMajor && version[1] < minMinor || version[0] === minMajor && version[1] === minMinor && version[2] < minPatch || version[0] >= maxMajor) {\n      throw new Error('Bootstrap\\'s JavaScript requires at least jQuery v1.9.1 but less than v4.0.0')\n    }\n  }\n}\n\nUtil.jQueryDetection()\nsetTransitionEndSupport()\n\nexport default Util\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.4.1): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME                = 'alert'\nconst VERSION             = '4.4.1'\nconst DATA_KEY            = 'bs.alert'\nconst EVENT_KEY           = `.${DATA_KEY}`\nconst DATA_API_KEY        = '.data-api'\nconst JQUERY_NO_CONFLICT  = $.fn[NAME]\n\nconst Selector = {\n  DISMISS : '[data-dismiss=\"alert\"]'\n}\n\nconst Event = {\n  CLOSE          : `close${EVENT_KEY}`,\n  CLOSED         : `closed${EVENT_KEY}`,\n  CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n}\n\nconst ClassName = {\n  ALERT : 'alert',\n  FADE  : 'fade',\n  SHOW  : 'show'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert {\n  constructor(element) {\n    this._element = element\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  close(element) {\n    let rootElement = this._element\n    if (element) {\n      rootElement = this._getRootElement(element)\n    }\n\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    const selector = Util.getSelectorFromElement(element)\n    let parent     = false\n\n    if (selector) {\n      parent = document.querySelector(selector)\n    }\n\n    if (!parent) {\n      parent = $(element).closest(`.${ClassName.ALERT}`)[0]\n    }\n\n    return parent\n  }\n\n  _triggerCloseEvent(element) {\n    const closeEvent = $.Event(Event.CLOSE)\n\n    $(element).trigger(closeEvent)\n    return closeEvent\n  }\n\n  _removeElement(element) {\n    $(element).removeClass(ClassName.SHOW)\n\n    if (!$(element).hasClass(ClassName.FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = Util.getTransitionDurationFromElement(element)\n\n    $(element)\n      .one(Util.TRANSITION_END, (event) => this._destroyElement(element, event))\n      .emulateTransitionEnd(transitionDuration)\n  }\n\n  _destroyElement(element) {\n    $(element)\n      .detach()\n      .trigger(Event.CLOSED)\n      .remove()\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $element = $(this)\n      let data       = $element.data(DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n        $element.data(DATA_KEY, data)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static _handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document).on(\n  Event.CLICK_DATA_API,\n  Selector.DISMISS,\n  Alert._handleDismiss(new Alert())\n)\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME]             = Alert._jQueryInterface\n$.fn[NAME].Constructor = Alert\n$.fn[NAME].noConflict  = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Alert._jQueryInterface\n}\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.4.1): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME                = 'button'\nconst VERSION             = '4.4.1'\nconst DATA_KEY            = 'bs.button'\nconst EVENT_KEY           = `.${DATA_KEY}`\nconst DATA_API_KEY        = '.data-api'\nconst JQUERY_NO_CONFLICT  = $.fn[NAME]\n\nconst ClassName = {\n  ACTIVE : 'active',\n  BUTTON : 'btn',\n  FOCUS  : 'focus'\n}\n\nconst Selector = {\n  DATA_TOGGLE_CARROT   : '[data-toggle^=\"button\"]',\n  DATA_TOGGLES         : '[data-toggle=\"buttons\"]',\n  DATA_TOGGLE          : '[data-toggle=\"button\"]',\n  DATA_TOGGLES_BUTTONS : '[data-toggle=\"buttons\"] .btn',\n  INPUT                : 'input:not([type=\"hidden\"])',\n  ACTIVE               : '.active',\n  BUTTON               : '.btn'\n}\n\nconst Event = {\n  CLICK_DATA_API      : `click${EVENT_KEY}${DATA_API_KEY}`,\n  FOCUS_BLUR_DATA_API : `focus${EVENT_KEY}${DATA_API_KEY} ` +\n                          `blur${EVENT_KEY}${DATA_API_KEY}`,\n  LOAD_DATA_API       : `load${EVENT_KEY}${DATA_API_KEY}`\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button {\n  constructor(element) {\n    this._element = element\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  toggle() {\n    let triggerChangeEvent = true\n    let addAriaPressed = true\n    const rootElement = $(this._element).closest(\n      Selector.DATA_TOGGLES\n    )[0]\n\n    if (rootElement) {\n      const input = this._element.querySelector(Selector.INPUT)\n\n      if (input) {\n        if (input.type === 'radio') {\n          if (input.checked &&\n            this._element.classList.contains(ClassName.ACTIVE)) {\n            triggerChangeEvent = false\n          } else {\n            const activeElement = rootElement.querySelector(Selector.ACTIVE)\n\n            if (activeElement) {\n              $(activeElement).removeClass(ClassName.ACTIVE)\n            }\n          }\n        } else if (input.type === 'checkbox') {\n          if (this._element.tagName === 'LABEL' && input.checked === this._element.classList.contains(ClassName.ACTIVE)) {\n            triggerChangeEvent = false\n          }\n        } else {\n          // if it's not a radio button or checkbox don't add a pointless/invalid checked property to the input\n          triggerChangeEvent = false\n        }\n\n        if (triggerChangeEvent) {\n          input.checked = !this._element.classList.contains(ClassName.ACTIVE)\n          $(input).trigger('change')\n        }\n\n        input.focus()\n        addAriaPressed = false\n      }\n    }\n\n    if (!(this._element.hasAttribute('disabled') || this._element.classList.contains('disabled'))) {\n      if (addAriaPressed) {\n        this._element.setAttribute('aria-pressed',\n          !this._element.classList.contains(ClassName.ACTIVE))\n      }\n\n      if (triggerChangeEvent) {\n        $(this._element).toggleClass(ClassName.ACTIVE)\n      }\n    }\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document)\n  .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE_CARROT, (event) => {\n    let button = event.target\n\n    if (!$(button).hasClass(ClassName.BUTTON)) {\n      button = $(button).closest(Selector.BUTTON)[0]\n    }\n\n    if (!button || button.hasAttribute('disabled') || button.classList.contains('disabled')) {\n      event.preventDefault() // work around Firefox bug #1540995\n    } else {\n      const inputBtn = button.querySelector(Selector.INPUT)\n\n      if (inputBtn && (inputBtn.hasAttribute('disabled') || inputBtn.classList.contains('disabled'))) {\n        event.preventDefault() // work around Firefox bug #1540995\n        return\n      }\n\n      Button._jQueryInterface.call($(button), 'toggle')\n    }\n  })\n  .on(Event.FOCUS_BLUR_DATA_API, Selector.DATA_TOGGLE_CARROT, (event) => {\n    const button = $(event.target).closest(Selector.BUTTON)[0]\n    $(button).toggleClass(ClassName.FOCUS, /^focus(in)?$/.test(event.type))\n  })\n\n$(window).on(Event.LOAD_DATA_API, () => {\n  // ensure correct active class is set to match the controls' actual values/states\n\n  // find all checkboxes/readio buttons inside data-toggle groups\n  let buttons = [].slice.call(document.querySelectorAll(Selector.DATA_TOGGLES_BUTTONS))\n  for (let i = 0, len = buttons.length; i < len; i++) {\n    const button = buttons[i]\n    const input = button.querySelector(Selector.INPUT)\n    if (input.checked || input.hasAttribute('checked')) {\n      button.classList.add(ClassName.ACTIVE)\n    } else {\n      button.classList.remove(ClassName.ACTIVE)\n    }\n  }\n\n  // find all button toggles\n  buttons = [].slice.call(document.querySelectorAll(Selector.DATA_TOGGLE))\n  for (let i = 0, len = buttons.length; i < len; i++) {\n    const button = buttons[i]\n    if (button.getAttribute('aria-pressed') === 'true') {\n      button.classList.add(ClassName.ACTIVE)\n    } else {\n      button.classList.remove(ClassName.ACTIVE)\n    }\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Button._jQueryInterface\n$.fn[NAME].Constructor = Button\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Button._jQueryInterface\n}\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.4.1): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME                   = 'carousel'\nconst VERSION                = '4.4.1'\nconst DATA_KEY               = 'bs.carousel'\nconst EVENT_KEY              = `.${DATA_KEY}`\nconst DATA_API_KEY           = '.data-api'\nconst JQUERY_NO_CONFLICT     = $.fn[NAME]\nconst ARROW_LEFT_KEYCODE     = 37 // KeyboardEvent.which value for left arrow key\nconst ARROW_RIGHT_KEYCODE    = 39 // KeyboardEvent.which value for right arrow key\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD        = 40\n\nconst Default = {\n  interval : 5000,\n  keyboard : true,\n  slide    : false,\n  pause    : 'hover',\n  wrap     : true,\n  touch    : true\n}\n\nconst DefaultType = {\n  interval : '(number|boolean)',\n  keyboard : 'boolean',\n  slide    : '(boolean|string)',\n  pause    : '(string|boolean)',\n  wrap     : 'boolean',\n  touch    : 'boolean'\n}\n\nconst Direction = {\n  NEXT     : 'next',\n  PREV     : 'prev',\n  LEFT     : 'left',\n  RIGHT    : 'right'\n}\n\nconst Event = {\n  SLIDE          : `slide${EVENT_KEY}`,\n  SLID           : `slid${EVENT_KEY}`,\n  KEYDOWN        : `keydown${EVENT_KEY}`,\n  MOUSEENTER     : `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE     : `mouseleave${EVENT_KEY}`,\n  TOUCHSTART     : `touchstart${EVENT_KEY}`,\n  TOUCHMOVE      : `touchmove${EVENT_KEY}`,\n  TOUCHEND       : `touchend${EVENT_KEY}`,\n  POINTERDOWN    : `pointerdown${EVENT_KEY}`,\n  POINTERUP      : `pointerup${EVENT_KEY}`,\n  DRAG_START     : `dragstart${EVENT_KEY}`,\n  LOAD_DATA_API  : `load${EVENT_KEY}${DATA_API_KEY}`,\n  CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n}\n\nconst ClassName = {\n  CAROUSEL      : 'carousel',\n  ACTIVE        : 'active',\n  SLIDE         : 'slide',\n  RIGHT         : 'carousel-item-right',\n  LEFT          : 'carousel-item-left',\n  NEXT          : 'carousel-item-next',\n  PREV          : 'carousel-item-prev',\n  ITEM          : 'carousel-item',\n  POINTER_EVENT : 'pointer-event'\n}\n\nconst Selector = {\n  ACTIVE      : '.active',\n  ACTIVE_ITEM : '.active.carousel-item',\n  ITEM        : '.carousel-item',\n  ITEM_IMG    : '.carousel-item img',\n  NEXT_PREV   : '.carousel-item-next, .carousel-item-prev',\n  INDICATORS  : '.carousel-indicators',\n  DATA_SLIDE  : '[data-slide], [data-slide-to]',\n  DATA_RIDE   : '[data-ride=\"carousel\"]'\n}\n\nconst PointerType = {\n  TOUCH : 'touch',\n  PEN   : 'pen'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel {\n  constructor(element, config) {\n    this._items         = null\n    this._interval      = null\n    this._activeElement = null\n    this._isPaused      = false\n    this._isSliding     = false\n    this.touchTimeout   = null\n    this.touchStartX    = 0\n    this.touchDeltaX    = 0\n\n    this._config            = this._getConfig(config)\n    this._element           = element\n    this._indicatorsElement = this._element.querySelector(Selector.INDICATORS)\n    this._touchSupported    = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent      = Boolean(window.PointerEvent || window.MSPointerEvent)\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(Direction.NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden &&\n      ($(this._element).is(':visible') && $(this._element).css('visibility') !== 'hidden')) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(Direction.PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (this._element.querySelector(Selector.NEXT_PREV)) {\n      Util.triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config.interval && !this._isPaused) {\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = this._element.querySelector(Selector.ACTIVE_ITEM)\n\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      $(this._element).one(Event.SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const direction = index > activeIndex\n      ? Direction.NEXT\n      : Direction.PREV\n\n    this._slide(direction, this._items[index])\n  }\n\n  dispose() {\n    $(this._element).off(EVENT_KEY)\n    $.removeData(this._element, DATA_KEY)\n\n    this._items             = null\n    this._config            = null\n    this._element           = null\n    this._interval          = null\n    this._isPaused          = null\n    this._isSliding         = null\n    this._activeElement     = null\n    this._indicatorsElement = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    Util.typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    // swipe left\n    if (direction > 0) {\n      this.prev()\n    }\n\n    // swipe right\n    if (direction < 0) {\n      this.next()\n    }\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      $(this._element)\n        .on(Event.KEYDOWN, (event) => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      $(this._element)\n        .on(Event.MOUSEENTER, (event) => this.pause(event))\n        .on(Event.MOUSELEAVE, (event) => this.cycle(event))\n    }\n\n    if (this._config.touch) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    if (!this._touchSupported) {\n      return\n    }\n\n    const start = (event) => {\n      if (this._pointerEvent && PointerType[event.originalEvent.pointerType.toUpperCase()]) {\n        this.touchStartX = event.originalEvent.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.originalEvent.touches[0].clientX\n      }\n    }\n\n    const move = (event) => {\n      // ensure swiping with one touch and not pinching\n      if (event.originalEvent.touches && event.originalEvent.touches.length > 1) {\n        this.touchDeltaX = 0\n      } else {\n        this.touchDeltaX = event.originalEvent.touches[0].clientX - this.touchStartX\n      }\n    }\n\n    const end = (event) => {\n      if (this._pointerEvent && PointerType[event.originalEvent.pointerType.toUpperCase()]) {\n        this.touchDeltaX = event.originalEvent.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n        this.touchTimeout = setTimeout((event) => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    $(this._element.querySelectorAll(Selector.ITEM_IMG)).on(Event.DRAG_START, (e) => e.preventDefault())\n    if (this._pointerEvent) {\n      $(this._element).on(Event.POINTERDOWN, (event) => start(event))\n      $(this._element).on(Event.POINTERUP, (event) => end(event))\n\n      this._element.classList.add(ClassName.POINTER_EVENT)\n    } else {\n      $(this._element).on(Event.TOUCHSTART, (event) => start(event))\n      $(this._element).on(Event.TOUCHMOVE, (event) => move(event))\n      $(this._element).on(Event.TOUCHEND, (event) => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    switch (event.which) {\n      case ARROW_LEFT_KEYCODE:\n        event.preventDefault()\n        this.prev()\n        break\n      case ARROW_RIGHT_KEYCODE:\n        event.preventDefault()\n        this.next()\n        break\n      default:\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode\n      ? [].slice.call(element.parentNode.querySelectorAll(Selector.ITEM))\n      : []\n    return this._items.indexOf(element)\n  }\n\n  _getItemByDirection(direction, activeElement) {\n    const isNextDirection = direction === Direction.NEXT\n    const isPrevDirection = direction === Direction.PREV\n    const activeIndex     = this._getItemIndex(activeElement)\n    const lastItemIndex   = this._items.length - 1\n    const isGoingToWrap   = isPrevDirection && activeIndex === 0 ||\n                            isNextDirection && activeIndex === lastItemIndex\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta     = direction === Direction.PREV ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1\n      ? this._items[this._items.length - 1] : this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(this._element.querySelector(Selector.ACTIVE_ITEM))\n    const slideEvent = $.Event(Event.SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n\n    $(this._element).trigger(slideEvent)\n\n    return slideEvent\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const indicators = [].slice.call(this._indicatorsElement.querySelectorAll(Selector.ACTIVE))\n      $(indicators)\n        .removeClass(ClassName.ACTIVE)\n\n      const nextIndicator = this._indicatorsElement.children[\n        this._getItemIndex(element)\n      ]\n\n      if (nextIndicator) {\n        $(nextIndicator).addClass(ClassName.ACTIVE)\n      }\n    }\n  }\n\n  _slide(direction, element) {\n    const activeElement = this._element.querySelector(Selector.ACTIVE_ITEM)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement   = element || activeElement &&\n      this._getItemByDirection(direction, activeElement)\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    let directionalClassName\n    let orderClassName\n    let eventDirectionName\n\n    if (direction === Direction.NEXT) {\n      directionalClassName = ClassName.LEFT\n      orderClassName = ClassName.NEXT\n      eventDirectionName = Direction.LEFT\n    } else {\n      directionalClassName = ClassName.RIGHT\n      orderClassName = ClassName.PREV\n      eventDirectionName = Direction.RIGHT\n    }\n\n    if (nextElement && $(nextElement).hasClass(ClassName.ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n\n    const slidEvent = $.Event(Event.SLID, {\n      relatedTarget: nextElement,\n      direction: eventDirectionName,\n      from: activeElementIndex,\n      to: nextElementIndex\n    })\n\n    if ($(this._element).hasClass(ClassName.SLIDE)) {\n      $(nextElement).addClass(orderClassName)\n\n      Util.reflow(nextElement)\n\n      $(activeElement).addClass(directionalClassName)\n      $(nextElement).addClass(directionalClassName)\n\n      const nextElementInterval = parseInt(nextElement.getAttribute('data-interval'), 10)\n      if (nextElementInterval) {\n        this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n        this._config.interval = nextElementInterval\n      } else {\n        this._config.interval = this._config.defaultInterval || this._config.interval\n      }\n\n      const transitionDuration = Util.getTransitionDurationFromElement(activeElement)\n\n      $(activeElement)\n        .one(Util.TRANSITION_END, () => {\n          $(nextElement)\n            .removeClass(`${directionalClassName} ${orderClassName}`)\n            .addClass(ClassName.ACTIVE)\n\n          $(activeElement).removeClass(`${ClassName.ACTIVE} ${orderClassName} ${directionalClassName}`)\n\n          this._isSliding = false\n\n          setTimeout(() => $(this._element).trigger(slidEvent), 0)\n        })\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      $(activeElement).removeClass(ClassName.ACTIVE)\n      $(nextElement).addClass(ClassName.ACTIVE)\n\n      this._isSliding = false\n      $(this._element).trigger(slidEvent)\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      let _config = {\n        ...Default,\n        ...$(this).data()\n      }\n\n      if (typeof config === 'object') {\n        _config = {\n          ..._config,\n          ...config\n        }\n      }\n\n      const action = typeof config === 'string' ? config : _config.slide\n\n      if (!data) {\n        data = new Carousel(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'number') {\n        data.to(config)\n      } else if (typeof action === 'string') {\n        if (typeof data[action] === 'undefined') {\n          throw new TypeError(`No method named \"${action}\"`)\n        }\n        data[action]()\n      } else if (_config.interval && _config.ride) {\n        data.pause()\n        data.cycle()\n      }\n    })\n  }\n\n  static _dataApiClickHandler(event) {\n    const selector = Util.getSelectorFromElement(this)\n\n    if (!selector) {\n      return\n    }\n\n    const target = $(selector)[0]\n\n    if (!target || !$(target).hasClass(ClassName.CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...$(target).data(),\n      ...$(this).data()\n    }\n    const slideIndex = this.getAttribute('data-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel._jQueryInterface.call($(target), config)\n\n    if (slideIndex) {\n      $(target).data(DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document)\n  .on(Event.CLICK_DATA_API, Selector.DATA_SLIDE, Carousel._dataApiClickHandler)\n\n$(window).on(Event.LOAD_DATA_API, () => {\n  const carousels = [].slice.call(document.querySelectorAll(Selector.DATA_RIDE))\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    const $carousel = $(carousels[i])\n    Carousel._jQueryInterface.call($carousel, $carousel.data())\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Carousel._jQueryInterface\n$.fn[NAME].Constructor = Carousel\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Carousel._jQueryInterface\n}\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.4.1): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME                = 'collapse'\nconst VERSION             = '4.4.1'\nconst DATA_KEY            = 'bs.collapse'\nconst EVENT_KEY           = `.${DATA_KEY}`\nconst DATA_API_KEY        = '.data-api'\nconst JQUERY_NO_CONFLICT  = $.fn[NAME]\n\nconst Default = {\n  toggle : true,\n  parent : ''\n}\n\nconst DefaultType = {\n  toggle : 'boolean',\n  parent : '(string|element)'\n}\n\nconst Event = {\n  SHOW           : `show${EVENT_KEY}`,\n  SHOWN          : `shown${EVENT_KEY}`,\n  HIDE           : `hide${EVENT_KEY}`,\n  HIDDEN         : `hidden${EVENT_KEY}`,\n  CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n}\n\nconst ClassName = {\n  SHOW       : 'show',\n  COLLAPSE   : 'collapse',\n  COLLAPSING : 'collapsing',\n  COLLAPSED  : 'collapsed'\n}\n\nconst Dimension = {\n  WIDTH  : 'width',\n  HEIGHT : 'height'\n}\n\nconst Selector = {\n  ACTIVES     : '.show, .collapsing',\n  DATA_TOGGLE : '[data-toggle=\"collapse\"]'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse {\n  constructor(element, config) {\n    this._isTransitioning = false\n    this._element         = element\n    this._config          = this._getConfig(config)\n    this._triggerArray    = [].slice.call(document.querySelectorAll(\n      `[data-toggle=\"collapse\"][href=\"#${element.id}\"],` +\n      `[data-toggle=\"collapse\"][data-target=\"#${element.id}\"]`\n    ))\n\n    const toggleList = [].slice.call(document.querySelectorAll(Selector.DATA_TOGGLE))\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = Util.getSelectorFromElement(elem)\n      const filterElement = [].slice.call(document.querySelectorAll(selector))\n        .filter((foundElem) => foundElem === element)\n\n      if (selector !== null && filterElement.length > 0) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle() {\n    if ($(this._element).hasClass(ClassName.SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning ||\n      $(this._element).hasClass(ClassName.SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = [].slice.call(this._parent.querySelectorAll(Selector.ACTIVES))\n        .filter((elem) => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(ClassName.COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    if (actives) {\n      activesData = $(actives).not(this._selector).data(DATA_KEY)\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = $.Event(Event.SHOW)\n    $(this._element).trigger(startEvent)\n    if (startEvent.isDefaultPrevented()) {\n      return\n    }\n\n    if (actives) {\n      Collapse._jQueryInterface.call($(actives).not(this._selector), 'hide')\n      if (!activesData) {\n        $(actives).data(DATA_KEY, null)\n      }\n    }\n\n    const dimension = this._getDimension()\n\n    $(this._element)\n      .removeClass(ClassName.COLLAPSE)\n      .addClass(ClassName.COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      $(this._triggerArray)\n        .removeClass(ClassName.COLLAPSED)\n        .attr('aria-expanded', true)\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      $(this._element)\n        .removeClass(ClassName.COLLAPSING)\n        .addClass(ClassName.COLLAPSE)\n        .addClass(ClassName.SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      $(this._element).trigger(Event.SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n    $(this._element)\n      .one(Util.TRANSITION_END, complete)\n      .emulateTransitionEnd(transitionDuration)\n\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning ||\n      !$(this._element).hasClass(ClassName.SHOW)) {\n      return\n    }\n\n    const startEvent = $.Event(Event.HIDE)\n    $(this._element).trigger(startEvent)\n    if (startEvent.isDefaultPrevented()) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    Util.reflow(this._element)\n\n    $(this._element)\n      .addClass(ClassName.COLLAPSING)\n      .removeClass(ClassName.COLLAPSE)\n      .removeClass(ClassName.SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const selector = Util.getSelectorFromElement(trigger)\n\n        if (selector !== null) {\n          const $elem = $([].slice.call(document.querySelectorAll(selector)))\n          if (!$elem.hasClass(ClassName.SHOW)) {\n            $(trigger).addClass(ClassName.COLLAPSED)\n              .attr('aria-expanded', false)\n          }\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      $(this._element)\n        .removeClass(ClassName.COLLAPSING)\n        .addClass(ClassName.COLLAPSE)\n        .trigger(Event.HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n    $(this._element)\n      .one(Util.TRANSITION_END, complete)\n      .emulateTransitionEnd(transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n\n    this._config          = null\n    this._parent          = null\n    this._element         = null\n    this._triggerArray    = null\n    this._isTransitioning = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    Util.typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    const hasWidth = $(this._element).hasClass(Dimension.WIDTH)\n    return hasWidth ? Dimension.WIDTH : Dimension.HEIGHT\n  }\n\n  _getParent() {\n    let parent\n\n    if (Util.isElement(this._config.parent)) {\n      parent = this._config.parent\n\n      // It's a jQuery object\n      if (typeof this._config.parent.jquery !== 'undefined') {\n        parent = this._config.parent[0]\n      }\n    } else {\n      parent = document.querySelector(this._config.parent)\n    }\n\n    const selector =\n      `[data-toggle=\"collapse\"][data-parent=\"${this._config.parent}\"]`\n\n    const children = [].slice.call(parent.querySelectorAll(selector))\n    $(children).each((i, element) => {\n      this._addAriaAndCollapsedClass(\n        Collapse._getTargetFromElement(element),\n        [element]\n      )\n    })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    const isOpen = $(element).hasClass(ClassName.SHOW)\n\n    if (triggerArray.length) {\n      $(triggerArray)\n        .toggleClass(ClassName.COLLAPSED, !isOpen)\n        .attr('aria-expanded', isOpen)\n    }\n  }\n\n  // Static\n\n  static _getTargetFromElement(element) {\n    const selector = Util.getSelectorFromElement(element)\n    return selector ? document.querySelector(selector) : null\n  }\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $this   = $(this)\n      let data      = $this.data(DATA_KEY)\n      const _config = {\n        ...Default,\n        ...$this.data(),\n        ...typeof config === 'object' && config ? config : {}\n      }\n\n      if (!data && _config.toggle && /show|hide/.test(config)) {\n        _config.toggle = false\n      }\n\n      if (!data) {\n        data = new Collapse(this, _config)\n        $this.data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document).on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.currentTarget.tagName === 'A') {\n    event.preventDefault()\n  }\n\n  const $trigger = $(this)\n  const selector = Util.getSelectorFromElement(this)\n  const selectors = [].slice.call(document.querySelectorAll(selector))\n\n  $(selectors).each(function () {\n    const $target = $(this)\n    const data    = $target.data(DATA_KEY)\n    const config  = data ? 'toggle' : $trigger.data()\n    Collapse._jQueryInterface.call($target, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Collapse._jQueryInterface\n$.fn[NAME].Constructor = Collapse\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Collapse._jQueryInterface\n}\n\nexport default Collapse\n", "/**!\n * @fileOverview Kickass library to create and place poppers near their reference elements.\n * @version 1.16.0\n * @license\n * Copyright (c) 2016 <PERSON> and contributors\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && typeof navigator !== 'undefined';\n\nvar timeoutDuration = function () {\n  var longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\n  for (var i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n    if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n      return 1;\n    }\n  }\n  return 0;\n}();\n\nfunction microtaskDebounce(fn) {\n  var called = false;\n  return function () {\n    if (called) {\n      return;\n    }\n    called = true;\n    window.Promise.resolve().then(function () {\n      called = false;\n      fn();\n    });\n  };\n}\n\nfunction taskDebounce(fn) {\n  var scheduled = false;\n  return function () {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(function () {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\nvar supportsMicroTasks = isBrowser && window.Promise;\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nvar debounce = supportsMicroTasks ? microtaskDebounce : taskDebounce;\n\n/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nfunction isFunction(functionToCheck) {\n  var getType = {};\n  return functionToCheck && getType.toString.call(functionToCheck) === '[object Function]';\n}\n\n/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nfunction getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  var window = element.ownerDocument.defaultView;\n  var css = window.getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n\n/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nfunction getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nfunction getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body;\n  }\n\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body;\n    case '#document':\n      return element.body;\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n\n  var _getStyleComputedProp = getStyleComputedProperty(element),\n      overflow = _getStyleComputedProp.overflow,\n      overflowX = _getStyleComputedProp.overflowX,\n      overflowY = _getStyleComputedProp.overflowY;\n\n  if (/(auto|scroll|overlay)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n\n/**\n * Returns the reference node of the reference object, or the reference object itself.\n * @method\n * @memberof Popper.Utils\n * @param {Element|Object} reference - the reference element (the popper will be relative to this)\n * @returns {Element} parent\n */\nfunction getReferenceNode(reference) {\n  return reference && reference.referenceNode ? reference.referenceNode : reference;\n}\n\nvar isIE11 = isBrowser && !!(window.MSInputMethodContext && document.documentMode);\nvar isIE10 = isBrowser && /MSIE 10/.test(navigator.userAgent);\n\n/**\n * Determines if the browser is Internet Explorer\n * @method\n * @memberof Popper.Utils\n * @param {Number} version to check\n * @returns {Boolean} isIE\n */\nfunction isIE(version) {\n  if (version === 11) {\n    return isIE11;\n  }\n  if (version === 10) {\n    return isIE10;\n  }\n  return isIE11 || isIE10;\n}\n\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nfunction getOffsetParent(element) {\n  if (!element) {\n    return document.documentElement;\n  }\n\n  var noOffsetParent = isIE(10) ? document.body : null;\n\n  // NOTE: 1 DOM access here\n  var offsetParent = element.offsetParent || null;\n  // Skip hidden elements which don't have an offsetParent\n  while (offsetParent === noOffsetParent && element.nextElementSibling) {\n    offsetParent = (element = element.nextElementSibling).offsetParent;\n  }\n\n  var nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return element ? element.ownerDocument.documentElement : document.documentElement;\n  }\n\n  // .offsetParent will return the closest TH, TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (['TH', 'TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 && getStyleComputedProperty(offsetParent, 'position') === 'static') {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n\nfunction isOffsetContainer(element) {\n  var nodeName = element.nodeName;\n\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element;\n}\n\n/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nfunction getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nfunction findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  var order = element1.compareDocumentPosition(element2) & Node.DOCUMENT_POSITION_FOLLOWING;\n  var start = order ? element1 : element2;\n  var end = order ? element2 : element1;\n\n  // Get common ancestor container\n  var range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  var commonAncestorContainer = range.commonAncestorContainer;\n\n  // Both nodes are inside #document\n\n  if (element1 !== commonAncestorContainer && element2 !== commonAncestorContainer || start.contains(end)) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  var element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n\n/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nfunction getScroll(element) {\n  var side = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'top';\n\n  var upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  var nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    var html = element.ownerDocument.documentElement;\n    var scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nfunction includeScroll(rect, element) {\n  var subtract = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n  var scrollTop = getScroll(element, 'top');\n  var scrollLeft = getScroll(element, 'left');\n  var modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n\n/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nfunction getBordersSize(styles, axis) {\n  var sideA = axis === 'x' ? 'Left' : 'Top';\n  var sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return parseFloat(styles['border' + sideA + 'Width'], 10) + parseFloat(styles['border' + sideB + 'Width'], 10);\n}\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(body['offset' + axis], body['scroll' + axis], html['client' + axis], html['offset' + axis], html['scroll' + axis], isIE(10) ? parseInt(html['offset' + axis]) + parseInt(computedStyle['margin' + (axis === 'Height' ? 'Top' : 'Left')]) + parseInt(computedStyle['margin' + (axis === 'Height' ? 'Bottom' : 'Right')]) : 0);\n}\n\nfunction getWindowSizes(document) {\n  var body = document.body;\n  var html = document.documentElement;\n  var computedStyle = isIE(10) && getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle)\n  };\n}\n\nvar classCallCheck = function (instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n};\n\nvar createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\n\n\n\n\n\nvar defineProperty = function (obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n};\n\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n\n  return target;\n};\n\n/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nfunction getClientRect(offsets) {\n  return _extends({}, offsets, {\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height\n  });\n}\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nfunction getBoundingClientRect(element) {\n  var rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  try {\n    if (isIE(10)) {\n      rect = element.getBoundingClientRect();\n      var scrollTop = getScroll(element, 'top');\n      var scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    } else {\n      rect = element.getBoundingClientRect();\n    }\n  } catch (e) {}\n\n  var result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top\n  };\n\n  // subtract scrollbar size from sizes\n  var sizes = element.nodeName === 'HTML' ? getWindowSizes(element.ownerDocument) : {};\n  var width = sizes.width || element.clientWidth || result.width;\n  var height = sizes.height || element.clientHeight || result.height;\n\n  var horizScrollbar = element.offsetWidth - width;\n  var vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    var styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n\nfunction getOffsetRectRelativeToArbitraryNode(children, parent) {\n  var fixedPosition = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n  var isIE10 = isIE(10);\n  var isHTML = parent.nodeName === 'HTML';\n  var childrenRect = getBoundingClientRect(children);\n  var parentRect = getBoundingClientRect(parent);\n  var scrollParent = getScrollParent(children);\n\n  var styles = getStyleComputedProperty(parent);\n  var borderTopWidth = parseFloat(styles.borderTopWidth, 10);\n  var borderLeftWidth = parseFloat(styles.borderLeftWidth, 10);\n\n  // In cases where the parent is fixed, we must ignore negative scroll in offset calc\n  if (fixedPosition && isHTML) {\n    parentRect.top = Math.max(parentRect.top, 0);\n    parentRect.left = Math.max(parentRect.left, 0);\n  }\n  var offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    var marginTop = parseFloat(styles.marginTop, 10);\n    var marginLeft = parseFloat(styles.marginLeft, 10);\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (isIE10 && !fixedPosition ? parent.contains(scrollParent) : parent === scrollParent && scrollParent.nodeName !== 'BODY') {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n\nfunction getViewportOffsetRectRelativeToArtbitraryNode(element) {\n  var excludeScroll = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var html = element.ownerDocument.documentElement;\n  var relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  var width = Math.max(html.clientWidth, window.innerWidth || 0);\n  var height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  var scrollTop = !excludeScroll ? getScroll(html) : 0;\n  var scrollLeft = !excludeScroll ? getScroll(html, 'left') : 0;\n\n  var offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width: width,\n    height: height\n  };\n\n  return getClientRect(offset);\n}\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {Boolean} answer to \"isFixed?\"\n */\nfunction isFixed(element) {\n  var nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  var parentNode = getParentNode(element);\n  if (!parentNode) {\n    return false;\n  }\n  return isFixed(parentNode);\n}\n\n/**\n * Finds the first parent of an element that has a transformed property defined\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} first transformed parent or documentElement\n */\n\nfunction getFixedPositionOffsetParent(element) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element || !element.parentElement || isIE()) {\n    return document.documentElement;\n  }\n  var el = element.parentElement;\n  while (el && getStyleComputedProperty(el, 'transform') === 'none') {\n    el = el.parentElement;\n  }\n  return el || document.documentElement;\n}\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @param {Boolean} fixedPosition - Is in fixed position mode\n * @returns {Object} Coordinates of the boundaries\n */\nfunction getBoundaries(popper, reference, padding, boundariesElement) {\n  var fixedPosition = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n\n  // NOTE: 1 DOM access here\n\n  var boundaries = { top: 0, left: 0 };\n  var offsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport') {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent, fixedPosition);\n  } else {\n    // Handle other cases based on DOM element used as boundaries\n    var boundariesNode = void 0;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(reference));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = popper.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = popper.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    var offsets = getOffsetRectRelativeToArbitraryNode(boundariesNode, offsetParent, fixedPosition);\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      var _getWindowSizes = getWindowSizes(popper.ownerDocument),\n          height = _getWindowSizes.height,\n          width = _getWindowSizes.width;\n\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  padding = padding || 0;\n  var isPaddingNumber = typeof padding === 'number';\n  boundaries.left += isPaddingNumber ? padding : padding.left || 0;\n  boundaries.top += isPaddingNumber ? padding : padding.top || 0;\n  boundaries.right -= isPaddingNumber ? padding : padding.right || 0;\n  boundaries.bottom -= isPaddingNumber ? padding : padding.bottom || 0;\n\n  return boundaries;\n}\n\nfunction getArea(_ref) {\n  var width = _ref.width,\n      height = _ref.height;\n\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeAutoPlacement(placement, refRect, popper, reference, boundariesElement) {\n  var padding = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 0;\n\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  var boundaries = getBoundaries(popper, reference, padding, boundariesElement);\n\n  var rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height\n    }\n  };\n\n  var sortedAreas = Object.keys(rects).map(function (key) {\n    return _extends({\n      key: key\n    }, rects[key], {\n      area: getArea(rects[key])\n    });\n  }).sort(function (a, b) {\n    return b.area - a.area;\n  });\n\n  var filteredAreas = sortedAreas.filter(function (_ref2) {\n    var width = _ref2.width,\n        height = _ref2.height;\n    return width >= popper.clientWidth && height >= popper.clientHeight;\n  });\n\n  var computedPlacement = filteredAreas.length > 0 ? filteredAreas[0].key : sortedAreas[0].key;\n\n  var variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? '-' + variation : '');\n}\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @param {Element} fixedPosition - is in fixed position mode\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nfunction getReferenceOffsets(state, popper, reference) {\n  var fixedPosition = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n\n  var commonOffsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent, fixedPosition);\n}\n\n/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nfunction getOuterSizes(element) {\n  var window = element.ownerDocument.defaultView;\n  var styles = window.getComputedStyle(element);\n  var x = parseFloat(styles.marginTop || 0) + parseFloat(styles.marginBottom || 0);\n  var y = parseFloat(styles.marginLeft || 0) + parseFloat(styles.marginRight || 0);\n  var result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x\n  };\n  return result;\n}\n\n/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nfunction getOppositePlacement(placement) {\n  var hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nfunction getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  var popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  var popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  var isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  var mainSide = isHoriz ? 'top' : 'left';\n  var secondarySide = isHoriz ? 'left' : 'top';\n  var measurement = isHoriz ? 'height' : 'width';\n  var secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] = referenceOffsets[mainSide] + referenceOffsets[measurement] / 2 - popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] = referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] = referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n\n/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(function (cur) {\n      return cur[prop] === value;\n    });\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  var match = find(arr, function (obj) {\n    return obj[prop] === value;\n  });\n  return arr.indexOf(match);\n}\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nfunction runModifiers(modifiers, data, ends) {\n  var modifiersToRun = ends === undefined ? modifiers : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(function (modifier) {\n    if (modifier['function']) {\n      // eslint-disable-line dot-notation\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    var fn = modifier['function'] || modifier.fn; // eslint-disable-line dot-notation\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n\n/**\n * Updates the position of the popper, computing the new offsets and applying\n * the new style.<br />\n * Prefer `scheduleUpdate` over `update` because of performance reasons.\n * @method\n * @memberof Popper\n */\nfunction update() {\n  // if popper is destroyed, don't perform any further update\n  if (this.state.isDestroyed) {\n    return;\n  }\n\n  var data = {\n    instance: this,\n    styles: {},\n    arrowStyles: {},\n    attributes: {},\n    flipped: false,\n    offsets: {}\n  };\n\n  // compute reference element offsets\n  data.offsets.reference = getReferenceOffsets(this.state, this.popper, this.reference, this.options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  data.placement = computeAutoPlacement(this.options.placement, data.offsets.reference, this.popper, this.reference, this.options.modifiers.flip.boundariesElement, this.options.modifiers.flip.padding);\n\n  // store the computed placement inside `originalPlacement`\n  data.originalPlacement = data.placement;\n\n  data.positionFixed = this.options.positionFixed;\n\n  // compute the popper offsets\n  data.offsets.popper = getPopperOffsets(this.popper, data.offsets.reference, data.placement);\n\n  data.offsets.popper.position = this.options.positionFixed ? 'fixed' : 'absolute';\n\n  // run the modifiers\n  data = runModifiers(this.modifiers, data);\n\n  // the first `update` will call `onCreate` callback\n  // the other ones will call `onUpdate` callback\n  if (!this.state.isCreated) {\n    this.state.isCreated = true;\n    this.options.onCreate(data);\n  } else {\n    this.options.onUpdate(data);\n  }\n}\n\n/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nfunction isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(function (_ref) {\n    var name = _ref.name,\n        enabled = _ref.enabled;\n    return enabled && name === modifierName;\n  });\n}\n\n/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nfunction getSupportedPropertyName(property) {\n  var prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  var upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (var i = 0; i < prefixes.length; i++) {\n    var prefix = prefixes[i];\n    var toCheck = prefix ? '' + prefix + upperProp : property;\n    if (typeof document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n\n/**\n * Destroys the popper.\n * @method\n * @memberof Popper\n */\nfunction destroy() {\n  this.state.isDestroyed = true;\n\n  // touch DOM only if `applyStyle` modifier is enabled\n  if (isModifierEnabled(this.modifiers, 'applyStyle')) {\n    this.popper.removeAttribute('x-placement');\n    this.popper.style.position = '';\n    this.popper.style.top = '';\n    this.popper.style.left = '';\n    this.popper.style.right = '';\n    this.popper.style.bottom = '';\n    this.popper.style.willChange = '';\n    this.popper.style[getSupportedPropertyName('transform')] = '';\n  }\n\n  this.disableEventListeners();\n\n  // remove the popper if user explicitly asked for the deletion on destroy\n  // do not use `remove` because IE11 doesn't support it\n  if (this.options.removeOnDestroy) {\n    this.popper.parentNode.removeChild(this.popper);\n  }\n  return this;\n}\n\n/**\n * Get the window associated with the element\n * @argument {Element} element\n * @returns {Window}\n */\nfunction getWindow(element) {\n  var ownerDocument = element.ownerDocument;\n  return ownerDocument ? ownerDocument.defaultView : window;\n}\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  var isBody = scrollParent.nodeName === 'BODY';\n  var target = isBody ? scrollParent.ownerDocument.defaultView : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(getScrollParent(target.parentNode), event, callback, scrollParents);\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction setupEventListeners(reference, options, state, updateBound) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  getWindow(reference).addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  var scrollElement = getScrollParent(reference);\n  attachToScrollParents(scrollElement, 'scroll', state.updateBound, state.scrollParents);\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n\n/**\n * It will add resize/scroll events and start recalculating\n * position of the popper element when they are triggered.\n * @method\n * @memberof Popper\n */\nfunction enableEventListeners() {\n  if (!this.state.eventsEnabled) {\n    this.state = setupEventListeners(this.reference, this.options, this.state, this.scheduleUpdate);\n  }\n}\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  getWindow(reference).removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(function (target) {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n\n/**\n * It will remove resize/scroll events and won't recalculate popper position\n * when they are triggered. It also won't trigger `onUpdate` callback anymore,\n * unless you call `update` method manually.\n * @method\n * @memberof Popper\n */\nfunction disableEventListeners() {\n  if (this.state.eventsEnabled) {\n    cancelAnimationFrame(this.scheduleUpdate);\n    this.state = removeEventListeners(this.reference, this.state);\n  }\n}\n\n/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nfunction isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setStyles(element, styles) {\n  Object.keys(styles).forEach(function (prop) {\n    var unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !== -1 && isNumeric(styles[prop])) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n\n/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function (prop) {\n    var value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} data.styles - List of style properties - values to apply to popper element\n * @argument {Object} data.attributes - List of attribute properties - values to apply to popper element\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The same data object\n */\nfunction applyStyle(data) {\n  // any property present in `data.styles` will be applied to the popper,\n  // in this way we can make the 3rd party modifiers add custom styles to it\n  // Be aware, modifiers could override the properties defined in the previous\n  // lines of this modifier!\n  setStyles(data.instance.popper, data.styles);\n\n  // any property present in `data.attributes` will be applied to the popper,\n  // they will be set as HTML attributes of the element\n  setAttributes(data.instance.popper, data.attributes);\n\n  // if arrowElement is defined and arrowStyles has some properties\n  if (data.arrowElement && Object.keys(data.arrowStyles).length) {\n    setStyles(data.arrowElement, data.arrowStyles);\n  }\n\n  return data;\n}\n\n/**\n * Set the x-placement attribute before everything else because it could be used\n * to add margins to the popper margins needs to be calculated to get the\n * correct popper offsets.\n * @method\n * @memberof Popper.modifiers\n * @param {HTMLElement} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper\n * @param {Object} options - Popper.js options\n */\nfunction applyStyleOnLoad(reference, popper, options, modifierOptions, state) {\n  // compute reference element offsets\n  var referenceOffsets = getReferenceOffsets(state, popper, reference, options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  var placement = computeAutoPlacement(options.placement, referenceOffsets, popper, reference, options.modifiers.flip.boundariesElement, options.modifiers.flip.padding);\n\n  popper.setAttribute('x-placement', placement);\n\n  // Apply `position` to popper before anything else because\n  // without the position applied we can't guarantee correct computations\n  setStyles(popper, { position: options.positionFixed ? 'fixed' : 'absolute' });\n\n  return options;\n}\n\n/**\n * @function\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Boolean} shouldRound - If the offsets should be rounded at all\n * @returns {Object} The popper's position offsets rounded\n *\n * The tale of pixel-perfect positioning. It's still not 100% perfect, but as\n * good as it can be within reason.\n * Discussion here: https://github.com/FezVrasta/popper.js/pull/715\n *\n * Low DPI screens cause a popper to be blurry if not using full pixels (Safari\n * as well on High DPI screens).\n *\n * Firefox prefers no rounding for positioning and does not have blurriness on\n * high DPI screens.\n *\n * Only horizontal placement and left/right values need to be considered.\n */\nfunction getRoundedOffsets(data, shouldRound) {\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n  var round = Math.round,\n      floor = Math.floor;\n\n  var noRound = function noRound(v) {\n    return v;\n  };\n\n  var referenceWidth = round(reference.width);\n  var popperWidth = round(popper.width);\n\n  var isVertical = ['left', 'right'].indexOf(data.placement) !== -1;\n  var isVariation = data.placement.indexOf('-') !== -1;\n  var sameWidthParity = referenceWidth % 2 === popperWidth % 2;\n  var bothOddWidth = referenceWidth % 2 === 1 && popperWidth % 2 === 1;\n\n  var horizontalToInteger = !shouldRound ? noRound : isVertical || isVariation || sameWidthParity ? round : floor;\n  var verticalToInteger = !shouldRound ? noRound : round;\n\n  return {\n    left: horizontalToInteger(bothOddWidth && !isVariation && shouldRound ? popper.left - 1 : popper.left),\n    top: verticalToInteger(popper.top),\n    bottom: verticalToInteger(popper.bottom),\n    right: horizontalToInteger(popper.right)\n  };\n}\n\nvar isFirefox = isBrowser && /Firefox/i.test(navigator.userAgent);\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeStyle(data, options) {\n  var x = options.x,\n      y = options.y;\n  var popper = data.offsets.popper;\n\n  // Remove this legacy support in Popper.js v2\n\n  var legacyGpuAccelerationOption = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'applyStyle';\n  }).gpuAcceleration;\n  if (legacyGpuAccelerationOption !== undefined) {\n    console.warn('WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!');\n  }\n  var gpuAcceleration = legacyGpuAccelerationOption !== undefined ? legacyGpuAccelerationOption : options.gpuAcceleration;\n\n  var offsetParent = getOffsetParent(data.instance.popper);\n  var offsetParentRect = getBoundingClientRect(offsetParent);\n\n  // Styles\n  var styles = {\n    position: popper.position\n  };\n\n  var offsets = getRoundedOffsets(data, window.devicePixelRatio < 2 || !isFirefox);\n\n  var sideA = x === 'bottom' ? 'top' : 'bottom';\n  var sideB = y === 'right' ? 'left' : 'right';\n\n  // if gpuAcceleration is set to `true` and transform is supported,\n  //  we use `translate3d` to apply the position to the popper we\n  // automatically use the supported prefixed version if needed\n  var prefixedProperty = getSupportedPropertyName('transform');\n\n  // now, let's make a step back and look at this code closely (wtf?)\n  // If the content of the popper grows once it's been positioned, it\n  // may happen that the popper gets misplaced because of the new content\n  // overflowing its reference element\n  // To avoid this problem, we provide two options (x and y), which allow\n  // the consumer to define the offset origin.\n  // If we position a popper on top of a reference element, we can set\n  // `x` to `top` to make the popper grow towards its top instead of\n  // its bottom.\n  var left = void 0,\n      top = void 0;\n  if (sideA === 'bottom') {\n    // when offsetParent is <html> the positioning is relative to the bottom of the screen (excluding the scrollbar)\n    // and not the bottom of the html element\n    if (offsetParent.nodeName === 'HTML') {\n      top = -offsetParent.clientHeight + offsets.bottom;\n    } else {\n      top = -offsetParentRect.height + offsets.bottom;\n    }\n  } else {\n    top = offsets.top;\n  }\n  if (sideB === 'right') {\n    if (offsetParent.nodeName === 'HTML') {\n      left = -offsetParent.clientWidth + offsets.right;\n    } else {\n      left = -offsetParentRect.width + offsets.right;\n    }\n  } else {\n    left = offsets.left;\n  }\n  if (gpuAcceleration && prefixedProperty) {\n    styles[prefixedProperty] = 'translate3d(' + left + 'px, ' + top + 'px, 0)';\n    styles[sideA] = 0;\n    styles[sideB] = 0;\n    styles.willChange = 'transform';\n  } else {\n    // othwerise, we use the standard `top`, `left`, `bottom` and `right` properties\n    var invertTop = sideA === 'bottom' ? -1 : 1;\n    var invertLeft = sideB === 'right' ? -1 : 1;\n    styles[sideA] = top * invertTop;\n    styles[sideB] = left * invertLeft;\n    styles.willChange = sideA + ', ' + sideB;\n  }\n\n  // Attributes\n  var attributes = {\n    'x-placement': data.placement\n  };\n\n  // Update `data` attributes, styles and arrowStyles\n  data.attributes = _extends({}, attributes, data.attributes);\n  data.styles = _extends({}, styles, data.styles);\n  data.arrowStyles = _extends({}, data.offsets.arrow, data.arrowStyles);\n\n  return data;\n}\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nfunction isModifierRequired(modifiers, requestingName, requestedName) {\n  var requesting = find(modifiers, function (_ref) {\n    var name = _ref.name;\n    return name === requestingName;\n  });\n\n  var isRequired = !!requesting && modifiers.some(function (modifier) {\n    return modifier.name === requestedName && modifier.enabled && modifier.order < requesting.order;\n  });\n\n  if (!isRequired) {\n    var _requesting = '`' + requestingName + '`';\n    var requested = '`' + requestedName + '`';\n    console.warn(requested + ' modifier is required by ' + _requesting + ' modifier in order to work, be sure to include it before ' + _requesting + '!');\n  }\n  return isRequired;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction arrow(data, options) {\n  var _data$offsets$arrow;\n\n  // arrow depends on keepTogether in order to work\n  if (!isModifierRequired(data.instance.modifiers, 'arrow', 'keepTogether')) {\n    return data;\n  }\n\n  var arrowElement = options.element;\n\n  // if arrowElement is a string, suppose it's a CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = data.instance.popper.querySelector(arrowElement);\n\n    // if arrowElement is not found, don't run the modifier\n    if (!arrowElement) {\n      return data;\n    }\n  } else {\n    // if the arrowElement isn't a query selector we must check that the\n    // provided DOM node is child of its popper node\n    if (!data.instance.popper.contains(arrowElement)) {\n      console.warn('WARNING: `arrow.element` must be child of its popper element!');\n      return data;\n    }\n  }\n\n  var placement = data.placement.split('-')[0];\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var isVertical = ['left', 'right'].indexOf(placement) !== -1;\n\n  var len = isVertical ? 'height' : 'width';\n  var sideCapitalized = isVertical ? 'Top' : 'Left';\n  var side = sideCapitalized.toLowerCase();\n  var altSide = isVertical ? 'left' : 'top';\n  var opSide = isVertical ? 'bottom' : 'right';\n  var arrowElementSize = getOuterSizes(arrowElement)[len];\n\n  //\n  // extends keepTogether behavior making sure the popper and its\n  // reference have enough pixels in conjunction\n  //\n\n  // top/left side\n  if (reference[opSide] - arrowElementSize < popper[side]) {\n    data.offsets.popper[side] -= popper[side] - (reference[opSide] - arrowElementSize);\n  }\n  // bottom/right side\n  if (reference[side] + arrowElementSize > popper[opSide]) {\n    data.offsets.popper[side] += reference[side] + arrowElementSize - popper[opSide];\n  }\n  data.offsets.popper = getClientRect(data.offsets.popper);\n\n  // compute center of the popper\n  var center = reference[side] + reference[len] / 2 - arrowElementSize / 2;\n\n  // Compute the sideValue using the updated popper offsets\n  // take popper margin in account because we don't have this info available\n  var css = getStyleComputedProperty(data.instance.popper);\n  var popperMarginSide = parseFloat(css['margin' + sideCapitalized], 10);\n  var popperBorderSide = parseFloat(css['border' + sideCapitalized + 'Width'], 10);\n  var sideValue = center - data.offsets.popper[side] - popperMarginSide - popperBorderSide;\n\n  // prevent arrowElement from being placed not contiguously to its popper\n  sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0);\n\n  data.arrowElement = arrowElement;\n  data.offsets.arrow = (_data$offsets$arrow = {}, defineProperty(_data$offsets$arrow, side, Math.round(sideValue)), defineProperty(_data$offsets$arrow, altSide, ''), _data$offsets$arrow);\n\n  return data;\n}\n\n/**\n * Get the opposite placement variation of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement variation\n * @returns {String} flipped placement variation\n */\nfunction getOppositeVariation(variation) {\n  if (variation === 'end') {\n    return 'start';\n  } else if (variation === 'start') {\n    return 'end';\n  }\n  return variation;\n}\n\n/**\n * List of accepted placements to use as values of the `placement` option.<br />\n * Valid placements are:\n * - `auto`\n * - `top`\n * - `right`\n * - `bottom`\n * - `left`\n *\n * Each placement can have a variation from this list:\n * - `-start`\n * - `-end`\n *\n * Variations are interpreted easily if you think of them as the left to right\n * written languages. Horizontally (`top` and `bottom`), `start` is left and `end`\n * is right.<br />\n * Vertically (`left` and `right`), `start` is top and `end` is bottom.\n *\n * Some valid examples are:\n * - `top-end` (on top of reference, right aligned)\n * - `right-start` (on right of reference, top aligned)\n * - `bottom` (on bottom, centered)\n * - `auto-end` (on the side with more space available, alignment depends by placement)\n *\n * @static\n * @type {Array}\n * @enum {String}\n * @readonly\n * @method placements\n * @memberof Popper\n */\nvar placements = ['auto-start', 'auto', 'auto-end', 'top-start', 'top', 'top-end', 'right-start', 'right', 'right-end', 'bottom-end', 'bottom', 'bottom-start', 'left-end', 'left', 'left-start'];\n\n// Get rid of `auto` `auto-start` and `auto-end`\nvar validPlacements = placements.slice(3);\n\n/**\n * Given an initial placement, returns all the subsequent placements\n * clockwise (or counter-clockwise).\n *\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement - A valid placement (it accepts variations)\n * @argument {Boolean} counter - Set to true to walk the placements counterclockwise\n * @returns {Array} placements including their variations\n */\nfunction clockwise(placement) {\n  var counter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var index = validPlacements.indexOf(placement);\n  var arr = validPlacements.slice(index + 1).concat(validPlacements.slice(0, index));\n  return counter ? arr.reverse() : arr;\n}\n\nvar BEHAVIORS = {\n  FLIP: 'flip',\n  CLOCKWISE: 'clockwise',\n  COUNTERCLOCKWISE: 'counterclockwise'\n};\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction flip(data, options) {\n  // if `inner` modifier is enabled, we can't use the `flip` modifier\n  if (isModifierEnabled(data.instance.modifiers, 'inner')) {\n    return data;\n  }\n\n  if (data.flipped && data.placement === data.originalPlacement) {\n    // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n    return data;\n  }\n\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, options.boundariesElement, data.positionFixed);\n\n  var placement = data.placement.split('-')[0];\n  var placementOpposite = getOppositePlacement(placement);\n  var variation = data.placement.split('-')[1] || '';\n\n  var flipOrder = [];\n\n  switch (options.behavior) {\n    case BEHAVIORS.FLIP:\n      flipOrder = [placement, placementOpposite];\n      break;\n    case BEHAVIORS.CLOCKWISE:\n      flipOrder = clockwise(placement);\n      break;\n    case BEHAVIORS.COUNTERCLOCKWISE:\n      flipOrder = clockwise(placement, true);\n      break;\n    default:\n      flipOrder = options.behavior;\n  }\n\n  flipOrder.forEach(function (step, index) {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return data;\n    }\n\n    placement = data.placement.split('-')[0];\n    placementOpposite = getOppositePlacement(placement);\n\n    var popperOffsets = data.offsets.popper;\n    var refOffsets = data.offsets.reference;\n\n    // using floor because the reference offsets may contain decimals we are not going to consider here\n    var floor = Math.floor;\n    var overlapsRef = placement === 'left' && floor(popperOffsets.right) > floor(refOffsets.left) || placement === 'right' && floor(popperOffsets.left) < floor(refOffsets.right) || placement === 'top' && floor(popperOffsets.bottom) > floor(refOffsets.top) || placement === 'bottom' && floor(popperOffsets.top) < floor(refOffsets.bottom);\n\n    var overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left);\n    var overflowsRight = floor(popperOffsets.right) > floor(boundaries.right);\n    var overflowsTop = floor(popperOffsets.top) < floor(boundaries.top);\n    var overflowsBottom = floor(popperOffsets.bottom) > floor(boundaries.bottom);\n\n    var overflowsBoundaries = placement === 'left' && overflowsLeft || placement === 'right' && overflowsRight || placement === 'top' && overflowsTop || placement === 'bottom' && overflowsBottom;\n\n    // flip the variation if required\n    var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n\n    // flips variation if reference element overflows boundaries\n    var flippedVariationByRef = !!options.flipVariations && (isVertical && variation === 'start' && overflowsLeft || isVertical && variation === 'end' && overflowsRight || !isVertical && variation === 'start' && overflowsTop || !isVertical && variation === 'end' && overflowsBottom);\n\n    // flips variation if popper content overflows boundaries\n    var flippedVariationByContent = !!options.flipVariationsByContent && (isVertical && variation === 'start' && overflowsRight || isVertical && variation === 'end' && overflowsLeft || !isVertical && variation === 'start' && overflowsBottom || !isVertical && variation === 'end' && overflowsTop);\n\n    var flippedVariation = flippedVariationByRef || flippedVariationByContent;\n\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      // this boolean to detect any flip loop\n      data.flipped = true;\n\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n\n      data.placement = placement + (variation ? '-' + variation : '');\n\n      // this object contains `position`, we want to preserve it along with\n      // any additional property we may add in the future\n      data.offsets.popper = _extends({}, data.offsets.popper, getPopperOffsets(data.instance.popper, data.offsets.reference, data.placement));\n\n      data = runModifiers(data.instance.modifiers, data, 'flip');\n    }\n  });\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction keepTogether(data) {\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var placement = data.placement.split('-')[0];\n  var floor = Math.floor;\n  var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n  var side = isVertical ? 'right' : 'bottom';\n  var opSide = isVertical ? 'left' : 'top';\n  var measurement = isVertical ? 'width' : 'height';\n\n  if (popper[side] < floor(reference[opSide])) {\n    data.offsets.popper[opSide] = floor(reference[opSide]) - popper[measurement];\n  }\n  if (popper[opSide] > floor(reference[side])) {\n    data.offsets.popper[opSide] = floor(reference[side]);\n  }\n\n  return data;\n}\n\n/**\n * Converts a string containing value + unit into a px value number\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} str - Value + unit string\n * @argument {String} measurement - `height` or `width`\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @returns {Number|String}\n * Value in pixels, or original string if no values were extracted\n */\nfunction toValue(str, measurement, popperOffsets, referenceOffsets) {\n  // separate value from unit\n  var split = str.match(/((?:\\-|\\+)?\\d*\\.?\\d*)(.*)/);\n  var value = +split[1];\n  var unit = split[2];\n\n  // If it's not a number it's an operator, I guess\n  if (!value) {\n    return str;\n  }\n\n  if (unit.indexOf('%') === 0) {\n    var element = void 0;\n    switch (unit) {\n      case '%p':\n        element = popperOffsets;\n        break;\n      case '%':\n      case '%r':\n      default:\n        element = referenceOffsets;\n    }\n\n    var rect = getClientRect(element);\n    return rect[measurement] / 100 * value;\n  } else if (unit === 'vh' || unit === 'vw') {\n    // if is a vh or vw, we calculate the size based on the viewport\n    var size = void 0;\n    if (unit === 'vh') {\n      size = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);\n    } else {\n      size = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);\n    }\n    return size / 100 * value;\n  } else {\n    // if is an explicit pixel unit, we get rid of the unit and keep the value\n    // if is an implicit unit, it's px, and we return just the value\n    return value;\n  }\n}\n\n/**\n * Parse an `offset` string to extrapolate `x` and `y` numeric offsets.\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} offset\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @argument {String} basePlacement\n * @returns {Array} a two cells array with x and y offsets in numbers\n */\nfunction parseOffset(offset, popperOffsets, referenceOffsets, basePlacement) {\n  var offsets = [0, 0];\n\n  // Use height if placement is left or right and index is 0 otherwise use width\n  // in this way the first offset will use an axis and the second one\n  // will use the other one\n  var useHeight = ['right', 'left'].indexOf(basePlacement) !== -1;\n\n  // Split the offset string to obtain a list of values and operands\n  // The regex addresses values with the plus or minus sign in front (+10, -20, etc)\n  var fragments = offset.split(/(\\+|\\-)/).map(function (frag) {\n    return frag.trim();\n  });\n\n  // Detect if the offset string contains a pair of values or a single one\n  // they could be separated by comma or space\n  var divider = fragments.indexOf(find(fragments, function (frag) {\n    return frag.search(/,|\\s/) !== -1;\n  }));\n\n  if (fragments[divider] && fragments[divider].indexOf(',') === -1) {\n    console.warn('Offsets separated by white space(s) are deprecated, use a comma (,) instead.');\n  }\n\n  // If divider is found, we divide the list of values and operands to divide\n  // them by ofset X and Y.\n  var splitRegex = /\\s*,\\s*|\\s+/;\n  var ops = divider !== -1 ? [fragments.slice(0, divider).concat([fragments[divider].split(splitRegex)[0]]), [fragments[divider].split(splitRegex)[1]].concat(fragments.slice(divider + 1))] : [fragments];\n\n  // Convert the values with units to absolute pixels to allow our computations\n  ops = ops.map(function (op, index) {\n    // Most of the units rely on the orientation of the popper\n    var measurement = (index === 1 ? !useHeight : useHeight) ? 'height' : 'width';\n    var mergeWithPrevious = false;\n    return op\n    // This aggregates any `+` or `-` sign that aren't considered operators\n    // e.g.: 10 + +5 => [10, +, +5]\n    .reduce(function (a, b) {\n      if (a[a.length - 1] === '' && ['+', '-'].indexOf(b) !== -1) {\n        a[a.length - 1] = b;\n        mergeWithPrevious = true;\n        return a;\n      } else if (mergeWithPrevious) {\n        a[a.length - 1] += b;\n        mergeWithPrevious = false;\n        return a;\n      } else {\n        return a.concat(b);\n      }\n    }, [])\n    // Here we convert the string values into number values (in px)\n    .map(function (str) {\n      return toValue(str, measurement, popperOffsets, referenceOffsets);\n    });\n  });\n\n  // Loop trough the offsets arrays and execute the operations\n  ops.forEach(function (op, index) {\n    op.forEach(function (frag, index2) {\n      if (isNumeric(frag)) {\n        offsets[index] += frag * (op[index2 - 1] === '-' ? -1 : 1);\n      }\n    });\n  });\n  return offsets;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @argument {Number|String} options.offset=0\n * The offset value as described in the modifier description\n * @returns {Object} The data object, properly modified\n */\nfunction offset(data, _ref) {\n  var offset = _ref.offset;\n  var placement = data.placement,\n      _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var basePlacement = placement.split('-')[0];\n\n  var offsets = void 0;\n  if (isNumeric(+offset)) {\n    offsets = [+offset, 0];\n  } else {\n    offsets = parseOffset(offset, popper, reference, basePlacement);\n  }\n\n  if (basePlacement === 'left') {\n    popper.top += offsets[0];\n    popper.left -= offsets[1];\n  } else if (basePlacement === 'right') {\n    popper.top += offsets[0];\n    popper.left += offsets[1];\n  } else if (basePlacement === 'top') {\n    popper.left += offsets[0];\n    popper.top -= offsets[1];\n  } else if (basePlacement === 'bottom') {\n    popper.left += offsets[0];\n    popper.top += offsets[1];\n  }\n\n  data.popper = popper;\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction preventOverflow(data, options) {\n  var boundariesElement = options.boundariesElement || getOffsetParent(data.instance.popper);\n\n  // If offsetParent is the reference element, we really want to\n  // go one step up and use the next offsetParent as reference to\n  // avoid to make this modifier completely useless and look like broken\n  if (data.instance.reference === boundariesElement) {\n    boundariesElement = getOffsetParent(boundariesElement);\n  }\n\n  // NOTE: DOM access here\n  // resets the popper's position so that the document size can be calculated excluding\n  // the size of the popper element itself\n  var transformProp = getSupportedPropertyName('transform');\n  var popperStyles = data.instance.popper.style; // assignment to help minification\n  var top = popperStyles.top,\n      left = popperStyles.left,\n      transform = popperStyles[transformProp];\n\n  popperStyles.top = '';\n  popperStyles.left = '';\n  popperStyles[transformProp] = '';\n\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, boundariesElement, data.positionFixed);\n\n  // NOTE: DOM access here\n  // restores the original style properties after the offsets have been computed\n  popperStyles.top = top;\n  popperStyles.left = left;\n  popperStyles[transformProp] = transform;\n\n  options.boundaries = boundaries;\n\n  var order = options.priority;\n  var popper = data.offsets.popper;\n\n  var check = {\n    primary: function primary(placement) {\n      var value = popper[placement];\n      if (popper[placement] < boundaries[placement] && !options.escapeWithReference) {\n        value = Math.max(popper[placement], boundaries[placement]);\n      }\n      return defineProperty({}, placement, value);\n    },\n    secondary: function secondary(placement) {\n      var mainSide = placement === 'right' ? 'left' : 'top';\n      var value = popper[mainSide];\n      if (popper[placement] > boundaries[placement] && !options.escapeWithReference) {\n        value = Math.min(popper[mainSide], boundaries[placement] - (placement === 'right' ? popper.width : popper.height));\n      }\n      return defineProperty({}, mainSide, value);\n    }\n  };\n\n  order.forEach(function (placement) {\n    var side = ['left', 'top'].indexOf(placement) !== -1 ? 'primary' : 'secondary';\n    popper = _extends({}, popper, check[side](placement));\n  });\n\n  data.offsets.popper = popper;\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction shift(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var shiftvariation = placement.split('-')[1];\n\n  // if shift shiftvariation is specified, run the modifier\n  if (shiftvariation) {\n    var _data$offsets = data.offsets,\n        reference = _data$offsets.reference,\n        popper = _data$offsets.popper;\n\n    var isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    var side = isVertical ? 'left' : 'top';\n    var measurement = isVertical ? 'width' : 'height';\n\n    var shiftOffsets = {\n      start: defineProperty({}, side, reference[side]),\n      end: defineProperty({}, side, reference[side] + reference[measurement] - popper[measurement])\n    };\n\n    data.offsets.popper = _extends({}, popper, shiftOffsets[shiftvariation]);\n  }\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction hide(data) {\n  if (!isModifierRequired(data.instance.modifiers, 'hide', 'preventOverflow')) {\n    return data;\n  }\n\n  var refRect = data.offsets.reference;\n  var bound = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'preventOverflow';\n  }).boundaries;\n\n  if (refRect.bottom < bound.top || refRect.left > bound.right || refRect.top > bound.bottom || refRect.right < bound.left) {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === true) {\n      return data;\n    }\n\n    data.hide = true;\n    data.attributes['x-out-of-boundaries'] = '';\n  } else {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === false) {\n      return data;\n    }\n\n    data.hide = false;\n    data.attributes['x-out-of-boundaries'] = false;\n  }\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction inner(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var isHoriz = ['left', 'right'].indexOf(basePlacement) !== -1;\n\n  var subtractLength = ['top', 'left'].indexOf(basePlacement) === -1;\n\n  popper[isHoriz ? 'left' : 'top'] = reference[basePlacement] - (subtractLength ? popper[isHoriz ? 'width' : 'height'] : 0);\n\n  data.placement = getOppositePlacement(placement);\n  data.offsets.popper = getClientRect(popper);\n\n  return data;\n}\n\n/**\n * Modifier function, each modifier can have a function of this type assigned\n * to its `fn` property.<br />\n * These functions will be called on each update, this means that you must\n * make sure they are performant enough to avoid performance bottlenecks.\n *\n * @function ModifierFn\n * @argument {dataObject} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {dataObject} The data object, properly modified\n */\n\n/**\n * Modifiers are plugins used to alter the behavior of your poppers.<br />\n * Popper.js uses a set of 9 modifiers to provide all the basic functionalities\n * needed by the library.\n *\n * Usually you don't want to override the `order`, `fn` and `onLoad` props.\n * All the other properties are configurations that could be tweaked.\n * @namespace modifiers\n */\nvar modifiers = {\n  /**\n   * Modifier used to shift the popper on the start or end of its reference\n   * element.<br />\n   * It will read the variation of the `placement` property.<br />\n   * It can be one either `-end` or `-start`.\n   * @memberof modifiers\n   * @inner\n   */\n  shift: {\n    /** @prop {number} order=100 - Index used to define the order of execution */\n    order: 100,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: shift\n  },\n\n  /**\n   * The `offset` modifier can shift your popper on both its axis.\n   *\n   * It accepts the following units:\n   * - `px` or unit-less, interpreted as pixels\n   * - `%` or `%r`, percentage relative to the length of the reference element\n   * - `%p`, percentage relative to the length of the popper element\n   * - `vw`, CSS viewport width unit\n   * - `vh`, CSS viewport height unit\n   *\n   * For length is intended the main axis relative to the placement of the popper.<br />\n   * This means that if the placement is `top` or `bottom`, the length will be the\n   * `width`. In case of `left` or `right`, it will be the `height`.\n   *\n   * You can provide a single value (as `Number` or `String`), or a pair of values\n   * as `String` divided by a comma or one (or more) white spaces.<br />\n   * The latter is a deprecated method because it leads to confusion and will be\n   * removed in v2.<br />\n   * Additionally, it accepts additions and subtractions between different units.\n   * Note that multiplications and divisions aren't supported.\n   *\n   * Valid examples are:\n   * ```\n   * 10\n   * '10%'\n   * '10, 10'\n   * '10%, 10'\n   * '10 + 10%'\n   * '10 - 5vh + 3%'\n   * '-10px + 5vh, 5px - 6%'\n   * ```\n   * > **NB**: If you desire to apply offsets to your poppers in a way that may make them overlap\n   * > with their reference element, unfortunately, you will have to disable the `flip` modifier.\n   * > You can read more on this at this [issue](https://github.com/FezVrasta/popper.js/issues/373).\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  offset: {\n    /** @prop {number} order=200 - Index used to define the order of execution */\n    order: 200,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: offset,\n    /** @prop {Number|String} offset=0\n     * The offset value as described in the modifier description\n     */\n    offset: 0\n  },\n\n  /**\n   * Modifier used to prevent the popper from being positioned outside the boundary.\n   *\n   * A scenario exists where the reference itself is not within the boundaries.<br />\n   * We can say it has \"escaped the boundaries\" — or just \"escaped\".<br />\n   * In this case we need to decide whether the popper should either:\n   *\n   * - detach from the reference and remain \"trapped\" in the boundaries, or\n   * - if it should ignore the boundary and \"escape with its reference\"\n   *\n   * When `escapeWithReference` is set to`true` and reference is completely\n   * outside its boundaries, the popper will overflow (or completely leave)\n   * the boundaries in order to remain attached to the edge of the reference.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  preventOverflow: {\n    /** @prop {number} order=300 - Index used to define the order of execution */\n    order: 300,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: preventOverflow,\n    /**\n     * @prop {Array} [priority=['left','right','top','bottom']]\n     * Popper will try to prevent overflow following these priorities by default,\n     * then, it could overflow on the left and on top of the `boundariesElement`\n     */\n    priority: ['left', 'right', 'top', 'bottom'],\n    /**\n     * @prop {number} padding=5\n     * Amount of pixel used to define a minimum distance between the boundaries\n     * and the popper. This makes sure the popper always has a little padding\n     * between the edges of its container\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='scrollParent'\n     * Boundaries used by the modifier. Can be `scrollParent`, `window`,\n     * `viewport` or any DOM element.\n     */\n    boundariesElement: 'scrollParent'\n  },\n\n  /**\n   * Modifier used to make sure the reference and its popper stay near each other\n   * without leaving any gap between the two. Especially useful when the arrow is\n   * enabled and you want to ensure that it points to its reference element.\n   * It cares only about the first axis. You can still have poppers with margin\n   * between the popper and its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  keepTogether: {\n    /** @prop {number} order=400 - Index used to define the order of execution */\n    order: 400,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: keepTogether\n  },\n\n  /**\n   * This modifier is used to move the `arrowElement` of the popper to make\n   * sure it is positioned between the reference element and its popper element.\n   * It will read the outer size of the `arrowElement` node to detect how many\n   * pixels of conjunction are needed.\n   *\n   * It has no effect if no `arrowElement` is provided.\n   * @memberof modifiers\n   * @inner\n   */\n  arrow: {\n    /** @prop {number} order=500 - Index used to define the order of execution */\n    order: 500,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: arrow,\n    /** @prop {String|HTMLElement} element='[x-arrow]' - Selector or node used as arrow */\n    element: '[x-arrow]'\n  },\n\n  /**\n   * Modifier used to flip the popper's placement when it starts to overlap its\n   * reference element.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   *\n   * **NOTE:** this modifier will interrupt the current update cycle and will\n   * restart it if it detects the need to flip the placement.\n   * @memberof modifiers\n   * @inner\n   */\n  flip: {\n    /** @prop {number} order=600 - Index used to define the order of execution */\n    order: 600,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: flip,\n    /**\n     * @prop {String|Array} behavior='flip'\n     * The behavior used to change the popper's placement. It can be one of\n     * `flip`, `clockwise`, `counterclockwise` or an array with a list of valid\n     * placements (with optional variations)\n     */\n    behavior: 'flip',\n    /**\n     * @prop {number} padding=5\n     * The popper will flip if it hits the edges of the `boundariesElement`\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='viewport'\n     * The element which will define the boundaries of the popper position.\n     * The popper will never be placed outside of the defined boundaries\n     * (except if `keepTogether` is enabled)\n     */\n    boundariesElement: 'viewport',\n    /**\n     * @prop {Boolean} flipVariations=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the reference element overlaps its boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariations: false,\n    /**\n     * @prop {Boolean} flipVariationsByContent=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the popper element overlaps its reference boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariationsByContent: false\n  },\n\n  /**\n   * Modifier used to make the popper flow toward the inner of the reference element.\n   * By default, when this modifier is disabled, the popper will be placed outside\n   * the reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  inner: {\n    /** @prop {number} order=700 - Index used to define the order of execution */\n    order: 700,\n    /** @prop {Boolean} enabled=false - Whether the modifier is enabled or not */\n    enabled: false,\n    /** @prop {ModifierFn} */\n    fn: inner\n  },\n\n  /**\n   * Modifier used to hide the popper when its reference element is outside of the\n   * popper boundaries. It will set a `x-out-of-boundaries` attribute which can\n   * be used to hide with a CSS selector the popper when its reference is\n   * out of boundaries.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * @memberof modifiers\n   * @inner\n   */\n  hide: {\n    /** @prop {number} order=800 - Index used to define the order of execution */\n    order: 800,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: hide\n  },\n\n  /**\n   * Computes the style that will be applied to the popper element to gets\n   * properly positioned.\n   *\n   * Note that this modifier will not touch the DOM, it just prepares the styles\n   * so that `applyStyle` modifier can apply it. This separation is useful\n   * in case you need to replace `applyStyle` with a custom implementation.\n   *\n   * This modifier has `850` as `order` value to maintain backward compatibility\n   * with previous versions of Popper.js. Expect the modifiers ordering method\n   * to change in future major versions of the library.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  computeStyle: {\n    /** @prop {number} order=850 - Index used to define the order of execution */\n    order: 850,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: computeStyle,\n    /**\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: true,\n    /**\n     * @prop {string} [x='bottom']\n     * Where to anchor the X axis (`bottom` or `top`). AKA X offset origin.\n     * Change this if your popper should grow in a direction different from `bottom`\n     */\n    x: 'bottom',\n    /**\n     * @prop {string} [x='left']\n     * Where to anchor the Y axis (`left` or `right`). AKA Y offset origin.\n     * Change this if your popper should grow in a direction different from `right`\n     */\n    y: 'right'\n  },\n\n  /**\n   * Applies the computed styles to the popper element.\n   *\n   * All the DOM manipulations are limited to this modifier. This is useful in case\n   * you want to integrate Popper.js inside a framework or view library and you\n   * want to delegate all the DOM manipulations to it.\n   *\n   * Note that if you disable this modifier, you must make sure the popper element\n   * has its position set to `absolute` before Popper.js can do its work!\n   *\n   * Just disable this modifier and define your own to achieve the desired effect.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  applyStyle: {\n    /** @prop {number} order=900 - Index used to define the order of execution */\n    order: 900,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: applyStyle,\n    /** @prop {Function} */\n    onLoad: applyStyleOnLoad,\n    /**\n     * @deprecated since version 1.10.0, the property moved to `computeStyle` modifier\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: undefined\n  }\n};\n\n/**\n * The `dataObject` is an object containing all the information used by Popper.js.\n * This object is passed to modifiers and to the `onCreate` and `onUpdate` callbacks.\n * @name dataObject\n * @property {Object} data.instance The Popper.js instance\n * @property {String} data.placement Placement applied to popper\n * @property {String} data.originalPlacement Placement originally defined on init\n * @property {Boolean} data.flipped True if popper has been flipped by flip modifier\n * @property {Boolean} data.hide True if the reference element is out of boundaries, useful to know when to hide the popper\n * @property {HTMLElement} data.arrowElement Node used as arrow by arrow modifier\n * @property {Object} data.styles Any CSS property defined here will be applied to the popper. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.arrowStyles Any CSS property defined here will be applied to the popper arrow. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.boundaries Offsets of the popper boundaries\n * @property {Object} data.offsets The measurements of popper, reference and arrow elements\n * @property {Object} data.offsets.popper `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.reference `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.arrow] `top` and `left` offsets, only one of them will be different from 0\n */\n\n/**\n * Default options provided to Popper.js constructor.<br />\n * These can be overridden using the `options` argument of Popper.js.<br />\n * To override an option, simply pass an object with the same\n * structure of the `options` object, as the 3rd argument. For example:\n * ```\n * new Popper(ref, pop, {\n *   modifiers: {\n *     preventOverflow: { enabled: false }\n *   }\n * })\n * ```\n * @type {Object}\n * @static\n * @memberof Popper\n */\nvar Defaults = {\n  /**\n   * Popper's placement.\n   * @prop {Popper.placements} placement='bottom'\n   */\n  placement: 'bottom',\n\n  /**\n   * Set this to true if you want popper to position it self in 'fixed' mode\n   * @prop {Boolean} positionFixed=false\n   */\n  positionFixed: false,\n\n  /**\n   * Whether events (resize, scroll) are initially enabled.\n   * @prop {Boolean} eventsEnabled=true\n   */\n  eventsEnabled: true,\n\n  /**\n   * Set to true if you want to automatically remove the popper when\n   * you call the `destroy` method.\n   * @prop {Boolean} removeOnDestroy=false\n   */\n  removeOnDestroy: false,\n\n  /**\n   * Callback called when the popper is created.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onCreate}\n   */\n  onCreate: function onCreate() {},\n\n  /**\n   * Callback called when the popper is updated. This callback is not called\n   * on the initialization/creation of the popper, but only on subsequent\n   * updates.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onUpdate}\n   */\n  onUpdate: function onUpdate() {},\n\n  /**\n   * List of modifiers used to modify the offsets before they are applied to the popper.\n   * They provide most of the functionalities of Popper.js.\n   * @prop {modifiers}\n   */\n  modifiers: modifiers\n};\n\n/**\n * @callback onCreate\n * @param {dataObject} data\n */\n\n/**\n * @callback onUpdate\n * @param {dataObject} data\n */\n\n// Utils\n// Methods\nvar Popper = function () {\n  /**\n   * Creates a new Popper.js instance.\n   * @class Popper\n   * @param {Element|referenceObject} reference - The reference element used to position the popper\n   * @param {Element} popper - The HTML / XML element used as the popper\n   * @param {Object} options - Your custom options to override the ones defined in [Defaults](#defaults)\n   * @return {Object} instance - The generated Popper.js instance\n   */\n  function Popper(reference, popper) {\n    var _this = this;\n\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    classCallCheck(this, Popper);\n\n    this.scheduleUpdate = function () {\n      return requestAnimationFrame(_this.update);\n    };\n\n    // make update() debounced, so that it only runs at most once-per-tick\n    this.update = debounce(this.update.bind(this));\n\n    // with {} we create a new object with the options inside it\n    this.options = _extends({}, Popper.Defaults, options);\n\n    // init state\n    this.state = {\n      isDestroyed: false,\n      isCreated: false,\n      scrollParents: []\n    };\n\n    // get reference and popper elements (allow jQuery wrappers)\n    this.reference = reference && reference.jquery ? reference[0] : reference;\n    this.popper = popper && popper.jquery ? popper[0] : popper;\n\n    // Deep merge modifiers options\n    this.options.modifiers = {};\n    Object.keys(_extends({}, Popper.Defaults.modifiers, options.modifiers)).forEach(function (name) {\n      _this.options.modifiers[name] = _extends({}, Popper.Defaults.modifiers[name] || {}, options.modifiers ? options.modifiers[name] : {});\n    });\n\n    // Refactoring modifiers' list (Object => Array)\n    this.modifiers = Object.keys(this.options.modifiers).map(function (name) {\n      return _extends({\n        name: name\n      }, _this.options.modifiers[name]);\n    })\n    // sort the modifiers by order\n    .sort(function (a, b) {\n      return a.order - b.order;\n    });\n\n    // modifiers have the ability to execute arbitrary code when Popper.js get inited\n    // such code is executed in the same order of its modifier\n    // they could add new properties to their options configuration\n    // BE AWARE: don't add options to `options.modifiers.name` but to `modifierOptions`!\n    this.modifiers.forEach(function (modifierOptions) {\n      if (modifierOptions.enabled && isFunction(modifierOptions.onLoad)) {\n        modifierOptions.onLoad(_this.reference, _this.popper, _this.options, modifierOptions, _this.state);\n      }\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n\n    var eventsEnabled = this.options.eventsEnabled;\n    if (eventsEnabled) {\n      // setup event listeners, they will take care of update the position in specific situations\n      this.enableEventListeners();\n    }\n\n    this.state.eventsEnabled = eventsEnabled;\n  }\n\n  // We can't use class properties because they don't get listed in the\n  // class prototype and break stuff like Sinon stubs\n\n\n  createClass(Popper, [{\n    key: 'update',\n    value: function update$$1() {\n      return update.call(this);\n    }\n  }, {\n    key: 'destroy',\n    value: function destroy$$1() {\n      return destroy.call(this);\n    }\n  }, {\n    key: 'enableEventListeners',\n    value: function enableEventListeners$$1() {\n      return enableEventListeners.call(this);\n    }\n  }, {\n    key: 'disableEventListeners',\n    value: function disableEventListeners$$1() {\n      return disableEventListeners.call(this);\n    }\n\n    /**\n     * Schedules an update. It will run on the next UI update available.\n     * @method scheduleUpdate\n     * @memberof Popper\n     */\n\n\n    /**\n     * Collection of utilities useful when writing custom modifiers.\n     * Starting from version 1.7, this method is available only if you\n     * include `popper-utils.js` before `popper.js`.\n     *\n     * **DEPRECATION**: This way to access PopperUtils is deprecated\n     * and will be removed in v2! Use the PopperUtils module directly instead.\n     * Due to the high instability of the methods contained in Utils, we can't\n     * guarantee them to follow semver. Use them at your own risk!\n     * @static\n     * @private\n     * @type {Object}\n     * @deprecated since version 1.8\n     * @member Utils\n     * @memberof Popper\n     */\n\n  }]);\n  return Popper;\n}();\n\n/**\n * The `referenceObject` is an object that provides an interface compatible with Popper.js\n * and lets you use it as replacement of a real DOM node.<br />\n * You can use this method to position a popper relatively to a set of coordinates\n * in case you don't have a DOM node to use as reference.\n *\n * ```\n * new Popper(referenceObject, popperNode);\n * ```\n *\n * NB: This feature isn't supported in Internet Explorer 10.\n * @name referenceObject\n * @property {Function} data.getBoundingClientRect\n * A function that returns a set of coordinates compatible with the native `getBoundingClientRect` method.\n * @property {number} data.clientWidth\n * An ES6 getter that will return the width of the virtual reference element.\n * @property {number} data.clientHeight\n * An ES6 getter that will return the height of the virtual reference element.\n */\n\n\nPopper.Utils = (typeof window !== 'undefined' ? window : global).PopperUtils;\nPopper.placements = placements;\nPopper.Defaults = Defaults;\n\nexport default Popper;\n//# sourceMappingURL=popper.js.map\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.4.1): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Popper from 'popper.js'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME                     = 'dropdown'\nconst VERSION                  = '4.4.1'\nconst DATA_KEY                 = 'bs.dropdown'\nconst EVENT_KEY                = `.${DATA_KEY}`\nconst DATA_API_KEY             = '.data-api'\nconst JQUERY_NO_CONFLICT       = $.fn[NAME]\nconst ESCAPE_KEYCODE           = 27 // KeyboardEvent.which value for Escape (Esc) key\nconst SPACE_KEYCODE            = 32 // KeyboardEvent.which value for space key\nconst TAB_KEYCODE              = 9 // KeyboardEvent.which value for tab key\nconst ARROW_UP_KEYCODE         = 38 // KeyboardEvent.which value for up arrow key\nconst ARROW_DOWN_KEYCODE       = 40 // KeyboardEvent.which value for down arrow key\nconst RIGHT_MOUSE_BUTTON_WHICH = 3 // MouseEvent.which value for the right button (assuming a right-handed mouse)\nconst REGEXP_KEYDOWN           = new RegExp(`${ARROW_UP_KEYCODE}|${ARROW_DOWN_KEYCODE}|${ESCAPE_KEYCODE}`)\n\nconst Event = {\n  HIDE             : `hide${EVENT_KEY}`,\n  HIDDEN           : `hidden${EVENT_KEY}`,\n  SHOW             : `show${EVENT_KEY}`,\n  SHOWN            : `shown${EVENT_KEY}`,\n  CLICK            : `click${EVENT_KEY}`,\n  CLICK_DATA_API   : `click${EVENT_KEY}${DATA_API_KEY}`,\n  KEYDOWN_DATA_API : `keydown${EVENT_KEY}${DATA_API_KEY}`,\n  KEYUP_DATA_API   : `keyup${EVENT_KEY}${DATA_API_KEY}`\n}\n\nconst ClassName = {\n  DISABLED        : 'disabled',\n  SHOW            : 'show',\n  DROPUP          : 'dropup',\n  DROPRIGHT       : 'dropright',\n  DROPLEFT        : 'dropleft',\n  MENURIGHT       : 'dropdown-menu-right',\n  MENULEFT        : 'dropdown-menu-left',\n  POSITION_STATIC : 'position-static'\n}\n\nconst Selector = {\n  DATA_TOGGLE   : '[data-toggle=\"dropdown\"]',\n  FORM_CHILD    : '.dropdown form',\n  MENU          : '.dropdown-menu',\n  NAVBAR_NAV    : '.navbar-nav',\n  VISIBLE_ITEMS : '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n}\n\nconst AttachmentMap = {\n  TOP       : 'top-start',\n  TOPEND    : 'top-end',\n  BOTTOM    : 'bottom-start',\n  BOTTOMEND : 'bottom-end',\n  RIGHT     : 'right-start',\n  RIGHTEND  : 'right-end',\n  LEFT      : 'left-start',\n  LEFTEND   : 'left-end'\n}\n\nconst Default = {\n  offset       : 0,\n  flip         : true,\n  boundary     : 'scrollParent',\n  reference    : 'toggle',\n  display      : 'dynamic',\n  popperConfig : null\n}\n\nconst DefaultType = {\n  offset       : '(number|string|function)',\n  flip         : 'boolean',\n  boundary     : '(string|element)',\n  reference    : '(string|element)',\n  display      : 'string',\n  popperConfig : '(null|object)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown {\n  constructor(element, config) {\n    this._element  = element\n    this._popper   = null\n    this._config   = this._getConfig(config)\n    this._menu     = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || $(this._element).hasClass(ClassName.DISABLED)) {\n      return\n    }\n\n    const isActive = $(this._menu).hasClass(ClassName.SHOW)\n\n    Dropdown._clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show(true)\n  }\n\n  show(usePopper = false) {\n    if (this._element.disabled || $(this._element).hasClass(ClassName.DISABLED) || $(this._menu).hasClass(ClassName.SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n    const showEvent = $.Event(Event.SHOW, relatedTarget)\n    const parent = Dropdown._getParentFromElement(this._element)\n\n    $(parent).trigger(showEvent)\n\n    if (showEvent.isDefaultPrevented()) {\n      return\n    }\n\n    // Disable totally Popper.js for Dropdown in Navbar\n    if (!this._inNavbar && usePopper) {\n      /**\n       * Check for Popper dependency\n       * Popper - https://popper.js.org\n       */\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper.js (https://popper.js.org/)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (Util.isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      }\n\n      // If boundary is not `scrollParent`, then set position to `static`\n      // to allow the menu to \"escape\" the scroll parent's boundaries\n      // https://github.com/twbs/bootstrap/issues/24251\n      if (this._config.boundary !== 'scrollParent') {\n        $(parent).addClass(ClassName.POSITION_STATIC)\n      }\n      this._popper = new Popper(referenceElement, this._menu, this._getPopperConfig())\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n        $(parent).closest(Selector.NAVBAR_NAV).length === 0) {\n      $(document.body).children().on('mouseover', null, $.noop)\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    $(this._menu).toggleClass(ClassName.SHOW)\n    $(parent)\n      .toggleClass(ClassName.SHOW)\n      .trigger($.Event(Event.SHOWN, relatedTarget))\n  }\n\n  hide() {\n    if (this._element.disabled || $(this._element).hasClass(ClassName.DISABLED) || !$(this._menu).hasClass(ClassName.SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n    const hideEvent = $.Event(Event.HIDE, relatedTarget)\n    const parent = Dropdown._getParentFromElement(this._element)\n\n    $(parent).trigger(hideEvent)\n\n    if (hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    $(this._menu).toggleClass(ClassName.SHOW)\n    $(parent)\n      .toggleClass(ClassName.SHOW)\n      .trigger($.Event(Event.HIDDEN, relatedTarget))\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    $(this._element).off(EVENT_KEY)\n    this._element = null\n    this._menu = null\n    if (this._popper !== null) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    $(this._element).on(Event.CLICK, (event) => {\n      event.preventDefault()\n      event.stopPropagation()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...$(this._element).data(),\n      ...config\n    }\n\n    Util.typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _getMenuElement() {\n    if (!this._menu) {\n      const parent = Dropdown._getParentFromElement(this._element)\n\n      if (parent) {\n        this._menu = parent.querySelector(Selector.MENU)\n      }\n    }\n    return this._menu\n  }\n\n  _getPlacement() {\n    const $parentDropdown = $(this._element.parentNode)\n    let placement = AttachmentMap.BOTTOM\n\n    // Handle dropup\n    if ($parentDropdown.hasClass(ClassName.DROPUP)) {\n      placement = AttachmentMap.TOP\n      if ($(this._menu).hasClass(ClassName.MENURIGHT)) {\n        placement = AttachmentMap.TOPEND\n      }\n    } else if ($parentDropdown.hasClass(ClassName.DROPRIGHT)) {\n      placement = AttachmentMap.RIGHT\n    } else if ($parentDropdown.hasClass(ClassName.DROPLEFT)) {\n      placement = AttachmentMap.LEFT\n    } else if ($(this._menu).hasClass(ClassName.MENURIGHT)) {\n      placement = AttachmentMap.BOTTOMEND\n    }\n    return placement\n  }\n\n  _detectNavbar() {\n    return $(this._element).closest('.navbar').length > 0\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this._config.offset === 'function') {\n      offset.fn = (data) => {\n        data.offsets = {\n          ...data.offsets,\n          ...this._config.offset(data.offsets, this._element) || {}\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this._config.offset\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const popperConfig = {\n      placement: this._getPlacement(),\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          enabled: this._config.flip\n        },\n        preventOverflow: {\n          boundariesElement: this._config.boundary\n        }\n      }\n    }\n\n    // Disable Popper.js if we have a static display\n    if (this._config.display === 'static') {\n      popperConfig.modifiers.applyStyle = {\n        enabled: false\n      }\n    }\n\n    return {\n      ...popperConfig,\n      ...this._config.popperConfig\n    }\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data) {\n        data = new Dropdown(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n        data[config]()\n      }\n    })\n  }\n\n  static _clearMenus(event) {\n    if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH ||\n      event.type === 'keyup' && event.which !== TAB_KEYCODE)) {\n      return\n    }\n\n    const toggles = [].slice.call(document.querySelectorAll(Selector.DATA_TOGGLE))\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const parent = Dropdown._getParentFromElement(toggles[i])\n      const context = $(toggles[i]).data(DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!$(parent).hasClass(ClassName.SHOW)) {\n        continue\n      }\n\n      if (event && (event.type === 'click' &&\n          /input|textarea/i.test(event.target.tagName) || event.type === 'keyup' && event.which === TAB_KEYCODE) &&\n          $.contains(parent, event.target)) {\n        continue\n      }\n\n      const hideEvent = $.Event(Event.HIDE, relatedTarget)\n      $(parent).trigger(hideEvent)\n      if (hideEvent.isDefaultPrevented()) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        $(document.body).children().off('mouseover', null, $.noop)\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      $(dropdownMenu).removeClass(ClassName.SHOW)\n      $(parent)\n        .removeClass(ClassName.SHOW)\n        .trigger($.Event(Event.HIDDEN, relatedTarget))\n    }\n  }\n\n  static _getParentFromElement(element) {\n    let parent\n    const selector = Util.getSelectorFromElement(element)\n\n    if (selector) {\n      parent = document.querySelector(selector)\n    }\n\n    return parent || element.parentNode\n  }\n\n  // eslint-disable-next-line complexity\n  static _dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName)\n      ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE &&\n      (event.which !== ARROW_DOWN_KEYCODE && event.which !== ARROW_UP_KEYCODE ||\n        $(event.target).closest(Selector.MENU).length) : !REGEXP_KEYDOWN.test(event.which)) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (this.disabled || $(this).hasClass(ClassName.DISABLED)) {\n      return\n    }\n\n    const parent   = Dropdown._getParentFromElement(this)\n    const isActive = $(parent).hasClass(ClassName.SHOW)\n\n    if (!isActive && event.which === ESCAPE_KEYCODE) {\n      return\n    }\n\n    if (!isActive || isActive && (event.which === ESCAPE_KEYCODE || event.which === SPACE_KEYCODE)) {\n      if (event.which === ESCAPE_KEYCODE) {\n        const toggle = parent.querySelector(Selector.DATA_TOGGLE)\n        $(toggle).trigger('focus')\n      }\n\n      $(this).trigger('click')\n      return\n    }\n\n    const items = [].slice.call(parent.querySelectorAll(Selector.VISIBLE_ITEMS))\n      .filter((item) => $(item).is(':visible'))\n\n    if (items.length === 0) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    if (event.which === ARROW_UP_KEYCODE && index > 0) { // Up\n      index--\n    }\n\n    if (event.which === ARROW_DOWN_KEYCODE && index < items.length - 1) { // Down\n      index++\n    }\n\n    if (index < 0) {\n      index = 0\n    }\n\n    items[index].focus()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document)\n  .on(Event.KEYDOWN_DATA_API, Selector.DATA_TOGGLE, Dropdown._dataApiKeydownHandler)\n  .on(Event.KEYDOWN_DATA_API, Selector.MENU, Dropdown._dataApiKeydownHandler)\n  .on(`${Event.CLICK_DATA_API} ${Event.KEYUP_DATA_API}`, Dropdown._clearMenus)\n  .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n    event.preventDefault()\n    event.stopPropagation()\n    Dropdown._jQueryInterface.call($(this), 'toggle')\n  })\n  .on(Event.CLICK_DATA_API, Selector.FORM_CHILD, (e) => {\n    e.stopPropagation()\n  })\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Dropdown._jQueryInterface\n$.fn[NAME].Constructor = Dropdown\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Dropdown._jQueryInterface\n}\n\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.4.1): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME               = 'modal'\nconst VERSION            = '4.4.1'\nconst DATA_KEY           = 'bs.modal'\nconst EVENT_KEY          = `.${DATA_KEY}`\nconst DATA_API_KEY       = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\nconst ESCAPE_KEYCODE     = 27 // KeyboardEvent.which value for Escape (Esc) key\n\nconst Default = {\n  backdrop : true,\n  keyboard : true,\n  focus    : true,\n  show     : true\n}\n\nconst DefaultType = {\n  backdrop : '(boolean|string)',\n  keyboard : 'boolean',\n  focus    : 'boolean',\n  show     : 'boolean'\n}\n\nconst Event = {\n  HIDE              : `hide${EVENT_KEY}`,\n  HIDE_PREVENTED    : `hidePrevented${EVENT_KEY}`,\n  HIDDEN            : `hidden${EVENT_KEY}`,\n  SHOW              : `show${EVENT_KEY}`,\n  SHOWN             : `shown${EVENT_KEY}`,\n  FOCUSIN           : `focusin${EVENT_KEY}`,\n  RESIZE            : `resize${EVENT_KEY}`,\n  CLICK_DISMISS     : `click.dismiss${EVENT_KEY}`,\n  KEYDOWN_DISMISS   : `keydown.dismiss${EVENT_KEY}`,\n  MOUSEUP_DISMISS   : `mouseup.dismiss${EVENT_KEY}`,\n  MOUSEDOWN_DISMISS : `mousedown.dismiss${EVENT_KEY}`,\n  CLICK_DATA_API    : `click${EVENT_KEY}${DATA_API_KEY}`\n}\n\nconst ClassName = {\n  SCROLLABLE         : 'modal-dialog-scrollable',\n  SCROLLBAR_MEASURER : 'modal-scrollbar-measure',\n  BACKDROP           : 'modal-backdrop',\n  OPEN               : 'modal-open',\n  FADE               : 'fade',\n  SHOW               : 'show',\n  STATIC             : 'modal-static'\n}\n\nconst Selector = {\n  DIALOG         : '.modal-dialog',\n  MODAL_BODY     : '.modal-body',\n  DATA_TOGGLE    : '[data-toggle=\"modal\"]',\n  DATA_DISMISS   : '[data-dismiss=\"modal\"]',\n  FIXED_CONTENT  : '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top',\n  STICKY_CONTENT : '.sticky-top'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal {\n  constructor(element, config) {\n    this._config              = this._getConfig(config)\n    this._element             = element\n    this._dialog              = element.querySelector(Selector.DIALOG)\n    this._backdrop            = null\n    this._isShown             = false\n    this._isBodyOverflowing   = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning     = false\n    this._scrollbarWidth      = 0\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if ($(this._element).hasClass(ClassName.FADE)) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = $.Event(Event.SHOW, {\n      relatedTarget\n    })\n\n    $(this._element).trigger(showEvent)\n\n    if (this._isShown || showEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    $(this._element).on(\n      Event.CLICK_DISMISS,\n      Selector.DATA_DISMISS,\n      (event) => this.hide(event)\n    )\n\n    $(this._dialog).on(Event.MOUSEDOWN_DISMISS, () => {\n      $(this._element).one(Event.MOUSEUP_DISMISS, (event) => {\n        if ($(event.target).is(this._element)) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = $.Event(Event.HIDE)\n\n    $(this._element).trigger(hideEvent)\n\n    if (!this._isShown || hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._isShown = false\n    const transition = $(this._element).hasClass(ClassName.FADE)\n\n    if (transition) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    $(document).off(Event.FOCUSIN)\n\n    $(this._element).removeClass(ClassName.SHOW)\n\n    $(this._element).off(Event.CLICK_DISMISS)\n    $(this._dialog).off(Event.MOUSEDOWN_DISMISS)\n\n\n    if (transition) {\n      const transitionDuration  = Util.getTransitionDurationFromElement(this._element)\n\n      $(this._element)\n        .one(Util.TRANSITION_END, (event) => this._hideModal(event))\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach((htmlElement) => $(htmlElement).off(EVENT_KEY))\n\n    /**\n     * `document` has 2 events `Event.FOCUSIN` and `Event.CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `Event.CLICK_DATA_API` event that should remain\n     */\n    $(document).off(Event.FOCUSIN)\n\n    $.removeData(this._element, DATA_KEY)\n\n    this._config              = null\n    this._element             = null\n    this._dialog              = null\n    this._backdrop            = null\n    this._isShown             = null\n    this._isBodyOverflowing   = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning     = null\n    this._scrollbarWidth      = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    Util.typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _triggerBackdropTransition() {\n    if (this._config.backdrop === 'static') {\n      const hideEventPrevented = $.Event(Event.HIDE_PREVENTED)\n\n      $(this._element).trigger(hideEventPrevented)\n      if (hideEventPrevented.defaultPrevented) {\n        return\n      }\n\n      this._element.classList.add(ClassName.STATIC)\n\n      const modalTransitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n      $(this._element).one(Util.TRANSITION_END, () => {\n        this._element.classList.remove(ClassName.STATIC)\n      })\n        .emulateTransitionEnd(modalTransitionDuration)\n      this._element.focus()\n    } else {\n      this.hide()\n    }\n  }\n\n  _showElement(relatedTarget) {\n    const transition = $(this._element).hasClass(ClassName.FADE)\n    const modalBody = this._dialog ? this._dialog.querySelector(Selector.MODAL_BODY) : null\n\n    if (!this._element.parentNode ||\n        this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n\n    if ($(this._dialog).hasClass(ClassName.SCROLLABLE) && modalBody) {\n      modalBody.scrollTop = 0\n    } else {\n      this._element.scrollTop = 0\n    }\n\n    if (transition) {\n      Util.reflow(this._element)\n    }\n\n    $(this._element).addClass(ClassName.SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const shownEvent = $.Event(Event.SHOWN, {\n      relatedTarget\n    })\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n      this._isTransitioning = false\n      $(this._element).trigger(shownEvent)\n    }\n\n    if (transition) {\n      const transitionDuration  = Util.getTransitionDurationFromElement(this._dialog)\n\n      $(this._dialog)\n        .one(Util.TRANSITION_END, transitionComplete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    $(document)\n      .off(Event.FOCUSIN) // Guard against infinite focus loop\n      .on(Event.FOCUSIN, (event) => {\n        if (document !== event.target &&\n            this._element !== event.target &&\n            $(this._element).has(event.target).length === 0) {\n          this._element.focus()\n        }\n      })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown && this._config.keyboard) {\n      $(this._element).on(Event.KEYDOWN_DISMISS, (event) => {\n        if (event.which === ESCAPE_KEYCODE) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else if (!this._isShown) {\n      $(this._element).off(Event.KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      $(window).on(Event.RESIZE, (event) => this.handleUpdate(event))\n    } else {\n      $(window).off(Event.RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      $(document.body).removeClass(ClassName.OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      $(this._element).trigger(Event.HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    if (this._backdrop) {\n      $(this._backdrop).remove()\n      this._backdrop = null\n    }\n  }\n\n  _showBackdrop(callback) {\n    const animate = $(this._element).hasClass(ClassName.FADE)\n      ? ClassName.FADE : ''\n\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = ClassName.BACKDROP\n\n      if (animate) {\n        this._backdrop.classList.add(animate)\n      }\n\n      $(this._backdrop).appendTo(document.body)\n\n      $(this._element).on(Event.CLICK_DISMISS, (event) => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        this._triggerBackdropTransition()\n      })\n\n      if (animate) {\n        Util.reflow(this._backdrop)\n      }\n\n      $(this._backdrop).addClass(ClassName.SHOW)\n\n      if (!callback) {\n        return\n      }\n\n      if (!animate) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = Util.getTransitionDurationFromElement(this._backdrop)\n\n      $(this._backdrop)\n        .one(Util.TRANSITION_END, callback)\n        .emulateTransitionEnd(backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      $(this._backdrop).removeClass(ClassName.SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        if (callback) {\n          callback()\n        }\n      }\n\n      if ($(this._element).hasClass(ClassName.FADE)) {\n        const backdropTransitionDuration = Util.getTransitionDurationFromElement(this._backdrop)\n\n        $(this._backdrop)\n          .one(Util.TRANSITION_END, callbackRemove)\n          .emulateTransitionEnd(backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else if (callback) {\n      callback()\n    }\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // todo (fat): these should probably be refactored out of modal.js\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing =\n      this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!this._isBodyOverflowing && isModalOverflowing) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if (this._isBodyOverflowing && !isModalOverflowing) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = rect.left + rect.right < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n      //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n      const fixedContent = [].slice.call(document.querySelectorAll(Selector.FIXED_CONTENT))\n      const stickyContent = [].slice.call(document.querySelectorAll(Selector.STICKY_CONTENT))\n\n      // Adjust fixed content padding\n      $(fixedContent).each((index, element) => {\n        const actualPadding = element.style.paddingRight\n        const calculatedPadding = $(element).css('padding-right')\n        $(element)\n          .data('padding-right', actualPadding)\n          .css('padding-right', `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`)\n      })\n\n      // Adjust sticky content margin\n      $(stickyContent).each((index, element) => {\n        const actualMargin = element.style.marginRight\n        const calculatedMargin = $(element).css('margin-right')\n        $(element)\n          .data('margin-right', actualMargin)\n          .css('margin-right', `${parseFloat(calculatedMargin) - this._scrollbarWidth}px`)\n      })\n\n      // Adjust body padding\n      const actualPadding = document.body.style.paddingRight\n      const calculatedPadding = $(document.body).css('padding-right')\n      $(document.body)\n        .data('padding-right', actualPadding)\n        .css('padding-right', `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`)\n    }\n\n    $(document.body).addClass(ClassName.OPEN)\n  }\n\n  _resetScrollbar() {\n    // Restore fixed content padding\n    const fixedContent = [].slice.call(document.querySelectorAll(Selector.FIXED_CONTENT))\n    $(fixedContent).each((index, element) => {\n      const padding = $(element).data('padding-right')\n      $(element).removeData('padding-right')\n      element.style.paddingRight = padding ? padding : ''\n    })\n\n    // Restore sticky content\n    const elements = [].slice.call(document.querySelectorAll(`${Selector.STICKY_CONTENT}`))\n    $(elements).each((index, element) => {\n      const margin = $(element).data('margin-right')\n      if (typeof margin !== 'undefined') {\n        $(element).css('margin-right', margin).removeData('margin-right')\n      }\n    })\n\n    // Restore body padding\n    const padding = $(document.body).data('padding-right')\n    $(document.body).removeData('padding-right')\n    document.body.style.paddingRight = padding ? padding : ''\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = ClassName.SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static _jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = {\n        ...Default,\n        ...$(this).data(),\n        ...typeof config === 'object' && config ? config : {}\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n        data[config](relatedTarget)\n      } else if (_config.show) {\n        data.show(relatedTarget)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document).on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n  let target\n  const selector = Util.getSelectorFromElement(this)\n\n  if (selector) {\n    target = document.querySelector(selector)\n  }\n\n  const config = $(target).data(DATA_KEY)\n    ? 'toggle' : {\n      ...$(target).data(),\n      ...$(this).data()\n    }\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  const $target = $(target).one(Event.SHOW, (showEvent) => {\n    if (showEvent.isDefaultPrevented()) {\n      // Only register focus restorer if modal will actually get shown\n      return\n    }\n\n    $target.one(Event.HIDDEN, () => {\n      if ($(this).is(':visible')) {\n        this.focus()\n      }\n    })\n  })\n\n  Modal._jQueryInterface.call($(target), config, this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Modal._jQueryInterface\n$.fn[NAME].Constructor = Modal\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Modal._jQueryInterface\n}\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.4.1): tools/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = [\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n]\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\nexport const DefaultWhitelist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^&:/?#]*(?:[/?#]|$))/gi\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+/]+=*$/i\n\nfunction allowedAttribute(attr, allowedAttributeList) {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.indexOf(attrName) !== -1) {\n    if (uriAttrs.indexOf(attrName) !== -1) {\n      return Boolean(attr.nodeValue.match(SAFE_URL_PATTERN) || attr.nodeValue.match(DATA_URL_PATTERN))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter((attrRegex) => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, l = regExp.length; i < l; i++) {\n    if (attrName.match(regExp[i])) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport function sanitizeHtml(unsafeHtml, whiteList, sanitizeFn) {\n  if (unsafeHtml.length === 0) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const whitelistKeys = Object.keys(whiteList)\n  const elements = [].slice.call(createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].slice.call(el.attributes)\n    const whitelistedAttributes = [].concat(whiteList['*'] || [], whiteList[elName] || [])\n\n    attributeList.forEach((attr) => {\n      if (!allowedAttribute(attr, whitelistedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.4.1): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  DefaultWhitelist,\n  sanitizeHtml\n} from './tools/sanitizer'\nimport $ from 'jquery'\nimport Popper from 'popper.js'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME                  = 'tooltip'\nconst VERSION               = '4.4.1'\nconst DATA_KEY              = 'bs.tooltip'\nconst EVENT_KEY             = `.${DATA_KEY}`\nconst JQUERY_NO_CONFLICT    = $.fn[NAME]\nconst CLASS_PREFIX          = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX    = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = ['sanitize', 'whiteList', 'sanitizeFn']\n\nconst DefaultType = {\n  animation         : 'boolean',\n  template          : 'string',\n  title             : '(string|element|function)',\n  trigger           : 'string',\n  delay             : '(number|object)',\n  html              : 'boolean',\n  selector          : '(string|boolean)',\n  placement         : '(string|function)',\n  offset            : '(number|string|function)',\n  container         : '(string|element|boolean)',\n  fallbackPlacement : '(string|array)',\n  boundary          : '(string|element)',\n  sanitize          : 'boolean',\n  sanitizeFn        : '(null|function)',\n  whiteList         : 'object',\n  popperConfig      : '(null|object)'\n}\n\nconst AttachmentMap = {\n  AUTO   : 'auto',\n  TOP    : 'top',\n  RIGHT  : 'right',\n  BOTTOM : 'bottom',\n  LEFT   : 'left'\n}\n\nconst Default = {\n  animation         : true,\n  template          : '<div class=\"tooltip\" role=\"tooltip\">' +\n                    '<div class=\"arrow\"></div>' +\n                    '<div class=\"tooltip-inner\"></div></div>',\n  trigger           : 'hover focus',\n  title             : '',\n  delay             : 0,\n  html              : false,\n  selector          : false,\n  placement         : 'top',\n  offset            : 0,\n  container         : false,\n  fallbackPlacement : 'flip',\n  boundary          : 'scrollParent',\n  sanitize          : true,\n  sanitizeFn        : null,\n  whiteList         : DefaultWhitelist,\n  popperConfig      : null\n}\n\nconst HoverState = {\n  SHOW : 'show',\n  OUT  : 'out'\n}\n\nconst Event = {\n  HIDE       : `hide${EVENT_KEY}`,\n  HIDDEN     : `hidden${EVENT_KEY}`,\n  SHOW       : `show${EVENT_KEY}`,\n  SHOWN      : `shown${EVENT_KEY}`,\n  INSERTED   : `inserted${EVENT_KEY}`,\n  CLICK      : `click${EVENT_KEY}`,\n  FOCUSIN    : `focusin${EVENT_KEY}`,\n  FOCUSOUT   : `focusout${EVENT_KEY}`,\n  MOUSEENTER : `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE : `mouseleave${EVENT_KEY}`\n}\n\nconst ClassName = {\n  FADE : 'fade',\n  SHOW : 'show'\n}\n\nconst Selector = {\n  TOOLTIP       : '.tooltip',\n  TOOLTIP_INNER : '.tooltip-inner',\n  ARROW         : '.arrow'\n}\n\nconst Trigger = {\n  HOVER  : 'hover',\n  FOCUS  : 'focus',\n  CLICK  : 'click',\n  MANUAL : 'manual'\n}\n\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper.js (https://popper.js.org/)')\n    }\n\n    // private\n    this._isEnabled     = true\n    this._timeout       = 0\n    this._hoverState    = ''\n    this._activeTrigger = {}\n    this._popper        = null\n\n    // Protected\n    this.element = element\n    this.config  = this._getConfig(config)\n    this.tip     = null\n\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const dataKey = this.constructor.DATA_KEY\n      let context = $(event.currentTarget).data(dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.currentTarget,\n          this._getDelegateConfig()\n        )\n        $(event.currentTarget).data(dataKey, context)\n      }\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if ($(this.getTipElement()).hasClass(ClassName.SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    $.removeData(this.element, this.constructor.DATA_KEY)\n\n    $(this.element).off(this.constructor.EVENT_KEY)\n    $(this.element).closest('.modal').off('hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip) {\n      $(this.tip).remove()\n    }\n\n    this._isEnabled     = null\n    this._timeout       = null\n    this._hoverState    = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.element = null\n    this.config  = null\n    this.tip     = null\n  }\n\n  show() {\n    if ($(this.element).css('display') === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    const showEvent = $.Event(this.constructor.Event.SHOW)\n    if (this.isWithContent() && this._isEnabled) {\n      $(this.element).trigger(showEvent)\n\n      const shadowRoot = Util.findShadowRoot(this.element)\n      const isInTheDom = $.contains(\n        shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement,\n        this.element\n      )\n\n      if (showEvent.isDefaultPrevented() || !isInTheDom) {\n        return\n      }\n\n      const tip   = this.getTipElement()\n      const tipId = Util.getUID(this.constructor.NAME)\n\n      tip.setAttribute('id', tipId)\n      this.element.setAttribute('aria-describedby', tipId)\n\n      this.setContent()\n\n      if (this.config.animation) {\n        $(tip).addClass(ClassName.FADE)\n      }\n\n      const placement  = typeof this.config.placement === 'function'\n        ? this.config.placement.call(this, tip, this.element)\n        : this.config.placement\n\n      const attachment = this._getAttachment(placement)\n      this.addAttachmentClass(attachment)\n\n      const container = this._getContainer()\n      $(tip).data(this.constructor.DATA_KEY, this)\n\n      if (!$.contains(this.element.ownerDocument.documentElement, this.tip)) {\n        $(tip).appendTo(container)\n      }\n\n      $(this.element).trigger(this.constructor.Event.INSERTED)\n\n      this._popper = new Popper(this.element, tip, this._getPopperConfig(attachment))\n\n      $(tip).addClass(ClassName.SHOW)\n\n      // If this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement) {\n        $(document.body).children().on('mouseover', null, $.noop)\n      }\n\n      const complete = () => {\n        if (this.config.animation) {\n          this._fixTransition()\n        }\n        const prevHoverState = this._hoverState\n        this._hoverState     = null\n\n        $(this.element).trigger(this.constructor.Event.SHOWN)\n\n        if (prevHoverState === HoverState.OUT) {\n          this._leave(null, this)\n        }\n      }\n\n      if ($(this.tip).hasClass(ClassName.FADE)) {\n        const transitionDuration = Util.getTransitionDurationFromElement(this.tip)\n\n        $(this.tip)\n          .one(Util.TRANSITION_END, complete)\n          .emulateTransitionEnd(transitionDuration)\n      } else {\n        complete()\n      }\n    }\n  }\n\n  hide(callback) {\n    const tip       = this.getTipElement()\n    const hideEvent = $.Event(this.constructor.Event.HIDE)\n    const complete = () => {\n      if (this._hoverState !== HoverState.SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this.element.removeAttribute('aria-describedby')\n      $(this.element).trigger(this.constructor.Event.HIDDEN)\n      if (this._popper !== null) {\n        this._popper.destroy()\n      }\n\n      if (callback) {\n        callback()\n      }\n    }\n\n    $(this.element).trigger(hideEvent)\n\n    if (hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    $(tip).removeClass(ClassName.SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      $(document.body).children().off('mouseover', null, $.noop)\n    }\n\n    this._activeTrigger[Trigger.CLICK] = false\n    this._activeTrigger[Trigger.FOCUS] = false\n    this._activeTrigger[Trigger.HOVER] = false\n\n    if ($(this.tip).hasClass(ClassName.FADE)) {\n      const transitionDuration = Util.getTransitionDurationFromElement(tip)\n\n      $(tip)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  addAttachmentClass(attachment) {\n    $(this.getTipElement()).addClass(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  getTipElement() {\n    this.tip = this.tip || $(this.config.template)[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent($(tip.querySelectorAll(Selector.TOOLTIP_INNER)), this.getTitle())\n    $(tip).removeClass(`${ClassName.FADE} ${ClassName.SHOW}`)\n  }\n\n  setElementContent($element, content) {\n    if (typeof content === 'object' && (content.nodeType || content.jquery)) {\n      // Content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (!$(content).parent().is($element)) {\n          $element.empty().append(content)\n        }\n      } else {\n        $element.text($(content).text())\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.whiteList, this.config.sanitizeFn)\n      }\n\n      $element.html(content)\n    } else {\n      $element.text(content)\n    }\n  }\n\n  getTitle() {\n    let title = this.element.getAttribute('data-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function'\n        ? this.config.title.call(this.element)\n        : this.config.title\n    }\n\n    return title\n  }\n\n  // Private\n\n  _getPopperConfig(attachment) {\n    const defaultBsConfig = {\n      placement: attachment,\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          behavior: this.config.fallbackPlacement\n        },\n        arrow: {\n          element: Selector.ARROW\n        },\n        preventOverflow: {\n          boundariesElement: this.config.boundary\n        }\n      },\n      onCreate: (data) => {\n        if (data.originalPlacement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      },\n      onUpdate: (data) => this._handlePopperPlacementChange(data)\n    }\n\n    return {\n      ...defaultBsConfig,\n      ...this.config.popperConfig\n    }\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this.config.offset === 'function') {\n      offset.fn = (data) => {\n        data.offsets = {\n          ...data.offsets,\n          ...this.config.offset(data.offsets, this.element) || {}\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this.config.offset\n    }\n\n    return offset\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (Util.isElement(this.config.container)) {\n      return $(this.config.container)\n    }\n\n    return $(document).find(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach((trigger) => {\n      if (trigger === 'click') {\n        $(this.element).on(\n          this.constructor.Event.CLICK,\n          this.config.selector,\n          (event) => this.toggle(event)\n        )\n      } else if (trigger !== Trigger.MANUAL) {\n        const eventIn = trigger === Trigger.HOVER\n          ? this.constructor.Event.MOUSEENTER\n          : this.constructor.Event.FOCUSIN\n        const eventOut = trigger === Trigger.HOVER\n          ? this.constructor.Event.MOUSELEAVE\n          : this.constructor.Event.FOCUSOUT\n\n        $(this.element)\n          .on(\n            eventIn,\n            this.config.selector,\n            (event) => this._enter(event)\n          )\n          .on(\n            eventOut,\n            this.config.selector,\n            (event) => this._leave(event)\n          )\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this.element) {\n        this.hide()\n      }\n    }\n\n    $(this.element).closest('.modal').on(\n      'hide.bs.modal',\n      this._hideModalHandler\n    )\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const titleType = typeof this.element.getAttribute('data-original-title')\n\n    if (this.element.getAttribute('title') || titleType !== 'string') {\n      this.element.setAttribute(\n        'data-original-title',\n        this.element.getAttribute('title') || ''\n      )\n\n      this.element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || $(event.currentTarget).data(dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.currentTarget,\n        this._getDelegateConfig()\n      )\n      $(event.currentTarget).data(dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER\n      ] = true\n    }\n\n    if ($(context.getTipElement()).hasClass(ClassName.SHOW) || context._hoverState === HoverState.SHOW) {\n      context._hoverState = HoverState.SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HoverState.SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HoverState.SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || $(event.currentTarget).data(dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.currentTarget,\n        this._getDelegateConfig()\n      )\n      $(event.currentTarget).data(dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER\n      ] = false\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HoverState.OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HoverState.OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = $(this.element).data()\n\n    Object.keys(dataAttributes)\n      .forEach((dataAttr) => {\n        if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {\n          delete dataAttributes[dataAttr]\n        }\n      })\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    Util.typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.whiteList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const $tip = $(this.getTipElement())\n    const tabClass = $tip.attr('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length) {\n      $tip.removeClass(tabClass.join(''))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const popperInstance = popperData.instance\n    this.tip = popperInstance.popper\n    this._cleanTipClass()\n    this.addAttachmentClass(this._getAttachment(popperData.placement))\n  }\n\n  _fixTransition() {\n    const tip = this.getTipElement()\n    const initConfigAnimation = this.config.animation\n\n    if (tip.getAttribute('x-placement') !== null) {\n      return\n    }\n\n    $(tip).removeClass(ClassName.FADE)\n    this.config.animation = false\n    this.hide()\n    this.show()\n    this.config.animation = initConfigAnimation\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Tooltip._jQueryInterface\n$.fn[NAME].Constructor = Tooltip\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Tooltip._jQueryInterface\n}\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.4.1): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME                = 'popover'\nconst VERSION             = '4.4.1'\nconst DATA_KEY            = 'bs.popover'\nconst EVENT_KEY           = `.${DATA_KEY}`\nconst JQUERY_NO_CONFLICT  = $.fn[NAME]\nconst CLASS_PREFIX        = 'bs-popover'\nconst BSCLS_PREFIX_REGEX  = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement : 'right',\n  trigger   : 'click',\n  content   : '',\n  template  : '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div></div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content : '(string|element|function)'\n}\n\nconst ClassName = {\n  FADE : 'fade',\n  SHOW : 'show'\n}\n\nconst Selector = {\n  TITLE   : '.popover-header',\n  CONTENT : '.popover-body'\n}\n\nconst Event = {\n  HIDE       : `hide${EVENT_KEY}`,\n  HIDDEN     : `hidden${EVENT_KEY}`,\n  SHOW       : `show${EVENT_KEY}`,\n  SHOWN      : `shown${EVENT_KEY}`,\n  INSERTED   : `inserted${EVENT_KEY}`,\n  CLICK      : `click${EVENT_KEY}`,\n  FOCUSIN    : `focusin${EVENT_KEY}`,\n  FOCUSOUT   : `focusout${EVENT_KEY}`,\n  MOUSEENTER : `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE : `mouseleave${EVENT_KEY}`\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  addAttachmentClass(attachment) {\n    $(this.getTipElement()).addClass(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  getTipElement() {\n    this.tip = this.tip || $(this.config.template)[0]\n    return this.tip\n  }\n\n  setContent() {\n    const $tip = $(this.getTipElement())\n\n    // We use append for html objects to maintain js events\n    this.setElementContent($tip.find(Selector.TITLE), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this.element)\n    }\n    this.setElementContent($tip.find(Selector.CONTENT), content)\n\n    $tip.removeClass(`${ClassName.FADE} ${ClassName.SHOW}`)\n  }\n\n  // Private\n\n  _getContent() {\n    return this.element.getAttribute('data-content') ||\n      this.config.content\n  }\n\n  _cleanTipClass() {\n    const $tip = $(this.getTipElement())\n    const tabClass = $tip.attr('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      $tip.removeClass(tabClass.join(''))\n    }\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Popover._jQueryInterface\n$.fn[NAME].Constructor = Popover\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Popover._jQueryInterface\n}\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.4.1): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME               = 'scrollspy'\nconst VERSION            = '4.4.1'\nconst DATA_KEY           = 'bs.scrollspy'\nconst EVENT_KEY          = `.${DATA_KEY}`\nconst DATA_API_KEY       = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst Default = {\n  offset : 10,\n  method : 'auto',\n  target : ''\n}\n\nconst DefaultType = {\n  offset : 'number',\n  method : 'string',\n  target : '(string|element)'\n}\n\nconst Event = {\n  ACTIVATE      : `activate${EVENT_KEY}`,\n  SCROLL        : `scroll${EVENT_KEY}`,\n  LOAD_DATA_API : `load${EVENT_KEY}${DATA_API_KEY}`\n}\n\nconst ClassName = {\n  DROPDOWN_ITEM : 'dropdown-item',\n  DROPDOWN_MENU : 'dropdown-menu',\n  ACTIVE        : 'active'\n}\n\nconst Selector = {\n  DATA_SPY        : '[data-spy=\"scroll\"]',\n  ACTIVE          : '.active',\n  NAV_LIST_GROUP  : '.nav, .list-group',\n  NAV_LINKS       : '.nav-link',\n  NAV_ITEMS       : '.nav-item',\n  LIST_ITEMS      : '.list-group-item',\n  DROPDOWN        : '.dropdown',\n  DROPDOWN_ITEMS  : '.dropdown-item',\n  DROPDOWN_TOGGLE : '.dropdown-toggle'\n}\n\nconst OffsetMethod = {\n  OFFSET   : 'offset',\n  POSITION : 'position'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy {\n  constructor(element, config) {\n    this._element       = element\n    this._scrollElement = element.tagName === 'BODY' ? window : element\n    this._config        = this._getConfig(config)\n    this._selector      = `${this._config.target} ${Selector.NAV_LINKS},` +\n                          `${this._config.target} ${Selector.LIST_ITEMS},` +\n                          `${this._config.target} ${Selector.DROPDOWN_ITEMS}`\n    this._offsets       = []\n    this._targets       = []\n    this._activeTarget  = null\n    this._scrollHeight  = 0\n\n    $(this._scrollElement).on(Event.SCROLL, (event) => this._process(event))\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window\n      ? OffsetMethod.OFFSET : OffsetMethod.POSITION\n\n    const offsetMethod = this._config.method === 'auto'\n      ? autoMethod : this._config.method\n\n    const offsetBase = offsetMethod === OffsetMethod.POSITION\n      ? this._getScrollTop() : 0\n\n    this._offsets = []\n    this._targets = []\n\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = [].slice.call(document.querySelectorAll(this._selector))\n\n    targets\n      .map((element) => {\n        let target\n        const targetSelector = Util.getSelectorFromElement(element)\n\n        if (targetSelector) {\n          target = document.querySelector(targetSelector)\n        }\n\n        if (target) {\n          const targetBCR = target.getBoundingClientRect()\n          if (targetBCR.width || targetBCR.height) {\n            // TODO (fat): remove sketch reliance on jQuery position/offset\n            return [\n              $(target)[offsetMethod]().top + offsetBase,\n              targetSelector\n            ]\n          }\n        }\n        return null\n      })\n      .filter((item) => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach((item) => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    $(this._scrollElement).off(EVENT_KEY)\n\n    this._element       = null\n    this._scrollElement = null\n    this._config        = null\n    this._selector      = null\n    this._offsets       = null\n    this._targets       = null\n    this._activeTarget  = null\n    this._scrollHeight  = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    if (typeof config.target !== 'string') {\n      let id = $(config.target).attr('id')\n      if (!id) {\n        id = Util.getUID(NAME)\n        $(config.target).attr('id', id)\n      }\n      config.target = `#${id}`\n    }\n\n    Util.typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window\n      ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window\n      ? window.innerHeight : this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop    = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll    = this._config.offset +\n      scrollHeight -\n      this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    const offsetLength = this._offsets.length\n    for (let i = offsetLength; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' ||\n              scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector\n      .split(',')\n      .map((selector) => `${selector}[data-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const $link = $([].slice.call(document.querySelectorAll(queries.join(','))))\n\n    if ($link.hasClass(ClassName.DROPDOWN_ITEM)) {\n      $link.closest(Selector.DROPDOWN).find(Selector.DROPDOWN_TOGGLE).addClass(ClassName.ACTIVE)\n      $link.addClass(ClassName.ACTIVE)\n    } else {\n      // Set triggered link as active\n      $link.addClass(ClassName.ACTIVE)\n      // Set triggered links parents as active\n      // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n      $link.parents(Selector.NAV_LIST_GROUP).prev(`${Selector.NAV_LINKS}, ${Selector.LIST_ITEMS}`).addClass(ClassName.ACTIVE)\n      // Handle special case when .nav-link is inside .nav-item\n      $link.parents(Selector.NAV_LIST_GROUP).prev(Selector.NAV_ITEMS).children(Selector.NAV_LINKS).addClass(ClassName.ACTIVE)\n    }\n\n    $(this._scrollElement).trigger(Event.ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    [].slice.call(document.querySelectorAll(this._selector))\n      .filter((node) => node.classList.contains(ClassName.ACTIVE))\n      .forEach((node) => node.classList.remove(ClassName.ACTIVE))\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(window).on(Event.LOAD_DATA_API, () => {\n  const scrollSpys = [].slice.call(document.querySelectorAll(Selector.DATA_SPY))\n  const scrollSpysLength = scrollSpys.length\n\n  for (let i = scrollSpysLength; i--;) {\n    const $spy = $(scrollSpys[i])\n    ScrollSpy._jQueryInterface.call($spy, $spy.data())\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = ScrollSpy._jQueryInterface\n$.fn[NAME].Constructor = ScrollSpy\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return ScrollSpy._jQueryInterface\n}\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.4.1): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME               = 'tab'\nconst VERSION            = '4.4.1'\nconst DATA_KEY           = 'bs.tab'\nconst EVENT_KEY          = `.${DATA_KEY}`\nconst DATA_API_KEY       = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst Event = {\n  HIDE           : `hide${EVENT_KEY}`,\n  HIDDEN         : `hidden${EVENT_KEY}`,\n  SHOW           : `show${EVENT_KEY}`,\n  SHOWN          : `shown${EVENT_KEY}`,\n  CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n}\n\nconst ClassName = {\n  DROPDOWN_MENU : 'dropdown-menu',\n  ACTIVE        : 'active',\n  DISABLED      : 'disabled',\n  FADE          : 'fade',\n  SHOW          : 'show'\n}\n\nconst Selector = {\n  DROPDOWN              : '.dropdown',\n  NAV_LIST_GROUP        : '.nav, .list-group',\n  ACTIVE                : '.active',\n  ACTIVE_UL             : '> li > .active',\n  DATA_TOGGLE           : '[data-toggle=\"tab\"], [data-toggle=\"pill\"], [data-toggle=\"list\"]',\n  DROPDOWN_TOGGLE       : '.dropdown-toggle',\n  DROPDOWN_ACTIVE_CHILD : '> .dropdown-menu .active'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab {\n  constructor(element) {\n    this._element = element\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  show() {\n    if (this._element.parentNode &&\n        this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n        $(this._element).hasClass(ClassName.ACTIVE) ||\n        $(this._element).hasClass(ClassName.DISABLED)) {\n      return\n    }\n\n    let target\n    let previous\n    const listElement = $(this._element).closest(Selector.NAV_LIST_GROUP)[0]\n    const selector = Util.getSelectorFromElement(this._element)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? Selector.ACTIVE_UL : Selector.ACTIVE\n      previous = $.makeArray($(listElement).find(itemSelector))\n      previous = previous[previous.length - 1]\n    }\n\n    const hideEvent = $.Event(Event.HIDE, {\n      relatedTarget: this._element\n    })\n\n    const showEvent = $.Event(Event.SHOW, {\n      relatedTarget: previous\n    })\n\n    if (previous) {\n      $(previous).trigger(hideEvent)\n    }\n\n    $(this._element).trigger(showEvent)\n\n    if (showEvent.isDefaultPrevented() ||\n        hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    if (selector) {\n      target = document.querySelector(selector)\n    }\n\n    this._activate(\n      this._element,\n      listElement\n    )\n\n    const complete = () => {\n      const hiddenEvent = $.Event(Event.HIDDEN, {\n        relatedTarget: this._element\n      })\n\n      const shownEvent = $.Event(Event.SHOWN, {\n        relatedTarget: previous\n      })\n\n      $(previous).trigger(hiddenEvent)\n      $(this._element).trigger(shownEvent)\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL')\n      ? $(container).find(Selector.ACTIVE_UL)\n      : $(container).children(Selector.ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback && (active && $(active).hasClass(ClassName.FADE))\n    const complete = () => this._transitionComplete(\n      element,\n      active,\n      callback\n    )\n\n    if (active && isTransitioning) {\n      const transitionDuration = Util.getTransitionDurationFromElement(active)\n\n      $(active)\n        .removeClass(ClassName.SHOW)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      $(active).removeClass(ClassName.ACTIVE)\n\n      const dropdownChild = $(active.parentNode).find(\n        Selector.DROPDOWN_ACTIVE_CHILD\n      )[0]\n\n      if (dropdownChild) {\n        $(dropdownChild).removeClass(ClassName.ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    $(element).addClass(ClassName.ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    Util.reflow(element)\n\n    if (element.classList.contains(ClassName.FADE)) {\n      element.classList.add(ClassName.SHOW)\n    }\n\n    if (element.parentNode && $(element.parentNode).hasClass(ClassName.DROPDOWN_MENU)) {\n      const dropdownElement = $(element).closest(Selector.DROPDOWN)[0]\n\n      if (dropdownElement) {\n        const dropdownToggleList = [].slice.call(dropdownElement.querySelectorAll(Selector.DROPDOWN_TOGGLE))\n\n        $(dropdownToggleList).addClass(ClassName.ACTIVE)\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $this = $(this)\n      let data = $this.data(DATA_KEY)\n\n      if (!data) {\n        data = new Tab(this)\n        $this.data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document)\n  .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n    event.preventDefault()\n    Tab._jQueryInterface.call($(this), 'show')\n  })\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Tab._jQueryInterface\n$.fn[NAME].Constructor = Tab\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Tab._jQueryInterface\n}\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.4.1): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME               = 'toast'\nconst VERSION            = '4.4.1'\nconst DATA_KEY           = 'bs.toast'\nconst EVENT_KEY          = `.${DATA_KEY}`\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst Event = {\n  CLICK_DISMISS : `click.dismiss${EVENT_KEY}`,\n  HIDE          : `hide${EVENT_KEY}`,\n  HIDDEN        : `hidden${EVENT_KEY}`,\n  SHOW          : `show${EVENT_KEY}`,\n  SHOWN         : `shown${EVENT_KEY}`\n}\n\nconst ClassName = {\n  FADE    : 'fade',\n  HIDE    : 'hide',\n  SHOW    : 'show',\n  SHOWING : 'showing'\n}\n\nconst DefaultType = {\n  animation : 'boolean',\n  autohide  : 'boolean',\n  delay     : 'number'\n}\n\nconst Default = {\n  animation : true,\n  autohide  : true,\n  delay     : 500\n}\n\nconst Selector = {\n  DATA_DISMISS : '[data-dismiss=\"toast\"]'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast {\n  constructor(element, config) {\n    this._element = element\n    this._config  = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  show() {\n    const showEvent = $.Event(Event.SHOW)\n\n    $(this._element).trigger(showEvent)\n    if (showEvent.isDefaultPrevented()) {\n      return\n    }\n\n    if (this._config.animation) {\n      this._element.classList.add(ClassName.FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(ClassName.SHOWING)\n      this._element.classList.add(ClassName.SHOW)\n\n      $(this._element).trigger(Event.SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(ClassName.HIDE)\n    Util.reflow(this._element)\n    this._element.classList.add(ClassName.SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(ClassName.SHOW)) {\n      return\n    }\n\n    const hideEvent = $.Event(Event.HIDE)\n\n    $(this._element).trigger(hideEvent)\n    if (hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._close()\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n\n    if (this._element.classList.contains(ClassName.SHOW)) {\n      this._element.classList.remove(ClassName.SHOW)\n    }\n\n    $(this._element).off(Event.CLICK_DISMISS)\n\n    $.removeData(this._element, DATA_KEY)\n    this._element = null\n    this._config  = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...$(this._element).data(),\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    Util.typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _setListeners() {\n    $(this._element).on(\n      Event.CLICK_DISMISS,\n      Selector.DATA_DISMISS,\n      () => this.hide()\n    )\n  }\n\n  _close() {\n    const complete = () => {\n      this._element.classList.add(ClassName.HIDE)\n      $(this._element).trigger(Event.HIDDEN)\n    }\n\n    this._element.classList.remove(ClassName.SHOW)\n    if (this._config.animation) {\n      const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $element = $(this)\n      let data       = $element.data(DATA_KEY)\n      const _config  = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n        $element.data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME]             = Toast._jQueryInterface\n$.fn[NAME].Constructor = Toast\n$.fn[NAME].noConflict  = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Toast._jQueryInterface\n}\n\nexport default Toast\n"]}