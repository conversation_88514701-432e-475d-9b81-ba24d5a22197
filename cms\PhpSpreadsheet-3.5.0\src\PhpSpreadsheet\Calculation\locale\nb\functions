############################################################
##
## PhpSpreadsheet - function name translations
##
## Norsk Bokmål (Norwegian Bokmål)
##
############################################################


##
## Kubefunksjoner (Cube Functions)
##
CUBEKPIMEMBER = KUBEKPIMEDLEM
CUBEMEMBER = KUBEMEDLEM
CUBEMEMBERPROPERTY = KUBEMEDLEMEGENSKAP
CUBERANKEDMEMBER = KUBERANGERTMEDLEM
CUBESET = KUBESETT
CUBESETCOUNT = KUBESETTANTALL
CUBEVALUE = KUBEVERDI

##
## Databasefunksjoner (Database Functions)
##
DAVERAGE = DGJENNOMSNITT
DCOUNT = DANTALL
DCOUNTA = DANTALLA
DGET = DHENT
DMAX = DMAKS
DMIN = DMIN
DPRODUCT = DPRODUKT
DSTDEV = DSTDAV
DSTDEVP = DSTDAVP
DSUM = DSUMMER
DVAR = DVARIANS
DVARP = DVARIANSP

##
## Dato- og tidsfunksjoner (Date & Time Functions)
##
DATE = DATO
DATEDIF = DATODIFF
DATESTRING = DATOSTRENG
DATEVALUE = DATOVERDI
DAY = DAG
DAYS = DAGER
DAYS360 = DAGER360
EDATE = DAG.ETTER
EOMONTH = MÅNEDSSLUTT
HOUR = TIME
ISOWEEKNUM = ISOUKENR
MINUTE = MINUTT
MONTH = MÅNED
NETWORKDAYS = NETT.ARBEIDSDAGER
NETWORKDAYS.INTL = NETT.ARBEIDSDAGER.INTL
NOW = NÅ
SECOND = SEKUND
THAIDAYOFWEEK = THAIUKEDAG
THAIMONTHOFYEAR = THAIMÅNED
THAIYEAR = THAIÅR
TIME = TID
TIMEVALUE = TIDSVERDI
TODAY = IDAG
WEEKDAY = UKEDAG
WEEKNUM = UKENR
WORKDAY = ARBEIDSDAG
WORKDAY.INTL = ARBEIDSDAG.INTL
YEAR = ÅR
YEARFRAC = ÅRDEL

##
## Tekniske funksjoner (Engineering Functions)
##
BESSELI = BESSELI
BESSELJ = BESSELJ
BESSELK = BESSELK
BESSELY = BESSELY
BIN2DEC = BINTILDES
BIN2HEX = BINTILHEKS
BIN2OCT = BINTILOKT
BITAND = BITOG
BITLSHIFT = BITVFORSKYV
BITOR = BITELLER
BITRSHIFT = BITHFORSKYV
BITXOR = BITEKSKLUSIVELLER
COMPLEX = KOMPLEKS
CONVERT = KONVERTER
DEC2BIN = DESTILBIN
DEC2HEX = DESTILHEKS
DEC2OCT = DESTILOKT
DELTA = DELTA
ERF = FEILF
ERF.PRECISE = FEILF.PRESIS
ERFC = FEILFK
ERFC.PRECISE = FEILFK.PRESIS
GESTEP = GRENSEVERDI
HEX2BIN = HEKSTILBIN
HEX2DEC = HEKSTILDES
HEX2OCT = HEKSTILOKT
IMABS = IMABS
IMAGINARY = IMAGINÆR
IMARGUMENT = IMARGUMENT
IMCONJUGATE = IMKONJUGERT
IMCOS = IMCOS
IMCOSH = IMCOSH
IMCOT = IMCOT
IMCSC = IMCSC
IMCSCH = IMCSCH
IMDIV = IMDIV
IMEXP = IMEKSP
IMLN = IMLN
IMLOG10 = IMLOG10
IMLOG2 = IMLOG2
IMPOWER = IMOPPHØY
IMPRODUCT = IMPRODUKT
IMREAL = IMREELL
IMSEC = IMSEC
IMSECH = IMSECH
IMSIN = IMSIN
IMSINH = IMSINH
IMSQRT = IMROT
IMSUB = IMSUB
IMSUM = IMSUMMER
IMTAN = IMTAN
OCT2BIN = OKTTILBIN
OCT2DEC = OKTTILDES
OCT2HEX = OKTTILHEKS

##
## Økonomiske funksjoner (Financial Functions)
##
ACCRINT = PÅLØPT.PERIODISK.RENTE
ACCRINTM = PÅLØPT.FORFALLSRENTE
AMORDEGRC = AMORDEGRC
AMORLINC = AMORLINC
COUPDAYBS = OBLIG.DAGER.FF
COUPDAYS = OBLIG.DAGER
COUPDAYSNC = OBLIG.DAGER.NF
COUPNCD = OBLIG.DAGER.EF
COUPNUM = OBLIG.ANTALL
COUPPCD = OBLIG.DAG.FORRIGE
CUMIPMT = SAMLET.RENTE
CUMPRINC = SAMLET.HOVEDSTOL
DB = DAVSKR
DDB = DEGRAVS
DISC = DISKONTERT
DOLLARDE = DOLLARDE
DOLLARFR = DOLLARBR
DURATION = VARIGHET
EFFECT = EFFEKTIV.RENTE
FV = SLUTTVERDI
FVSCHEDULE = SVPLAN
INTRATE = RENTESATS
IPMT = RAVDRAG
IRR = IR
ISPMT = ER.AVDRAG
MDURATION = MVARIGHET
MIRR = MODIR
NOMINAL = NOMINELL
NPER = PERIODER
NPV = NNV
ODDFPRICE = AVVIKFP.PRIS
ODDFYIELD = AVVIKFP.AVKASTNING
ODDLPRICE = AVVIKSP.PRIS
ODDLYIELD = AVVIKSP.AVKASTNING
PDURATION = PVARIGHET
PMT = AVDRAG
PPMT = AMORT
PRICE = PRIS
PRICEDISC = PRIS.DISKONTERT
PRICEMAT = PRIS.FORFALL
PV = NÅVERDI
RATE = RENTE
RECEIVED = MOTTATT.AVKAST
RRI = REALISERT.AVKASTNING
SLN = LINAVS
SYD = ÅRSAVS
TBILLEQ = TBILLEKV
TBILLPRICE = TBILLPRIS
TBILLYIELD = TBILLAVKASTNING
VDB = VERDIAVS
XIRR = XIR
XNPV = XNNV
YIELD = AVKAST
YIELDDISC = AVKAST.DISKONTERT
YIELDMAT = AVKAST.FORFALL

##
## Informasjonsfunksjoner (Information Functions)
##
CELL = CELLE
ERROR.TYPE = FEIL.TYPE
INFO = INFO
ISBLANK = ERTOM
ISERR = ERF
ISERROR = ERFEIL
ISEVEN = ERPARTALL
ISFORMULA = ERFORMEL
ISLOGICAL = ERLOGISK
ISNA = ERIT
ISNONTEXT = ERIKKETEKST
ISNUMBER = ERTALL
ISODD = ERODDE
ISREF = ERREF
ISTEXT = ERTEKST
N = N
NA = IT
SHEET = ARK
SHEETS = ANTALL.ARK
TYPE = VERDITYPE

##
## Logiske funksjoner (Logical Functions)
##
AND = OG
FALSE = USANN
IF = HVIS
IFERROR = HVISFEIL
IFNA = HVIS.IT
IFS = HVIS.SETT
NOT = IKKE
OR = ELLER
SWITCH = BRYTER
TRUE = SANN
XOR = EKSKLUSIVELLER

##
## Oppslag- og referansefunksjoner (Lookup & Reference Functions)
##
ADDRESS = ADRESSE
AREAS = OMRÅDER
CHOOSE = VELG
COLUMN = KOLONNE
COLUMNS = KOLONNER
FORMULATEXT = FORMELTEKST
GETPIVOTDATA = HENTPIVOTDATA
HLOOKUP = FINN.KOLONNE
HYPERLINK = HYPERKOBLING
INDEX = INDEKS
INDIRECT = INDIREKTE
LOOKUP = SLÅ.OPP
MATCH = SAMMENLIGNE
OFFSET = FORSKYVNING
ROW = RAD
ROWS = RADER
RTD = RTD
TRANSPOSE = TRANSPONER
VLOOKUP = FINN.RAD
*RC = RK

##
## Matematikk- og trigonometrifunksjoner (Math & Trig Functions)
##
ABS = ABS
ACOS = ARCCOS
ACOSH = ARCCOSH
ACOT = ACOT
ACOTH = ACOTH
AGGREGATE = MENGDE
ARABIC = ARABISK
ASIN = ARCSIN
ASINH = ARCSINH
ATAN = ARCTAN
ATAN2 = ARCTAN2
ATANH = ARCTANH
BASE = GRUNNTALL
CEILING.MATH = AVRUND.GJELDENDE.MULTIPLUM.OPP.MATEMATISK
CEILING.PRECISE = AVRUND.GJELDENDE.MULTIPLUM.PRESIS
COMBIN = KOMBINASJON
COMBINA = KOMBINASJONA
COS = COS
COSH = COSH
COT = COT
COTH = COTH
CSC = CSC
CSCH = CSCH
DECIMAL = DESIMAL
DEGREES = GRADER
ECMA.CEILING = ECMA.AVRUND.GJELDENDE.MULTIPLUM
EVEN = AVRUND.TIL.PARTALL
EXP = EKSP
FACT = FAKULTET
FACTDOUBLE = DOBBELFAKT
FLOOR.MATH = AVRUND.GJELDENDE.MULTIPLUM.NED.MATEMATISK
FLOOR.PRECISE = AVRUND.GJELDENDE.MULTIPLUM.NED.PRESIS
GCD = SFF
INT = HELTALL
ISO.CEILING = ISO.AVRUND.GJELDENDE.MULTIPLUM
LCM = MFM
LN = LN
LOG = LOG
LOG10 = LOG10
MDETERM = MDETERM
MINVERSE = MINVERS
MMULT = MMULT
MOD = REST
MROUND = MRUND
MULTINOMIAL = MULTINOMINELL
MUNIT = MENHET
ODD = AVRUND.TIL.ODDETALL
PI = PI
POWER = OPPHØYD.I
PRODUCT = PRODUKT
QUOTIENT = KVOTIENT
RADIANS = RADIANER
RAND = TILFELDIG
RANDBETWEEN = TILFELDIGMELLOM
ROMAN = ROMERTALL
ROUND = AVRUND
ROUNDBAHTDOWN = RUNDAVBAHTNEDOVER
ROUNDBAHTUP = RUNDAVBAHTOPPOVER
ROUNDDOWN = AVRUND.NED
ROUNDUP = AVRUND.OPP
SEC = SEC
SECH = SECH
SERIESSUM = SUMMER.REKKE
SIGN = FORTEGN
SIN = SIN
SINH = SINH
SQRT = ROT
SQRTPI = ROTPI
SUBTOTAL = DELSUM
SUM = SUMMER
SUMIF = SUMMERHVIS
SUMIFS = SUMMER.HVIS.SETT
SUMPRODUCT = SUMMERPRODUKT
SUMSQ = SUMMERKVADRAT
SUMX2MY2 = SUMMERX2MY2
SUMX2PY2 = SUMMERX2PY2
SUMXMY2 = SUMMERXMY2
TAN = TAN
TANH = TANH
TRUNC = AVKORT

##
## Statistiske funksjoner (Statistical Functions)
##
AVEDEV = GJENNOMSNITTSAVVIK
AVERAGE = GJENNOMSNITT
AVERAGEA = GJENNOMSNITTA
AVERAGEIF = GJENNOMSNITTHVIS
AVERAGEIFS = GJENNOMSNITT.HVIS.SETT
BETA.DIST = BETA.FORDELING.N
BETA.INV = BETA.INV
BINOM.DIST = BINOM.FORDELING.N
BINOM.DIST.RANGE = BINOM.FORDELING.OMRÅDE
BINOM.INV = BINOM.INV
CHISQ.DIST = KJIKVADRAT.FORDELING
CHISQ.DIST.RT = KJIKVADRAT.FORDELING.H
CHISQ.INV = KJIKVADRAT.INV
CHISQ.INV.RT = KJIKVADRAT.INV.H
CHISQ.TEST = KJIKVADRAT.TEST
CONFIDENCE.NORM = KONFIDENS.NORM
CONFIDENCE.T = KONFIDENS.T
CORREL = KORRELASJON
COUNT = ANTALL
COUNTA = ANTALLA
COUNTBLANK = TELLBLANKE
COUNTIF = ANTALL.HVIS
COUNTIFS = ANTALL.HVIS.SETT
COVARIANCE.P = KOVARIANS.P
COVARIANCE.S = KOVARIANS.S
DEVSQ = AVVIK.KVADRERT
EXPON.DIST = EKSP.FORDELING.N
F.DIST = F.FORDELING
F.DIST.RT = F.FORDELING.H
F.INV = F.INV
F.INV.RT = F.INV.H
F.TEST = F.TEST
FISHER = FISHER
FISHERINV = FISHERINV
FORECAST.ETS = PROGNOSE.ETS
FORECAST.ETS.CONFINT = PROGNOSE.ETS.CONFINT
FORECAST.ETS.SEASONALITY = PROGNOSE.ETS.SESONGAVHENGIGHET
FORECAST.ETS.STAT = PROGNOSE.ETS.STAT
FORECAST.LINEAR = PROGNOSE.LINEÆR
FREQUENCY = FREKVENS
GAMMA = GAMMA
GAMMA.DIST = GAMMA.FORDELING
GAMMA.INV = GAMMA.INV
GAMMALN = GAMMALN
GAMMALN.PRECISE = GAMMALN.PRESIS
GAUSS = GAUSS
GEOMEAN = GJENNOMSNITT.GEOMETRISK
GROWTH = VEKST
HARMEAN = GJENNOMSNITT.HARMONISK
HYPGEOM.DIST = HYPGEOM.FORDELING.N
INTERCEPT = SKJÆRINGSPUNKT
KURT = KURT
LARGE = N.STØRST
LINEST = RETTLINJE
LOGEST = KURVE
LOGNORM.DIST = LOGNORM.FORDELING
LOGNORM.INV = LOGNORM.INV
MAX = STØRST
MAXA = MAKSA
MAXIFS = MAKS.HVIS.SETT
MEDIAN = MEDIAN
MIN = MIN
MINA = MINA
MINIFS = MIN.HVIS.SETT
MODE.MULT = MODUS.MULT
MODE.SNGL = MODUS.SNGL
NEGBINOM.DIST = NEGBINOM.FORDELING.N
NORM.DIST = NORM.FORDELING
NORM.INV = NORM.INV
NORM.S.DIST = NORM.S.FORDELING
NORM.S.INV = NORM.S.INV
PEARSON = PEARSON
PERCENTILE.EXC = PERSENTIL.EKS
PERCENTILE.INC = PERSENTIL.INK
PERCENTRANK.EXC = PROSENTDEL.EKS
PERCENTRANK.INC = PROSENTDEL.INK
PERMUT = PERMUTER
PERMUTATIONA = PERMUTASJONA
PHI = PHI
POISSON.DIST = POISSON.FORDELING
PROB = SANNSYNLIG
QUARTILE.EXC = KVARTIL.EKS
QUARTILE.INC = KVARTIL.INK
RANK.AVG = RANG.GJSN
RANK.EQ = RANG.EKV
RSQ = RKVADRAT
SKEW = SKJEVFORDELING
SKEW.P = SKJEVFORDELING.P
SLOPE = STIGNINGSTALL
SMALL = N.MINST
STANDARDIZE = NORMALISER
STDEV.P = STDAV.P
STDEV.S = STDAV.S
STDEVA = STDAVVIKA
STDEVPA = STDAVVIKPA
STEYX = STANDARDFEIL
T.DIST = T.FORDELING
T.DIST.2T = T.FORDELING.2T
T.DIST.RT = T.FORDELING.H
T.INV = T.INV
T.INV.2T = T.INV.2T
T.TEST = T.TEST
TREND = TREND
TRIMMEAN = TRIMMET.GJENNOMSNITT
VAR.P = VARIANS.P
VAR.S = VARIANS.S
VARA = VARIANSA
VARPA = VARIANSPA
WEIBULL.DIST = WEIBULL.DIST.N
Z.TEST = Z.TEST

##
## Tekstfunksjoner (Text Functions)
##
ASC = STIGENDE
BAHTTEXT = BAHTTEKST
CHAR = TEGNKODE
CLEAN = RENSK
CODE = KODE
CONCAT = KJED.SAMMEN
DOLLAR = VALUTA
EXACT = EKSAKT
FIND = FINN
FIXED = FASTSATT
ISTHAIDIGIT = ERTHAISIFFER
LEFT = VENSTRE
LEN = LENGDE
LOWER = SMÅ
MID = DELTEKST
NUMBERSTRING = TALLSTRENG
NUMBERVALUE = TALLVERDI
PHONETIC = FURIGANA
PROPER = STOR.FORBOKSTAV
REPLACE = ERSTATT
REPT = GJENTA
RIGHT = HØYRE
SEARCH = SØK
SUBSTITUTE = BYTT.UT
T = T
TEXT = TEKST
TEXTJOIN = TEKST.KOMBINER
THAIDIGIT = THAISIFFER
THAINUMSOUND = THAINUMLYD
THAINUMSTRING = THAINUMSTRENG
THAISTRINGLENGTH = THAISTRENGLENGDE
TRIM = TRIMME
UNICHAR = UNICODETEGN
UNICODE = UNICODE
UPPER = STORE
VALUE = VERDI

##
## Nettfunksjoner (Web Functions)
##
ENCODEURL = URL.KODE
FILTERXML = FILTRERXML
WEBSERVICE = NETTJENESTE

##
## Kompatibilitetsfunksjoner (Compatibility Functions)
##
BETADIST = BETA.FORDELING
BETAINV = INVERS.BETA.FORDELING
BINOMDIST = BINOM.FORDELING
CEILING = AVRUND.GJELDENDE.MULTIPLUM
CHIDIST = KJI.FORDELING
CHIINV = INVERS.KJI.FORDELING
CHITEST = KJI.TEST
CONCATENATE = KJEDE.SAMMEN
CONFIDENCE = KONFIDENS
COVAR = KOVARIANS
CRITBINOM = GRENSE.BINOM
EXPONDIST = EKSP.FORDELING
FDIST = FFORDELING
FINV = FFORDELING.INVERS
FLOOR = AVRUND.GJELDENDE.MULTIPLUM.NED
FORECAST = PROGNOSE
FTEST = FTEST
GAMMADIST = GAMMAFORDELING
GAMMAINV = GAMMAINV
HYPGEOMDIST = HYPGEOM.FORDELING
LOGINV = LOGINV
LOGNORMDIST = LOGNORMFORD
MODE = MODUS
NEGBINOMDIST = NEGBINOM.FORDELING
NORMDIST = NORMALFORDELING
NORMINV = NORMINV
NORMSDIST = NORMSFORDELING
NORMSINV = NORMSINV
PERCENTILE = PERSENTIL
PERCENTRANK = PROSENTDEL
POISSON = POISSON
QUARTILE = KVARTIL
RANK = RANG
STDEV = STDAV
STDEVP = STDAVP
TDIST = TFORDELING
TINV = TINV
TTEST = TTEST
VAR = VARIANS
VARP = VARIANSP
WEIBULL = WEIBULL.FORDELING
ZTEST = ZTEST
