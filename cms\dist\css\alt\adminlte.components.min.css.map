{"version": 3, "sources": ["../../../build/scss/parts/adminlte.components.scss", "../../../build/scss/_forms.scss", "../../../node_modules/bootstrap/scss/mixins/_border-radius.scss", "dist/css/alt/adminlte.components.css", "../../../node_modules/bootstrap/scss/vendor/_rfs.scss", "../../../node_modules/bootstrap/scss/mixins/_gradients.scss", "../../../build/scss/mixins/_custom-forms.scss", "../../../build/scss/_progress-bars.scss", "../../../node_modules/bootstrap/scss/mixins/_box-shadow.scss", "../../../build/scss/mixins/_cards.scss", "../../../build/scss/_cards.scss", "../../../node_modules/bootstrap/scss/mixins/_clearfix.scss", "../../../node_modules/bootstrap/scss/mixins/_lists.scss", "../../../build/scss/_modals.scss", "../../../build/scss/_toasts.scss", "../../../build/scss/mixins/_toasts.scss", "../../../build/scss/_buttons.scss", "../../../node_modules/bootstrap/scss/mixins/_buttons.scss", "../../../build/scss/_callout.scss", "../../../build/scss/_alerts.scss", "../../../node_modules/bootstrap/scss/mixins/_alert.scss", "../../../build/scss/_table.scss", "adminlte.components.css", "../../../build/scss/_carousel.scss", "../../../build/scss/_close.scss", "../../../node_modules/bootstrap/scss/mixins/_hover.scss"], "names": [], "mappings": "AAAA;;;;;;ACIA,qBAEI,SAAA,SAFJ,mCAKM,cAAA,KALN,gCASM,iBAAA,YACA,OAAA,EACA,OAAA,QACA,UAAA,KAEA,QAAA,QAAA,OACA,SAAA,SACA,MAAA,IACA,IAAA,EAMN,gDAAA,+CCTI,cAAA,EDmBJ,0BAAA,2BAAA,2BAAA,2BAAA,2BAAA,2BAAA,2BAAA,sCASI,YAAA,oBEXJ,0CAGA,2CAEA,2CADA,2CAFA,2CADA,2CAMA,2CADA,sDFSA,oCAAA,qCAAA,qCAAA,qCAAA,qCAAA,qCAAA,qCAAA,gDAUI,YAAA,qBAIJ,uDAAA,wDAAA,wDAAA,wDAAA,wDAAA,wDAAA,wDAAA,mEAUM,YAAA,qBEvBN,0CAGA,2CAEA,2CADA,2CAFA,2CADA,2CAMA,2CADA,sDFsBA,oCAAA,qCAAA,qCAAA,qCAAA,qCAAA,qCAAA,qCAAA,gDAUI,YAAA,sBAIJ,uDAAA,wDAAA,wDAAA,wDAAA,wDAAA,wDAAA,wDAAA,mEAUM,YAAA,sBAKN,qDACE,YAAA,IAGF,kBGxCI,UAAA,IH0CF,MAAA,QACA,QAAA,KACA,WAAA,OACA,MAAA,KAGF,iBCtGI,cAAA,OE6GE,UAAA,QHJJ,iBAAA,mBACA,MAAA,QACA,QAAA,KACA,YAAA,IACA,WAAA,MACA,UAAA,KACA,QAAA,OAAA,MACA,SAAA,SACA,IAAA,KACA,QAAA,EAGF,yBAEI,aAAA,QAFJ,+BAaM,aAAA,QACA,WAAA,EAAA,EAAA,EAAA,EAAA,oBAdN,2CEhCA,0CFmDM,QAAA,MAMN,iCAGM,cAAA,QACA,oBAAA,IAAA,wBAAA,MAAA,wBAKN,0BAEI,aAAA,QAFJ,gCAUM,aAAA,QACA,WAAA,EAAA,EAAA,EAAA,EAAA,oBAXN,4CE/CA,2CF+DM,QAAA,MAMN,gDEhEA,+CFoEM,QAAA,MAKN,+CAGM,MAAA,QAHN,+CEhEA,8CFwEM,QAAA,MAKN,uDAEI,MAAA,QAFJ,+DAKM,aAAA,QALN,mDEhEA,kDF2EI,QAAA,MAXJ,uEI/MI,iBAAA,QJgOE,aAAA,QAjBN,qEAuBM,WAAA,EAAA,EAAA,EAAA,EAAA,oBAvBN,mFA2BM,aAAA,QAMN,iDAGM,aAAA,QAHN,gDE3EA,+CFmFM,QAAA,MARN,uDAaQ,aAAA,QACA,WAAA,EAAA,EAAA,EAAA,EAAA,oBAOR,+BAEI,UAAA,QEpFJ,mCFyFA,kCAGI,WAAA,EACA,YAAA,EACA,aAAA,EACA,cAAA,EACA,WAAA,QEvFJ,kDFgFA,iDAUM,oBAAA,IErFN,kDF2EA,iDAaM,oBAAA,IKxRF,6FACE,iBAAA,QACA,aAAA,QAGF,mGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,4FACE,iBAAA,QAKF,oGACE,iBAAA,QACA,aAAA,QAGF,0GACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,mGACE,iBAAA,QAzBF,+FACE,iBAAA,QACA,aAAA,QAGF,qGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,sBAGF,8FACE,iBAAA,QAKF,sGACE,iBAAA,QACA,aAAA,QAGF,4GACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,sBAGF,qGACE,iBAAA,QAzBF,6FACE,iBAAA,QACA,aAAA,QAGF,mGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,4FACE,iBAAA,QAKF,oGACE,iBAAA,QACA,aAAA,QAGF,0GACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,mGACE,iBAAA,QAzBF,0FACE,iBAAA,QACA,aAAA,QAGF,gGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,yFACE,iBAAA,QAKF,iGACE,iBAAA,QACA,aAAA,QAGF,uGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,gGACE,iBAAA,QAzBF,6FACE,iBAAA,QACA,aAAA,QAGF,mGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,4FACE,iBAAA,QAKF,oGACE,iBAAA,QACA,aAAA,QAGF,0GACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,mGACE,iBAAA,QAzBF,4FACE,iBAAA,QACA,aAAA,QAGF,kGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,2FACE,iBAAA,QAKF,mGACE,iBAAA,QACA,aAAA,QAGF,yGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,kGACE,iBAAA,QAzBF,2FACE,iBAAA,QACA,aAAA,QAGF,iGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,sBAGF,0FACE,iBAAA,QAKF,kGACE,iBAAA,QACA,aAAA,QAGF,wGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,sBAGF,iGACE,iBAAA,KAzBF,0FACE,iBAAA,QACA,aAAA,QAGF,gGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,mBAGF,yFACE,iBAAA,KAKF,iGACE,iBAAA,QACA,aAAA,QAGF,uGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,mBAGF,gGACE,iBAAA,QAzBF,+FACE,iBAAA,QACA,aAAA,QAGF,qGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,8FACE,iBAAA,QAKF,sGACE,iBAAA,QACA,aAAA,QAGF,4GACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,qGACE,iBAAA,QAzBF,0FACE,iBAAA,QACA,aAAA,KAGF,gGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,kBAGF,yFACE,iBAAA,KAKF,iGACE,iBAAA,QACA,aAAA,KAGF,uGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,kBAGF,gGACE,iBAAA,QAzBF,2FACE,iBAAA,QACA,aAAA,QAGF,iGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,0FACE,iBAAA,QAKF,kGACE,iBAAA,QACA,aAAA,QAGF,wGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,iGACE,iBAAA,QAzBF,0FACE,iBAAA,QACA,aAAA,QAGF,gGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,yFACE,iBAAA,QAKF,iGACE,iBAAA,QACA,aAAA,QAGF,uGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,gGACE,iBAAA,QAzBF,6FACE,iBAAA,QACA,aAAA,QAGF,mGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,4FACE,iBAAA,QAKF,oGACE,iBAAA,QACA,aAAA,QAGF,0GACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,mGACE,iBAAA,QAzBF,4FACE,iBAAA,QACA,aAAA,QAGF,kGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,2FACE,iBAAA,QAKF,mGACE,iBAAA,QACA,aAAA,QAGF,yGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,kGACE,iBAAA,QAzBF,0FACE,iBAAA,QACA,aAAA,QAGF,gGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,yFACE,iBAAA,QAKF,iGACE,iBAAA,QACA,aAAA,QAGF,uGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,gGACE,iBAAA,QAzBF,4FACE,iBAAA,QACA,aAAA,QAGF,kGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,2FACE,iBAAA,QAKF,mGACE,iBAAA,QACA,aAAA,QAGF,yGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,kGACE,iBAAA,QAzBF,4FACE,iBAAA,QACA,aAAA,QAGF,kGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,2FACE,iBAAA,QAKF,mGACE,iBAAA,QACA,aAAA,QAGF,yGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,kGACE,iBAAA,QAzBF,0FACE,iBAAA,QACA,aAAA,QAGF,gGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,yFACE,iBAAA,QAKF,iGACE,iBAAA,QACA,aAAA,QAGF,uGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,gGACE,iBAAA,QAzBF,yFACE,iBAAA,QACA,aAAA,QAGF,+FACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,wFACE,iBAAA,QAKF,gGACE,iBAAA,QACA,aAAA,QAGF,sGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,+FACE,iBAAA,QAzBF,4FACE,iBAAA,QACA,aAAA,QAGF,kGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,2FACE,iBAAA,QAKF,mGACE,iBAAA,QACA,aAAA,QAGF,yGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,kGACE,iBAAA,QAzBF,4FACE,iBAAA,QACA,aAAA,QAGF,kGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,2FACE,iBAAA,QAKF,mGACE,iBAAA,QACA,aAAA,QAGF,yGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,kGACE,iBAAA,QAzBF,2FACE,iBAAA,QACA,aAAA,QAGF,iGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,0FACE,iBAAA,QAKF,kGACE,iBAAA,QACA,aAAA,QAGF,wGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,iGACE,iBAAA,QAzBF,0FACE,iBAAA,QACA,aAAA,QAGF,gGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,yFACE,iBAAA,QAKF,iGACE,iBAAA,QACA,aAAA,QAGF,uGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,gGACE,iBAAA,QAzBF,0FACE,iBAAA,QACA,aAAA,QAGF,gGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,yFACE,iBAAA,QAKF,iGACE,iBAAA,QACA,aAAA,QAGF,uGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,gGACE,iBAAA,QAzBF,2FACE,iBAAA,KACA,aAAA,KAGF,iGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,sBAGF,0FACE,iBAAA,QAKF,kGACE,iBAAA,KACA,aAAA,KAGF,wGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,sBAGF,iGACE,iBAAA,KAzBF,0FACE,iBAAA,QACA,aAAA,QAGF,gGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,sBAGF,yFACE,iBAAA,QAKF,iGACE,iBAAA,QACA,aAAA,QAGF,uGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,sBAGF,gGACE,iBAAA,QAzBF,+FACE,iBAAA,QACA,aAAA,QAGF,qGACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,mBAGF,8FACE,iBAAA,KAKF,sGACE,iBAAA,QACA,aAAA,QAGF,4GACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,mBAGF,qGACE,iBAAA,QAQF,yCACE,QAAA,EAEA,+DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,2DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,oDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAIJ,yDACE,iBAAA,QAEA,gEACE,iBAAA,QAIJ,qDACE,iBAAA,QAEA,4DACE,iBAAA,QAIJ,8CACE,iBAAA,QAEA,qDACE,iBAAA,QApCJ,2CACE,QAAA,EAEA,iEACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,sBAGF,6DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,sBAGF,sDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,sBAIJ,2DACE,iBAAA,QAEA,kEACE,iBAAA,QAIJ,uDACE,iBAAA,QAEA,8DACE,iBAAA,QAIJ,gDACE,iBAAA,QAEA,uDACE,iBAAA,QApCJ,yCACE,QAAA,EAEA,+DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,2DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,oDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAIJ,yDACE,iBAAA,QAEA,gEACE,iBAAA,QAIJ,qDACE,iBAAA,QAEA,4DACE,iBAAA,QAIJ,8CACE,iBAAA,QAEA,qDACE,iBAAA,QApCJ,sCACE,QAAA,EAEA,4DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,wDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,iDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAIJ,sDACE,iBAAA,QAEA,6DACE,iBAAA,QAIJ,kDACE,iBAAA,QAEA,yDACE,iBAAA,QAIJ,2CACE,iBAAA,QAEA,kDACE,iBAAA,QApCJ,yCACE,QAAA,EAEA,+DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,2DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,oDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAIJ,yDACE,iBAAA,QAEA,gEACE,iBAAA,QAIJ,qDACE,iBAAA,QAEA,4DACE,iBAAA,QAIJ,8CACE,iBAAA,QAEA,qDACE,iBAAA,QApCJ,wCACE,QAAA,EAEA,8DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,0DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,mDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAIJ,wDACE,iBAAA,QAEA,+DACE,iBAAA,QAIJ,oDACE,iBAAA,QAEA,2DACE,iBAAA,QAIJ,6CACE,iBAAA,QAEA,oDACE,iBAAA,QApCJ,uCACE,QAAA,EAEA,6DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,sBAGF,yDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,sBAGF,kDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,sBAIJ,uDACE,iBAAA,QAEA,8DACE,iBAAA,KAIJ,mDACE,iBAAA,QAEA,0DACE,iBAAA,KAIJ,4CACE,iBAAA,QAEA,mDACE,iBAAA,KApCJ,sCACE,QAAA,EAEA,4DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,mBAGF,wDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,mBAGF,iDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,mBAIJ,sDACE,iBAAA,QAEA,6DACE,iBAAA,QAIJ,kDACE,iBAAA,QAEA,yDACE,iBAAA,QAIJ,2CACE,iBAAA,QAEA,kDACE,iBAAA,QApCJ,2CACE,QAAA,EAEA,iEACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,6DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,sDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAIJ,2DACE,iBAAA,QAEA,kEACE,iBAAA,QAIJ,uDACE,iBAAA,QAEA,8DACE,iBAAA,QAIJ,gDACE,iBAAA,QAEA,uDACE,iBAAA,QApCJ,sCACE,QAAA,EAEA,4DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,kBAGF,wDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,kBAGF,iDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,kBAIJ,sDACE,iBAAA,QAEA,6DACE,iBAAA,QAIJ,kDACE,iBAAA,QAEA,yDACE,iBAAA,QAIJ,2CACE,iBAAA,QAEA,kDACE,iBAAA,QApCJ,uCACE,QAAA,EAEA,6DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,yDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,kDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAIJ,uDACE,iBAAA,QAEA,8DACE,iBAAA,QAIJ,mDACE,iBAAA,QAEA,0DACE,iBAAA,QAIJ,4CACE,iBAAA,QAEA,mDACE,iBAAA,QApCJ,sCACE,QAAA,EAEA,4DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,wDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,iDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAIJ,sDACE,iBAAA,QAEA,6DACE,iBAAA,QAIJ,kDACE,iBAAA,QAEA,yDACE,iBAAA,QAIJ,2CACE,iBAAA,QAEA,kDACE,iBAAA,QApCJ,yCACE,QAAA,EAEA,+DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,2DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,oDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAIJ,yDACE,iBAAA,QAEA,gEACE,iBAAA,QAIJ,qDACE,iBAAA,QAEA,4DACE,iBAAA,QAIJ,8CACE,iBAAA,QAEA,qDACE,iBAAA,QApCJ,wCACE,QAAA,EAEA,8DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,0DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,mDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAIJ,wDACE,iBAAA,QAEA,+DACE,iBAAA,QAIJ,oDACE,iBAAA,QAEA,2DACE,iBAAA,QAIJ,6CACE,iBAAA,QAEA,oDACE,iBAAA,QApCJ,sCACE,QAAA,EAEA,4DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,wDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,iDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAIJ,sDACE,iBAAA,QAEA,6DACE,iBAAA,QAIJ,kDACE,iBAAA,QAEA,yDACE,iBAAA,QAIJ,2CACE,iBAAA,QAEA,kDACE,iBAAA,QApCJ,wCACE,QAAA,EAEA,8DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,0DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,mDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAIJ,wDACE,iBAAA,QAEA,+DACE,iBAAA,QAIJ,oDACE,iBAAA,QAEA,2DACE,iBAAA,QAIJ,6CACE,iBAAA,QAEA,oDACE,iBAAA,QApCJ,wCACE,QAAA,EAEA,8DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,0DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,mDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAIJ,wDACE,iBAAA,QAEA,+DACE,iBAAA,QAIJ,oDACE,iBAAA,QAEA,2DACE,iBAAA,QAIJ,6CACE,iBAAA,QAEA,oDACE,iBAAA,QApCJ,sCACE,QAAA,EAEA,4DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,wDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,iDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAIJ,sDACE,iBAAA,QAEA,6DACE,iBAAA,QAIJ,kDACE,iBAAA,QAEA,yDACE,iBAAA,QAIJ,2CACE,iBAAA,QAEA,kDACE,iBAAA,QApCJ,qCACE,QAAA,EAEA,2DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,uDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,gDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAIJ,qDACE,iBAAA,QAEA,4DACE,iBAAA,QAIJ,iDACE,iBAAA,QAEA,wDACE,iBAAA,QAIJ,0CACE,iBAAA,QAEA,iDACE,iBAAA,QApCJ,wCACE,QAAA,EAEA,8DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,0DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,mDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAIJ,wDACE,iBAAA,QAEA,+DACE,iBAAA,QAIJ,oDACE,iBAAA,QAEA,2DACE,iBAAA,QAIJ,6CACE,iBAAA,QAEA,oDACE,iBAAA,QApCJ,wCACE,QAAA,EAEA,8DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,0DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,mDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAIJ,wDACE,iBAAA,QAEA,+DACE,iBAAA,QAIJ,oDACE,iBAAA,QAEA,2DACE,iBAAA,QAIJ,6CACE,iBAAA,QAEA,oDACE,iBAAA,QApCJ,uCACE,QAAA,EAEA,6DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,yDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAGF,kDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,oBAIJ,uDACE,iBAAA,QAEA,8DACE,iBAAA,QAIJ,mDACE,iBAAA,QAEA,0DACE,iBAAA,QAIJ,4CACE,iBAAA,QAEA,mDACE,iBAAA,QApCJ,sCACE,QAAA,EAEA,4DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,wDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,iDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAIJ,sDACE,iBAAA,QAEA,6DACE,iBAAA,QAIJ,kDACE,iBAAA,QAEA,yDACE,iBAAA,QAIJ,2CACE,iBAAA,QAEA,kDACE,iBAAA,QApCJ,sCACE,QAAA,EAEA,4DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,wDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAGF,iDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,qBAIJ,sDACE,iBAAA,QAEA,6DACE,iBAAA,QAIJ,kDACE,iBAAA,QAEA,yDACE,iBAAA,QAIJ,2CACE,iBAAA,QAEA,kDACE,iBAAA,QApCJ,uCACE,QAAA,EAEA,6DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,sBAGF,yDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,sBAGF,kDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,sBAIJ,uDACE,iBAAA,KAEA,8DACE,iBAAA,KAIJ,mDACE,iBAAA,KAEA,0DACE,iBAAA,KAIJ,4CACE,iBAAA,KAEA,mDACE,iBAAA,KApCJ,sCACE,QAAA,EAEA,4DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,sBAGF,wDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,sBAGF,iDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,sBAIJ,sDACE,iBAAA,QAEA,6DACE,iBAAA,QAIJ,kDACE,iBAAA,QAEA,yDACE,iBAAA,QAIJ,2CACE,iBAAA,QAEA,kDACE,iBAAA,QApCJ,2CACE,QAAA,EAEA,iEACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,mBAGF,6DACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,mBAGF,sDACE,WAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,EAAA,EAAA,EAAA,IAAA,mBAIJ,2DACE,iBAAA,QAEA,kEACE,iBAAA,QAIJ,uDACE,iBAAA,QAEA,8DACE,iBAAA,QAIJ,gDACE,iBAAA,QAEA,uDACE,iBAAA,QAaN,oEAEI,aAAA,QDrFF,iBAAA,QCmFF,+GAQM,iBAAA,sNARN,4GAWM,iBAAA,gKAXN,kEAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,oBAlBN,gFAyBI,aAAA,QAzBJ,kFA6BI,iBAAA,QACA,aAAA,QA9BJ,sEAEI,aAAA,QDrFF,iBAAA,QCmFF,iHAQM,iBAAA,sNARN,8GAWM,iBAAA,gKAXN,oEAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,sBAlBN,kFAyBI,aAAA,QAzBJ,oFA6BI,iBAAA,QACA,aAAA,QA9BJ,oEAEI,aAAA,QDrFF,iBAAA,QCmFF,+GAQM,iBAAA,sNARN,4GAWM,iBAAA,gKAXN,kEAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,oBAlBN,gFAyBI,aAAA,QAzBJ,kFA6BI,iBAAA,QACA,aAAA,QA9BJ,iEAEI,aAAA,QDrFF,iBAAA,QCmFF,4GAQM,iBAAA,sNARN,yGAWM,iBAAA,gKAXN,+DAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,qBAlBN,6EAyBI,aAAA,QAzBJ,+EA6BI,iBAAA,QACA,aAAA,QA9BJ,oEAEI,aAAA,QDrFF,iBAAA,QCmFF,+GAQM,iBAAA,sNARN,4GAWM,iBAAA,gKAXN,kEAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,oBAlBN,gFAyBI,aAAA,QAzBJ,kFA6BI,iBAAA,QACA,aAAA,QA9BJ,mEAEI,aAAA,QDrFF,iBAAA,QCmFF,8GAQM,iBAAA,sNARN,2GAWM,iBAAA,gKAXN,iEAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,oBAlBN,+EAyBI,aAAA,QAzBJ,iFA6BI,iBAAA,QACA,aAAA,QA9BJ,kEAEI,aAAA,QDrFF,iBAAA,QCmFF,6GAQM,iBAAA,sNARN,0GAWM,iBAAA,gKAXN,gEAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,sBAlBN,8EAyBI,aAAA,KAzBJ,gFA6BI,iBAAA,KACA,aAAA,KA9BJ,iEAEI,aAAA,QDrFF,iBAAA,QCmFF,4GAQM,iBAAA,sNARN,yGAWM,iBAAA,gKAXN,+DAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,mBAlBN,6EAyBI,aAAA,QAzBJ,+EA6BI,iBAAA,QACA,aAAA,QA9BJ,sEAEI,aAAA,QDrFF,iBAAA,QCmFF,iHAQM,iBAAA,sNARN,8GAWM,iBAAA,gKAXN,oEAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,qBAlBN,kFAyBI,aAAA,QAzBJ,oFA6BI,iBAAA,QACA,aAAA,QA9BJ,iEAEI,aAAA,QDrFF,iBAAA,QCmFF,4GAQM,iBAAA,sNARN,yGAWM,iBAAA,gKAXN,+DAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,kBAlBN,6EAyBI,aAAA,QAzBJ,+EA6BI,iBAAA,QACA,aAAA,QA9BJ,kEAEI,aAAA,QDrFF,iBAAA,QCmFF,6GAQM,iBAAA,sNARN,0GAWM,iBAAA,gKAXN,gEAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,qBAlBN,8EAyBI,aAAA,QAzBJ,gFA6BI,iBAAA,QACA,aAAA,QA9BJ,iEAEI,aAAA,QDrFF,iBAAA,QCmFF,4GAQM,iBAAA,sNARN,yGAWM,iBAAA,gKAXN,+DAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,oBAlBN,6EAyBI,aAAA,QAzBJ,+EA6BI,iBAAA,QACA,aAAA,QA9BJ,oEAEI,aAAA,QDrFF,iBAAA,QCmFF,+GAQM,iBAAA,sNARN,4GAWM,iBAAA,gKAXN,kEAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,qBAlBN,gFAyBI,aAAA,QAzBJ,kFA6BI,iBAAA,QACA,aAAA,QA9BJ,mEAEI,aAAA,QDrFF,iBAAA,QCmFF,8GAQM,iBAAA,sNARN,2GAWM,iBAAA,gKAXN,iEAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,oBAlBN,+EAyBI,aAAA,QAzBJ,iFA6BI,iBAAA,QACA,aAAA,QA9BJ,iEAEI,aAAA,QDrFF,iBAAA,QCmFF,4GAQM,iBAAA,sNARN,yGAWM,iBAAA,gKAXN,+DAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,oBAlBN,6EAyBI,aAAA,QAzBJ,+EA6BI,iBAAA,QACA,aAAA,QA9BJ,mEAEI,aAAA,QDrFF,iBAAA,QCmFF,8GAQM,iBAAA,sNARN,2GAWM,iBAAA,gKAXN,iEAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,qBAlBN,+EAyBI,aAAA,QAzBJ,iFA6BI,iBAAA,QACA,aAAA,QA9BJ,mEAEI,aAAA,QDrFF,iBAAA,QCmFF,8GAQM,iBAAA,sNARN,2GAWM,iBAAA,gKAXN,iEAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,qBAlBN,+EAyBI,aAAA,QAzBJ,iFA6BI,iBAAA,QACA,aAAA,QA9BJ,iEAEI,aAAA,QDrFF,iBAAA,QCmFF,4GAQM,iBAAA,sNARN,yGAWM,iBAAA,gKAXN,+DAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,qBAlBN,6EAyBI,aAAA,QAzBJ,+EA6BI,iBAAA,QACA,aAAA,QA9BJ,gEAEI,aAAA,QDrFF,iBAAA,QCmFF,2GAQM,iBAAA,sNARN,wGAWM,iBAAA,gKAXN,8DAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,oBAlBN,4EAyBI,aAAA,QAzBJ,8EA6BI,iBAAA,QACA,aAAA,QA9BJ,mEAEI,aAAA,QDrFF,iBAAA,QCmFF,8GAQM,iBAAA,sNARN,2GAWM,iBAAA,gKAXN,iEAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,qBAlBN,+EAyBI,aAAA,QAzBJ,iFA6BI,iBAAA,QACA,aAAA,QA9BJ,mEAEI,aAAA,QDrFF,iBAAA,QCmFF,8GAQM,iBAAA,sNARN,2GAWM,iBAAA,gKAXN,iEAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,oBAlBN,+EAyBI,aAAA,QAzBJ,iFA6BI,iBAAA,QACA,aAAA,QA9BJ,kEAEI,aAAA,QDrFF,iBAAA,QCmFF,6GAQM,iBAAA,sNARN,0GAWM,iBAAA,gKAXN,gEAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,oBAlBN,8EAyBI,aAAA,QAzBJ,gFA6BI,iBAAA,QACA,aAAA,QA9BJ,iEAEI,aAAA,QDrFF,iBAAA,QCmFF,4GAQM,iBAAA,sNARN,yGAWM,iBAAA,gKAXN,+DAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,qBAlBN,6EAyBI,aAAA,QAzBJ,+EA6BI,iBAAA,QACA,aAAA,QA9BJ,iEAEI,aAAA,QDrFF,iBAAA,QCmFF,4GAQM,iBAAA,sNARN,yGAWM,iBAAA,gKAXN,+DAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,qBAlBN,6EAyBI,aAAA,QAzBJ,+EA6BI,iBAAA,QACA,aAAA,QA9BJ,kEAEI,aAAA,KDrFF,iBAAA,KCmFF,6GAQM,iBAAA,mNARN,0GAWM,iBAAA,6JAXN,gEAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,sBAlBN,8EAyBI,aAAA,KAzBJ,gFA6BI,iBAAA,KACA,aAAA,KA9BJ,iEAEI,aAAA,QDrFF,iBAAA,QCmFF,4GAQM,iBAAA,sNARN,yGAWM,iBAAA,gKAXN,+DAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,sBAlBN,6EAyBI,aAAA,QAzBJ,+EA6BI,iBAAA,QACA,aAAA,QA9BJ,sEAEI,aAAA,QDrFF,iBAAA,QCmFF,iHAQM,iBAAA,sNARN,8GAWM,iBAAA,gKAXN,oEAkBM,WAAA,MAAA,EAAA,EAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,MAAA,mBAlBN,kFAyBI,aAAA,QAzBJ,oFA6BI,iBAAA,QACA,aAAA,QL4MN,4DAEI,iBAAA,sBACA,WAAA,KAHJ,oEI7TI,iBAAA,YFsoFJ,yCAFA,8BACA,qCAFA,0BF5zEA,yBEg0EA,6BFzzEI,iBAAA,QACA,MAAA,KARJ,gFAWI,aAAA,QAXJ,kBAcI,iBAAA,QACA,MAAA,KACA,aAAA,QAhBJ,6BAoBI,aAAA,QApBJ,wEEo1EA,yEF3zEI,iBAAA,QACA,aAAA,QACA,MAAA,KA3BJ,wDAgCM,iBAAA,QAhCN,2CAmCM,iBAAA,QAnCN,oCAsCM,iBAAA,QAtCN,oCEu2EA,6CF1zEM,iBAAA,QACA,OAAA,IAAA,MAAA,QA9CN,0CAkDQ,iBAAA,QAlDR,0CAqDQ,iBAAA,QEg0ER,6EFr3EA,8EA4DQ,iBAAA,QACA,MAAA,KACA,OAAA,IAAA,MAAA,QACA,YAAA,KMvYR,UCWM,WAAA,KNEF,cAAA,IKbJ,mBAMI,QAAA,aACA,OAAA,MACA,aAAA,KACA,SAAA,SACA,MAAA,KAVJ,iCAaM,OAAA,EACA,SAAA,SACA,MAAA,KAfN,+BAAA,sBAqBM,MAAA,KArBN,+BAAA,sBA0BM,MAAA,KA1BN,gCAAA,uBA+BM,MAAA,IAKN,gBACE,cAAA,MAIF,aACE,OAAA,KAGF,aACE,OAAA,IAGF,cACE,OAAA,IAIF,uBAGM,OAAA,EAKN,qBAEI,WAAA,QEhEF,8CAGM,iBAAA,QAHN,8CNgwFF,gDMzvFU,MAAA,KAPR,uDAWQ,MAAA,QAXR,2BAiBI,WAAA,IAAA,MAAA,QAjBJ,qDAwBU,WAAA,IAAA,MAAA,QAxBV,sDA4BU,WAAA,IAAA,MAAA,QNyvFZ,+BMlvFE,sBNmvFF,2CM/uFM,MAAA,qBNovFN,qCMxvFE,4BNyvFF,iDMlvFQ,MAAA,KNwvFR,qEACA,qEMpvFE,4DNkvFF,4DM7uFQ,OAAA,KNyvFR,8EACA,+EACA,iFACA,iFAJA,+FAJA,qEACA,sEACA,wEACA,wEM5vFE,sFAaM,iBAAA,QACA,MAAA,KNyvFR,kFMvwFE,yEAkBM,oBAAA,KN2vFR,2EACA,iFM9wFE,kEN4wFF,wEMrvFQ,iBAAA,QACA,MAAA,KAvEN,gDAGM,iBAAA,QAHN,gDNu0FF,kDMh0FU,MAAA,KAPR,yDAWQ,MAAA,QAXR,6BAiBI,WAAA,IAAA,MAAA,QAjBJ,uDAwBU,WAAA,IAAA,MAAA,QAxBV,wDA4BU,WAAA,IAAA,MAAA,QNg0FZ,iCMzzFE,wBN0zFF,6CMtzFM,MAAA,qBN2zFN,uCM/zFE,8BNg0FF,mDMzzFQ,MAAA,KN+zFR,uEACA,uEM3zFE,8DNyzFF,8DMpzFQ,OAAA,KNg0FR,gFACA,iFACA,mFACA,mFAJA,iGAJA,uEACA,wEACA,0EACA,0EMn0FE,wFAaM,iBAAA,QACA,MAAA,KNg0FR,oFM90FE,2EAkBM,oBAAA,KNk0FR,6EACA,mFMr1FE,oENm1FF,0EM5zFQ,iBAAA,QACA,MAAA,KAvEN,8CAGM,iBAAA,QAHN,8CN84FF,gDMv4FU,MAAA,KAPR,uDAWQ,MAAA,QAXR,2BAiBI,WAAA,IAAA,MAAA,QAjBJ,qDAwBU,WAAA,IAAA,MAAA,QAxBV,sDA4BU,WAAA,IAAA,MAAA,QNu4FZ,+BMh4FE,sBNi4FF,2CM73FM,MAAA,qBNk4FN,qCMt4FE,4BNu4FF,iDMh4FQ,MAAA,KNs4FR,qEACA,qEMl4FE,4DNg4FF,4DM33FQ,OAAA,KNu4FR,8EACA,+EACA,iFACA,iFAJA,+FAJA,qEACA,sEACA,wEACA,wEM14FE,sFAaM,iBAAA,QACA,MAAA,KNu4FR,kFMr5FE,yEAkBM,oBAAA,KNy4FR,2EACA,iFM55FE,kEN05FF,wEMn4FQ,iBAAA,QACA,MAAA,KAvEN,2CAGM,iBAAA,QAHN,2CNq9FF,6CM98FU,MAAA,KAPR,oDAWQ,MAAA,QAXR,wBAiBI,WAAA,IAAA,MAAA,QAjBJ,kDAwBU,WAAA,IAAA,MAAA,QAxBV,mDA4BU,WAAA,IAAA,MAAA,QN88FZ,4BMv8FE,mBNw8FF,wCMp8FM,MAAA,qBNy8FN,kCM78FE,yBN88FF,8CMv8FQ,MAAA,KN68FR,kEACA,kEMz8FE,yDNu8FF,yDMl8FQ,OAAA,KN88FR,2EACA,4EACA,8EACA,8EAJA,4FAJA,kEACA,mEACA,qEACA,qEMj9FE,mFAaM,iBAAA,QACA,MAAA,KN88FR,+EM59FE,sEAkBM,oBAAA,KNg9FR,wEACA,8EMn+FE,+DNi+FF,qEM18FQ,iBAAA,QACA,MAAA,KAvEN,8CAGM,iBAAA,QAHN,8CN4hGF,gDMrhGU,MAAA,QAPR,uDAWQ,MAAA,QAXR,2BAiBI,WAAA,IAAA,MAAA,QAjBJ,qDAwBU,WAAA,IAAA,MAAA,QAxBV,sDA4BU,WAAA,IAAA,MAAA,QNqhGZ,+BM9gGE,sBN+gGF,2CM3gGM,MAAA,kBNghGN,qCMphGE,4BNqhGF,iDM9gGQ,MAAA,QNohGR,qEACA,qEMhhGE,4DN8gGF,4DMzgGQ,OAAA,KNqhGR,8EACA,+EACA,iFACA,iFAJA,+FAJA,qEACA,sEACA,wEACA,wEMxhGE,sFAaM,iBAAA,QACA,MAAA,QNqhGR,kFMniGE,yEAkBM,oBAAA,QNuhGR,2EACA,iFM1iGE,kENwiGF,wEMjhGQ,iBAAA,QACA,MAAA,QAvEN,6CAGM,iBAAA,QAHN,6CNmmGF,+CM5lGU,MAAA,KAPR,sDAWQ,MAAA,QAXR,0BAiBI,WAAA,IAAA,MAAA,QAjBJ,oDAwBU,WAAA,IAAA,MAAA,QAxBV,qDA4BU,WAAA,IAAA,MAAA,QAOV,qBNqlGF,8BACA,0CMllGM,MAAA,qBAJJ,2BN2lGF,oCACA,gDMrlGQ,MAAA,KAKN,2DNqlGF,2DACA,oEACA,oEMllGQ,OAAA,KNulGR,oEACA,qEACA,uEACA,uEM/lGE,qFNimGF,6EACA,8EACA,gFACA,gFAJA,8FMnlGQ,iBAAA,QACA,MAAA,KAdN,wEN0mGF,iFMxlGQ,oBAAA,KAlBN,iEN+mGF,uEACA,0EACA,gFM1lGQ,iBAAA,QACA,MAAA,KAvEN,4CAGM,iBAAA,QAHN,4CN0qGF,8CMnqGU,MAAA,QAPR,qDAWQ,MAAA,QAXR,yBAiBI,WAAA,IAAA,MAAA,QAjBJ,mDAwBU,WAAA,IAAA,MAAA,QAxBV,oDA4BU,WAAA,IAAA,MAAA,QNmqGZ,6BM5pGE,oBN6pGF,yCMzpGM,MAAA,kBN8pGN,mCMlqGE,0BNmqGF,+CM5pGQ,MAAA,QNkqGR,mEACA,mEM9pGE,0DN4pGF,0DMvpGQ,OAAA,KNmqGR,4EACA,6EACA,+EACA,+EAJA,6FAJA,mEACA,oEACA,sEACA,sEMtqGE,oFAaM,iBAAA,QACA,MAAA,QNmqGR,gFMjrGE,uEAkBM,oBAAA,QNqqGR,yEACA,+EMxrGE,gENsrGF,sEM/pGQ,iBAAA,KACA,MAAA,QAvEN,2CAGM,iBAAA,QAHN,2CNivGF,6CM1uGU,MAAA,KAPR,oDAWQ,MAAA,QAXR,wBAiBI,WAAA,IAAA,MAAA,QAjBJ,kDAwBU,WAAA,IAAA,MAAA,QAxBV,mDA4BU,WAAA,IAAA,MAAA,QAOV,mBNmuGF,4BACA,wCMhuGM,MAAA,qBAJJ,yBNyuGF,kCACA,8CMnuGQ,MAAA,KAKN,yDNmuGF,yDACA,kEACA,kEMhuGQ,OAAA,KNquGR,kEACA,mEACA,qEACA,qEM7uGE,mFN+uGF,2EACA,4EACA,8EACA,8EAJA,4FMjuGQ,iBAAA,QACA,MAAA,KAdN,sENwvGF,+EMtuGQ,oBAAA,KAlBN,+DN6vGF,qEACA,wEACA,8EMxuGQ,iBAAA,QACA,MAAA,KAvEN,gDAGM,iBAAA,QAHN,gDNwzGF,kDMjzGU,MAAA,KAPR,yDAWQ,MAAA,QAXR,6BAiBI,WAAA,IAAA,MAAA,QAjBJ,uDAwBU,WAAA,IAAA,MAAA,QAxBV,wDA4BU,WAAA,IAAA,MAAA,QNizGZ,iCM1yGE,wBN2yGF,6CMvyGM,MAAA,qBN4yGN,uCMhzGE,8BNizGF,mDM1yGQ,MAAA,KNgzGR,uEACA,uEM5yGE,8DN0yGF,8DMryGQ,OAAA,KNizGR,gFACA,iFACA,mFACA,mFAJA,iGAJA,uEACA,wEACA,0EACA,0EMpzGE,wFAaM,iBAAA,QACA,MAAA,KNizGR,oFM/zGE,2EAkBM,oBAAA,KNmzGR,6EACA,mFMt0GE,oENo0GF,0EM7yGQ,iBAAA,QACA,MAAA,KAvEN,2CAGM,iBAAA,QAHN,2CN+3GF,6CMx3GU,MAAA,KAPR,oDAWQ,MAAA,QAXR,wBAiBI,WAAA,IAAA,MAAA,QAjBJ,kDAwBU,WAAA,IAAA,MAAA,QAxBV,mDA4BU,WAAA,IAAA,MAAA,QNw3GZ,4BMj3GE,mBNk3GF,wCM92GM,MAAA,qBNm3GN,kCMv3GE,yBNw3GF,8CMj3GQ,MAAA,KNu3GR,kEACA,kEMn3GE,yDNi3GF,yDM52GQ,OAAA,KNw3GR,2EACA,4EACA,8EACA,8EAJA,4FAJA,kEACA,mEACA,qEACA,qEM33GE,mFAaM,iBAAA,QACA,MAAA,KNw3GR,+EMt4GE,sEAkBM,oBAAA,KN03GR,wEACA,8EM74GE,+DN24GF,qEMp3GQ,iBAAA,QACA,MAAA,KAvEN,4CAGM,iBAAA,QAHN,4CNs8GF,8CM/7GU,MAAA,KAPR,qDAWQ,MAAA,QAXR,yBAiBI,WAAA,IAAA,MAAA,QAjBJ,mDAwBU,WAAA,IAAA,MAAA,QAxBV,oDA4BU,WAAA,IAAA,MAAA,QN+7GZ,6BMx7GE,oBNy7GF,yCMr7GM,MAAA,qBN07GN,mCM97GE,0BN+7GF,+CMx7GQ,MAAA,KN87GR,mEACA,mEM17GE,0DNw7GF,0DMn7GQ,OAAA,KN+7GR,4EACA,6EACA,+EACA,+EAJA,6FAJA,mEACA,oEACA,sEACA,sEMl8GE,oFAaM,iBAAA,QACA,MAAA,KN+7GR,gFM78GE,uEAkBM,oBAAA,KNi8GR,yEACA,+EMp9GE,gENk9GF,sEM37GQ,iBAAA,QACA,MAAA,KAvEN,2CAGM,iBAAA,QAHN,2CN6gHF,6CMtgHU,MAAA,QAPR,oDAWQ,MAAA,QAXR,wBAiBI,WAAA,IAAA,MAAA,QAjBJ,kDAwBU,WAAA,IAAA,MAAA,QAxBV,mDA4BU,WAAA,IAAA,MAAA,QNsgHZ,4BM//GE,mBNggHF,wCM5/GM,MAAA,kBNigHN,kCMrgHE,yBNsgHF,8CM//GQ,MAAA,QNqgHR,kEACA,kEMjgHE,yDN+/GF,yDM1/GQ,OAAA,KNsgHR,2EACA,4EACA,8EACA,8EAJA,4FAJA,kEACA,mEACA,qEACA,qEMzgHE,mFAaM,iBAAA,QACA,MAAA,QNsgHR,+EMphHE,sEAkBM,oBAAA,QNwgHR,wEACA,8EM3hHE,+DNyhHF,qEMlgHQ,iBAAA,QACA,MAAA,QAvEN,8CAGM,iBAAA,QAHN,8CNolHF,gDM7kHU,MAAA,KAPR,uDAWQ,MAAA,QAXR,2BAiBI,WAAA,IAAA,MAAA,QAjBJ,qDAwBU,WAAA,IAAA,MAAA,QAxBV,sDA4BU,WAAA,IAAA,MAAA,QAOV,sBNskHF,+BACA,2CMnkHM,MAAA,qBAJJ,4BN4kHF,qCACA,iDMtkHQ,MAAA,KAKN,4DNskHF,4DACA,qEACA,qEMnkHQ,OAAA,KNwkHR,qEACA,sEACA,wEACA,wEMhlHE,sFNklHF,8EACA,+EACA,iFACA,iFAJA,+FMpkHQ,iBAAA,QACA,MAAA,KAdN,yEN2lHF,kFMzkHQ,oBAAA,KAlBN,kENgmHF,wEACA,2EACA,iFM3kHQ,iBAAA,QACA,MAAA,KAvEN,6CAGM,iBAAA,QAHN,6CN2pHF,+CMppHU,MAAA,KAPR,sDAWQ,MAAA,QAXR,0BAiBI,WAAA,IAAA,MAAA,QAjBJ,oDAwBU,WAAA,IAAA,MAAA,QAxBV,qDA4BU,WAAA,IAAA,MAAA,QNopHZ,8BM7oHE,qBN8oHF,0CM1oHM,MAAA,qBN+oHN,oCMnpHE,2BNopHF,gDM7oHQ,MAAA,KNmpHR,oEACA,oEM/oHE,2DN6oHF,2DMxoHQ,OAAA,KNopHR,6EACA,8EACA,gFACA,gFAJA,8FAJA,oEACA,qEACA,uEACA,uEMvpHE,qFAaM,iBAAA,QACA,MAAA,KNopHR,iFMlqHE,wEAkBM,oBAAA,KNspHR,0EACA,gFMzqHE,iENuqHF,uEMhpHQ,iBAAA,QACA,MAAA,KAvEN,2CAGM,iBAAA,QAHN,2CNkuHF,6CM3tHU,MAAA,KAPR,oDAWQ,MAAA,QAXR,wBAiBI,WAAA,IAAA,MAAA,QAjBJ,kDAwBU,WAAA,IAAA,MAAA,QAxBV,mDA4BU,WAAA,IAAA,MAAA,QAOV,mBNotHF,4BACA,wCMjtHM,MAAA,qBAJJ,yBN0tHF,kCACA,8CMptHQ,MAAA,KAKN,yDNotHF,yDACA,kEACA,kEMjtHQ,OAAA,KNstHR,kEACA,mEACA,qEACA,qEM9tHE,mFNguHF,2EACA,4EACA,8EACA,8EAJA,4FMltHQ,iBAAA,QACA,MAAA,KAdN,sENyuHF,+EMvtHQ,oBAAA,KAlBN,+DN8uHF,qEACA,wEACA,8EMztHQ,iBAAA,QACA,MAAA,KAvEN,6CAGM,iBAAA,QAHN,6CNyyHF,+CMlyHU,MAAA,KAPR,sDAWQ,MAAA,QAXR,0BAiBI,WAAA,IAAA,MAAA,QAjBJ,oDAwBU,WAAA,IAAA,MAAA,QAxBV,qDA4BU,WAAA,IAAA,MAAA,QNkyHZ,8BM3xHE,qBN4xHF,0CMxxHM,MAAA,qBN6xHN,oCMjyHE,2BNkyHF,gDM3xHQ,MAAA,KNiyHR,oEACA,oEM7xHE,2DN2xHF,2DMtxHQ,OAAA,KNkyHR,6EACA,8EACA,gFACA,gFAJA,8FAJA,oEACA,qEACA,uEACA,uEMryHE,qFAaM,iBAAA,QACA,MAAA,KNkyHR,iFMhzHE,wEAkBM,oBAAA,KNoyHR,0EACA,gFMvzHE,iENqzHF,uEM9xHQ,iBAAA,QACA,MAAA,KAvEN,6CAGM,iBAAA,QAHN,6CNg3HF,+CMz2HU,MAAA,KAPR,sDAWQ,MAAA,QAXR,0BAiBI,WAAA,IAAA,MAAA,QAjBJ,oDAwBU,WAAA,IAAA,MAAA,QAxBV,qDA4BU,WAAA,IAAA,MAAA,QNy2HZ,8BMl2HE,qBNm2HF,0CM/1HM,MAAA,qBNo2HN,oCMx2HE,2BNy2HF,gDMl2HQ,MAAA,KNw2HR,oEACA,oEMp2HE,2DNk2HF,2DM71HQ,OAAA,KNy2HR,6EACA,8EACA,gFACA,gFAJA,8FAJA,oEACA,qEACA,uEACA,uEM52HE,qFAaM,iBAAA,QACA,MAAA,KNy2HR,iFMv3HE,wEAkBM,oBAAA,KN22HR,0EACA,gFM93HE,iEN43HF,uEMr2HQ,iBAAA,QACA,MAAA,KAvEN,2CAGM,iBAAA,QAHN,2CNu7HF,6CMh7HU,MAAA,KAPR,oDAWQ,MAAA,QAXR,wBAiBI,WAAA,IAAA,MAAA,QAjBJ,kDAwBU,WAAA,IAAA,MAAA,QAxBV,mDA4BU,WAAA,IAAA,MAAA,QNg7HZ,4BMz6HE,mBN06HF,wCMt6HM,MAAA,qBN26HN,kCM/6HE,yBNg7HF,8CMz6HQ,MAAA,KN+6HR,kEACA,kEM36HE,yDNy6HF,yDMp6HQ,OAAA,KNg7HR,2EACA,4EACA,8EACA,8EAJA,4FAJA,kEACA,mEACA,qEACA,qEMn7HE,mFAaM,iBAAA,QACA,MAAA,KNg7HR,+EM97HE,sEAkBM,oBAAA,KNk7HR,wEACA,8EMr8HE,+DNm8HF,qEM56HQ,iBAAA,QACA,MAAA,KAvEN,0CAGM,iBAAA,QAHN,0CN8/HF,4CMv/HU,MAAA,KAPR,mDAWQ,MAAA,QAXR,uBAiBI,WAAA,IAAA,MAAA,QAjBJ,iDAwBU,WAAA,IAAA,MAAA,QAxBV,kDA4BU,WAAA,IAAA,MAAA,QNu/HZ,2BMh/HE,kBNi/HF,uCM7+HM,MAAA,qBNk/HN,iCMt/HE,wBNu/HF,6CMh/HQ,MAAA,KNs/HR,iEACA,iEMl/HE,wDNg/HF,wDM3+HQ,OAAA,KNu/HR,0EACA,2EACA,6EACA,6EAJA,2FAJA,iEACA,kEACA,oEACA,oEM1/HE,kFAaM,iBAAA,QACA,MAAA,KNu/HR,8EMrgIE,qEAkBM,oBAAA,KNy/HR,uEACA,6EM5gIE,8DN0gIF,oEMn/HQ,iBAAA,QACA,MAAA,KAvEN,6CAGM,iBAAA,QAHN,6CNqkIF,+CM9jIU,MAAA,QAPR,sDAWQ,MAAA,QAXR,0BAiBI,WAAA,IAAA,MAAA,QAjBJ,oDAwBU,WAAA,IAAA,MAAA,QAxBV,qDA4BU,WAAA,IAAA,MAAA,QN8jIZ,8BMvjIE,qBNwjIF,0CMpjIM,MAAA,kBNyjIN,oCM7jIE,2BN8jIF,gDMvjIQ,MAAA,QN6jIR,oEACA,oEMzjIE,2DNujIF,2DMljIQ,OAAA,KN8jIR,6EACA,8EACA,gFACA,gFAJA,8FAJA,oEACA,qEACA,uEACA,uEMjkIE,qFAaM,iBAAA,QACA,MAAA,QN8jIR,iFM5kIE,wEAkBM,oBAAA,QNgkIR,0EACA,gFMnlIE,iENilIF,uEM1jIQ,iBAAA,QACA,MAAA,QAvEN,6CAGM,iBAAA,QAHN,6CN4oIF,+CMroIU,MAAA,QAPR,sDAWQ,MAAA,QAXR,0BAiBI,WAAA,IAAA,MAAA,QAjBJ,oDAwBU,WAAA,IAAA,MAAA,QAxBV,qDA4BU,WAAA,IAAA,MAAA,QNqoIZ,8BM9nIE,qBN+nIF,0CM3nIM,MAAA,kBNgoIN,oCMpoIE,2BNqoIF,gDM9nIQ,MAAA,QNooIR,oEACA,oEMhoIE,2DN8nIF,2DMznIQ,OAAA,KNqoIR,6EACA,8EACA,gFACA,gFAJA,8FAJA,oEACA,qEACA,uEACA,uEMxoIE,qFAaM,iBAAA,QACA,MAAA,QNqoIR,iFMnpIE,wEAkBM,oBAAA,QNuoIR,0EACA,gFM1pIE,iENwpIF,uEMjoIQ,iBAAA,QACA,MAAA,QAvEN,4CAGM,iBAAA,QAHN,4CNmtIF,8CM5sIU,MAAA,KAPR,qDAWQ,MAAA,QAXR,yBAiBI,WAAA,IAAA,MAAA,QAjBJ,mDAwBU,WAAA,IAAA,MAAA,QAxBV,oDA4BU,WAAA,IAAA,MAAA,QN4sIZ,6BMrsIE,oBNssIF,yCMlsIM,MAAA,qBNusIN,mCM3sIE,0BN4sIF,+CMrsIQ,MAAA,KN2sIR,mEACA,mEMvsIE,0DNqsIF,0DMhsIQ,OAAA,KN4sIR,4EACA,6EACA,+EACA,+EAJA,6FAJA,mEACA,oEACA,sEACA,sEM/sIE,oFAaM,iBAAA,QACA,MAAA,KN4sIR,gFM1tIE,uEAkBM,oBAAA,KN8sIR,yEACA,+EMjuIE,gEN+tIF,sEMxsIQ,iBAAA,QACA,MAAA,KAvEN,2CAGM,iBAAA,QAHN,2CN0xIF,6CMnxIU,MAAA,KAPR,oDAWQ,MAAA,QAXR,wBAiBI,WAAA,IAAA,MAAA,QAjBJ,kDAwBU,WAAA,IAAA,MAAA,QAxBV,mDA4BU,WAAA,IAAA,MAAA,QNmxIZ,4BM5wIE,mBN6wIF,wCMzwIM,MAAA,qBN8wIN,kCMlxIE,yBNmxIF,8CM5wIQ,MAAA,KNkxIR,kEACA,kEM9wIE,yDN4wIF,yDMvwIQ,OAAA,KNmxIR,2EACA,4EACA,8EACA,8EAJA,4FAJA,kEACA,mEACA,qEACA,qEMtxIE,mFAaM,iBAAA,QACA,MAAA,KNmxIR,+EMjyIE,sEAkBM,oBAAA,KNqxIR,wEACA,8EMxyIE,+DNsyIF,qEM/wIQ,iBAAA,QACA,MAAA,KAvEN,2CAGM,iBAAA,QAHN,2CNi2IF,6CM11IU,MAAA,KAPR,oDAWQ,MAAA,QAXR,wBAiBI,WAAA,IAAA,MAAA,QAjBJ,kDAwBU,WAAA,IAAA,MAAA,QAxBV,mDA4BU,WAAA,IAAA,MAAA,QAOV,mBNm1IF,4BACA,wCMh1IM,MAAA,qBAJJ,yBNy1IF,kCACA,8CMn1IQ,MAAA,KAKN,yDNm1IF,yDACA,kEACA,kEMh1IQ,OAAA,KNq1IR,kEACA,mEACA,qEACA,qEM71IE,mFN+1IF,2EACA,4EACA,8EACA,8EAJA,4FMj1IQ,iBAAA,QACA,MAAA,KAdN,sENw2IF,+EMt1IQ,oBAAA,KAlBN,+DN62IF,qEACA,wEACA,8EMx1IQ,iBAAA,QACA,MAAA,KAvEN,4CAGM,iBAAA,KAHN,4CNw6IF,8CMj6IU,MAAA,QAPR,qDAWQ,MAAA,QAXR,yBAiBI,WAAA,IAAA,MAAA,KAjBJ,mDAwBU,WAAA,IAAA,MAAA,QAxBV,oDA4BU,WAAA,IAAA,MAAA,KNi6IZ,6BM15IE,oBN25IF,yCMv5IM,MAAA,kBN45IN,mCMh6IE,0BNi6IF,+CM15IQ,MAAA,QNg6IR,mEACA,mEM55IE,0DN05IF,0DMr5IQ,OAAA,KNi6IR,4EACA,6EACA,+EACA,+EAJA,6FAJA,mEACA,oEACA,sEACA,sEMp6IE,oFAaM,iBAAA,QACA,MAAA,QNi6IR,gFM/6IE,uEAkBM,oBAAA,QNm6IR,yEACA,+EMt7IE,gENo7IF,sEM75IQ,iBAAA,KACA,MAAA,QAvEN,2CAGM,iBAAA,QAHN,2CN++IF,6CMx+IU,MAAA,KAPR,oDAWQ,MAAA,QAXR,wBAiBI,WAAA,IAAA,MAAA,QAjBJ,kDAwBU,WAAA,IAAA,MAAA,QAxBV,mDA4BU,WAAA,IAAA,MAAA,QNw+IZ,4BMj+IE,mBNk+IF,wCM99IM,MAAA,qBNm+IN,kCMv+IE,yBNw+IF,8CMj+IQ,MAAA,KNu+IR,kEACA,kEMn+IE,yDNi+IF,yDM59IQ,OAAA,KNw+IR,2EACA,4EACA,8EACA,8EAJA,4FAJA,kEACA,mEACA,qEACA,qEM3+IE,mFAaM,iBAAA,QACA,MAAA,KNw+IR,+EMt/IE,sEAkBM,oBAAA,KN0+IR,wEACA,8EM7/IE,+DN2/IF,qEMp+IQ,iBAAA,QACA,MAAA,KAvEN,gDAGM,iBAAA,QAHN,gDNsjJF,kDM/iJU,MAAA,KAPR,yDAWQ,MAAA,QAXR,6BAiBI,WAAA,IAAA,MAAA,QAjBJ,uDAwBU,WAAA,IAAA,MAAA,QAxBV,wDA4BU,WAAA,IAAA,MAAA,QN+iJZ,iCMxiJE,wBNyiJF,6CMriJM,MAAA,qBN0iJN,uCM9iJE,8BN+iJF,mDMxiJQ,MAAA,KN8iJR,uEACA,uEM1iJE,8DNwiJF,8DMniJQ,OAAA,KN+iJR,gFACA,iFACA,mFACA,mFAJA,iGAJA,uEACA,wEACA,0EACA,0EMljJE,wFAaM,iBAAA,QACA,MAAA,KN+iJR,oFM7jJE,2EAkBM,oBAAA,KNijJR,6EACA,mFMpkJE,oENkkJF,0EM3iJQ,iBAAA,QACA,MAAA,KC/DR,MFGM,WAAA,EAAA,EAAA,IAAA,gBAAA,CAAA,EAAA,IAAA,IAAA,eEDJ,cAAA,KAFF,2BAMM,aAAA,QANN,cP0nJA,yBO/mJM,MAAA,KAXN,qBAgBI,OAAA,eACA,KAAA,EACA,WAAA,eACA,UAAA,eACA,SAAA,MACA,IAAA,EACA,MAAA,eACA,QAAA,KAvBJ,8CA0BM,QAAA,gBA1BN,gCA8BM,SAAA,KA9BN,4CAkCM,QAAA,KPonJN,kCOtpJA,kCRKI,cAAA,YQLJ,gCP2pJA,kCO5mJM,QAAA,KA/CN,0BAqDM,cAAA,IAAA,MAAA,iBACA,OAAA,EAtDN,uCAyDQ,cAAA,EAzDR,gCAiEM,WAAA,MACA,SAAA,KAlEN,oBAuEI,aAAA,IAAA,MAAA,iBAvEJ,mBA2EI,YAAA,IAAA,MAAA,iBA3EJ,gDAiFQ,cAAA,EAjFR,gFAqFY,kBAAA,YArFZ,uCA6FQ,cAAA,EA7FR,6DAgGU,YAAA,EACA,YAAA,EAjGV,4BAuGM,OAAA,MAAA,MAvGN,iEA4GQ,cAAA,EA5GR,2EA+GU,cAAA,EA/GV,qFAkHY,cAAA,EAlHZ,gEA4HY,cAAA,KA5HZ,wBAoII,WAAA,EApIJ,qEAyIU,YAAA,EACA,YAAA,EA1IV,uCA+IQ,WAAA,IAAA,MAAA,YA/IR,6CAkJU,WAAA,IAAA,MAAA,QAlJV,oDAuJY,WAAA,EAvJZ,oCA8JM,OAAA,MAAA,MAAA,MA9JN,yEAkKM,cAAA,EAlKN,mFAqKQ,cAAA,EArKR,6FAwKU,cAAA,EAxKV,wEAiLY,cAAA,KAUZ,oBACE,SAAA,OP4kJF,kBACA,oBQrxJE,oBACE,QAAA,MACA,MAAA,KACA,QAAA,GDgNJ,aACE,iBAAA,YACA,cAAA,IAAA,MAAA,iBACA,QAAA,OAAA,QACA,SAAA,SR7LE,uBAAA,OACA,wBAAA,OQkMF,6BACE,cAAA,EAXJ,yBAeI,MAAA,MACA,aAAA,SAhBJ,sCP2lJA,8BACA,qCOvkJM,cAAA,OACA,WAAA,OAtBN,+CA0BM,SAAA,SAKN,YACE,MAAA,KACA,UAAA,OACA,YAAA,IACA,OAAA,EAGF,WACE,MAAA,KAKF,UACE,iBAAA,YACA,MAAA,QACA,UAAA,QACA,OAAA,QAAA,EACA,QAAA,OAAA,MAEA,0BAAA,gBAEE,MAAA,QAGF,gBAAA,gBAEE,WAAA,eAIJ,qBAEI,UAAA,KAFJ,mBAMI,QAAA,MAAA,MAKJ,kBAQI,cAAA,EP4jJJ,8BOpkJA,8BAYM,iBAAA,EAZN,eAkBI,WAAA,IAlBJ,6BAsBI,OAAA,MAtBJ,iCA0BI,OAAA,KAIJ,cEvTE,aAAA,EACA,WAAA,KFwTA,OAAA,KAAA,EAGE,yBALJ,iBAMM,MAAA,KACA,aAAA,MAMN,eACE,iBAAA,QADF,6BAKI,cAAA,IAAA,MAAA,QACA,QAAA,IAAA,EC7UF,oCACE,QAAA,MACA,MAAA,KACA,QAAA,GDoUJ,0CASM,cAAA,EATN,2CAaM,YAAA,EAbN,iCAiBM,OAAA,SACA,MAAA,SACA,MAAA,KAnBN,6BAwBI,MAAA,QACA,YAAA,KAzBJ,yBA6BI,MAAA,QACA,QAAA,MACA,YAAA,IA/BJ,2BAmCI,UAAA,KACA,YAAA,IAQJ,WACE,WAAA,KACA,OAAA,EACA,SAAA,KACA,QAAA,EAJF,cRlWI,cAAA,IQ2WA,iBAAA,QACA,YAAA,IAAA,MAAA,QACA,MAAA,QACA,cAAA,IACA,QAAA,KAbJ,2BAgBM,cAAA,EAhBN,mCAoBM,OAAA,EAAA,KAAA,EAAA,IApBN,oBAwBM,QAAA,aACA,YAAA,IACA,YAAA,IA1BN,qBA+BM,UAAA,MACA,YAAA,KAhCN,qBAqCM,MAAA,QACA,QAAA,KACA,MAAA,MAvCN,yBPmmJA,0BAEA,0BADA,0BAFA,0BADA,0BAMA,0BADA,qCOpjJQ,OAAA,QACA,aAAA,IAnDR,2BAwDM,QAAA,aAxDN,mBA4DM,MAAA,QA5DN,yBA+DQ,YAAA,IACA,gBAAA,aAhER,0BAoEQ,iBAAA,kBApER,oBA4EM,kBAAA,QA5EN,sBA4EM,kBAAA,QA5EN,oBA4EM,kBAAA,QA5EN,iBA4EM,kBAAA,QA5EN,oBA4EM,kBAAA,QA5EN,mBA4EM,kBAAA,QA5EN,kBA4EM,kBAAA,QA5EN,iBA4EM,kBAAA,QA5EN,sBAkFM,kBAAA,QAlFN,iBAkFM,kBAAA,QAlFN,kBAkFM,kBAAA,QAlFN,iBAkFM,kBAAA,QAlFN,oBAkFM,kBAAA,QAlFN,mBAkFM,kBAAA,QAlFN,iBAkFM,kBAAA,QAlFN,mBAkFM,kBAAA,QAlFN,mBAkFM,kBAAA,QAlFN,iBAkFM,kBAAA,QAlFN,gBAkFM,kBAAA,QAlFN,mBAkFM,kBAAA,QAlFN,mBAkFM,kBAAA,QAlFN,kBAkFM,kBAAA,QAlFN,iBAkFM,kBAAA,QAlFN,iBAkFM,kBAAA,QAlFN,kBAkFM,kBAAA,KAlFN,iBAkFM,kBAAA,QAlFN,sBAkFM,kBAAA,QAlFN,mBAuFI,OAAA,KACA,QAAA,aACA,OAAA,EAAA,IAOJ,YACE,UAAA,MAIF,8CAGM,YAAA,EAKN,iBAGI,iBAAA,QACA,MAAA,KAJJ,uBAOM,iBAAA,QACA,MAAA,KARN,qCAWM,oBAAA,QAXN,8BAcM,iBAAA,eAdN,wDAiBM,aAAA,QAjBN,0DAoBM,MAAA,KApBN,0BAyBI,iBAAA,QAzBJ,oCA2BM,MAAA,QA3BN,wCA8BM,oBAAA,QA9BN,yBAmCI,iBAAA,QACA,aAAA,QACA,MAAA,KGjgBJ,uBAEI,iBAAA,KACA,QAAA,MACA,OAAA,KACA,KAAA,EACA,QAAA,GACA,SAAA,SACA,IAAA,EACA,MAAA,KACA,QAAA,KVqpKJ,wCU/oKA,wCAIM,aAAA,QAJN,gCAAA,8BAAA,iCAAA,mCAAA,iCAcM,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KV0oKN,yBUroKA,yBAGI,aAAA,QAHJ,0BAMI,iBAAA,QVwoKJ,mDU9oKA,mDAWQ,aAAA,QAXR,4CAcQ,MAAA,kBACA,YAAA,EAAA,IAAA,EAAA,kBV4oKR,kDADiD,kDAAjD,gDADsD,gDADtD,mDUxpKA,mDVypKA,qDADoD,qDAIpD,mDADmD,mDUjoK3C,aAAA,KC/DR,kBACE,SAAA,SACA,MAAA,EACA,IAAA,EACA,QAAA,KAJF,wBAOI,SAAA,MAIJ,iBACE,KAAA,EACA,SAAA,SACA,IAAA,EACA,QAAA,KAJF,uBAOI,SAAA,MAIJ,qBACE,OAAA,EACA,SAAA,SACA,MAAA,EACA,QAAA,KAJF,2BAOI,SAAA,MAIJ,oBACE,OAAA,EACA,KAAA,EACA,SAAA,SACA,QAAA,KAJF,0BAOI,SAAA,MAIJ,kBAEI,iBAAA,mBACA,MAAA,KAHJ,gCAMM,iBAAA,kBACA,MAAA,QCjDJ,kBACE,iBAAA,6BAGE,yBACE,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KAIJ,gCACE,iBAAA,oBACA,MAAA,KAZJ,oBACE,iBAAA,+BAGE,2BACE,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KAIJ,kCACE,iBAAA,sBACA,MAAA,KAZJ,kBACE,iBAAA,6BAGE,yBACE,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KAIJ,gCACE,iBAAA,oBACA,MAAA,KAZJ,eACE,iBAAA,8BAGE,sBACE,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KAIJ,6BACE,iBAAA,qBACA,MAAA,KAZJ,kBACE,iBAAA,6BASA,gCACE,iBAAA,oBACA,MAAA,QAZJ,iBACE,iBAAA,6BAGE,wBACE,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KAIJ,+BACE,iBAAA,oBACA,MAAA,KAZJ,gBACE,iBAAA,+BASA,8BACE,iBAAA,sBACA,MAAA,QAZJ,eACE,iBAAA,4BAGE,sBACE,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KAIJ,6BACE,iBAAA,mBACA,MAAA,KAZJ,oBACE,iBAAA,8BAGE,2BACE,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KAIJ,kCACE,iBAAA,qBACA,MAAA,KAZJ,eACE,iBAAA,2BAGE,sBACE,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KAIJ,6BACE,iBAAA,kBACA,MAAA,KAZJ,gBACE,iBAAA,8BAGE,uBACE,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KAIJ,8BACE,iBAAA,qBACA,MAAA,KAZJ,eACE,iBAAA,6BASA,6BACE,iBAAA,oBACA,MAAA,QAZJ,kBACE,iBAAA,8BAGE,yBACE,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KAIJ,gCACE,iBAAA,qBACA,MAAA,KAZJ,iBACE,iBAAA,6BAGE,wBACE,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KAIJ,+BACE,iBAAA,oBACA,MAAA,KAZJ,eACE,iBAAA,6BAGE,sBACE,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KAIJ,6BACE,iBAAA,oBACA,MAAA,KAZJ,iBACE,iBAAA,8BAGE,wBACE,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KAIJ,+BACE,iBAAA,qBACA,MAAA,KAZJ,iBACE,iBAAA,8BAGE,wBACE,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KAIJ,+BACE,iBAAA,qBACA,MAAA,KAZJ,eACE,iBAAA,8BAGE,sBACE,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KAIJ,6BACE,iBAAA,qBACA,MAAA,KAZJ,cACE,iBAAA,6BAGE,qBACE,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KAIJ,4BACE,iBAAA,oBACA,MAAA,KAZJ,iBACE,iBAAA,8BASA,+BACE,iBAAA,qBACA,MAAA,QAZJ,iBACE,iBAAA,6BASA,+BACE,iBAAA,oBACA,MAAA,QAZJ,gBACE,iBAAA,6BAGE,uBACE,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KAIJ,8BACE,iBAAA,oBACA,MAAA,KAZJ,eACE,iBAAA,8BAGE,sBACE,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KAIJ,6BACE,iBAAA,qBACA,MAAA,KAZJ,eACE,iBAAA,8BAGE,sBACE,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KAIJ,6BACE,iBAAA,qBACA,MAAA,KAZJ,gBACE,iBAAA,+BASA,8BACE,iBAAA,sBACA,MAAA,QAZJ,eACE,iBAAA,+BAGE,sBACE,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KAIJ,6BACE,iBAAA,sBACA,MAAA,KAZJ,oBACE,iBAAA,4BAGE,2BACE,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KAIJ,kCACE,iBAAA,mBACA,MAAA,KCdN,cAAA,cAGI,OAAA,YAHJ,cdcI,cAAA,EcLA,aAAA,IACA,WAAA,KAVJ,cAeI,SAAA,OACA,SAAA,SAhBJ,+BAmBM,iBAAA,KACA,OAAA,QACA,QAAA,MACA,UAAA,MACA,WAAA,KACA,UAAA,KACA,QAAA,EACA,QAAA,EACA,SAAA,SACA,MAAA,EACA,WAAA,MACA,IAAA,EAIJ,cACE,UAAA,kBAKJ,aACE,iBAAA,QACA,aAAA,KACA,MAAA,KAHF,mBAAA,oBAAA,mBAQI,iBAAA,QACA,MAAA,QAKJ,SdxCI,cAAA,Ic0CF,iBAAA,QACA,OAAA,IAAA,MAAA,KACA,MAAA,QACA,UAAA,KACA,OAAA,KACA,OAAA,EAAA,EAAA,KAAA,KACA,UAAA,KACA,QAAA,KAAA,IACA,SAAA,SACA,WAAA,OAXF,ab+lLA,cAEA,cADA,cAFA,cADA,cAMA,cADA,yBa5kLI,QAAA,MACA,UAAA,KAvBJ,yBA2BI,OAAA,EAAA,KA3BJ,eA+BI,iBAAA,QACA,aAAA,KACA,MAAA,KAjCJ,gBAAA,eR1CM,WAAA,MAAA,EAAA,IAAA,IAAA,iBQ0CN,gBA2CI,UAAA,KACA,YAAA,IACA,SAAA,SACA,MAAA,MACA,IAAA,KAMJ,QCPE,QAAA,QAAA,ObuBI,UAAA,OarBJ,YAAA,IfxFE,cAAA,OC8qLJ,oBa7kLA,wBAGI,iBAAA,QACA,MAAA,KACA,aAAA,QbglLJ,0BADA,0BaplLA,8BAAA,8BASM,iBAAA,QACA,MAAA,QACA,aAAA,QAXN,sBAeI,iBAAA,QACA,MAAA,KACA,aAAA,QAjBJ,4BAAA,4BAqBM,iBAAA,QACA,MAAA,QACA,aAAA,QErIN,ShBaI,cAAA,OgBPA,WAAA,EAAA,IAAA,IAAA,eAAA,CAAA,EAAA,IAAA,IAAA,gBAKF,iBAAA,KACA,YAAA,IAAA,MAAA,QACA,cAAA,KACA,QAAA,KAdF,WAiBI,MAAA,QACA,gBAAA,UAlBJ,iBAqBM,MAAA,QArBN,sBA0BI,cAAA,EA1BJ,wBA+BI,kBAAA,QA/BJ,yBAmCI,kBAAA,QAnCJ,sBAuCI,kBAAA,QAvCJ,yBA2CI,kBAAA,QAIJ,oBAEI,iBAAA,QClDJ,aAEI,aAAA,KAFJ,cAMI,MAAA,KACA,QAAA,GAPJ,oBAUM,QAAA,GAVN,SAeI,MAAA,KACA,gBAAA,UAMF,eACE,MAAA,KACA,iBAAA,QACA,aAAA,QAGF,uBC/BA,MAAA,QfKE,iBAAA,QeHF,aAAA,QAEA,0BACE,iBAAA,QAGF,mCACE,MAAA,QDgBF,iBACE,MAAA,KACA,iBAAA,QACA,aAAA,QAGF,yBC/BA,MAAA,QfKE,iBAAA,QeHF,aAAA,QAEA,4BACE,iBAAA,QAGF,qCACE,MAAA,QDgBF,eACE,MAAA,KACA,iBAAA,QACA,aAAA,QAGF,uBC/BA,MAAA,QfKE,iBAAA,QeHF,aAAA,QAEA,0BACE,iBAAA,QAGF,mCACE,MAAA,QDgBF,YACE,MAAA,KACA,iBAAA,QACA,aAAA,QAGF,oBC/BA,MAAA,QfKE,iBAAA,QeHF,aAAA,QAEA,uBACE,iBAAA,QAGF,gCACE,MAAA,QDgBF,eACE,MAAA,QACA,iBAAA,QACA,aAAA,QAGF,uBC/BA,MAAA,QfKE,iBAAA,QeHF,aAAA,QAEA,0BACE,iBAAA,QAGF,mCACE,MAAA,QDgBF,cACE,MAAA,KACA,iBAAA,QACA,aAAA,QAGF,sBC/BA,MAAA,QfKE,iBAAA,QeHF,aAAA,QAEA,yBACE,iBAAA,QAGF,kCACE,MAAA,QDgBF,aACE,MAAA,QACA,iBAAA,QACA,aAAA,QAGF,qBC/BA,MAAA,QfKE,iBAAA,QeHF,aAAA,QAEA,wBACE,iBAAA,QAGF,iCACE,MAAA,QDgBF,YACE,MAAA,KACA,iBAAA,QACA,aAAA,QAGF,oBC/BA,MAAA,QfKE,iBAAA,QeHF,aAAA,QAEA,uBACE,iBAAA,QAGF,gCACE,MAAA,QCNJ,wBAEI,MAAA,QAFJ,iDAQM,iBAAA,KACA,cAAA,EACA,WAAA,MAAA,EAAA,IAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,EAAA,QACA,SAAA,eAAA,SAAA,OACA,IAAA,EACA,QAAA,GAbN,4DAmBU,iBAAA,QACA,WAAA,MAAA,EAAA,IAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,EAAA,QApBV,iBlBs8LA,oBACA,oBkBx6LM,OAAA,EA/BN,mBlB48LA,sBACA,sBkBr6LM,WAAA,OlB46LN,uCADA,uCADA,uCkBl9LA,uCAiDM,eAAA,OlB46LN,gDADA,gDADA,gDADA,gDADA,gDkBp6LE,gDAQM,aAAA,OlBy6LR,+CADA,+CADA,+CADA,+CADA,+CkB76LE,+CAYM,cAAA,OAQR,4CACE,iBAAA,kBCm6LF,+BD/5LE,OAAA,QCm6LF,iCDh6LI,WAAA,UAAA,IAAA,OCo6LJ,sED95LQ,UAAA,cCk6LR,qED/5LQ,UAAA,eAOR,oBAEI,QAAA,YACA,MAAA,KAHJ,wBlBm6LA,sBkB55LM,QAAA,OAPN,wBAYI,MAAA,oBACA,OAAA,EAAA,EAAA,EAAA,OAbJ,0ClB66LA,0CkB35LQ,WAAA,KAMR,2BlB05LA,8BACA,8BkBt5LM,aAAA,QALN,uCAUM,MAAA,QACA,iBAAA,QACA,aAAA,QAZN,2BAiBM,oBAAA,QlBy5LN,qBkB16LA,qBAqBM,iBAAA,QArBN,4DAyBQ,iBAAA,QEjJN,qDACE,YAAA,MAEF,qDACE,aAAA,KALJ,kCpBsjMA,mCAEA,mCADA,mCAFA,mCADA,mCAMA,mCADA,8CoBziMI,QAAA,aACA,UAAA,KACA,WAAA,MACA,SAAA,SACA,IAAA,IACA,QAAA,ECzBJ,OACE,MAAA,MpB8HI,UAAA,OoB5HJ,YAAA,IACA,YAAA,EACA,MAAA,KACA,YAAA,EAAA,IAAA,EAAA,KACA,QAAA,GCKA,aDDE,MAAA,KACA,gBAAA,KCIF,2CAAA,2CDCI,QAAA,IAjBN,aAsBI,QAAA,EAUJ,aACE,QAAA,EACA,iBAAA,YACA,OAAA,EAMF,iBACE,eAAA", "sourcesContent": ["/*!\n *   AdminLTE v3.1.0-rc\n *     Only Components\n *   Author: Colorlib\n *   Website: AdminLTE.io <https://adminlte.io>\n *   License: Open source - MIT <https://opensource.org/licenses/MIT>\n */\n\n// Bootstrap\n// ---------------------------------------------------\n@import \"~bootstrap/scss/functions\";\n@import \"../bootstrap-variables\";\n@import \"~bootstrap/scss/mixins\";\n\n// Variables and Mixins\n// ---------------------------------------------------\n@import \"../variables\";\n@import \"../mixins\";\n\n@import \"components\";\n", "//\n// Component: Forms\n//\n\n.form-group {\n  &.has-icon {\n    position: relative;\n\n    .form-control {\n      padding-right: 35px;\n    }\n\n    .form-icon {\n      background-color: transparent;\n      border: 0;\n      cursor: pointer;\n      font-size: 1rem;\n      // margin-top: -3px;\n      padding: $input-btn-padding-y $input-btn-padding-x;\n      position: absolute;\n      right: 3px;\n      top: 0;\n    }\n  }\n}\n\n// Button groups\n.btn-group-vertical {\n  .btn {\n    &.btn-flat:first-of-type,\n    &.btn-flat:last-of-type {\n      @include border-radius(0);\n    }\n  }\n}\n\n// Support icons in form-control\n.form-control-feedback {\n  &.fa,\n  &.fas,\n  &.far,\n  &.fab,\n  &.fal,\n  &.fad,\n  &.svg-inline--fa,\n  &.ion {\n    line-height: $input-height;\n  }\n}\n\n.input-lg + .form-control-feedback,\n.input-group-lg + .form-control-feedback {\n  &.fa,\n  &.fas,\n  &.far,\n  &.fab,\n  &.fal,\n  &.fad,\n  &.svg-inline--fa,\n  &.ion {\n    line-height: $input-height-lg;\n  }\n}\n\n.form-group-lg {\n  .form-control + .form-control-feedback {\n    &.fa,\n    &.fas,\n    &.far,\n    &.fab,\n    &.fal,\n    &.fad,\n    &.svg-inline--fa,\n    &.ion {\n      line-height: $input-height-lg;\n    }\n  }\n}\n\n.input-sm + .form-control-feedback,\n.input-group-sm + .form-control-feedback {\n  &.fa,\n  &.fas,\n  &.far,\n  &.fab,\n  &.fal,\n  &.fad,\n  &.svg-inline--fa,\n  &.ion {\n    line-height: $input-height-sm;\n  }\n}\n\n.form-group-sm {\n  .form-control + .form-control-feedback {\n    &.fa,\n    &.fas,\n    &.far,\n    &.fab,\n    &.fal,\n    &.fad,\n    &.svg-inline--fa,\n    &.ion {\n      line-height: $input-height-sm;\n    }\n  }\n}\n\nlabel:not(.form-check-label):not(.custom-file-label) {\n  font-weight: $font-weight-bold;\n}\n\n.warning-feedback {\n  @include font-size($form-feedback-font-size);\n  color: theme-color(\"warning\");\n  display: none;\n  margin-top: $form-feedback-margin-top;\n  width: 100%;\n}\n\n.warning-tooltip {\n  @include border-radius($form-feedback-tooltip-border-radius);\n  @include font-size($form-feedback-tooltip-font-size);\n  background-color: rgba(theme-color(\"warning\"), $form-feedback-tooltip-opacity);\n  color: color-yiq(theme-color(\"warning\"));\n  display: none;\n  line-height: $form-feedback-tooltip-line-height;\n  margin-top: .1rem;\n  max-width: 100%; // Contain to parent when possible\n  padding: $form-feedback-tooltip-padding-y $form-feedback-tooltip-padding-x;\n  position: absolute;\n  top: 100%;\n  z-index: 5;\n}\n\n.form-control {\n  &.is-warning {\n    border-color: theme-color(\"warning\");\n\n    @if $enable-validation-icons {\n      // padding-right: $input-height-inner;\n      // background-image: none;\n      // background-repeat: no-repeat;\n      // background-position: center right $input-height-inner-quarter;\n      // background-size: $input-height-inner-half $input-height-inner-half;\n    }\n\n    &:focus {\n      border-color: theme-color(\"warning\");\n      box-shadow: 0 0 0 $input-focus-width rgba(theme-color(\"warning\"), .25);\n    }\n\n    ~ .warning-feedback,\n    ~ .warning-tooltip {\n      display: block;\n    }\n  }\n}\n\n// stylelint-disable-next-line selector-no-qualifying-type\ntextarea.form-control {\n  &.is-warning {\n    @if $enable-validation-icons {\n      padding-right: $input-height-inner;\n      background-position: top $input-height-inner-quarter right $input-height-inner-quarter;\n    }\n  }\n}\n\n.custom-select {\n  &.is-warning {\n    border-color: theme-color(\"warning\");\n\n    @if $enable-validation-icons {\n      // padding-right: $custom-select-feedback-icon-padding-right;\n      // background: $custom-select-background, none $custom-select-bg no-repeat $custom-select-feedback-icon-position / $custom-select-feedback-icon-size;\n    }\n\n    &:focus {\n      border-color: theme-color(\"warning\");\n      box-shadow: 0 0 0 $input-focus-width rgba(theme-color(\"warning\"), .25);\n    }\n\n    ~ .warning-feedback,\n    ~ .warning-tooltip {\n      display: block;\n    }\n  }\n}\n\n\n.form-control-file {\n  &.is-warning {\n    ~ .warning-feedback,\n    ~ .warning-tooltip {\n      display: block;\n    }\n  }\n}\n\n.form-check-input {\n  &.is-warning {\n    ~ .form-check-label {\n      color: theme-color(\"warning\");\n    }\n\n    ~ .warning-feedback,\n    ~ .warning-tooltip {\n      display: block;\n    }\n  }\n}\n\n.custom-control-input.is-warning {\n  ~ .custom-control-label {\n    color: theme-color(\"warning\");\n\n    &::before {\n      border-color: theme-color(\"warning\");\n    }\n  }\n\n  ~ .warning-feedback,\n  ~ .warning-tooltip {\n    display: block;\n  }\n\n  &:checked {\n    ~ .custom-control-label::before {\n      @include gradient-bg(lighten(theme-color(\"warning\"), 10%));\n      border-color: lighten(theme-color(\"warning\"), 10%);\n    }\n  }\n\n  &:focus {\n    ~ .custom-control-label::before {\n      box-shadow: 0 0 0 $input-focus-width rgba(theme-color(\"warning\"), .25);\n    }\n\n    &:not(:checked) ~ .custom-control-label::before {\n      border-color: theme-color(\"warning\");\n    }\n  }\n}\n\n// custom file\n.custom-file-input {\n  &.is-warning {\n    ~ .custom-file-label {\n      border-color: theme-color(\"warning\");\n    }\n\n    ~ .warning-feedback,\n    ~ .warning-tooltip {\n      display: block;\n    }\n\n    &:focus {\n      ~ .custom-file-label {\n        border-color: theme-color(\"warning\");\n        box-shadow: 0 0 0 $input-focus-width rgba(theme-color(\"warning\"), .25);\n      }\n    }\n  }\n}\n\n// body.text-sm support\nbody.text-sm {\n  .input-group-text {\n    font-size: $font-size-sm;\n  }\n}\n\n// custom .form-control styles\n.form-control,\n.custom-select {\n  &.form-control-border {\n    border-top: 0;\n    border-left: 0;\n    border-right: 0;\n    border-radius: 0;\n    box-shadow: inherit;\n\n    &.border-width-2 {\n      border-bottom-width: 2px;\n    }\n    &.border-width-3 {\n      border-bottom-width: 3px;\n    }\n  }\n}\n\n// custom switch color variations\n.custom-switch {\n  @each $name, $color in $theme-colors {\n    @include custom-switch-variant($name, $color);\n  }\n\n  @each $name, $color in $colors {\n    @include custom-switch-variant($name, $color);\n  }\n}\n\n// custom range color variations\n.custom-range {\n  @each $name, $color in $theme-colors {\n    @include custom-range-variant($name, $color);\n  }\n\n  @each $name, $color in $colors {\n    @include custom-range-variant($name, $color);\n  }\n}\n\n// custom control input variations\n@each $name, $color in $theme-colors {\n  @include custom-control-input-variant($name, $color);\n}\n\n@each $name, $color in $colors {\n  @include custom-control-input-variant($name, $color);\n}\n\n.custom-control-input-outline {\n  ~ .custom-control-label::before {\n    background-color: transparent !important;\n    box-shadow: none;\n  }\n  &:checked ~ .custom-control-label::before {\n    @include gradient-bg(transparent);\n  }\n}\n\n.dark-mode {\n  .form-control,\n  .custom-select,\n  .custom-file-label,\n  .custom-file-label::after,\n  .custom-control-label::before,\n  .input-group-text {\n    background-color: $dark;\n    color: $white;\n  }\n  .form-control:not(.form-control-navbar):not(.is-invalid):not(:focus) {\n    border-color: $gray-600;\n  }\n  select {\n    background-color: $dark;\n    color: $white;\n    border-color: $gray-600;\n  }\n\n  .input-group-text {\n    border-color: $gray-600;\n  }\n\n  .custom-control-input:disabled ~ .custom-control-label::before,\n  .custom-control-input[disabled] ~ .custom-control-label::before {\n    background-color: lighten($dark, 5%);\n    border-color: $gray-600;\n    color: $white;\n  }\n\n  .custom-range {\n    &::-webkit-slider-runnable-track {\n      background-color: lighten($dark, 7.5%);\n    }\n    &::-moz-range-track {\n      background-color: lighten($dark, 7.5%);\n    }\n    &::-ms-track {\n      background-color: lighten($dark, 7.5%);\n    }\n  }\n\n  .navbar-dark {\n    .btn-navbar,\n    .form-control-navbar {\n      background-color: $dark;\n      border: $input-border-width solid $gray-600;\n    }\n    .btn-navbar {\n      &:hover {\n        background-color: lighten($sidebar-dark-bg, 7.5%);\n      }\n      &:focus {\n        background-color: lighten($sidebar-dark-bg, 10%);\n      }\n    }\n\n    .form-control-navbar + .input-group-prepend,\n    .form-control-navbar + .input-group-append {\n      > .btn-navbar {\n        background-color: lighten($dark, 5%);\n        color: $white;\n        border: $input-border-width solid $gray-600;\n        border-left: none;\n      }\n    }\n  }\n}\n", "// stylelint-disable property-disallowed-list\n// Single side border-radius\n\n// Helper function to replace negative values with 0\n@function valid-radius($radius) {\n  $return: ();\n  @each $value in $radius {\n    @if type-of($value) == number {\n      $return: append($return, max($value, 0));\n    } @else {\n      $return: append($return, $value);\n    }\n  }\n  @return $return;\n}\n\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: valid-radius($radius);\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-right-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-left-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n", "/*!\n *   AdminLTE v3.1.0-rc\n *     Only Components\n *   Author: Colorlib\n *   Website: AdminLTE.io <https://adminlte.io>\n *   License: Open source - MIT <https://opensource.org/licenses/MIT>\n */\n.form-group.has-icon {\n  position: relative;\n}\n\n.form-group.has-icon .form-control {\n  padding-right: 35px;\n}\n\n.form-group.has-icon .form-icon {\n  background-color: transparent;\n  border: 0;\n  cursor: pointer;\n  font-size: 1rem;\n  padding: 0.375rem 0.75rem;\n  position: absolute;\n  right: 3px;\n  top: 0;\n}\n\n.btn-group-vertical .btn.btn-flat:first-of-type, .btn-group-vertical .btn.btn-flat:last-of-type {\n  border-radius: 0;\n}\n\n.form-control-feedback.fa, .form-control-feedback.fas, .form-control-feedback.far, .form-control-feedback.fab, .form-control-feedback.fal, .form-control-feedback.fad, .form-control-feedback.svg-inline--fa, .form-control-feedback.ion {\n  line-height: calc(2.25rem + 2px);\n}\n\n.input-lg + .form-control-feedback.fa, .input-lg + .form-control-feedback.fas, .input-lg + .form-control-feedback.far, .input-lg + .form-control-feedback.fab, .input-lg + .form-control-feedback.fal, .input-lg + .form-control-feedback.fad, .input-lg + .form-control-feedback.svg-inline--fa, .input-lg + .form-control-feedback.ion,\n.input-group-lg + .form-control-feedback.fa,\n.input-group-lg + .form-control-feedback.fas,\n.input-group-lg + .form-control-feedback.far,\n.input-group-lg + .form-control-feedback.fab,\n.input-group-lg + .form-control-feedback.fal,\n.input-group-lg + .form-control-feedback.fad,\n.input-group-lg + .form-control-feedback.svg-inline--fa,\n.input-group-lg + .form-control-feedback.ion {\n  line-height: calc(2.875rem + 2px);\n}\n\n.form-group-lg .form-control + .form-control-feedback.fa, .form-group-lg .form-control + .form-control-feedback.fas, .form-group-lg .form-control + .form-control-feedback.far, .form-group-lg .form-control + .form-control-feedback.fab, .form-group-lg .form-control + .form-control-feedback.fal, .form-group-lg .form-control + .form-control-feedback.fad, .form-group-lg .form-control + .form-control-feedback.svg-inline--fa, .form-group-lg .form-control + .form-control-feedback.ion {\n  line-height: calc(2.875rem + 2px);\n}\n\n.input-sm + .form-control-feedback.fa, .input-sm + .form-control-feedback.fas, .input-sm + .form-control-feedback.far, .input-sm + .form-control-feedback.fab, .input-sm + .form-control-feedback.fal, .input-sm + .form-control-feedback.fad, .input-sm + .form-control-feedback.svg-inline--fa, .input-sm + .form-control-feedback.ion,\n.input-group-sm + .form-control-feedback.fa,\n.input-group-sm + .form-control-feedback.fas,\n.input-group-sm + .form-control-feedback.far,\n.input-group-sm + .form-control-feedback.fab,\n.input-group-sm + .form-control-feedback.fal,\n.input-group-sm + .form-control-feedback.fad,\n.input-group-sm + .form-control-feedback.svg-inline--fa,\n.input-group-sm + .form-control-feedback.ion {\n  line-height: calc(1.8125rem + 2px);\n}\n\n.form-group-sm .form-control + .form-control-feedback.fa, .form-group-sm .form-control + .form-control-feedback.fas, .form-group-sm .form-control + .form-control-feedback.far, .form-group-sm .form-control + .form-control-feedback.fab, .form-group-sm .form-control + .form-control-feedback.fal, .form-group-sm .form-control + .form-control-feedback.fad, .form-group-sm .form-control + .form-control-feedback.svg-inline--fa, .form-group-sm .form-control + .form-control-feedback.ion {\n  line-height: calc(1.8125rem + 2px);\n}\n\nlabel:not(.form-check-label):not(.custom-file-label) {\n  font-weight: 700;\n}\n\n.warning-feedback {\n  font-size: 80%;\n  color: #ffc107;\n  display: none;\n  margin-top: 0.25rem;\n  width: 100%;\n}\n\n.warning-tooltip {\n  border-radius: 0.25rem;\n  font-size: 0.875rem;\n  background-color: rgba(255, 193, 7, 0.9);\n  color: #1f2d3d;\n  display: none;\n  line-height: 1.5;\n  margin-top: .1rem;\n  max-width: 100%;\n  padding: 0.25rem 0.5rem;\n  position: absolute;\n  top: 100%;\n  z-index: 5;\n}\n\n.form-control.is-warning {\n  border-color: #ffc107;\n}\n\n.form-control.is-warning:focus {\n  border-color: #ffc107;\n  box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.25);\n}\n\n.form-control.is-warning ~ .warning-feedback,\n.form-control.is-warning ~ .warning-tooltip {\n  display: block;\n}\n\ntextarea.form-control.is-warning {\n  padding-right: 2.25rem;\n  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);\n}\n\n.custom-select.is-warning {\n  border-color: #ffc107;\n}\n\n.custom-select.is-warning:focus {\n  border-color: #ffc107;\n  box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.25);\n}\n\n.custom-select.is-warning ~ .warning-feedback,\n.custom-select.is-warning ~ .warning-tooltip {\n  display: block;\n}\n\n.form-control-file.is-warning ~ .warning-feedback,\n.form-control-file.is-warning ~ .warning-tooltip {\n  display: block;\n}\n\n.form-check-input.is-warning ~ .form-check-label {\n  color: #ffc107;\n}\n\n.form-check-input.is-warning ~ .warning-feedback,\n.form-check-input.is-warning ~ .warning-tooltip {\n  display: block;\n}\n\n.custom-control-input.is-warning ~ .custom-control-label {\n  color: #ffc107;\n}\n\n.custom-control-input.is-warning ~ .custom-control-label::before {\n  border-color: #ffc107;\n}\n\n.custom-control-input.is-warning ~ .warning-feedback,\n.custom-control-input.is-warning ~ .warning-tooltip {\n  display: block;\n}\n\n.custom-control-input.is-warning:checked ~ .custom-control-label::before {\n  background-color: #ffce3a;\n  border-color: #ffce3a;\n}\n\n.custom-control-input.is-warning:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.25);\n}\n\n.custom-control-input.is-warning:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #ffc107;\n}\n\n.custom-file-input.is-warning ~ .custom-file-label {\n  border-color: #ffc107;\n}\n\n.custom-file-input.is-warning ~ .warning-feedback,\n.custom-file-input.is-warning ~ .warning-tooltip {\n  display: block;\n}\n\n.custom-file-input.is-warning:focus ~ .custom-file-label {\n  border-color: #ffc107;\n  box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.25);\n}\n\nbody.text-sm .input-group-text {\n  font-size: 0.875rem;\n}\n\n.form-control.form-control-border,\n.custom-select.form-control-border {\n  border-top: 0;\n  border-left: 0;\n  border-right: 0;\n  border-radius: 0;\n  box-shadow: inherit;\n}\n\n.form-control.form-control-border.border-width-2,\n.custom-select.form-control-border.border-width-2 {\n  border-bottom-width: 2px;\n}\n\n.form-control.form-control-border.border-width-3,\n.custom-select.form-control-border.border-width-3 {\n  border-bottom-width: 3px;\n}\n\n.custom-switch.custom-switch-off-primary .custom-control-input ~ .custom-control-label::before {\n  background-color: #007bff;\n  border-color: #004a99;\n}\n\n.custom-switch.custom-switch-off-primary .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-switch.custom-switch-off-primary .custom-control-input ~ .custom-control-label::after {\n  background-color: #003e80;\n}\n\n.custom-switch.custom-switch-on-primary .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #007bff;\n  border-color: #004a99;\n}\n\n.custom-switch.custom-switch-on-primary .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-switch.custom-switch-on-primary .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #99caff;\n}\n\n.custom-switch.custom-switch-off-secondary .custom-control-input ~ .custom-control-label::before {\n  background-color: #6c757d;\n  border-color: #3d4246;\n}\n\n.custom-switch.custom-switch-off-secondary .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-switch.custom-switch-off-secondary .custom-control-input ~ .custom-control-label::after {\n  background-color: #313539;\n}\n\n.custom-switch.custom-switch-on-secondary .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #6c757d;\n  border-color: #3d4246;\n}\n\n.custom-switch.custom-switch-on-secondary .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-switch.custom-switch-on-secondary .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #bcc1c6;\n}\n\n.custom-switch.custom-switch-off-success .custom-control-input ~ .custom-control-label::before {\n  background-color: #28a745;\n  border-color: #145523;\n}\n\n.custom-switch.custom-switch-off-success .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-switch.custom-switch-off-success .custom-control-input ~ .custom-control-label::after {\n  background-color: #0f401b;\n}\n\n.custom-switch.custom-switch-on-success .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #28a745;\n  border-color: #145523;\n}\n\n.custom-switch.custom-switch-on-success .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-switch.custom-switch-on-success .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #86e29b;\n}\n\n.custom-switch.custom-switch-off-info .custom-control-input ~ .custom-control-label::before {\n  background-color: #17a2b8;\n  border-color: #0c525d;\n}\n\n.custom-switch.custom-switch-off-info .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-switch.custom-switch-off-info .custom-control-input ~ .custom-control-label::after {\n  background-color: #093e47;\n}\n\n.custom-switch.custom-switch-on-info .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #17a2b8;\n  border-color: #0c525d;\n}\n\n.custom-switch.custom-switch-on-info .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-switch.custom-switch-on-info .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #7adeee;\n}\n\n.custom-switch.custom-switch-off-warning .custom-control-input ~ .custom-control-label::before {\n  background-color: #ffc107;\n  border-color: #a07800;\n}\n\n.custom-switch.custom-switch-off-warning .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-switch.custom-switch-off-warning .custom-control-input ~ .custom-control-label::after {\n  background-color: #876500;\n}\n\n.custom-switch.custom-switch-on-warning .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #ffc107;\n  border-color: #a07800;\n}\n\n.custom-switch.custom-switch-on-warning .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-switch.custom-switch-on-warning .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #ffe7a0;\n}\n\n.custom-switch.custom-switch-off-danger .custom-control-input ~ .custom-control-label::before {\n  background-color: #dc3545;\n  border-color: #921925;\n}\n\n.custom-switch.custom-switch-off-danger .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-switch.custom-switch-off-danger .custom-control-input ~ .custom-control-label::after {\n  background-color: #7c151f;\n}\n\n.custom-switch.custom-switch-on-danger .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #dc3545;\n  border-color: #921925;\n}\n\n.custom-switch.custom-switch-on-danger .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-switch.custom-switch-on-danger .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #f3b7bd;\n}\n\n.custom-switch.custom-switch-off-light .custom-control-input ~ .custom-control-label::before {\n  background-color: #f8f9fa;\n  border-color: #bdc6d0;\n}\n\n.custom-switch.custom-switch-off-light .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(248, 249, 250, 0.25);\n}\n\n.custom-switch.custom-switch-off-light .custom-control-input ~ .custom-control-label::after {\n  background-color: #aeb9c5;\n}\n\n.custom-switch.custom-switch-on-light .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #f8f9fa;\n  border-color: #bdc6d0;\n}\n\n.custom-switch.custom-switch-on-light .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(248, 249, 250, 0.25);\n}\n\n.custom-switch.custom-switch-on-light .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: white;\n}\n\n.custom-switch.custom-switch-off-dark .custom-control-input ~ .custom-control-label::before {\n  background-color: #343a40;\n  border-color: #060708;\n}\n\n.custom-switch.custom-switch-off-dark .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-switch.custom-switch-off-dark .custom-control-input ~ .custom-control-label::after {\n  background-color: black;\n}\n\n.custom-switch.custom-switch-on-dark .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #343a40;\n  border-color: #060708;\n}\n\n.custom-switch.custom-switch-on-dark .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-switch.custom-switch-on-dark .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #7a8793;\n}\n\n.custom-switch.custom-switch-off-lightblue .custom-control-input ~ .custom-control-label::before {\n  background-color: #3c8dbc;\n  border-color: #23536f;\n}\n\n.custom-switch.custom-switch-off-lightblue .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(60, 141, 188, 0.25);\n}\n\n.custom-switch.custom-switch-off-lightblue .custom-control-input ~ .custom-control-label::after {\n  background-color: #1d455b;\n}\n\n.custom-switch.custom-switch-on-lightblue .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #3c8dbc;\n  border-color: #23536f;\n}\n\n.custom-switch.custom-switch-on-lightblue .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(60, 141, 188, 0.25);\n}\n\n.custom-switch.custom-switch-on-lightblue .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #acd0e5;\n}\n\n.custom-switch.custom-switch-off-navy .custom-control-input ~ .custom-control-label::before {\n  background-color: #001f3f;\n  border-color: black;\n}\n\n.custom-switch.custom-switch-off-navy .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 31, 63, 0.25);\n}\n\n.custom-switch.custom-switch-off-navy .custom-control-input ~ .custom-control-label::after {\n  background-color: black;\n}\n\n.custom-switch.custom-switch-on-navy .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #001f3f;\n  border-color: black;\n}\n\n.custom-switch.custom-switch-on-navy .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 31, 63, 0.25);\n}\n\n.custom-switch.custom-switch-on-navy .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #006ad8;\n}\n\n.custom-switch.custom-switch-off-olive .custom-control-input ~ .custom-control-label::before {\n  background-color: #3d9970;\n  border-color: #20503b;\n}\n\n.custom-switch.custom-switch-off-olive .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(61, 153, 112, 0.25);\n}\n\n.custom-switch.custom-switch-off-olive .custom-control-input ~ .custom-control-label::after {\n  background-color: #193e2d;\n}\n\n.custom-switch.custom-switch-on-olive .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #3d9970;\n  border-color: #20503b;\n}\n\n.custom-switch.custom-switch-on-olive .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(61, 153, 112, 0.25);\n}\n\n.custom-switch.custom-switch-on-olive .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #99d6bb;\n}\n\n.custom-switch.custom-switch-off-lime .custom-control-input ~ .custom-control-label::before {\n  background-color: #01ff70;\n  border-color: #009a43;\n}\n\n.custom-switch.custom-switch-off-lime .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(1, 255, 112, 0.25);\n}\n\n.custom-switch.custom-switch-off-lime .custom-control-input ~ .custom-control-label::after {\n  background-color: #008138;\n}\n\n.custom-switch.custom-switch-on-lime .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #01ff70;\n  border-color: #009a43;\n}\n\n.custom-switch.custom-switch-on-lime .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(1, 255, 112, 0.25);\n}\n\n.custom-switch.custom-switch-on-lime .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #9affc6;\n}\n\n.custom-switch.custom-switch-off-fuchsia .custom-control-input ~ .custom-control-label::before {\n  background-color: #f012be;\n  border-color: #930974;\n}\n\n.custom-switch.custom-switch-off-fuchsia .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(240, 18, 190, 0.25);\n}\n\n.custom-switch.custom-switch-off-fuchsia .custom-control-input ~ .custom-control-label::after {\n  background-color: #7b0861;\n}\n\n.custom-switch.custom-switch-on-fuchsia .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #f012be;\n  border-color: #930974;\n}\n\n.custom-switch.custom-switch-on-fuchsia .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(240, 18, 190, 0.25);\n}\n\n.custom-switch.custom-switch-on-fuchsia .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #f9a2e5;\n}\n\n.custom-switch.custom-switch-off-maroon .custom-control-input ~ .custom-control-label::before {\n  background-color: #d81b60;\n  border-color: #7d1038;\n}\n\n.custom-switch.custom-switch-off-maroon .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(216, 27, 96, 0.25);\n}\n\n.custom-switch.custom-switch-off-maroon .custom-control-input ~ .custom-control-label::after {\n  background-color: #670d2e;\n}\n\n.custom-switch.custom-switch-on-maroon .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #d81b60;\n  border-color: #7d1038;\n}\n\n.custom-switch.custom-switch-on-maroon .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(216, 27, 96, 0.25);\n}\n\n.custom-switch.custom-switch-on-maroon .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #f29aba;\n}\n\n.custom-switch.custom-switch-off-blue .custom-control-input ~ .custom-control-label::before {\n  background-color: #007bff;\n  border-color: #004a99;\n}\n\n.custom-switch.custom-switch-off-blue .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-switch.custom-switch-off-blue .custom-control-input ~ .custom-control-label::after {\n  background-color: #003e80;\n}\n\n.custom-switch.custom-switch-on-blue .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #007bff;\n  border-color: #004a99;\n}\n\n.custom-switch.custom-switch-on-blue .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-switch.custom-switch-on-blue .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #99caff;\n}\n\n.custom-switch.custom-switch-off-indigo .custom-control-input ~ .custom-control-label::before {\n  background-color: #6610f2;\n  border-color: #3d0894;\n}\n\n.custom-switch.custom-switch-off-indigo .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(102, 16, 242, 0.25);\n}\n\n.custom-switch.custom-switch-off-indigo .custom-control-input ~ .custom-control-label::after {\n  background-color: #33077c;\n}\n\n.custom-switch.custom-switch-on-indigo .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #6610f2;\n  border-color: #3d0894;\n}\n\n.custom-switch.custom-switch-on-indigo .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(102, 16, 242, 0.25);\n}\n\n.custom-switch.custom-switch-on-indigo .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #c3a1fa;\n}\n\n.custom-switch.custom-switch-off-purple .custom-control-input ~ .custom-control-label::before {\n  background-color: #6f42c1;\n  border-color: #432776;\n}\n\n.custom-switch.custom-switch-off-purple .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(111, 66, 193, 0.25);\n}\n\n.custom-switch.custom-switch-off-purple .custom-control-input ~ .custom-control-label::after {\n  background-color: #382063;\n}\n\n.custom-switch.custom-switch-on-purple .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #6f42c1;\n  border-color: #432776;\n}\n\n.custom-switch.custom-switch-on-purple .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(111, 66, 193, 0.25);\n}\n\n.custom-switch.custom-switch-on-purple .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #c7b5e7;\n}\n\n.custom-switch.custom-switch-off-pink .custom-control-input ~ .custom-control-label::before {\n  background-color: #e83e8c;\n  border-color: #ac145a;\n}\n\n.custom-switch.custom-switch-off-pink .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(232, 62, 140, 0.25);\n}\n\n.custom-switch.custom-switch-off-pink .custom-control-input ~ .custom-control-label::after {\n  background-color: #95124e;\n}\n\n.custom-switch.custom-switch-on-pink .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #e83e8c;\n  border-color: #ac145a;\n}\n\n.custom-switch.custom-switch-on-pink .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(232, 62, 140, 0.25);\n}\n\n.custom-switch.custom-switch-on-pink .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #f8c7dd;\n}\n\n.custom-switch.custom-switch-off-red .custom-control-input ~ .custom-control-label::before {\n  background-color: #dc3545;\n  border-color: #921925;\n}\n\n.custom-switch.custom-switch-off-red .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-switch.custom-switch-off-red .custom-control-input ~ .custom-control-label::after {\n  background-color: #7c151f;\n}\n\n.custom-switch.custom-switch-on-red .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #dc3545;\n  border-color: #921925;\n}\n\n.custom-switch.custom-switch-on-red .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-switch.custom-switch-on-red .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #f3b7bd;\n}\n\n.custom-switch.custom-switch-off-orange .custom-control-input ~ .custom-control-label::before {\n  background-color: #fd7e14;\n  border-color: #aa4e01;\n}\n\n.custom-switch.custom-switch-off-orange .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(253, 126, 20, 0.25);\n}\n\n.custom-switch.custom-switch-off-orange .custom-control-input ~ .custom-control-label::after {\n  background-color: #904201;\n}\n\n.custom-switch.custom-switch-on-orange .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #fd7e14;\n  border-color: #aa4e01;\n}\n\n.custom-switch.custom-switch-on-orange .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(253, 126, 20, 0.25);\n}\n\n.custom-switch.custom-switch-on-orange .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #fed1ac;\n}\n\n.custom-switch.custom-switch-off-yellow .custom-control-input ~ .custom-control-label::before {\n  background-color: #ffc107;\n  border-color: #a07800;\n}\n\n.custom-switch.custom-switch-off-yellow .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-switch.custom-switch-off-yellow .custom-control-input ~ .custom-control-label::after {\n  background-color: #876500;\n}\n\n.custom-switch.custom-switch-on-yellow .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #ffc107;\n  border-color: #a07800;\n}\n\n.custom-switch.custom-switch-on-yellow .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-switch.custom-switch-on-yellow .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #ffe7a0;\n}\n\n.custom-switch.custom-switch-off-green .custom-control-input ~ .custom-control-label::before {\n  background-color: #28a745;\n  border-color: #145523;\n}\n\n.custom-switch.custom-switch-off-green .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-switch.custom-switch-off-green .custom-control-input ~ .custom-control-label::after {\n  background-color: #0f401b;\n}\n\n.custom-switch.custom-switch-on-green .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #28a745;\n  border-color: #145523;\n}\n\n.custom-switch.custom-switch-on-green .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-switch.custom-switch-on-green .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #86e29b;\n}\n\n.custom-switch.custom-switch-off-teal .custom-control-input ~ .custom-control-label::before {\n  background-color: #20c997;\n  border-color: #127155;\n}\n\n.custom-switch.custom-switch-off-teal .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(32, 201, 151, 0.25);\n}\n\n.custom-switch.custom-switch-off-teal .custom-control-input ~ .custom-control-label::after {\n  background-color: #0e5b44;\n}\n\n.custom-switch.custom-switch-on-teal .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #20c997;\n  border-color: #127155;\n}\n\n.custom-switch.custom-switch-on-teal .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(32, 201, 151, 0.25);\n}\n\n.custom-switch.custom-switch-on-teal .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #94eed3;\n}\n\n.custom-switch.custom-switch-off-cyan .custom-control-input ~ .custom-control-label::before {\n  background-color: #17a2b8;\n  border-color: #0c525d;\n}\n\n.custom-switch.custom-switch-off-cyan .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-switch.custom-switch-off-cyan .custom-control-input ~ .custom-control-label::after {\n  background-color: #093e47;\n}\n\n.custom-switch.custom-switch-on-cyan .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #17a2b8;\n  border-color: #0c525d;\n}\n\n.custom-switch.custom-switch-on-cyan .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-switch.custom-switch-on-cyan .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #7adeee;\n}\n\n.custom-switch.custom-switch-off-white .custom-control-input ~ .custom-control-label::before {\n  background-color: #fff;\n  border-color: #cccccc;\n}\n\n.custom-switch.custom-switch-off-white .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 255, 255, 0.25);\n}\n\n.custom-switch.custom-switch-off-white .custom-control-input ~ .custom-control-label::after {\n  background-color: #bfbfbf;\n}\n\n.custom-switch.custom-switch-on-white .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #fff;\n  border-color: #cccccc;\n}\n\n.custom-switch.custom-switch-on-white .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 255, 255, 0.25);\n}\n\n.custom-switch.custom-switch-on-white .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: white;\n}\n\n.custom-switch.custom-switch-off-gray .custom-control-input ~ .custom-control-label::before {\n  background-color: #6c757d;\n  border-color: #3d4246;\n}\n\n.custom-switch.custom-switch-off-gray .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-switch.custom-switch-off-gray .custom-control-input ~ .custom-control-label::after {\n  background-color: #313539;\n}\n\n.custom-switch.custom-switch-on-gray .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #6c757d;\n  border-color: #3d4246;\n}\n\n.custom-switch.custom-switch-on-gray .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-switch.custom-switch-on-gray .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #bcc1c6;\n}\n\n.custom-switch.custom-switch-off-gray-dark .custom-control-input ~ .custom-control-label::before {\n  background-color: #343a40;\n  border-color: #060708;\n}\n\n.custom-switch.custom-switch-off-gray-dark .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-switch.custom-switch-off-gray-dark .custom-control-input ~ .custom-control-label::after {\n  background-color: black;\n}\n\n.custom-switch.custom-switch-on-gray-dark .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #343a40;\n  border-color: #060708;\n}\n\n.custom-switch.custom-switch-on-gray-dark .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-switch.custom-switch-on-gray-dark .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #7a8793;\n}\n\n.custom-range.custom-range-primary:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-primary:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-range.custom-range-primary:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-range.custom-range-primary:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-range.custom-range-primary::-webkit-slider-thumb {\n  background-color: #007bff;\n}\n\n.custom-range.custom-range-primary::-webkit-slider-thumb:active {\n  background-color: #b3d7ff;\n}\n\n.custom-range.custom-range-primary::-moz-range-thumb {\n  background-color: #007bff;\n}\n\n.custom-range.custom-range-primary::-moz-range-thumb:active {\n  background-color: #b3d7ff;\n}\n\n.custom-range.custom-range-primary::-ms-thumb {\n  background-color: #007bff;\n}\n\n.custom-range.custom-range-primary::-ms-thumb:active {\n  background-color: #b3d7ff;\n}\n\n.custom-range.custom-range-secondary:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-secondary:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-range.custom-range-secondary:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-range.custom-range-secondary:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-range.custom-range-secondary::-webkit-slider-thumb {\n  background-color: #6c757d;\n}\n\n.custom-range.custom-range-secondary::-webkit-slider-thumb:active {\n  background-color: #caced1;\n}\n\n.custom-range.custom-range-secondary::-moz-range-thumb {\n  background-color: #6c757d;\n}\n\n.custom-range.custom-range-secondary::-moz-range-thumb:active {\n  background-color: #caced1;\n}\n\n.custom-range.custom-range-secondary::-ms-thumb {\n  background-color: #6c757d;\n}\n\n.custom-range.custom-range-secondary::-ms-thumb:active {\n  background-color: #caced1;\n}\n\n.custom-range.custom-range-success:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-success:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-range.custom-range-success:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-range.custom-range-success:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-range.custom-range-success::-webkit-slider-thumb {\n  background-color: #28a745;\n}\n\n.custom-range.custom-range-success::-webkit-slider-thumb:active {\n  background-color: #9be7ac;\n}\n\n.custom-range.custom-range-success::-moz-range-thumb {\n  background-color: #28a745;\n}\n\n.custom-range.custom-range-success::-moz-range-thumb:active {\n  background-color: #9be7ac;\n}\n\n.custom-range.custom-range-success::-ms-thumb {\n  background-color: #28a745;\n}\n\n.custom-range.custom-range-success::-ms-thumb:active {\n  background-color: #9be7ac;\n}\n\n.custom-range.custom-range-info:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-info:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-range.custom-range-info:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-range.custom-range-info:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-range.custom-range-info::-webkit-slider-thumb {\n  background-color: #17a2b8;\n}\n\n.custom-range.custom-range-info::-webkit-slider-thumb:active {\n  background-color: #90e4f1;\n}\n\n.custom-range.custom-range-info::-moz-range-thumb {\n  background-color: #17a2b8;\n}\n\n.custom-range.custom-range-info::-moz-range-thumb:active {\n  background-color: #90e4f1;\n}\n\n.custom-range.custom-range-info::-ms-thumb {\n  background-color: #17a2b8;\n}\n\n.custom-range.custom-range-info::-ms-thumb:active {\n  background-color: #90e4f1;\n}\n\n.custom-range.custom-range-warning:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-warning:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-range.custom-range-warning:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-range.custom-range-warning:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-range.custom-range-warning::-webkit-slider-thumb {\n  background-color: #ffc107;\n}\n\n.custom-range.custom-range-warning::-webkit-slider-thumb:active {\n  background-color: #ffeeba;\n}\n\n.custom-range.custom-range-warning::-moz-range-thumb {\n  background-color: #ffc107;\n}\n\n.custom-range.custom-range-warning::-moz-range-thumb:active {\n  background-color: #ffeeba;\n}\n\n.custom-range.custom-range-warning::-ms-thumb {\n  background-color: #ffc107;\n}\n\n.custom-range.custom-range-warning::-ms-thumb:active {\n  background-color: #ffeeba;\n}\n\n.custom-range.custom-range-danger:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-danger:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-range.custom-range-danger:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-range.custom-range-danger:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-range.custom-range-danger::-webkit-slider-thumb {\n  background-color: #dc3545;\n}\n\n.custom-range.custom-range-danger::-webkit-slider-thumb:active {\n  background-color: #f6cdd1;\n}\n\n.custom-range.custom-range-danger::-moz-range-thumb {\n  background-color: #dc3545;\n}\n\n.custom-range.custom-range-danger::-moz-range-thumb:active {\n  background-color: #f6cdd1;\n}\n\n.custom-range.custom-range-danger::-ms-thumb {\n  background-color: #dc3545;\n}\n\n.custom-range.custom-range-danger::-ms-thumb:active {\n  background-color: #f6cdd1;\n}\n\n.custom-range.custom-range-light:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-light:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(248, 249, 250, 0.25);\n}\n\n.custom-range.custom-range-light:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(248, 249, 250, 0.25);\n}\n\n.custom-range.custom-range-light:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(248, 249, 250, 0.25);\n}\n\n.custom-range.custom-range-light::-webkit-slider-thumb {\n  background-color: #f8f9fa;\n}\n\n.custom-range.custom-range-light::-webkit-slider-thumb:active {\n  background-color: white;\n}\n\n.custom-range.custom-range-light::-moz-range-thumb {\n  background-color: #f8f9fa;\n}\n\n.custom-range.custom-range-light::-moz-range-thumb:active {\n  background-color: white;\n}\n\n.custom-range.custom-range-light::-ms-thumb {\n  background-color: #f8f9fa;\n}\n\n.custom-range.custom-range-light::-ms-thumb:active {\n  background-color: white;\n}\n\n.custom-range.custom-range-dark:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-dark:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-range.custom-range-dark:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-range.custom-range-dark:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-range.custom-range-dark::-webkit-slider-thumb {\n  background-color: #343a40;\n}\n\n.custom-range.custom-range-dark::-webkit-slider-thumb:active {\n  background-color: #88939e;\n}\n\n.custom-range.custom-range-dark::-moz-range-thumb {\n  background-color: #343a40;\n}\n\n.custom-range.custom-range-dark::-moz-range-thumb:active {\n  background-color: #88939e;\n}\n\n.custom-range.custom-range-dark::-ms-thumb {\n  background-color: #343a40;\n}\n\n.custom-range.custom-range-dark::-ms-thumb:active {\n  background-color: #88939e;\n}\n\n.custom-range.custom-range-lightblue:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-lightblue:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(60, 141, 188, 0.25);\n}\n\n.custom-range.custom-range-lightblue:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(60, 141, 188, 0.25);\n}\n\n.custom-range.custom-range-lightblue:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(60, 141, 188, 0.25);\n}\n\n.custom-range.custom-range-lightblue::-webkit-slider-thumb {\n  background-color: #3c8dbc;\n}\n\n.custom-range.custom-range-lightblue::-webkit-slider-thumb:active {\n  background-color: #c0dbeb;\n}\n\n.custom-range.custom-range-lightblue::-moz-range-thumb {\n  background-color: #3c8dbc;\n}\n\n.custom-range.custom-range-lightblue::-moz-range-thumb:active {\n  background-color: #c0dbeb;\n}\n\n.custom-range.custom-range-lightblue::-ms-thumb {\n  background-color: #3c8dbc;\n}\n\n.custom-range.custom-range-lightblue::-ms-thumb:active {\n  background-color: #c0dbeb;\n}\n\n.custom-range.custom-range-navy:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-navy:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 31, 63, 0.25);\n}\n\n.custom-range.custom-range-navy:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 31, 63, 0.25);\n}\n\n.custom-range.custom-range-navy:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 31, 63, 0.25);\n}\n\n.custom-range.custom-range-navy::-webkit-slider-thumb {\n  background-color: #001f3f;\n}\n\n.custom-range.custom-range-navy::-webkit-slider-thumb:active {\n  background-color: #0077f2;\n}\n\n.custom-range.custom-range-navy::-moz-range-thumb {\n  background-color: #001f3f;\n}\n\n.custom-range.custom-range-navy::-moz-range-thumb:active {\n  background-color: #0077f2;\n}\n\n.custom-range.custom-range-navy::-ms-thumb {\n  background-color: #001f3f;\n}\n\n.custom-range.custom-range-navy::-ms-thumb:active {\n  background-color: #0077f2;\n}\n\n.custom-range.custom-range-olive:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-olive:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(61, 153, 112, 0.25);\n}\n\n.custom-range.custom-range-olive:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(61, 153, 112, 0.25);\n}\n\n.custom-range.custom-range-olive:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(61, 153, 112, 0.25);\n}\n\n.custom-range.custom-range-olive::-webkit-slider-thumb {\n  background-color: #3d9970;\n}\n\n.custom-range.custom-range-olive::-webkit-slider-thumb:active {\n  background-color: #abdec7;\n}\n\n.custom-range.custom-range-olive::-moz-range-thumb {\n  background-color: #3d9970;\n}\n\n.custom-range.custom-range-olive::-moz-range-thumb:active {\n  background-color: #abdec7;\n}\n\n.custom-range.custom-range-olive::-ms-thumb {\n  background-color: #3d9970;\n}\n\n.custom-range.custom-range-olive::-ms-thumb:active {\n  background-color: #abdec7;\n}\n\n.custom-range.custom-range-lime:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-lime:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(1, 255, 112, 0.25);\n}\n\n.custom-range.custom-range-lime:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(1, 255, 112, 0.25);\n}\n\n.custom-range.custom-range-lime:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(1, 255, 112, 0.25);\n}\n\n.custom-range.custom-range-lime::-webkit-slider-thumb {\n  background-color: #01ff70;\n}\n\n.custom-range.custom-range-lime::-webkit-slider-thumb:active {\n  background-color: #b4ffd4;\n}\n\n.custom-range.custom-range-lime::-moz-range-thumb {\n  background-color: #01ff70;\n}\n\n.custom-range.custom-range-lime::-moz-range-thumb:active {\n  background-color: #b4ffd4;\n}\n\n.custom-range.custom-range-lime::-ms-thumb {\n  background-color: #01ff70;\n}\n\n.custom-range.custom-range-lime::-ms-thumb:active {\n  background-color: #b4ffd4;\n}\n\n.custom-range.custom-range-fuchsia:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-fuchsia:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(240, 18, 190, 0.25);\n}\n\n.custom-range.custom-range-fuchsia:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(240, 18, 190, 0.25);\n}\n\n.custom-range.custom-range-fuchsia:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(240, 18, 190, 0.25);\n}\n\n.custom-range.custom-range-fuchsia::-webkit-slider-thumb {\n  background-color: #f012be;\n}\n\n.custom-range.custom-range-fuchsia::-webkit-slider-thumb:active {\n  background-color: #fbbaec;\n}\n\n.custom-range.custom-range-fuchsia::-moz-range-thumb {\n  background-color: #f012be;\n}\n\n.custom-range.custom-range-fuchsia::-moz-range-thumb:active {\n  background-color: #fbbaec;\n}\n\n.custom-range.custom-range-fuchsia::-ms-thumb {\n  background-color: #f012be;\n}\n\n.custom-range.custom-range-fuchsia::-ms-thumb:active {\n  background-color: #fbbaec;\n}\n\n.custom-range.custom-range-maroon:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-maroon:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(216, 27, 96, 0.25);\n}\n\n.custom-range.custom-range-maroon:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(216, 27, 96, 0.25);\n}\n\n.custom-range.custom-range-maroon:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(216, 27, 96, 0.25);\n}\n\n.custom-range.custom-range-maroon::-webkit-slider-thumb {\n  background-color: #d81b60;\n}\n\n.custom-range.custom-range-maroon::-webkit-slider-thumb:active {\n  background-color: #f5b0c9;\n}\n\n.custom-range.custom-range-maroon::-moz-range-thumb {\n  background-color: #d81b60;\n}\n\n.custom-range.custom-range-maroon::-moz-range-thumb:active {\n  background-color: #f5b0c9;\n}\n\n.custom-range.custom-range-maroon::-ms-thumb {\n  background-color: #d81b60;\n}\n\n.custom-range.custom-range-maroon::-ms-thumb:active {\n  background-color: #f5b0c9;\n}\n\n.custom-range.custom-range-blue:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-blue:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-range.custom-range-blue:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-range.custom-range-blue:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-range.custom-range-blue::-webkit-slider-thumb {\n  background-color: #007bff;\n}\n\n.custom-range.custom-range-blue::-webkit-slider-thumb:active {\n  background-color: #b3d7ff;\n}\n\n.custom-range.custom-range-blue::-moz-range-thumb {\n  background-color: #007bff;\n}\n\n.custom-range.custom-range-blue::-moz-range-thumb:active {\n  background-color: #b3d7ff;\n}\n\n.custom-range.custom-range-blue::-ms-thumb {\n  background-color: #007bff;\n}\n\n.custom-range.custom-range-blue::-ms-thumb:active {\n  background-color: #b3d7ff;\n}\n\n.custom-range.custom-range-indigo:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-indigo:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(102, 16, 242, 0.25);\n}\n\n.custom-range.custom-range-indigo:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(102, 16, 242, 0.25);\n}\n\n.custom-range.custom-range-indigo:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(102, 16, 242, 0.25);\n}\n\n.custom-range.custom-range-indigo::-webkit-slider-thumb {\n  background-color: #6610f2;\n}\n\n.custom-range.custom-range-indigo::-webkit-slider-thumb:active {\n  background-color: #d2b9fb;\n}\n\n.custom-range.custom-range-indigo::-moz-range-thumb {\n  background-color: #6610f2;\n}\n\n.custom-range.custom-range-indigo::-moz-range-thumb:active {\n  background-color: #d2b9fb;\n}\n\n.custom-range.custom-range-indigo::-ms-thumb {\n  background-color: #6610f2;\n}\n\n.custom-range.custom-range-indigo::-ms-thumb:active {\n  background-color: #d2b9fb;\n}\n\n.custom-range.custom-range-purple:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-purple:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(111, 66, 193, 0.25);\n}\n\n.custom-range.custom-range-purple:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(111, 66, 193, 0.25);\n}\n\n.custom-range.custom-range-purple:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(111, 66, 193, 0.25);\n}\n\n.custom-range.custom-range-purple::-webkit-slider-thumb {\n  background-color: #6f42c1;\n}\n\n.custom-range.custom-range-purple::-webkit-slider-thumb:active {\n  background-color: #d5c8ed;\n}\n\n.custom-range.custom-range-purple::-moz-range-thumb {\n  background-color: #6f42c1;\n}\n\n.custom-range.custom-range-purple::-moz-range-thumb:active {\n  background-color: #d5c8ed;\n}\n\n.custom-range.custom-range-purple::-ms-thumb {\n  background-color: #6f42c1;\n}\n\n.custom-range.custom-range-purple::-ms-thumb:active {\n  background-color: #d5c8ed;\n}\n\n.custom-range.custom-range-pink:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-pink:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(232, 62, 140, 0.25);\n}\n\n.custom-range.custom-range-pink:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(232, 62, 140, 0.25);\n}\n\n.custom-range.custom-range-pink:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(232, 62, 140, 0.25);\n}\n\n.custom-range.custom-range-pink::-webkit-slider-thumb {\n  background-color: #e83e8c;\n}\n\n.custom-range.custom-range-pink::-webkit-slider-thumb:active {\n  background-color: #fbddeb;\n}\n\n.custom-range.custom-range-pink::-moz-range-thumb {\n  background-color: #e83e8c;\n}\n\n.custom-range.custom-range-pink::-moz-range-thumb:active {\n  background-color: #fbddeb;\n}\n\n.custom-range.custom-range-pink::-ms-thumb {\n  background-color: #e83e8c;\n}\n\n.custom-range.custom-range-pink::-ms-thumb:active {\n  background-color: #fbddeb;\n}\n\n.custom-range.custom-range-red:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-red:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-range.custom-range-red:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-range.custom-range-red:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-range.custom-range-red::-webkit-slider-thumb {\n  background-color: #dc3545;\n}\n\n.custom-range.custom-range-red::-webkit-slider-thumb:active {\n  background-color: #f6cdd1;\n}\n\n.custom-range.custom-range-red::-moz-range-thumb {\n  background-color: #dc3545;\n}\n\n.custom-range.custom-range-red::-moz-range-thumb:active {\n  background-color: #f6cdd1;\n}\n\n.custom-range.custom-range-red::-ms-thumb {\n  background-color: #dc3545;\n}\n\n.custom-range.custom-range-red::-ms-thumb:active {\n  background-color: #f6cdd1;\n}\n\n.custom-range.custom-range-orange:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-orange:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(253, 126, 20, 0.25);\n}\n\n.custom-range.custom-range-orange:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(253, 126, 20, 0.25);\n}\n\n.custom-range.custom-range-orange:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(253, 126, 20, 0.25);\n}\n\n.custom-range.custom-range-orange::-webkit-slider-thumb {\n  background-color: #fd7e14;\n}\n\n.custom-range.custom-range-orange::-webkit-slider-thumb:active {\n  background-color: #ffdfc5;\n}\n\n.custom-range.custom-range-orange::-moz-range-thumb {\n  background-color: #fd7e14;\n}\n\n.custom-range.custom-range-orange::-moz-range-thumb:active {\n  background-color: #ffdfc5;\n}\n\n.custom-range.custom-range-orange::-ms-thumb {\n  background-color: #fd7e14;\n}\n\n.custom-range.custom-range-orange::-ms-thumb:active {\n  background-color: #ffdfc5;\n}\n\n.custom-range.custom-range-yellow:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-yellow:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-range.custom-range-yellow:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-range.custom-range-yellow:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-range.custom-range-yellow::-webkit-slider-thumb {\n  background-color: #ffc107;\n}\n\n.custom-range.custom-range-yellow::-webkit-slider-thumb:active {\n  background-color: #ffeeba;\n}\n\n.custom-range.custom-range-yellow::-moz-range-thumb {\n  background-color: #ffc107;\n}\n\n.custom-range.custom-range-yellow::-moz-range-thumb:active {\n  background-color: #ffeeba;\n}\n\n.custom-range.custom-range-yellow::-ms-thumb {\n  background-color: #ffc107;\n}\n\n.custom-range.custom-range-yellow::-ms-thumb:active {\n  background-color: #ffeeba;\n}\n\n.custom-range.custom-range-green:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-green:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-range.custom-range-green:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-range.custom-range-green:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-range.custom-range-green::-webkit-slider-thumb {\n  background-color: #28a745;\n}\n\n.custom-range.custom-range-green::-webkit-slider-thumb:active {\n  background-color: #9be7ac;\n}\n\n.custom-range.custom-range-green::-moz-range-thumb {\n  background-color: #28a745;\n}\n\n.custom-range.custom-range-green::-moz-range-thumb:active {\n  background-color: #9be7ac;\n}\n\n.custom-range.custom-range-green::-ms-thumb {\n  background-color: #28a745;\n}\n\n.custom-range.custom-range-green::-ms-thumb:active {\n  background-color: #9be7ac;\n}\n\n.custom-range.custom-range-teal:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-teal:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(32, 201, 151, 0.25);\n}\n\n.custom-range.custom-range-teal:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(32, 201, 151, 0.25);\n}\n\n.custom-range.custom-range-teal:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(32, 201, 151, 0.25);\n}\n\n.custom-range.custom-range-teal::-webkit-slider-thumb {\n  background-color: #20c997;\n}\n\n.custom-range.custom-range-teal::-webkit-slider-thumb:active {\n  background-color: #aaf1dc;\n}\n\n.custom-range.custom-range-teal::-moz-range-thumb {\n  background-color: #20c997;\n}\n\n.custom-range.custom-range-teal::-moz-range-thumb:active {\n  background-color: #aaf1dc;\n}\n\n.custom-range.custom-range-teal::-ms-thumb {\n  background-color: #20c997;\n}\n\n.custom-range.custom-range-teal::-ms-thumb:active {\n  background-color: #aaf1dc;\n}\n\n.custom-range.custom-range-cyan:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-cyan:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-range.custom-range-cyan:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-range.custom-range-cyan:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-range.custom-range-cyan::-webkit-slider-thumb {\n  background-color: #17a2b8;\n}\n\n.custom-range.custom-range-cyan::-webkit-slider-thumb:active {\n  background-color: #90e4f1;\n}\n\n.custom-range.custom-range-cyan::-moz-range-thumb {\n  background-color: #17a2b8;\n}\n\n.custom-range.custom-range-cyan::-moz-range-thumb:active {\n  background-color: #90e4f1;\n}\n\n.custom-range.custom-range-cyan::-ms-thumb {\n  background-color: #17a2b8;\n}\n\n.custom-range.custom-range-cyan::-ms-thumb:active {\n  background-color: #90e4f1;\n}\n\n.custom-range.custom-range-white:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-white:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 255, 255, 0.25);\n}\n\n.custom-range.custom-range-white:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 255, 255, 0.25);\n}\n\n.custom-range.custom-range-white:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 255, 255, 0.25);\n}\n\n.custom-range.custom-range-white::-webkit-slider-thumb {\n  background-color: #fff;\n}\n\n.custom-range.custom-range-white::-webkit-slider-thumb:active {\n  background-color: white;\n}\n\n.custom-range.custom-range-white::-moz-range-thumb {\n  background-color: #fff;\n}\n\n.custom-range.custom-range-white::-moz-range-thumb:active {\n  background-color: white;\n}\n\n.custom-range.custom-range-white::-ms-thumb {\n  background-color: #fff;\n}\n\n.custom-range.custom-range-white::-ms-thumb:active {\n  background-color: white;\n}\n\n.custom-range.custom-range-gray:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-gray:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-range.custom-range-gray:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-range.custom-range-gray:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-range.custom-range-gray::-webkit-slider-thumb {\n  background-color: #6c757d;\n}\n\n.custom-range.custom-range-gray::-webkit-slider-thumb:active {\n  background-color: #caced1;\n}\n\n.custom-range.custom-range-gray::-moz-range-thumb {\n  background-color: #6c757d;\n}\n\n.custom-range.custom-range-gray::-moz-range-thumb:active {\n  background-color: #caced1;\n}\n\n.custom-range.custom-range-gray::-ms-thumb {\n  background-color: #6c757d;\n}\n\n.custom-range.custom-range-gray::-ms-thumb:active {\n  background-color: #caced1;\n}\n\n.custom-range.custom-range-gray-dark:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-gray-dark:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-range.custom-range-gray-dark:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-range.custom-range-gray-dark:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-range.custom-range-gray-dark::-webkit-slider-thumb {\n  background-color: #343a40;\n}\n\n.custom-range.custom-range-gray-dark::-webkit-slider-thumb:active {\n  background-color: #88939e;\n}\n\n.custom-range.custom-range-gray-dark::-moz-range-thumb {\n  background-color: #343a40;\n}\n\n.custom-range.custom-range-gray-dark::-moz-range-thumb:active {\n  background-color: #88939e;\n}\n\n.custom-range.custom-range-gray-dark::-ms-thumb {\n  background-color: #343a40;\n}\n\n.custom-range.custom-range-gray-dark::-ms-thumb:active {\n  background-color: #88939e;\n}\n\n.custom-control-input-primary:checked ~ .custom-control-label::before {\n  border-color: #007bff;\n  background-color: #007bff;\n}\n\n.custom-control-input-primary.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23007bff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-primary.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23007bff'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-primary:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.custom-control-input-primary:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #80bdff;\n}\n\n.custom-control-input-primary:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #b3d7ff;\n  border-color: #b3d7ff;\n}\n\n.custom-control-input-secondary:checked ~ .custom-control-label::before {\n  border-color: #6c757d;\n  background-color: #6c757d;\n}\n\n.custom-control-input-secondary.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%236c757d' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-secondary.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%236c757d'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-secondary:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(108, 117, 125, 0.25);\n}\n\n.custom-control-input-secondary:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #afb5ba;\n}\n\n.custom-control-input-secondary:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #caced1;\n  border-color: #caced1;\n}\n\n.custom-control-input-success:checked ~ .custom-control-label::before {\n  border-color: #28a745;\n  background-color: #28a745;\n}\n\n.custom-control-input-success.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%2328a745' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-success.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%2328a745'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-success:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(40, 167, 69, 0.25);\n}\n\n.custom-control-input-success:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #71dd8a;\n}\n\n.custom-control-input-success:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #9be7ac;\n  border-color: #9be7ac;\n}\n\n.custom-control-input-info:checked ~ .custom-control-label::before {\n  border-color: #17a2b8;\n  background-color: #17a2b8;\n}\n\n.custom-control-input-info.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%2317a2b8' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-info.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%2317a2b8'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-info:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(23, 162, 184, 0.25);\n}\n\n.custom-control-input-info:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #63d9ec;\n}\n\n.custom-control-input-info:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #90e4f1;\n  border-color: #90e4f1;\n}\n\n.custom-control-input-warning:checked ~ .custom-control-label::before {\n  border-color: #ffc107;\n  background-color: #ffc107;\n}\n\n.custom-control-input-warning.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23ffc107' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-warning.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23ffc107'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-warning:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(255, 193, 7, 0.25);\n}\n\n.custom-control-input-warning:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #ffe187;\n}\n\n.custom-control-input-warning:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #ffeeba;\n  border-color: #ffeeba;\n}\n\n.custom-control-input-danger:checked ~ .custom-control-label::before {\n  border-color: #dc3545;\n  background-color: #dc3545;\n}\n\n.custom-control-input-danger.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23dc3545' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-danger.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23dc3545'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-danger:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n\n.custom-control-input-danger:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #efa2a9;\n}\n\n.custom-control-input-danger:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #f6cdd1;\n  border-color: #f6cdd1;\n}\n\n.custom-control-input-light:checked ~ .custom-control-label::before {\n  border-color: #f8f9fa;\n  background-color: #f8f9fa;\n}\n\n.custom-control-input-light.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23f8f9fa' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-light.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23f8f9fa'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-light:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(248, 249, 250, 0.25);\n}\n\n.custom-control-input-light:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: white;\n}\n\n.custom-control-input-light:not(:disabled):active ~ .custom-control-label::before {\n  background-color: white;\n  border-color: white;\n}\n\n.custom-control-input-dark:checked ~ .custom-control-label::before {\n  border-color: #343a40;\n  background-color: #343a40;\n}\n\n.custom-control-input-dark.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23343a40' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-dark.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23343a40'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-dark:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(52, 58, 64, 0.25);\n}\n\n.custom-control-input-dark:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #6d7a86;\n}\n\n.custom-control-input-dark:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #88939e;\n  border-color: #88939e;\n}\n\n.custom-control-input-lightblue:checked ~ .custom-control-label::before {\n  border-color: #3c8dbc;\n  background-color: #3c8dbc;\n}\n\n.custom-control-input-lightblue.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%233c8dbc' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-lightblue.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%233c8dbc'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-lightblue:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(60, 141, 188, 0.25);\n}\n\n.custom-control-input-lightblue:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #99c5de;\n}\n\n.custom-control-input-lightblue:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #c0dbeb;\n  border-color: #c0dbeb;\n}\n\n.custom-control-input-navy:checked ~ .custom-control-label::before {\n  border-color: #001f3f;\n  background-color: #001f3f;\n}\n\n.custom-control-input-navy.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23001f3f' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-navy.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23001f3f'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-navy:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(0, 31, 63, 0.25);\n}\n\n.custom-control-input-navy:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #005ebf;\n}\n\n.custom-control-input-navy:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #0077f2;\n  border-color: #0077f2;\n}\n\n.custom-control-input-olive:checked ~ .custom-control-label::before {\n  border-color: #3d9970;\n  background-color: #3d9970;\n}\n\n.custom-control-input-olive.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%233d9970' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-olive.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%233d9970'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-olive:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(61, 153, 112, 0.25);\n}\n\n.custom-control-input-olive:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #87cfaf;\n}\n\n.custom-control-input-olive:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #abdec7;\n  border-color: #abdec7;\n}\n\n.custom-control-input-lime:checked ~ .custom-control-label::before {\n  border-color: #01ff70;\n  background-color: #01ff70;\n}\n\n.custom-control-input-lime.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%2301ff70' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-lime.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%2301ff70'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-lime:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(1, 255, 112, 0.25);\n}\n\n.custom-control-input-lime:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #81ffb8;\n}\n\n.custom-control-input-lime:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #b4ffd4;\n  border-color: #b4ffd4;\n}\n\n.custom-control-input-fuchsia:checked ~ .custom-control-label::before {\n  border-color: #f012be;\n  background-color: #f012be;\n}\n\n.custom-control-input-fuchsia.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23f012be' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-fuchsia.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23f012be'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-fuchsia:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(240, 18, 190, 0.25);\n}\n\n.custom-control-input-fuchsia:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #f88adf;\n}\n\n.custom-control-input-fuchsia:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #fbbaec;\n  border-color: #fbbaec;\n}\n\n.custom-control-input-maroon:checked ~ .custom-control-label::before {\n  border-color: #d81b60;\n  background-color: #d81b60;\n}\n\n.custom-control-input-maroon.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23d81b60' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-maroon.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23d81b60'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-maroon:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(216, 27, 96, 0.25);\n}\n\n.custom-control-input-maroon:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #f083ab;\n}\n\n.custom-control-input-maroon:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #f5b0c9;\n  border-color: #f5b0c9;\n}\n\n.custom-control-input-blue:checked ~ .custom-control-label::before {\n  border-color: #007bff;\n  background-color: #007bff;\n}\n\n.custom-control-input-blue.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23007bff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-blue.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23007bff'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-blue:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.custom-control-input-blue:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #80bdff;\n}\n\n.custom-control-input-blue:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #b3d7ff;\n  border-color: #b3d7ff;\n}\n\n.custom-control-input-indigo:checked ~ .custom-control-label::before {\n  border-color: #6610f2;\n  background-color: #6610f2;\n}\n\n.custom-control-input-indigo.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%236610f2' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-indigo.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%236610f2'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-indigo:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(102, 16, 242, 0.25);\n}\n\n.custom-control-input-indigo:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #b389f9;\n}\n\n.custom-control-input-indigo:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #d2b9fb;\n  border-color: #d2b9fb;\n}\n\n.custom-control-input-purple:checked ~ .custom-control-label::before {\n  border-color: #6f42c1;\n  background-color: #6f42c1;\n}\n\n.custom-control-input-purple.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%236f42c1' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-purple.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%236f42c1'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-purple:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(111, 66, 193, 0.25);\n}\n\n.custom-control-input-purple:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #b8a2e0;\n}\n\n.custom-control-input-purple:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #d5c8ed;\n  border-color: #d5c8ed;\n}\n\n.custom-control-input-pink:checked ~ .custom-control-label::before {\n  border-color: #e83e8c;\n  background-color: #e83e8c;\n}\n\n.custom-control-input-pink.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23e83e8c' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-pink.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23e83e8c'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-pink:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(232, 62, 140, 0.25);\n}\n\n.custom-control-input-pink:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #f6b0d0;\n}\n\n.custom-control-input-pink:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #fbddeb;\n  border-color: #fbddeb;\n}\n\n.custom-control-input-red:checked ~ .custom-control-label::before {\n  border-color: #dc3545;\n  background-color: #dc3545;\n}\n\n.custom-control-input-red.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23dc3545' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-red.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23dc3545'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-red:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n\n.custom-control-input-red:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #efa2a9;\n}\n\n.custom-control-input-red:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #f6cdd1;\n  border-color: #f6cdd1;\n}\n\n.custom-control-input-orange:checked ~ .custom-control-label::before {\n  border-color: #fd7e14;\n  background-color: #fd7e14;\n}\n\n.custom-control-input-orange.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23fd7e14' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-orange.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23fd7e14'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-orange:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(253, 126, 20, 0.25);\n}\n\n.custom-control-input-orange:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #fec392;\n}\n\n.custom-control-input-orange:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #ffdfc5;\n  border-color: #ffdfc5;\n}\n\n.custom-control-input-yellow:checked ~ .custom-control-label::before {\n  border-color: #ffc107;\n  background-color: #ffc107;\n}\n\n.custom-control-input-yellow.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23ffc107' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-yellow.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23ffc107'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-yellow:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(255, 193, 7, 0.25);\n}\n\n.custom-control-input-yellow:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #ffe187;\n}\n\n.custom-control-input-yellow:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #ffeeba;\n  border-color: #ffeeba;\n}\n\n.custom-control-input-green:checked ~ .custom-control-label::before {\n  border-color: #28a745;\n  background-color: #28a745;\n}\n\n.custom-control-input-green.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%2328a745' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-green.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%2328a745'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-green:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(40, 167, 69, 0.25);\n}\n\n.custom-control-input-green:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #71dd8a;\n}\n\n.custom-control-input-green:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #9be7ac;\n  border-color: #9be7ac;\n}\n\n.custom-control-input-teal:checked ~ .custom-control-label::before {\n  border-color: #20c997;\n  background-color: #20c997;\n}\n\n.custom-control-input-teal.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%2320c997' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-teal.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%2320c997'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-teal:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(32, 201, 151, 0.25);\n}\n\n.custom-control-input-teal:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #7eeaca;\n}\n\n.custom-control-input-teal:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #aaf1dc;\n  border-color: #aaf1dc;\n}\n\n.custom-control-input-cyan:checked ~ .custom-control-label::before {\n  border-color: #17a2b8;\n  background-color: #17a2b8;\n}\n\n.custom-control-input-cyan.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%2317a2b8' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-cyan.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%2317a2b8'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-cyan:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(23, 162, 184, 0.25);\n}\n\n.custom-control-input-cyan:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #63d9ec;\n}\n\n.custom-control-input-cyan:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #90e4f1;\n  border-color: #90e4f1;\n}\n\n.custom-control-input-white:checked ~ .custom-control-label::before {\n  border-color: #fff;\n  background-color: #fff;\n}\n\n.custom-control-input-white.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-white.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23fff'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-white:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(255, 255, 255, 0.25);\n}\n\n.custom-control-input-white:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: white;\n}\n\n.custom-control-input-white:not(:disabled):active ~ .custom-control-label::before {\n  background-color: white;\n  border-color: white;\n}\n\n.custom-control-input-gray:checked ~ .custom-control-label::before {\n  border-color: #6c757d;\n  background-color: #6c757d;\n}\n\n.custom-control-input-gray.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%236c757d' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-gray.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%236c757d'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-gray:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(108, 117, 125, 0.25);\n}\n\n.custom-control-input-gray:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #afb5ba;\n}\n\n.custom-control-input-gray:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #caced1;\n  border-color: #caced1;\n}\n\n.custom-control-input-gray-dark:checked ~ .custom-control-label::before {\n  border-color: #343a40;\n  background-color: #343a40;\n}\n\n.custom-control-input-gray-dark.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23343a40' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-gray-dark.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23343a40'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-gray-dark:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(52, 58, 64, 0.25);\n}\n\n.custom-control-input-gray-dark:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #6d7a86;\n}\n\n.custom-control-input-gray-dark:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #88939e;\n  border-color: #88939e;\n}\n\n.custom-control-input-outline ~ .custom-control-label::before {\n  background-color: transparent !important;\n  box-shadow: none;\n}\n\n.custom-control-input-outline:checked ~ .custom-control-label::before {\n  background-color: transparent;\n}\n\n.dark-mode .form-control,\n.dark-mode .custom-select,\n.dark-mode .custom-file-label,\n.dark-mode .custom-file-label::after,\n.dark-mode .custom-control-label::before,\n.dark-mode .input-group-text {\n  background-color: #343a40;\n  color: #fff;\n}\n\n.dark-mode .form-control:not(.form-control-navbar):not(.is-invalid):not(:focus) {\n  border-color: #6c757d;\n}\n\n.dark-mode select {\n  background-color: #343a40;\n  color: #fff;\n  border-color: #6c757d;\n}\n\n.dark-mode .input-group-text {\n  border-color: #6c757d;\n}\n\n.dark-mode .custom-control-input:disabled ~ .custom-control-label::before,\n.dark-mode .custom-control-input[disabled] ~ .custom-control-label::before {\n  background-color: #3f474e;\n  border-color: #6c757d;\n  color: #fff;\n}\n\n.dark-mode .custom-range::-webkit-slider-runnable-track {\n  background-color: #454d55;\n}\n\n.dark-mode .custom-range::-moz-range-track {\n  background-color: #454d55;\n}\n\n.dark-mode .custom-range::-ms-track {\n  background-color: #454d55;\n}\n\n.dark-mode .navbar-dark .btn-navbar,\n.dark-mode .navbar-dark .form-control-navbar {\n  background-color: #343a40;\n  border: 1px solid #6c757d;\n}\n\n.dark-mode .navbar-dark .btn-navbar:hover {\n  background-color: #454d55;\n}\n\n.dark-mode .navbar-dark .btn-navbar:focus {\n  background-color: #4b545c;\n}\n\n.dark-mode .navbar-dark .form-control-navbar + .input-group-prepend > .btn-navbar,\n.dark-mode .navbar-dark .form-control-navbar + .input-group-append > .btn-navbar {\n  background-color: #3f474e;\n  color: #fff;\n  border: 1px solid #6c757d;\n  border-left: none;\n}\n\n.progress {\n  box-shadow: none;\n  border-radius: 1px;\n}\n\n.progress.vertical {\n  display: inline-block;\n  height: 200px;\n  margin-right: 10px;\n  position: relative;\n  width: 30px;\n}\n\n.progress.vertical > .progress-bar {\n  bottom: 0;\n  position: absolute;\n  width: 100%;\n}\n\n.progress.vertical.sm, .progress.vertical.progress-sm {\n  width: 20px;\n}\n\n.progress.vertical.xs, .progress.vertical.progress-xs {\n  width: 10px;\n}\n\n.progress.vertical.xxs, .progress.vertical.progress-xxs {\n  width: 3px;\n}\n\n.progress-group {\n  margin-bottom: 0.5rem;\n}\n\n.progress-sm {\n  height: 10px;\n}\n\n.progress-xs {\n  height: 7px;\n}\n\n.progress-xxs {\n  height: 3px;\n}\n\n.table tr > td .progress {\n  margin: 0;\n}\n\n.dark-mode .progress {\n  background: #454d55;\n}\n\n.card-primary:not(.card-outline) > .card-header {\n  background-color: #007bff;\n}\n\n.card-primary:not(.card-outline) > .card-header,\n.card-primary:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-primary:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-primary.card-outline {\n  border-top: 3px solid #007bff;\n}\n\n.card-primary.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-primary.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #007bff;\n}\n\n.bg-primary .btn-tool,\n.bg-gradient-primary .btn-tool,\n.card-primary:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-primary .btn-tool:hover,\n.bg-gradient-primary .btn-tool:hover,\n.card-primary:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-primary .bootstrap-datetimepicker-widget .table td,\n.card.bg-primary .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-primary .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-primary .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-primary .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-primary .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-primary .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #0067d6;\n  color: #fff;\n}\n\n.card.bg-primary .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-primary .bootstrap-datetimepicker-widget table td.active,\n.card.bg-primary .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #3395ff;\n  color: #fff;\n}\n\n.card-secondary:not(.card-outline) > .card-header {\n  background-color: #6c757d;\n}\n\n.card-secondary:not(.card-outline) > .card-header,\n.card-secondary:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-secondary:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-secondary.card-outline {\n  border-top: 3px solid #6c757d;\n}\n\n.card-secondary.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-secondary.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #6c757d;\n}\n\n.bg-secondary .btn-tool,\n.bg-gradient-secondary .btn-tool,\n.card-secondary:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-secondary .btn-tool:hover,\n.bg-gradient-secondary .btn-tool:hover,\n.card-secondary:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-secondary .bootstrap-datetimepicker-widget .table td,\n.card.bg-secondary .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-secondary .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-secondary .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-secondary .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-secondary .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-secondary .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #596167;\n  color: #fff;\n}\n\n.card.bg-secondary .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-secondary .bootstrap-datetimepicker-widget table td.active,\n.card.bg-secondary .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #868e96;\n  color: #fff;\n}\n\n.card-success:not(.card-outline) > .card-header {\n  background-color: #28a745;\n}\n\n.card-success:not(.card-outline) > .card-header,\n.card-success:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-success:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-success.card-outline {\n  border-top: 3px solid #28a745;\n}\n\n.card-success.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-success.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #28a745;\n}\n\n.bg-success .btn-tool,\n.bg-gradient-success .btn-tool,\n.card-success:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-success .btn-tool:hover,\n.bg-gradient-success .btn-tool:hover,\n.card-success:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-success .bootstrap-datetimepicker-widget .table td,\n.card.bg-success .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-success .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-success .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-success .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-success .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-success .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #208637;\n  color: #fff;\n}\n\n.card.bg-success .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-success .bootstrap-datetimepicker-widget table td.active,\n.card.bg-success .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #34ce57;\n  color: #fff;\n}\n\n.card-info:not(.card-outline) > .card-header {\n  background-color: #17a2b8;\n}\n\n.card-info:not(.card-outline) > .card-header,\n.card-info:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-info:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-info.card-outline {\n  border-top: 3px solid #17a2b8;\n}\n\n.card-info.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-info.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #17a2b8;\n}\n\n.bg-info .btn-tool,\n.bg-gradient-info .btn-tool,\n.card-info:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-info .btn-tool:hover,\n.bg-gradient-info .btn-tool:hover,\n.card-info:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-info .bootstrap-datetimepicker-widget .table td,\n.card.bg-info .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-info .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-info .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-info .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-info .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-info .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #128294;\n  color: #fff;\n}\n\n.card.bg-info .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-info .bootstrap-datetimepicker-widget table td.active,\n.card.bg-info .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #1fc8e3;\n  color: #fff;\n}\n\n.card-warning:not(.card-outline) > .card-header {\n  background-color: #ffc107;\n}\n\n.card-warning:not(.card-outline) > .card-header,\n.card-warning:not(.card-outline) > .card-header a {\n  color: #1f2d3d;\n}\n\n.card-warning:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-warning.card-outline {\n  border-top: 3px solid #ffc107;\n}\n\n.card-warning.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-warning.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #ffc107;\n}\n\n.bg-warning .btn-tool,\n.bg-gradient-warning .btn-tool,\n.card-warning:not(.card-outline) .btn-tool {\n  color: rgba(31, 45, 61, 0.8);\n}\n\n.bg-warning .btn-tool:hover,\n.bg-gradient-warning .btn-tool:hover,\n.card-warning:not(.card-outline) .btn-tool:hover {\n  color: #1f2d3d;\n}\n\n.card.bg-warning .bootstrap-datetimepicker-widget .table td,\n.card.bg-warning .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-warning .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-warning .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-warning .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-warning .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-warning .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #dda600;\n  color: #1f2d3d;\n}\n\n.card.bg-warning .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #1f2d3d;\n}\n\n.card.bg-warning .bootstrap-datetimepicker-widget table td.active,\n.card.bg-warning .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #ffce3a;\n  color: #1f2d3d;\n}\n\n.card-danger:not(.card-outline) > .card-header {\n  background-color: #dc3545;\n}\n\n.card-danger:not(.card-outline) > .card-header,\n.card-danger:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-danger:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-danger.card-outline {\n  border-top: 3px solid #dc3545;\n}\n\n.card-danger.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-danger.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #dc3545;\n}\n\n.bg-danger .btn-tool,\n.bg-gradient-danger .btn-tool,\n.card-danger:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-danger .btn-tool:hover,\n.bg-gradient-danger .btn-tool:hover,\n.card-danger:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-danger .bootstrap-datetimepicker-widget .table td,\n.card.bg-danger .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-danger .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-danger .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-danger .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-danger .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-danger .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #c62232;\n  color: #fff;\n}\n\n.card.bg-danger .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-danger .bootstrap-datetimepicker-widget table td.active,\n.card.bg-danger .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #e4606d;\n  color: #fff;\n}\n\n.card-light:not(.card-outline) > .card-header {\n  background-color: #f8f9fa;\n}\n\n.card-light:not(.card-outline) > .card-header,\n.card-light:not(.card-outline) > .card-header a {\n  color: #1f2d3d;\n}\n\n.card-light:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-light.card-outline {\n  border-top: 3px solid #f8f9fa;\n}\n\n.card-light.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-light.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #f8f9fa;\n}\n\n.bg-light .btn-tool,\n.bg-gradient-light .btn-tool,\n.card-light:not(.card-outline) .btn-tool {\n  color: rgba(31, 45, 61, 0.8);\n}\n\n.bg-light .btn-tool:hover,\n.bg-gradient-light .btn-tool:hover,\n.card-light:not(.card-outline) .btn-tool:hover {\n  color: #1f2d3d;\n}\n\n.card.bg-light .bootstrap-datetimepicker-widget .table td,\n.card.bg-light .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-light .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-light .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-light .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-light .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-light .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #e0e5e9;\n  color: #1f2d3d;\n}\n\n.card.bg-light .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #1f2d3d;\n}\n\n.card.bg-light .bootstrap-datetimepicker-widget table td.active,\n.card.bg-light .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: white;\n  color: #1f2d3d;\n}\n\n.card-dark:not(.card-outline) > .card-header {\n  background-color: #343a40;\n}\n\n.card-dark:not(.card-outline) > .card-header,\n.card-dark:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-dark:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-dark.card-outline {\n  border-top: 3px solid #343a40;\n}\n\n.card-dark.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-dark.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #343a40;\n}\n\n.bg-dark .btn-tool,\n.bg-gradient-dark .btn-tool,\n.card-dark:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-dark .btn-tool:hover,\n.bg-gradient-dark .btn-tool:hover,\n.card-dark:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-dark .bootstrap-datetimepicker-widget .table td,\n.card.bg-dark .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-dark .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-dark .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-dark .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-dark .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-dark .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #222629;\n  color: #fff;\n}\n\n.card.bg-dark .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-dark .bootstrap-datetimepicker-widget table td.active,\n.card.bg-dark .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #4b545c;\n  color: #fff;\n}\n\n.card-lightblue:not(.card-outline) > .card-header {\n  background-color: #3c8dbc;\n}\n\n.card-lightblue:not(.card-outline) > .card-header,\n.card-lightblue:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-lightblue:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-lightblue.card-outline {\n  border-top: 3px solid #3c8dbc;\n}\n\n.card-lightblue.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-lightblue.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #3c8dbc;\n}\n\n.bg-lightblue .btn-tool,\n.bg-gradient-lightblue .btn-tool,\n.card-lightblue:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-lightblue .btn-tool:hover,\n.bg-gradient-lightblue .btn-tool:hover,\n.card-lightblue:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-lightblue .bootstrap-datetimepicker-widget .table td,\n.card.bg-lightblue .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-lightblue .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-lightblue .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-lightblue .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-lightblue .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-lightblue .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #32769d;\n  color: #fff;\n}\n\n.card.bg-lightblue .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-lightblue .bootstrap-datetimepicker-widget table td.active,\n.card.bg-lightblue .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #5fa4cc;\n  color: #fff;\n}\n\n.card-navy:not(.card-outline) > .card-header {\n  background-color: #001f3f;\n}\n\n.card-navy:not(.card-outline) > .card-header,\n.card-navy:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-navy:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-navy.card-outline {\n  border-top: 3px solid #001f3f;\n}\n\n.card-navy.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-navy.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #001f3f;\n}\n\n.bg-navy .btn-tool,\n.bg-gradient-navy .btn-tool,\n.card-navy:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-navy .btn-tool:hover,\n.bg-gradient-navy .btn-tool:hover,\n.card-navy:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-navy .bootstrap-datetimepicker-widget .table td,\n.card.bg-navy .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-navy .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-navy .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-navy .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-navy .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-navy .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #000b16;\n  color: #fff;\n}\n\n.card.bg-navy .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-navy .bootstrap-datetimepicker-widget table td.active,\n.card.bg-navy .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #003872;\n  color: #fff;\n}\n\n.card-olive:not(.card-outline) > .card-header {\n  background-color: #3d9970;\n}\n\n.card-olive:not(.card-outline) > .card-header,\n.card-olive:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-olive:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-olive.card-outline {\n  border-top: 3px solid #3d9970;\n}\n\n.card-olive.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-olive.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #3d9970;\n}\n\n.bg-olive .btn-tool,\n.bg-gradient-olive .btn-tool,\n.card-olive:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-olive .btn-tool:hover,\n.bg-gradient-olive .btn-tool:hover,\n.card-olive:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-olive .bootstrap-datetimepicker-widget .table td,\n.card.bg-olive .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-olive .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-olive .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-olive .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-olive .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-olive .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #317c5b;\n  color: #fff;\n}\n\n.card.bg-olive .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-olive .bootstrap-datetimepicker-widget table td.active,\n.card.bg-olive .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #50b98a;\n  color: #fff;\n}\n\n.card-lime:not(.card-outline) > .card-header {\n  background-color: #01ff70;\n}\n\n.card-lime:not(.card-outline) > .card-header,\n.card-lime:not(.card-outline) > .card-header a {\n  color: #1f2d3d;\n}\n\n.card-lime:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-lime.card-outline {\n  border-top: 3px solid #01ff70;\n}\n\n.card-lime.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-lime.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #01ff70;\n}\n\n.bg-lime .btn-tool,\n.bg-gradient-lime .btn-tool,\n.card-lime:not(.card-outline) .btn-tool {\n  color: rgba(31, 45, 61, 0.8);\n}\n\n.bg-lime .btn-tool:hover,\n.bg-gradient-lime .btn-tool:hover,\n.card-lime:not(.card-outline) .btn-tool:hover {\n  color: #1f2d3d;\n}\n\n.card.bg-lime .bootstrap-datetimepicker-widget .table td,\n.card.bg-lime .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-lime .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-lime .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-lime .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-lime .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-lime .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #00d75e;\n  color: #1f2d3d;\n}\n\n.card.bg-lime .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #1f2d3d;\n}\n\n.card.bg-lime .bootstrap-datetimepicker-widget table td.active,\n.card.bg-lime .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #34ff8d;\n  color: #1f2d3d;\n}\n\n.card-fuchsia:not(.card-outline) > .card-header {\n  background-color: #f012be;\n}\n\n.card-fuchsia:not(.card-outline) > .card-header,\n.card-fuchsia:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-fuchsia:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-fuchsia.card-outline {\n  border-top: 3px solid #f012be;\n}\n\n.card-fuchsia.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-fuchsia.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #f012be;\n}\n\n.bg-fuchsia .btn-tool,\n.bg-gradient-fuchsia .btn-tool,\n.card-fuchsia:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-fuchsia .btn-tool:hover,\n.bg-gradient-fuchsia .btn-tool:hover,\n.card-fuchsia:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-fuchsia .bootstrap-datetimepicker-widget .table td,\n.card.bg-fuchsia .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #cc0da1;\n  color: #fff;\n}\n\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table td.active,\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #f342cb;\n  color: #fff;\n}\n\n.card-maroon:not(.card-outline) > .card-header {\n  background-color: #d81b60;\n}\n\n.card-maroon:not(.card-outline) > .card-header,\n.card-maroon:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-maroon:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-maroon.card-outline {\n  border-top: 3px solid #d81b60;\n}\n\n.card-maroon.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-maroon.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #d81b60;\n}\n\n.bg-maroon .btn-tool,\n.bg-gradient-maroon .btn-tool,\n.card-maroon:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-maroon .btn-tool:hover,\n.bg-gradient-maroon .btn-tool:hover,\n.card-maroon:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-maroon .bootstrap-datetimepicker-widget .table td,\n.card.bg-maroon .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-maroon .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-maroon .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-maroon .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-maroon .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-maroon .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #b41650;\n  color: #fff;\n}\n\n.card.bg-maroon .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-maroon .bootstrap-datetimepicker-widget table td.active,\n.card.bg-maroon .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #e73f7c;\n  color: #fff;\n}\n\n.card-blue:not(.card-outline) > .card-header {\n  background-color: #007bff;\n}\n\n.card-blue:not(.card-outline) > .card-header,\n.card-blue:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-blue:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-blue.card-outline {\n  border-top: 3px solid #007bff;\n}\n\n.card-blue.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-blue.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #007bff;\n}\n\n.bg-blue .btn-tool,\n.bg-gradient-blue .btn-tool,\n.card-blue:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-blue .btn-tool:hover,\n.bg-gradient-blue .btn-tool:hover,\n.card-blue:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-blue .bootstrap-datetimepicker-widget .table td,\n.card.bg-blue .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-blue .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-blue .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-blue .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-blue .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-blue .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #0067d6;\n  color: #fff;\n}\n\n.card.bg-blue .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-blue .bootstrap-datetimepicker-widget table td.active,\n.card.bg-blue .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #3395ff;\n  color: #fff;\n}\n\n.card-indigo:not(.card-outline) > .card-header {\n  background-color: #6610f2;\n}\n\n.card-indigo:not(.card-outline) > .card-header,\n.card-indigo:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-indigo:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-indigo.card-outline {\n  border-top: 3px solid #6610f2;\n}\n\n.card-indigo.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-indigo.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #6610f2;\n}\n\n.bg-indigo .btn-tool,\n.bg-gradient-indigo .btn-tool,\n.card-indigo:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-indigo .btn-tool:hover,\n.bg-gradient-indigo .btn-tool:hover,\n.card-indigo:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-indigo .bootstrap-datetimepicker-widget .table td,\n.card.bg-indigo .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-indigo .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-indigo .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-indigo .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-indigo .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-indigo .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #550bce;\n  color: #fff;\n}\n\n.card.bg-indigo .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-indigo .bootstrap-datetimepicker-widget table td.active,\n.card.bg-indigo .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #8540f5;\n  color: #fff;\n}\n\n.card-purple:not(.card-outline) > .card-header {\n  background-color: #6f42c1;\n}\n\n.card-purple:not(.card-outline) > .card-header,\n.card-purple:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-purple:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-purple.card-outline {\n  border-top: 3px solid #6f42c1;\n}\n\n.card-purple.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-purple.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #6f42c1;\n}\n\n.bg-purple .btn-tool,\n.bg-gradient-purple .btn-tool,\n.card-purple:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-purple .btn-tool:hover,\n.bg-gradient-purple .btn-tool:hover,\n.card-purple:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-purple .bootstrap-datetimepicker-widget .table td,\n.card.bg-purple .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-purple .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-purple .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-purple .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-purple .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-purple .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #5d36a4;\n  color: #fff;\n}\n\n.card.bg-purple .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-purple .bootstrap-datetimepicker-widget table td.active,\n.card.bg-purple .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #8c68ce;\n  color: #fff;\n}\n\n.card-pink:not(.card-outline) > .card-header {\n  background-color: #e83e8c;\n}\n\n.card-pink:not(.card-outline) > .card-header,\n.card-pink:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-pink:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-pink.card-outline {\n  border-top: 3px solid #e83e8c;\n}\n\n.card-pink.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-pink.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #e83e8c;\n}\n\n.bg-pink .btn-tool,\n.bg-gradient-pink .btn-tool,\n.card-pink:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-pink .btn-tool:hover,\n.bg-gradient-pink .btn-tool:hover,\n.card-pink:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-pink .bootstrap-datetimepicker-widget .table td,\n.card.bg-pink .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-pink .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-pink .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-pink .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-pink .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-pink .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #e21b76;\n  color: #fff;\n}\n\n.card.bg-pink .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-pink .bootstrap-datetimepicker-widget table td.active,\n.card.bg-pink .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #ed6ca7;\n  color: #fff;\n}\n\n.card-red:not(.card-outline) > .card-header {\n  background-color: #dc3545;\n}\n\n.card-red:not(.card-outline) > .card-header,\n.card-red:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-red:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-red.card-outline {\n  border-top: 3px solid #dc3545;\n}\n\n.card-red.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-red.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #dc3545;\n}\n\n.bg-red .btn-tool,\n.bg-gradient-red .btn-tool,\n.card-red:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-red .btn-tool:hover,\n.bg-gradient-red .btn-tool:hover,\n.card-red:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-red .bootstrap-datetimepicker-widget .table td,\n.card.bg-red .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-red .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-red .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-red .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-red .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-red .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #c62232;\n  color: #fff;\n}\n\n.card.bg-red .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-red .bootstrap-datetimepicker-widget table td.active,\n.card.bg-red .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #e4606d;\n  color: #fff;\n}\n\n.card-orange:not(.card-outline) > .card-header {\n  background-color: #fd7e14;\n}\n\n.card-orange:not(.card-outline) > .card-header,\n.card-orange:not(.card-outline) > .card-header a {\n  color: #1f2d3d;\n}\n\n.card-orange:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-orange.card-outline {\n  border-top: 3px solid #fd7e14;\n}\n\n.card-orange.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-orange.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #fd7e14;\n}\n\n.bg-orange .btn-tool,\n.bg-gradient-orange .btn-tool,\n.card-orange:not(.card-outline) .btn-tool {\n  color: rgba(31, 45, 61, 0.8);\n}\n\n.bg-orange .btn-tool:hover,\n.bg-gradient-orange .btn-tool:hover,\n.card-orange:not(.card-outline) .btn-tool:hover {\n  color: #1f2d3d;\n}\n\n.card.bg-orange .bootstrap-datetimepicker-widget .table td,\n.card.bg-orange .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-orange .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-orange .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-orange .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-orange .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-orange .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #e66a02;\n  color: #1f2d3d;\n}\n\n.card.bg-orange .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #1f2d3d;\n}\n\n.card.bg-orange .bootstrap-datetimepicker-widget table td.active,\n.card.bg-orange .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #fd9a47;\n  color: #1f2d3d;\n}\n\n.card-yellow:not(.card-outline) > .card-header {\n  background-color: #ffc107;\n}\n\n.card-yellow:not(.card-outline) > .card-header,\n.card-yellow:not(.card-outline) > .card-header a {\n  color: #1f2d3d;\n}\n\n.card-yellow:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-yellow.card-outline {\n  border-top: 3px solid #ffc107;\n}\n\n.card-yellow.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-yellow.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #ffc107;\n}\n\n.bg-yellow .btn-tool,\n.bg-gradient-yellow .btn-tool,\n.card-yellow:not(.card-outline) .btn-tool {\n  color: rgba(31, 45, 61, 0.8);\n}\n\n.bg-yellow .btn-tool:hover,\n.bg-gradient-yellow .btn-tool:hover,\n.card-yellow:not(.card-outline) .btn-tool:hover {\n  color: #1f2d3d;\n}\n\n.card.bg-yellow .bootstrap-datetimepicker-widget .table td,\n.card.bg-yellow .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-yellow .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-yellow .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-yellow .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-yellow .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-yellow .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #dda600;\n  color: #1f2d3d;\n}\n\n.card.bg-yellow .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #1f2d3d;\n}\n\n.card.bg-yellow .bootstrap-datetimepicker-widget table td.active,\n.card.bg-yellow .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #ffce3a;\n  color: #1f2d3d;\n}\n\n.card-green:not(.card-outline) > .card-header {\n  background-color: #28a745;\n}\n\n.card-green:not(.card-outline) > .card-header,\n.card-green:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-green:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-green.card-outline {\n  border-top: 3px solid #28a745;\n}\n\n.card-green.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-green.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #28a745;\n}\n\n.bg-green .btn-tool,\n.bg-gradient-green .btn-tool,\n.card-green:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-green .btn-tool:hover,\n.bg-gradient-green .btn-tool:hover,\n.card-green:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-green .bootstrap-datetimepicker-widget .table td,\n.card.bg-green .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-green .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-green .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-green .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-green .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-green .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #208637;\n  color: #fff;\n}\n\n.card.bg-green .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-green .bootstrap-datetimepicker-widget table td.active,\n.card.bg-green .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #34ce57;\n  color: #fff;\n}\n\n.card-teal:not(.card-outline) > .card-header {\n  background-color: #20c997;\n}\n\n.card-teal:not(.card-outline) > .card-header,\n.card-teal:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-teal:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-teal.card-outline {\n  border-top: 3px solid #20c997;\n}\n\n.card-teal.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-teal.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #20c997;\n}\n\n.bg-teal .btn-tool,\n.bg-gradient-teal .btn-tool,\n.card-teal:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-teal .btn-tool:hover,\n.bg-gradient-teal .btn-tool:hover,\n.card-teal:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-teal .bootstrap-datetimepicker-widget .table td,\n.card.bg-teal .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-teal .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-teal .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-teal .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-teal .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-teal .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #1aa67d;\n  color: #fff;\n}\n\n.card.bg-teal .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-teal .bootstrap-datetimepicker-widget table td.active,\n.card.bg-teal .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #3ce0af;\n  color: #fff;\n}\n\n.card-cyan:not(.card-outline) > .card-header {\n  background-color: #17a2b8;\n}\n\n.card-cyan:not(.card-outline) > .card-header,\n.card-cyan:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-cyan:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-cyan.card-outline {\n  border-top: 3px solid #17a2b8;\n}\n\n.card-cyan.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-cyan.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #17a2b8;\n}\n\n.bg-cyan .btn-tool,\n.bg-gradient-cyan .btn-tool,\n.card-cyan:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-cyan .btn-tool:hover,\n.bg-gradient-cyan .btn-tool:hover,\n.card-cyan:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-cyan .bootstrap-datetimepicker-widget .table td,\n.card.bg-cyan .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-cyan .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-cyan .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-cyan .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-cyan .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-cyan .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #128294;\n  color: #fff;\n}\n\n.card.bg-cyan .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-cyan .bootstrap-datetimepicker-widget table td.active,\n.card.bg-cyan .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #1fc8e3;\n  color: #fff;\n}\n\n.card-white:not(.card-outline) > .card-header {\n  background-color: #fff;\n}\n\n.card-white:not(.card-outline) > .card-header,\n.card-white:not(.card-outline) > .card-header a {\n  color: #1f2d3d;\n}\n\n.card-white:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-white.card-outline {\n  border-top: 3px solid #fff;\n}\n\n.card-white.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-white.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #fff;\n}\n\n.bg-white .btn-tool,\n.bg-gradient-white .btn-tool,\n.card-white:not(.card-outline) .btn-tool {\n  color: rgba(31, 45, 61, 0.8);\n}\n\n.bg-white .btn-tool:hover,\n.bg-gradient-white .btn-tool:hover,\n.card-white:not(.card-outline) .btn-tool:hover {\n  color: #1f2d3d;\n}\n\n.card.bg-white .bootstrap-datetimepicker-widget .table td,\n.card.bg-white .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-white .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-white .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-white .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-white .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-white .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #ebebeb;\n  color: #1f2d3d;\n}\n\n.card.bg-white .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #1f2d3d;\n}\n\n.card.bg-white .bootstrap-datetimepicker-widget table td.active,\n.card.bg-white .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: white;\n  color: #1f2d3d;\n}\n\n.card-gray:not(.card-outline) > .card-header {\n  background-color: #6c757d;\n}\n\n.card-gray:not(.card-outline) > .card-header,\n.card-gray:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-gray:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-gray.card-outline {\n  border-top: 3px solid #6c757d;\n}\n\n.card-gray.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-gray.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #6c757d;\n}\n\n.bg-gray .btn-tool,\n.bg-gradient-gray .btn-tool,\n.card-gray:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-gray .btn-tool:hover,\n.bg-gradient-gray .btn-tool:hover,\n.card-gray:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-gray .bootstrap-datetimepicker-widget .table td,\n.card.bg-gray .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-gray .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gray .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gray .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gray .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gray .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #596167;\n  color: #fff;\n}\n\n.card.bg-gray .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-gray .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gray .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #868e96;\n  color: #fff;\n}\n\n.card-gray-dark:not(.card-outline) > .card-header {\n  background-color: #343a40;\n}\n\n.card-gray-dark:not(.card-outline) > .card-header,\n.card-gray-dark:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-gray-dark:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-gray-dark.card-outline {\n  border-top: 3px solid #343a40;\n}\n\n.card-gray-dark.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-gray-dark.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #343a40;\n}\n\n.bg-gray-dark .btn-tool,\n.bg-gradient-gray-dark .btn-tool,\n.card-gray-dark:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-gray-dark .btn-tool:hover,\n.bg-gradient-gray-dark .btn-tool:hover,\n.card-gray-dark:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-gray-dark .bootstrap-datetimepicker-widget .table td,\n.card.bg-gray-dark .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #222629;\n  color: #fff;\n}\n\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #4b545c;\n  color: #fff;\n}\n\n.card {\n  box-shadow: 0 0 1px rgba(0, 0, 0, 0.125), 0 1px 3px rgba(0, 0, 0, 0.2);\n  margin-bottom: 1rem;\n}\n\n.card.bg-dark .card-header {\n  border-color: #383f45;\n}\n\n.card.bg-dark,\n.card.bg-dark .card-body {\n  color: #fff;\n}\n\n.card.maximized-card {\n  height: 100% !important;\n  left: 0;\n  max-height: 100% !important;\n  max-width: 100% !important;\n  position: fixed;\n  top: 0;\n  width: 100% !important;\n  z-index: 1040;\n}\n\n.card.maximized-card.was-collapsed .card-body {\n  display: block !important;\n}\n\n.card.maximized-card .card-body {\n  overflow: auto;\n}\n\n.card.maximized-card [data-widget=\"collapse\"] {\n  display: none;\n}\n\n.card.maximized-card .card-header,\n.card.maximized-card .card-footer {\n  border-radius: 0 !important;\n}\n\n.card.collapsed-card .card-body,\n.card.collapsed-card .card-footer {\n  display: none;\n}\n\n.card .nav.flex-column > li {\n  border-bottom: 1px solid rgba(0, 0, 0, 0.125);\n  margin: 0;\n}\n\n.card .nav.flex-column > li:last-of-type {\n  border-bottom: 0;\n}\n\n.card.height-control .card-body {\n  max-height: 300px;\n  overflow: auto;\n}\n\n.card .border-right {\n  border-right: 1px solid rgba(0, 0, 0, 0.125);\n}\n\n.card .border-left {\n  border-left: 1px solid rgba(0, 0, 0, 0.125);\n}\n\n.card.card-tabs:not(.card-outline) > .card-header {\n  border-bottom: 0;\n}\n\n.card.card-tabs:not(.card-outline) > .card-header .nav-item:first-child .nav-link {\n  border-left-color: transparent;\n}\n\n.card.card-tabs.card-outline .nav-item {\n  border-bottom: 0;\n}\n\n.card.card-tabs.card-outline .nav-item:first-child .nav-link {\n  border-left: 0;\n  margin-left: 0;\n}\n\n.card.card-tabs .card-tools {\n  margin: .3rem .5rem;\n}\n\n.card.card-tabs:not(.expanding-card).collapsed-card .card-header {\n  border-bottom: 0;\n}\n\n.card.card-tabs:not(.expanding-card).collapsed-card .card-header .nav-tabs {\n  border-bottom: 0;\n}\n\n.card.card-tabs:not(.expanding-card).collapsed-card .card-header .nav-tabs .nav-item {\n  margin-bottom: 0;\n}\n\n.card.card-tabs.expanding-card .card-header .nav-tabs .nav-item {\n  margin-bottom: -1px;\n}\n\n.card.card-outline-tabs {\n  border-top: 0;\n}\n\n.card.card-outline-tabs .card-header .nav-item:first-child .nav-link {\n  border-left: 0;\n  margin-left: 0;\n}\n\n.card.card-outline-tabs .card-header a {\n  border-top: 3px solid transparent;\n}\n\n.card.card-outline-tabs .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card.card-outline-tabs .card-header a.active:hover {\n  margin-top: 0;\n}\n\n.card.card-outline-tabs .card-tools {\n  margin: .5rem .5rem .3rem;\n}\n\n.card.card-outline-tabs:not(.expanding-card).collapsed-card .card-header {\n  border-bottom: 0;\n}\n\n.card.card-outline-tabs:not(.expanding-card).collapsed-card .card-header .nav-tabs {\n  border-bottom: 0;\n}\n\n.card.card-outline-tabs:not(.expanding-card).collapsed-card .card-header .nav-tabs .nav-item {\n  margin-bottom: 0;\n}\n\n.card.card-outline-tabs.expanding-card .card-header .nav-tabs .nav-item {\n  margin-bottom: -1px;\n}\n\nhtml.maximized-card {\n  overflow: hidden;\n}\n\n.card-header::after,\n.card-body::after,\n.card-footer::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.card-header {\n  background-color: transparent;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.125);\n  padding: 0.75rem 1.25rem;\n  position: relative;\n  border-top-left-radius: 0.25rem;\n  border-top-right-radius: 0.25rem;\n}\n\n.collapsed-card .card-header {\n  border-bottom: 0;\n}\n\n.card-header > .card-tools {\n  float: right;\n  margin-right: -0.625rem;\n}\n\n.card-header > .card-tools .input-group,\n.card-header > .card-tools .nav,\n.card-header > .card-tools .pagination {\n  margin-bottom: -0.3rem;\n  margin-top: -0.3rem;\n}\n\n.card-header > .card-tools [data-toggle=\"tooltip\"] {\n  position: relative;\n}\n\n.card-title {\n  float: left;\n  font-size: 1.1rem;\n  font-weight: 400;\n  margin: 0;\n}\n\n.card-text {\n  clear: both;\n}\n\n.btn-tool {\n  background-color: transparent;\n  color: #adb5bd;\n  font-size: 0.875rem;\n  margin: -0.75rem 0;\n  padding: .25rem .5rem;\n}\n\n.btn-group.show .btn-tool, .btn-tool:hover {\n  color: #495057;\n}\n\n.show .btn-tool, .btn-tool:focus {\n  box-shadow: none !important;\n}\n\n.text-sm .card-title {\n  font-size: 1rem;\n}\n\n.text-sm .nav-link {\n  padding: 0.4rem 0.8rem;\n}\n\n.card-body > .table {\n  margin-bottom: 0;\n}\n\n.card-body > .table > thead > tr > th,\n.card-body > .table > thead > tr > td {\n  border-top-width: 0;\n}\n\n.card-body .fc {\n  margin-top: 5px;\n}\n\n.card-body .full-width-chart {\n  margin: -19px;\n}\n\n.card-body.p-0 .full-width-chart {\n  margin: -9px;\n}\n\n.chart-legend {\n  padding-left: 0;\n  list-style: none;\n  margin: 10px 0;\n}\n\n@media (max-width: 576px) {\n  .chart-legend > li {\n    float: left;\n    margin-right: 10px;\n  }\n}\n\n.card-comments {\n  background-color: #f8f9fa;\n}\n\n.card-comments .card-comment {\n  border-bottom: 1px solid #e9ecef;\n  padding: 8px 0;\n}\n\n.card-comments .card-comment::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.card-comments .card-comment:last-of-type {\n  border-bottom: 0;\n}\n\n.card-comments .card-comment:first-of-type {\n  padding-top: 0;\n}\n\n.card-comments .card-comment img {\n  height: 1.875rem;\n  width: 1.875rem;\n  float: left;\n}\n\n.card-comments .comment-text {\n  color: #78838e;\n  margin-left: 40px;\n}\n\n.card-comments .username {\n  color: #495057;\n  display: block;\n  font-weight: 600;\n}\n\n.card-comments .text-muted {\n  font-size: 12px;\n  font-weight: 400;\n}\n\n.todo-list {\n  list-style: none;\n  margin: 0;\n  overflow: auto;\n  padding: 0;\n}\n\n.todo-list > li {\n  border-radius: 2px;\n  background-color: #f8f9fa;\n  border-left: 2px solid #e9ecef;\n  color: #495057;\n  margin-bottom: 2px;\n  padding: 10px;\n}\n\n.todo-list > li:last-of-type {\n  margin-bottom: 0;\n}\n\n.todo-list > li > input[type=\"checkbox\"] {\n  margin: 0 10px 0 5px;\n}\n\n.todo-list > li .text {\n  display: inline-block;\n  font-weight: 600;\n  margin-left: 5px;\n}\n\n.todo-list > li .badge {\n  font-size: .7rem;\n  margin-left: 10px;\n}\n\n.todo-list > li .tools {\n  color: #dc3545;\n  display: none;\n  float: right;\n}\n\n.todo-list > li .tools > .fa,\n.todo-list > li .tools > .fas,\n.todo-list > li .tools > .far,\n.todo-list > li .tools > .fab,\n.todo-list > li .tools > .fal,\n.todo-list > li .tools > .fad,\n.todo-list > li .tools > .svg-inline--fa,\n.todo-list > li .tools > .ion {\n  cursor: pointer;\n  margin-right: 5px;\n}\n\n.todo-list > li:hover .tools {\n  display: inline-block;\n}\n\n.todo-list > li.done {\n  color: #697582;\n}\n\n.todo-list > li.done .text {\n  font-weight: 500;\n  text-decoration: line-through;\n}\n\n.todo-list > li.done .badge {\n  background-color: #adb5bd !important;\n}\n\n.todo-list .primary {\n  border-left-color: #007bff;\n}\n\n.todo-list .secondary {\n  border-left-color: #6c757d;\n}\n\n.todo-list .success {\n  border-left-color: #28a745;\n}\n\n.todo-list .info {\n  border-left-color: #17a2b8;\n}\n\n.todo-list .warning {\n  border-left-color: #ffc107;\n}\n\n.todo-list .danger {\n  border-left-color: #dc3545;\n}\n\n.todo-list .light {\n  border-left-color: #f8f9fa;\n}\n\n.todo-list .dark {\n  border-left-color: #343a40;\n}\n\n.todo-list .lightblue {\n  border-left-color: #3c8dbc;\n}\n\n.todo-list .navy {\n  border-left-color: #001f3f;\n}\n\n.todo-list .olive {\n  border-left-color: #3d9970;\n}\n\n.todo-list .lime {\n  border-left-color: #01ff70;\n}\n\n.todo-list .fuchsia {\n  border-left-color: #f012be;\n}\n\n.todo-list .maroon {\n  border-left-color: #d81b60;\n}\n\n.todo-list .blue {\n  border-left-color: #007bff;\n}\n\n.todo-list .indigo {\n  border-left-color: #6610f2;\n}\n\n.todo-list .purple {\n  border-left-color: #6f42c1;\n}\n\n.todo-list .pink {\n  border-left-color: #e83e8c;\n}\n\n.todo-list .red {\n  border-left-color: #dc3545;\n}\n\n.todo-list .orange {\n  border-left-color: #fd7e14;\n}\n\n.todo-list .yellow {\n  border-left-color: #ffc107;\n}\n\n.todo-list .green {\n  border-left-color: #28a745;\n}\n\n.todo-list .teal {\n  border-left-color: #20c997;\n}\n\n.todo-list .cyan {\n  border-left-color: #17a2b8;\n}\n\n.todo-list .white {\n  border-left-color: #fff;\n}\n\n.todo-list .gray {\n  border-left-color: #6c757d;\n}\n\n.todo-list .gray-dark {\n  border-left-color: #343a40;\n}\n\n.todo-list .handle {\n  cursor: move;\n  display: inline-block;\n  margin: 0 5px;\n}\n\n.card-input {\n  max-width: 200px;\n}\n\n.card-default .nav-item:first-child .nav-link {\n  border-left: 0;\n}\n\n.dark-mode .card {\n  background-color: #343a40;\n  color: #fff;\n}\n\n.dark-mode .card .card {\n  background-color: #3f474e;\n  color: #fff;\n}\n\n.dark-mode .card .nav.flex-column > li {\n  border-bottom-color: #6c757d;\n}\n\n.dark-mode .card .card-footer {\n  background-color: rgba(0, 0, 0, 0.1);\n}\n\n.dark-mode .card.card-outline-tabs .card-header a:hover {\n  border-color: #6c757d;\n}\n\n.dark-mode .card:not(.card-outline) > .card-header a.active {\n  color: #fff;\n}\n\n.dark-mode .card-comments {\n  background-color: #373d44;\n}\n\n.dark-mode .card-comments .username {\n  color: #ced4da;\n}\n\n.dark-mode .card-comments .card-comment {\n  border-bottom-color: #454d55;\n}\n\n.dark-mode .todo-list > li {\n  background-color: #3f474e;\n  border-color: #454d55;\n  color: #fff;\n}\n\n.modal-dialog .overlay {\n  background-color: #000;\n  display: block;\n  height: 100%;\n  left: 0;\n  opacity: .7;\n  position: absolute;\n  top: 0;\n  width: 100%;\n  z-index: 1052;\n}\n\n.modal-content.bg-warning .modal-header,\n.modal-content.bg-warning .modal-footer {\n  border-color: #343a40;\n}\n\n.modal-content.bg-primary .close, .modal-content.bg-secondary .close, .modal-content.bg-info .close, .modal-content.bg-danger .close, .modal-content.bg-success .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.dark-mode .modal-header,\n.dark-mode .modal-footer {\n  border-color: #6c757d;\n}\n\n.dark-mode .modal-content {\n  background-color: #343a40;\n}\n\n.dark-mode .modal-content.bg-warning .modal-header,\n.dark-mode .modal-content.bg-warning .modal-footer {\n  border-color: #6c757d;\n}\n\n.dark-mode .modal-content.bg-warning .close {\n  color: #343a40 !important;\n  text-shadow: 0 1px 0 #495057 !important;\n}\n\n.dark-mode .modal-content.bg-primary .modal-header,\n.dark-mode .modal-content.bg-primary .modal-footer, .dark-mode .modal-content.bg-secondary .modal-header,\n.dark-mode .modal-content.bg-secondary .modal-footer, .dark-mode .modal-content.bg-info .modal-header,\n.dark-mode .modal-content.bg-info .modal-footer, .dark-mode .modal-content.bg-danger .modal-header,\n.dark-mode .modal-content.bg-danger .modal-footer, .dark-mode .modal-content.bg-success .modal-header,\n.dark-mode .modal-content.bg-success .modal-footer {\n  border-color: #fff;\n}\n\n.toasts-top-right {\n  position: absolute;\n  right: 0;\n  top: 0;\n  z-index: 1040;\n}\n\n.toasts-top-right.fixed {\n  position: fixed;\n}\n\n.toasts-top-left {\n  left: 0;\n  position: absolute;\n  top: 0;\n  z-index: 1040;\n}\n\n.toasts-top-left.fixed {\n  position: fixed;\n}\n\n.toasts-bottom-right {\n  bottom: 0;\n  position: absolute;\n  right: 0;\n  z-index: 1040;\n}\n\n.toasts-bottom-right.fixed {\n  position: fixed;\n}\n\n.toasts-bottom-left {\n  bottom: 0;\n  left: 0;\n  position: absolute;\n  z-index: 1040;\n}\n\n.toasts-bottom-left.fixed {\n  position: fixed;\n}\n\n.dark-mode .toast {\n  background-color: rgba(52, 58, 64, 0.85);\n  color: #fff;\n}\n\n.dark-mode .toast .toast-header {\n  background-color: rgba(52, 58, 64, 0.7);\n  color: #f8f9fa;\n}\n\n.toast.bg-primary {\n  background-color: rgba(0, 123, 255, 0.9) !important;\n}\n\n.toast.bg-primary .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-primary .toast-header {\n  background-color: rgba(0, 123, 255, 0.85);\n  color: #fff;\n}\n\n.toast.bg-secondary {\n  background-color: rgba(108, 117, 125, 0.9) !important;\n}\n\n.toast.bg-secondary .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-secondary .toast-header {\n  background-color: rgba(108, 117, 125, 0.85);\n  color: #fff;\n}\n\n.toast.bg-success {\n  background-color: rgba(40, 167, 69, 0.9) !important;\n}\n\n.toast.bg-success .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-success .toast-header {\n  background-color: rgba(40, 167, 69, 0.85);\n  color: #fff;\n}\n\n.toast.bg-info {\n  background-color: rgba(23, 162, 184, 0.9) !important;\n}\n\n.toast.bg-info .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-info .toast-header {\n  background-color: rgba(23, 162, 184, 0.85);\n  color: #fff;\n}\n\n.toast.bg-warning {\n  background-color: rgba(255, 193, 7, 0.9) !important;\n}\n\n.toast.bg-warning .toast-header {\n  background-color: rgba(255, 193, 7, 0.85);\n  color: #1f2d3d;\n}\n\n.toast.bg-danger {\n  background-color: rgba(220, 53, 69, 0.9) !important;\n}\n\n.toast.bg-danger .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-danger .toast-header {\n  background-color: rgba(220, 53, 69, 0.85);\n  color: #fff;\n}\n\n.toast.bg-light {\n  background-color: rgba(248, 249, 250, 0.9) !important;\n}\n\n.toast.bg-light .toast-header {\n  background-color: rgba(248, 249, 250, 0.85);\n  color: #1f2d3d;\n}\n\n.toast.bg-dark {\n  background-color: rgba(52, 58, 64, 0.9) !important;\n}\n\n.toast.bg-dark .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-dark .toast-header {\n  background-color: rgba(52, 58, 64, 0.85);\n  color: #fff;\n}\n\n.toast.bg-lightblue {\n  background-color: rgba(60, 141, 188, 0.9) !important;\n}\n\n.toast.bg-lightblue .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-lightblue .toast-header {\n  background-color: rgba(60, 141, 188, 0.85);\n  color: #fff;\n}\n\n.toast.bg-navy {\n  background-color: rgba(0, 31, 63, 0.9) !important;\n}\n\n.toast.bg-navy .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-navy .toast-header {\n  background-color: rgba(0, 31, 63, 0.85);\n  color: #fff;\n}\n\n.toast.bg-olive {\n  background-color: rgba(61, 153, 112, 0.9) !important;\n}\n\n.toast.bg-olive .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-olive .toast-header {\n  background-color: rgba(61, 153, 112, 0.85);\n  color: #fff;\n}\n\n.toast.bg-lime {\n  background-color: rgba(1, 255, 112, 0.9) !important;\n}\n\n.toast.bg-lime .toast-header {\n  background-color: rgba(1, 255, 112, 0.85);\n  color: #1f2d3d;\n}\n\n.toast.bg-fuchsia {\n  background-color: rgba(240, 18, 190, 0.9) !important;\n}\n\n.toast.bg-fuchsia .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-fuchsia .toast-header {\n  background-color: rgba(240, 18, 190, 0.85);\n  color: #fff;\n}\n\n.toast.bg-maroon {\n  background-color: rgba(216, 27, 96, 0.9) !important;\n}\n\n.toast.bg-maroon .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-maroon .toast-header {\n  background-color: rgba(216, 27, 96, 0.85);\n  color: #fff;\n}\n\n.toast.bg-blue {\n  background-color: rgba(0, 123, 255, 0.9) !important;\n}\n\n.toast.bg-blue .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-blue .toast-header {\n  background-color: rgba(0, 123, 255, 0.85);\n  color: #fff;\n}\n\n.toast.bg-indigo {\n  background-color: rgba(102, 16, 242, 0.9) !important;\n}\n\n.toast.bg-indigo .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-indigo .toast-header {\n  background-color: rgba(102, 16, 242, 0.85);\n  color: #fff;\n}\n\n.toast.bg-purple {\n  background-color: rgba(111, 66, 193, 0.9) !important;\n}\n\n.toast.bg-purple .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-purple .toast-header {\n  background-color: rgba(111, 66, 193, 0.85);\n  color: #fff;\n}\n\n.toast.bg-pink {\n  background-color: rgba(232, 62, 140, 0.9) !important;\n}\n\n.toast.bg-pink .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-pink .toast-header {\n  background-color: rgba(232, 62, 140, 0.85);\n  color: #fff;\n}\n\n.toast.bg-red {\n  background-color: rgba(220, 53, 69, 0.9) !important;\n}\n\n.toast.bg-red .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-red .toast-header {\n  background-color: rgba(220, 53, 69, 0.85);\n  color: #fff;\n}\n\n.toast.bg-orange {\n  background-color: rgba(253, 126, 20, 0.9) !important;\n}\n\n.toast.bg-orange .toast-header {\n  background-color: rgba(253, 126, 20, 0.85);\n  color: #1f2d3d;\n}\n\n.toast.bg-yellow {\n  background-color: rgba(255, 193, 7, 0.9) !important;\n}\n\n.toast.bg-yellow .toast-header {\n  background-color: rgba(255, 193, 7, 0.85);\n  color: #1f2d3d;\n}\n\n.toast.bg-green {\n  background-color: rgba(40, 167, 69, 0.9) !important;\n}\n\n.toast.bg-green .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-green .toast-header {\n  background-color: rgba(40, 167, 69, 0.85);\n  color: #fff;\n}\n\n.toast.bg-teal {\n  background-color: rgba(32, 201, 151, 0.9) !important;\n}\n\n.toast.bg-teal .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-teal .toast-header {\n  background-color: rgba(32, 201, 151, 0.85);\n  color: #fff;\n}\n\n.toast.bg-cyan {\n  background-color: rgba(23, 162, 184, 0.9) !important;\n}\n\n.toast.bg-cyan .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-cyan .toast-header {\n  background-color: rgba(23, 162, 184, 0.85);\n  color: #fff;\n}\n\n.toast.bg-white {\n  background-color: rgba(255, 255, 255, 0.9) !important;\n}\n\n.toast.bg-white .toast-header {\n  background-color: rgba(255, 255, 255, 0.85);\n  color: #1f2d3d;\n}\n\n.toast.bg-gray {\n  background-color: rgba(108, 117, 125, 0.9) !important;\n}\n\n.toast.bg-gray .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-gray .toast-header {\n  background-color: rgba(108, 117, 125, 0.85);\n  color: #fff;\n}\n\n.toast.bg-gray-dark {\n  background-color: rgba(52, 58, 64, 0.9) !important;\n}\n\n.toast.bg-gray-dark .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-gray-dark .toast-header {\n  background-color: rgba(52, 58, 64, 0.85);\n  color: #fff;\n}\n\n.btn.disabled, .btn:disabled {\n  cursor: not-allowed;\n}\n\n.btn.btn-flat {\n  border-radius: 0;\n  border-width: 1px;\n  box-shadow: none;\n}\n\n.btn.btn-file {\n  overflow: hidden;\n  position: relative;\n}\n\n.btn.btn-file > input[type=\"file\"] {\n  background-color: #fff;\n  cursor: inherit;\n  display: block;\n  font-size: 100px;\n  min-height: 100%;\n  min-width: 100%;\n  opacity: 0;\n  outline: none;\n  position: absolute;\n  right: 0;\n  text-align: right;\n  top: 0;\n}\n\n.text-sm .btn {\n  font-size: 0.875rem !important;\n}\n\n.btn-default {\n  background-color: #f8f9fa;\n  border-color: #ddd;\n  color: #444;\n}\n\n.btn-default:hover, .btn-default:active, .btn-default.hover {\n  background-color: #e9ecef;\n  color: #2b2b2b;\n}\n\n.btn-app {\n  border-radius: 3px;\n  background-color: #f8f9fa;\n  border: 1px solid #ddd;\n  color: #6c757d;\n  font-size: 12px;\n  height: 60px;\n  margin: 0 0 10px 10px;\n  min-width: 80px;\n  padding: 15px 5px;\n  position: relative;\n  text-align: center;\n}\n\n.btn-app > .fa,\n.btn-app > .fas,\n.btn-app > .far,\n.btn-app > .fab,\n.btn-app > .fal,\n.btn-app > .fad,\n.btn-app > .svg-inline--fa,\n.btn-app > .ion {\n  display: block;\n  font-size: 20px;\n}\n\n.btn-app > .svg-inline--fa {\n  margin: 0 auto;\n}\n\n.btn-app:hover {\n  background-color: #f8f9fa;\n  border-color: #aaaaaa;\n  color: #444;\n}\n\n.btn-app:active, .btn-app:focus {\n  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);\n}\n\n.btn-app > .badge {\n  font-size: 10px;\n  font-weight: 400;\n  position: absolute;\n  right: -10px;\n  top: -3px;\n}\n\n.btn-xs {\n  padding: 0.125rem 0.25rem;\n  font-size: 0.75rem;\n  line-height: 1.5;\n  border-radius: 0.15rem;\n}\n\n.dark-mode .btn-default,\n.dark-mode .btn-app {\n  background-color: #3a4047;\n  color: #fff;\n  border-color: #6c757d;\n}\n\n.dark-mode .btn-default:hover, .dark-mode .btn-default:focus,\n.dark-mode .btn-app:hover,\n.dark-mode .btn-app:focus {\n  background-color: #3f474e;\n  color: #dee2e6;\n  border-color: #727b84;\n}\n\n.dark-mode .btn-light {\n  background-color: #454d55;\n  color: #fff;\n  border-color: #6c757d;\n}\n\n.dark-mode .btn-light:hover, .dark-mode .btn-light:focus {\n  background-color: #4b545c;\n  color: #dee2e6;\n  border-color: #78828a;\n}\n\n.callout {\n  border-radius: 0.25rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);\n  background-color: #fff;\n  border-left: 5px solid #e9ecef;\n  margin-bottom: 1rem;\n  padding: 1rem;\n}\n\n.callout a {\n  color: #495057;\n  text-decoration: underline;\n}\n\n.callout a:hover {\n  color: #e9ecef;\n}\n\n.callout p:last-child {\n  margin-bottom: 0;\n}\n\n.callout.callout-danger {\n  border-left-color: #bd2130;\n}\n\n.callout.callout-warning {\n  border-left-color: #d39e00;\n}\n\n.callout.callout-info {\n  border-left-color: #117a8b;\n}\n\n.callout.callout-success {\n  border-left-color: #1e7e34;\n}\n\n.dark-mode .callout {\n  background-color: #3f474e;\n}\n\n.alert .icon {\n  margin-right: 10px;\n}\n\n.alert .close {\n  color: #000;\n  opacity: .2;\n}\n\n.alert .close:hover {\n  opacity: .5;\n}\n\n.alert a {\n  color: #fff;\n  text-decoration: underline;\n}\n\n.alert-primary {\n  color: #fff;\n  background-color: #007bff;\n  border-color: #006fe6;\n}\n\n.alert-default-primary {\n  color: #004085;\n  background-color: #cce5ff;\n  border-color: #b8daff;\n}\n\n.alert-default-primary hr {\n  border-top-color: #9fcdff;\n}\n\n.alert-default-primary .alert-link {\n  color: #002752;\n}\n\n.alert-secondary {\n  color: #fff;\n  background-color: #6c757d;\n  border-color: #60686f;\n}\n\n.alert-default-secondary {\n  color: #383d41;\n  background-color: #e2e3e5;\n  border-color: #d6d8db;\n}\n\n.alert-default-secondary hr {\n  border-top-color: #c8cbcf;\n}\n\n.alert-default-secondary .alert-link {\n  color: #202326;\n}\n\n.alert-success {\n  color: #fff;\n  background-color: #28a745;\n  border-color: #23923d;\n}\n\n.alert-default-success {\n  color: #155724;\n  background-color: #d4edda;\n  border-color: #c3e6cb;\n}\n\n.alert-default-success hr {\n  border-top-color: #b1dfbb;\n}\n\n.alert-default-success .alert-link {\n  color: #0b2e13;\n}\n\n.alert-info {\n  color: #fff;\n  background-color: #17a2b8;\n  border-color: #148ea1;\n}\n\n.alert-default-info {\n  color: #0c5460;\n  background-color: #d1ecf1;\n  border-color: #bee5eb;\n}\n\n.alert-default-info hr {\n  border-top-color: #abdde5;\n}\n\n.alert-default-info .alert-link {\n  color: #062c33;\n}\n\n.alert-warning {\n  color: #1f2d3d;\n  background-color: #ffc107;\n  border-color: #edb100;\n}\n\n.alert-default-warning {\n  color: #856404;\n  background-color: #fff3cd;\n  border-color: #ffeeba;\n}\n\n.alert-default-warning hr {\n  border-top-color: #ffe8a1;\n}\n\n.alert-default-warning .alert-link {\n  color: #533f03;\n}\n\n.alert-danger {\n  color: #fff;\n  background-color: #dc3545;\n  border-color: #d32535;\n}\n\n.alert-default-danger {\n  color: #721c24;\n  background-color: #f8d7da;\n  border-color: #f5c6cb;\n}\n\n.alert-default-danger hr {\n  border-top-color: #f1b0b7;\n}\n\n.alert-default-danger .alert-link {\n  color: #491217;\n}\n\n.alert-light {\n  color: #1f2d3d;\n  background-color: #f8f9fa;\n  border-color: #e9ecef;\n}\n\n.alert-default-light {\n  color: #818182;\n  background-color: #fefefe;\n  border-color: #fdfdfe;\n}\n\n.alert-default-light hr {\n  border-top-color: #ececf6;\n}\n\n.alert-default-light .alert-link {\n  color: #686868;\n}\n\n.alert-dark {\n  color: #fff;\n  background-color: #343a40;\n  border-color: #292d32;\n}\n\n.alert-default-dark {\n  color: #1b1e21;\n  background-color: #d6d8d9;\n  border-color: #c6c8ca;\n}\n\n.alert-default-dark hr {\n  border-top-color: #b9bbbe;\n}\n\n.alert-default-dark .alert-link {\n  color: #040505;\n}\n\n.table:not(.table-dark) {\n  color: inherit;\n}\n\n.table.table-head-fixed thead tr:nth-child(1) th {\n  background-color: #fff;\n  border-bottom: 0;\n  box-shadow: inset 0 1px 0 #dee2e6, inset 0 -1px 0 #dee2e6;\n  position: -webkit-sticky;\n  position: sticky;\n  top: 0;\n  z-index: 10;\n}\n\n.table.table-head-fixed.table-dark thead tr:nth-child(1) th {\n  background-color: #212529;\n  box-shadow: inset 0 1px 0 #383f45, inset 0 -1px 0 #383f45;\n}\n\n.table.no-border,\n.table.no-border td,\n.table.no-border th {\n  border: 0;\n}\n\n.table.text-center,\n.table.text-center td,\n.table.text-center th {\n  text-align: center;\n}\n\n.table.table-valign-middle thead > tr > th,\n.table.table-valign-middle thead > tr > td,\n.table.table-valign-middle tbody > tr > th,\n.table.table-valign-middle tbody > tr > td {\n  vertical-align: middle;\n}\n\n.card-body.p-0 .table thead > tr > th:first-of-type,\n.card-body.p-0 .table thead > tr > td:first-of-type,\n.card-body.p-0 .table tfoot > tr > th:first-of-type,\n.card-body.p-0 .table tfoot > tr > td:first-of-type,\n.card-body.p-0 .table tbody > tr > th:first-of-type,\n.card-body.p-0 .table tbody > tr > td:first-of-type {\n  padding-left: 1.5rem;\n}\n\n.card-body.p-0 .table thead > tr > th:last-of-type,\n.card-body.p-0 .table thead > tr > td:last-of-type,\n.card-body.p-0 .table tfoot > tr > th:last-of-type,\n.card-body.p-0 .table tfoot > tr > td:last-of-type,\n.card-body.p-0 .table tbody > tr > th:last-of-type,\n.card-body.p-0 .table tbody > tr > td:last-of-type {\n  padding-right: 1.5rem;\n}\n\n.table-hover tbody tr.expandable-body:hover {\n  background-color: inherit !important;\n}\n\n[data-widget=\"expandable-table\"] {\n  cursor: pointer;\n}\n\n[data-widget=\"expandable-table\"] i {\n  transition: transform 0.3s linear;\n}\n\n[data-widget=\"expandable-table\"][aria-expanded=\"true\"] td > i[class*=\"right\"] {\n  transform: rotate(90deg);\n}\n\n[data-widget=\"expandable-table\"][aria-expanded=\"true\"] td > i[class*=\"left\"] {\n  transform: rotate(-90deg);\n}\n\n.expandable-body > td {\n  padding: 0 !important;\n  width: 100%;\n}\n\n.expandable-body > td > div,\n.expandable-body > td > p {\n  padding: 0.75rem;\n}\n\n.expandable-body .table {\n  width: calc(100% - 0.75rem);\n  margin: 0 0 0 0.75rem;\n}\n\n.expandable-body .table tr:first-child td,\n.expandable-body .table tr:first-child th {\n  border-top: none;\n}\n\n.dark-mode .table-bordered,\n.dark-mode .table-bordered td,\n.dark-mode .table-bordered th {\n  border-color: #6c757d;\n}\n\n.dark-mode .table-hover tbody tr:hover {\n  color: #dee2e6;\n  background-color: #3a4047;\n  border-color: #6c757d;\n}\n\n.dark-mode .table thead th {\n  border-bottom-color: #6c757d;\n}\n\n.dark-mode .table th,\n.dark-mode .table td {\n  border-top-color: #6c757d;\n}\n\n.dark-mode .table.table-head-fixed thead tr:nth-child(1) th {\n  background-color: #3f474e;\n}\n\n.carousel-control-prev .carousel-control-custom-icon {\n  margin-left: -20px;\n}\n\n.carousel-control-next .carousel-control-custom-icon {\n  margin-right: 20px;\n}\n\n.carousel-control-custom-icon > .fa,\n.carousel-control-custom-icon > .fas,\n.carousel-control-custom-icon > .far,\n.carousel-control-custom-icon > .fab,\n.carousel-control-custom-icon > .fal,\n.carousel-control-custom-icon > .fad,\n.carousel-control-custom-icon > .svg-inline--fa,\n.carousel-control-custom-icon > .ion {\n  display: inline-block;\n  font-size: 40px;\n  margin-top: -20px;\n  position: absolute;\n  top: 50%;\n  z-index: 5;\n}\n\n.close {\n  float: right;\n  font-size: 1.5rem;\n  font-weight: 700;\n  line-height: 1;\n  color: #000;\n  text-shadow: 0 1px 0 #fff;\n  opacity: .5;\n}\n\n.close:hover {\n  color: #000;\n  text-decoration: none;\n}\n\n.close:not(:disabled):not(.disabled):hover, .close:not(:disabled):not(.disabled):focus {\n  opacity: .75;\n}\n\n.close:focus {\n  outline: none;\n}\n\nbutton.close {\n  padding: 0;\n  background-color: transparent;\n  border: 0;\n}\n\na.close.disabled {\n  pointer-events: none;\n}\n/*# sourceMappingURL=adminlte.components.css.map */", "// stylelint-disable property-blacklist, scss/dollar-variable-default\n\n// SCSS RFS mixin\n//\n// Automated font-resizing\n//\n// See https://github.com/twbs/rfs\n\n// Configuration\n\n// Base font size\n$rfs-base-font-size: 1.25rem !default;\n$rfs-font-size-unit: rem !default;\n\n// Breakpoint at where font-size starts decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n// Resize font-size based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != \"number\" or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-responsive-font-sizes to false\n$enable-responsive-font-sizes: true !default;\n\n// Cache $rfs-base-font-size unit\n$rfs-base-font-size-unit: unit($rfs-base-font-size);\n\n// Remove px-unit from $rfs-base-font-size for calculations\n@if $rfs-base-font-size-unit == \"px\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1);\n}\n@else if $rfs-base-font-size-unit == \"rem\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1 / $rfs-rem-value);\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == \"px\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == \"rem\" or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1 / $rfs-rem-value);\n}\n\n// Responsive font-size mixin\n@mixin rfs($fs, $important: false) {\n  // Cache $fs unit\n  $fs-unit: if(type-of($fs) == \"number\", unit($fs), false);\n\n  // Add !important suffix if needed\n  $rfs-suffix: if($important, \" !important\", \"\");\n\n  // If $fs isn't a number (like inherit) or $fs has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n  @if not $fs-unit or $fs-unit != \"\" and $fs-unit != \"px\" and $fs-unit != \"rem\" or $fs == 0 {\n    font-size: #{$fs}#{$rfs-suffix};\n  }\n  @else {\n    // Variables for storing static and fluid rescaling\n    $rfs-static: null;\n    $rfs-fluid: null;\n\n    // Remove px-unit from $fs for calculations\n    @if $fs-unit == \"px\" {\n      $fs: $fs / ($fs * 0 + 1);\n    }\n    @else if $fs-unit == \"rem\" {\n      $fs: $fs / ($fs * 0 + 1 / $rfs-rem-value);\n    }\n\n    // Set default font-size\n    @if $rfs-font-size-unit == rem {\n      $rfs-static: #{$fs / $rfs-rem-value}rem#{$rfs-suffix};\n    }\n    @else if $rfs-font-size-unit == px {\n      $rfs-static: #{$fs}px#{$rfs-suffix};\n    }\n    @else {\n      @error \"`#{$rfs-font-size-unit}` is not a valid unit for $rfs-font-size-unit. Use `px` or `rem`.\";\n    }\n\n    // Only add media query if font-size is bigger as the minimum font-size\n    // If $rfs-factor == 1, no rescaling will take place\n    @if $fs > $rfs-base-font-size and $enable-responsive-font-sizes {\n      $min-width: null;\n      $variable-unit: null;\n\n      // Calculate minimum font-size for given font-size\n      $fs-min: $rfs-base-font-size + ($fs - $rfs-base-font-size) / $rfs-factor;\n\n      // Calculate difference between given font-size and minimum font-size for given font-size\n      $fs-diff: $fs - $fs-min;\n\n      // Base font-size formatting\n      // No need to check if the unit is valid, because we did that before\n      $min-width: if($rfs-font-size-unit == rem, #{$fs-min / $rfs-rem-value}rem, #{$fs-min}px);\n\n      // If two-dimensional, use smallest of screen width and height\n      $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n      // Calculate the variable width between 0 and $rfs-breakpoint\n      $variable-width: #{$fs-diff * 100 / $rfs-breakpoint}#{$variable-unit};\n\n      // Set the calculated font-size.\n      $rfs-fluid: calc(#{$min-width} + #{$variable-width}) #{$rfs-suffix};\n    }\n\n    // Rendering\n    @if $rfs-fluid == null {\n      // Only render static font-size if no fluid font-size is available\n      font-size: $rfs-static;\n    }\n    @else {\n      $mq-value: null;\n\n      // RFS breakpoint formatting\n      @if $rfs-breakpoint-unit == em or $rfs-breakpoint-unit == rem {\n        $mq-value: #{$rfs-breakpoint / $rfs-rem-value}#{$rfs-breakpoint-unit};\n      }\n      @else if $rfs-breakpoint-unit == px {\n        $mq-value: #{$rfs-breakpoint}px;\n      }\n      @else {\n        @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n      }\n\n      @if $rfs-class == \"disable\" {\n        // Adding an extra class increases specificity,\n        // which prevents the media query to override the font size\n        &,\n        .disable-responsive-font-size &,\n        &.disable-responsive-font-size {\n          font-size: $rfs-static;\n        }\n      }\n      @else {\n        font-size: $rfs-static;\n      }\n\n      @if $rfs-two-dimensional {\n        @media (max-width: #{$mq-value}), (max-height: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n      @else {\n        @media (max-width: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n    }\n  }\n}\n\n// The font-size & responsive-font-size mixin uses RFS to rescale font sizes\n@mixin font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n\n@mixin responsive-font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n", "// Gradients\n\n@mixin gradient-bg($color) {\n  @if $enable-gradients {\n    background: $color linear-gradient(180deg, mix($body-bg, $color, 15%), $color) repeat-x;\n  } @else {\n    background-color: $color;\n  }\n}\n\n// Horizontal gradient, from left to right\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-x($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to right, $start-color $start-percent, $end-color $end-percent);\n  background-repeat: repeat-x;\n}\n\n// Vertical gradient, from top to bottom\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-y($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to bottom, $start-color $start-percent, $end-color $end-percent);\n  background-repeat: repeat-x;\n}\n\n@mixin gradient-directional($start-color: $gray-700, $end-color: $gray-800, $deg: 45deg) {\n  background-image: linear-gradient($deg, $start-color, $end-color);\n  background-repeat: repeat-x;\n}\n@mixin gradient-x-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient(to right, $start-color, $mid-color $color-stop, $end-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-y-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient($start-color, $mid-color $color-stop, $end-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-radial($inner-color: $gray-700, $outer-color: $gray-800) {\n  background-image: radial-gradient(circle, $inner-color, $outer-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-striped($color: rgba($white, .15), $angle: 45deg) {\n  background-image: linear-gradient($angle, $color 25%, transparent 25%, transparent 50%, $color 50%, $color 75%, transparent 75%, transparent);\n}\n", "//\n// Mixins: Custom Forms\n//\n\n// Custom Switch Variant\n@mixin custom-switch-variant($name, $color) {\n  &.custom-switch-off-#{$name} {\n    .custom-control-input ~ .custom-control-label::before {\n      background-color: #{$color};\n      border-color: darken($color, 20%);\n    }\n\n    .custom-control-input:focus ~ .custom-control-label::before {\n      box-shadow: 0 0 0 1px $body-bg, 0 0 0 2px rgba($color, .25);\n    }\n\n    .custom-control-input ~ .custom-control-label::after {\n      background-color: darken($color, 25%);\n    }\n  }\n\n  &.custom-switch-on-#{$name} {\n    .custom-control-input:checked ~ .custom-control-label::before {\n      background-color: #{$color};\n      border-color: darken($color, 20%);\n    }\n\n    .custom-control-input:checked:focus ~ .custom-control-label::before {\n      box-shadow: 0 0 0 1px $body-bg, 0 0 0 2px rgba($color, .25);\n    }\n\n    .custom-control-input:checked ~ .custom-control-label::after {\n      background-color: lighten($color, 30%);\n    }\n  }\n}\n\n// Custom Range Variant\n@mixin custom-range-variant($name, $color) {\n  &.custom-range-#{$name} {\n    &:focus {\n      outline: none;\n\n      &::-webkit-slider-thumb {\n        box-shadow: 0 0 0 1px $body-bg, 0 0 0 2px rgba($color, .25);\n      }\n\n      &::-moz-range-thumb     {\n        box-shadow: 0 0 0 1px $body-bg, 0 0 0 2px rgba($color, .25);\n      }\n\n      &::-ms-thumb            {\n        box-shadow: 0 0 0 1px $body-bg, 0 0 0 2px rgba($color, .25);\n      }\n    }\n\n    &::-webkit-slider-thumb {\n      background-color: $color;\n\n      &:active {\n        background-color: lighten($color, 35%);\n      }\n    }\n\n    &::-moz-range-thumb {\n      background-color: $color;\n\n      &:active {\n        background-color: lighten($color, 35%);\n      }\n    }\n\n    &::-ms-thumb {\n      background-color: $color;\n\n      &:active {\n        background-color: lighten($color, 35%);\n      }\n    }\n  }\n}\n\n\n// Custom Control Input Variant\n@mixin custom-control-input-variant($name, $color) {\n  $custom-control-indicator-checked-color: $color;\n  $custom-checkbox-indicator-icon-checked: str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='#{$custom-control-indicator-checked-color}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\"), \"#\", \"%23\");\n  $custom-radio-indicator-icon-checked: str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='#{$custom-control-indicator-checked-color}'/%3E%3C/svg%3E\"), \"#\", \"%23\");\n\n  .custom-control-input-#{$name} {\n    &:checked ~ .custom-control-label::before {\n      border-color: $color;\n      @include gradient-bg($color);\n    }\n\n    &.custom-control-input-outline:checked {\n      &[type=\"checkbox\"] ~ .custom-control-label::after {\n        background-image: $custom-checkbox-indicator-icon-checked !important;\n      }\n      &[type=\"radio\"] ~ .custom-control-label::after {\n        background-image: $custom-radio-indicator-icon-checked !important;\n      }\n    }\n\n    &:focus ~ .custom-control-label::before {\n      // the mixin is not used here to make sure there is feedback\n      @if $enable-shadows {\n        box-shadow: $input-box-shadow, 0 0 0 $input-btn-focus-width rgba($color, .25);\n      } @else {\n        box-shadow: 0 0 0 $input-btn-focus-width rgba($color, .25);\n      }\n    }\n\n    &:focus:not(:checked) ~ .custom-control-label::before {\n      border-color: lighten($color, 25%);\n    }\n\n    &:not(:disabled):active ~ .custom-control-label::before {\n      background-color: lighten($color, 35%);\n      border-color: lighten($color, 35%);\n    }\n  }\n}\n", "//\n// Component: Progress Bar\n//\n\n//General CSS\n.progress {\n  @include box-shadow(none);\n  @include border-radius($progress-bar-border-radius);\n\n  // Vertical bars\n  &.vertical {\n    display: inline-block;\n    height: 200px;\n    margin-right: 10px;\n    position: relative;\n    width: 30px;\n\n    > .progress-bar {\n      bottom: 0;\n      position: absolute;\n      width: 100%;\n    }\n\n    //Sizes\n    &.sm,\n    &.progress-sm {\n      width: 20px;\n    }\n\n    &.xs,\n    &.progress-xs {\n      width: 10px;\n    }\n\n    &.xxs,\n    &.progress-xxs {\n      width: 3px;\n    }\n  }\n}\n\n.progress-group {\n  margin-bottom: map-get($spacers, 2);\n}\n\n// size variation\n.progress-sm {\n  height: 10px;\n}\n\n.progress-xs {\n  height: 7px;\n}\n\n.progress-xxs {\n  height: 3px;\n}\n\n// Remove margins from progress bars when put in a table\n.table {\n  tr > td {\n    .progress {\n      margin: 0;\n    }\n  }\n}\n\n.dark-mode {\n  .progress {\n    background: lighten($dark, 7.5%);\n  }\n}\n", "@mixin box-shadow($shadow...) {\n  @if $enable-shadows {\n    $result: ();\n\n    @if (length($shadow) == 1) {\n      // We can pass `@include box-shadow(none);`\n      $result: $shadow;\n    } @else {\n      // Filter to avoid invalid properties for example `box-shadow: none, 1px 1px black;`\n      @for $i from 1 through length($shadow) {\n        @if nth($shadow, $i) != \"none\" {\n          $result: append($result, nth($shadow, $i), \"comma\");\n        }\n      }\n    }\n    @if (length($result) > 0) {\n      box-shadow: $result;\n    }\n  }\n}\n", "//\n// Mixins: Cards Variant\n//\n\n@mixin cards-variant($name, $color) {\n  .card-#{$name} {\n    &:not(.card-outline) {\n      > .card-header {\n        background-color: $color;\n\n        &,\n        a {\n          color: color-yiq($color);\n        }\n\n        a.active {\n          color: color-yiq($white);\n        }\n      }\n    }\n\n    &.card-outline {\n      border-top: 3px solid $color;\n    }\n\n    &.card-outline-tabs {\n      > .card-header {\n        a {\n          &:hover {\n            border-top: 3px solid $nav-tabs-border-color;\n          }\n\n          &.active {\n            border-top: 3px solid $color;\n          }\n        }\n      }\n    }\n  }\n\n  .bg-#{$name},\n  .bg-gradient-#{$name},\n  .card-#{$name}:not(.card-outline) {\n    .btn-tool {\n      color: rgba(color-yiq($color), .8);\n\n      &:hover {\n        color: color-yiq($color);\n      }\n    }\n  }\n\n  .card.bg-#{$name},\n  .card.bg-gradient-#{$name} {\n    .bootstrap-datetimepicker-widget {\n      .table td,\n      .table th {\n        border: none;\n      }\n\n      table thead tr:first-child th:hover,\n      table td.day:hover,\n      table td.hour:hover,\n      table td.minute:hover,\n      table td.second:hover {\n        background-color: darken($color, 8%);\n        color: color-yiq($color);\n      }\n\n      table td.today::before {\n        border-bottom-color: color-yiq($color);\n      }\n\n      table td.active,\n      table td.active:hover {\n        background-color: lighten($color, 10%);\n        color: color-yiq($color);\n      }\n    }\n  }\n}\n\n", "//\n// Component: Cards\n//\n\n// Color variants\n@each $name, $color in $theme-colors {\n  @include cards-variant($name, $color);\n}\n\n@each $name, $color in $colors {\n  @include cards-variant($name, $color);\n}\n\n.card {\n  @include box-shadow($card-shadow);\n  margin-bottom: map-get($spacers, 3);\n\n  &.bg-dark {\n    .card-header {\n      border-color: $card-dark-border-color;\n    }\n\n    &,\n    .card-body {\n      color: $white;\n    }\n  }\n\n  &.maximized-card {\n    height: 100% !important;\n    left: 0;\n    max-height: 100% !important;\n    max-width: 100% !important;\n    position: fixed;\n    top: 0;\n    width: 100% !important;\n    z-index: $zindex-modal-backdrop;\n\n    &.was-collapsed .card-body {\n      display: block !important;\n    }\n\n    .card-body {\n      overflow: auto;\n    }\n\n    [data-widget=\"collapse\"] {\n      display: none;\n    }\n\n    .card-header,\n    .card-footer {\n      @include border-radius(0 !important);\n    }\n  }\n\n  // collapsed mode\n  &.collapsed-card {\n    .card-body,\n    .card-footer {\n      display: none;\n    }\n  }\n\n  .nav.flex-column {\n    > li {\n      border-bottom: 1px solid $card-border-color;\n      margin: 0;\n\n      &:last-of-type {\n        border-bottom: 0;\n      }\n    }\n  }\n\n  // fixed height to 300px\n  &.height-control {\n    .card-body {\n      max-height: 300px;\n      overflow: auto;\n    }\n  }\n\n  .border-right {\n    border-right: 1px solid $card-border-color;\n  }\n\n  .border-left {\n    border-left: 1px solid $card-border-color;\n  }\n\n  &.card-tabs {\n    &:not(.card-outline) {\n      > .card-header {\n        border-bottom: 0;\n\n        .nav-item {\n          &:first-child .nav-link {\n            border-left-color: transparent;\n          }\n        }\n      }\n    }\n\n    &.card-outline {\n      .nav-item {\n        border-bottom: 0;\n\n        &:first-child .nav-link {\n          border-left: 0;\n          margin-left: 0;\n        }\n      }\n    }\n\n    .card-tools {\n      margin: .3rem .5rem;\n    }\n\n    &:not(.expanding-card).collapsed-card {\n      .card-header {\n        border-bottom: 0;\n\n        .nav-tabs {\n          border-bottom: 0;\n\n          .nav-item {\n            margin-bottom: 0;\n          }\n        }\n      }\n    }\n\n    &.expanding-card {\n      .card-header {\n        .nav-tabs {\n          .nav-item {\n            margin-bottom: -1px;\n          }\n        }\n      }\n    }\n  }\n\n  &.card-outline-tabs {\n    border-top: 0;\n\n    .card-header {\n      .nav-item {\n        &:first-child .nav-link {\n          border-left: 0;\n          margin-left: 0;\n        }\n      }\n\n      a {\n        border-top: 3px solid transparent;\n\n        &:hover {\n          border-top: 3px solid $nav-tabs-border-color;\n        }\n\n        &.active {\n          &:hover {\n            margin-top: 0;\n          }\n        }\n      }\n    }\n\n    .card-tools {\n      margin: .5rem .5rem .3rem;\n    }\n\n    &:not(.expanding-card).collapsed-card .card-header {\n      border-bottom: 0;\n\n      .nav-tabs {\n        border-bottom: 0;\n\n        .nav-item {\n          margin-bottom: 0;\n        }\n      }\n    }\n\n    &.expanding-card {\n      .card-header {\n        .nav-tabs {\n          .nav-item {\n            margin-bottom: -1px;\n          }\n        }\n      }\n    }\n  }\n\n}\n\n// Maximized Card Body Scroll fix\nhtml.maximized-card {\n  overflow: hidden;\n}\n\n// Add clearfix to header, body and footer\n.card-header,\n.card-body,\n.card-footer {\n  @include clearfix ();\n}\n\n// Box header\n.card-header {\n  background-color: transparent;\n  border-bottom: 1px solid $card-border-color;\n  padding: (($card-spacer-y / 2) * 2) $card-spacer-x;\n  position: relative;\n\n  @if $enable-rounded {\n    @include border-top-radius($border-radius);\n  }\n\n  .collapsed-card & {\n    border-bottom: 0;\n  }\n\n  > .card-tools {\n    float: right;\n    margin-right: -$card-spacer-x / 2;\n\n    .input-group,\n    .nav,\n    .pagination {\n      margin-bottom: -$card-spacer-y / 2.5;\n      margin-top: -$card-spacer-y / 2.5;\n    }\n\n    [data-toggle=\"tooltip\"] {\n      position: relative;\n    }\n  }\n}\n\n.card-title {\n  float: left;\n  font-size: $card-title-font-size;\n  font-weight: $card-title-font-weight;\n  margin: 0;\n}\n\n.card-text {\n  clear: both;\n}\n\n\n// Box Tools Buttons\n.btn-tool {\n  background-color: transparent;\n  color: $gray-500;\n  font-size: $font-size-sm;\n  margin: -(($card-spacer-y / 2) * 2) 0;\n  padding: .25rem .5rem;\n\n  .btn-group.show &,\n  &:hover {\n    color: $gray-700;\n  }\n\n  .show &,\n  &:focus {\n    box-shadow: none !important;\n  }\n}\n\n.text-sm {\n  .card-title {\n    font-size: $card-title-font-size-sm;\n  }\n\n  .nav-link {\n    padding: $card-nav-link-padding-sm-y $card-nav-link-padding-sm-x;\n  }\n}\n\n// Box Body\n.card-body {\n  // @include border-radius-sides(0, 0, $border-radius, $border-radius);\n  // .no-header & {\n  //   @include border-top-radius($border-radius);\n  // }\n\n  // Tables within the box body\n  > .table {\n    margin-bottom: 0;\n\n    > thead > tr > th,\n    > thead > tr > td {\n      border-top-width: 0;\n    }\n  }\n\n  // Calendar within the box body\n  .fc {\n    margin-top: 5px;\n  }\n\n  .full-width-chart {\n    margin: -19px;\n  }\n\n  &.p-0 .full-width-chart {\n    margin: -9px;\n  }\n}\n\n.chart-legend {\n  @include list-unstyled ();\n  margin: 10px 0;\n\n  > li {\n    @media (max-width: map-get($grid-breakpoints, sm)) {\n      float: left;\n      margin-right: 10px;\n    }\n  }\n}\n\n// Comment Box\n.card-comments {\n  background-color: $gray-100;\n\n  .card-comment {\n    @include clearfix ();\n    border-bottom: 1px solid $gray-200;\n    padding: 8px 0;\n\n    &:last-of-type {\n      border-bottom: 0;\n    }\n\n    &:first-of-type {\n      padding-top: 0;\n    }\n\n    img {\n      height: $card-img-size;\n      width: $card-img-size;\n      float: left;\n    }\n  }\n\n  .comment-text {\n    color: lighten($gray-700, 20%);\n    margin-left: 40px;\n  }\n\n  .username {\n    color: $gray-700;\n    display: block;\n    font-weight: 600;\n  }\n\n  .text-muted {\n    font-size: 12px;\n    font-weight: 400;\n  }\n}\n\n// Widgets\n//-----------\n\n// Widget: TODO LIST\n.todo-list {\n  list-style: none;\n  margin: 0;\n  overflow: auto;\n  padding: 0;\n\n  // Todo list element\n  > li {\n    @include border-radius(2px);\n    background-color: $gray-100;\n    border-left: 2px solid $gray-200;\n    color: $gray-700;\n    margin-bottom: 2px;\n    padding: 10px;\n\n    &:last-of-type {\n      margin-bottom: 0;\n    }\n\n    > input[type=\"checkbox\"] {\n      margin: 0 10px 0 5px;\n    }\n\n    .text {\n      display: inline-block;\n      font-weight: 600;\n      margin-left: 5px;\n    }\n\n    // Time labels\n    .badge {\n      font-size: .7rem;\n      margin-left: 10px;\n    }\n\n    // Tools and options box\n    .tools {\n      color: theme-color(\"danger\");\n      display: none;\n      float: right;\n\n      // icons\n      > .fa,\n      > .fas,\n      > .far,\n      > .fab,\n      > .fal,\n      > .fad,\n      > .svg-inline--fa,\n      > .ion {\n        cursor: pointer;\n        margin-right: 5px;\n      }\n    }\n\n    &:hover .tools {\n      display: inline-block;\n    }\n\n    &.done {\n      color: darken($gray-500, 25%);\n\n      .text {\n        font-weight: 500;\n        text-decoration: line-through;\n      }\n\n      .badge {\n        background-color: $gray-500 !important;\n      }\n    }\n  }\n\n  // Color variants\n  @each $name, $color in $theme-colors {\n    .#{$name} {\n      border-left-color: $color;\n    }\n  }\n\n  @each $name, $color in $colors {\n    .#{$name} {\n      border-left-color: $color;\n    }\n  }\n\n  .handle {\n    cursor: move;\n    display: inline-block;\n    margin: 0 5px;\n  }\n}\n\n// END TODO WIDGET\n\n// Input in box\n.card-input {\n  max-width: 200px;\n}\n\n// Nav Tabs override\n.card-default {\n  .nav-item {\n    &:first-child .nav-link {\n      border-left: 0;\n    }\n  }\n}\n\n.dark-mode {\n\n  .card {\n    background-color: $dark;\n    color: $white;\n\n    .card {\n      background-color: lighten($dark, 5%);\n      color: $white;\n    }\n    .nav.flex-column > li {\n      border-bottom-color: $gray-600;\n    }\n    .card-footer {\n      background-color: rgba($black, .1);\n    }\n    &.card-outline-tabs .card-header a:hover {\n      border-color: $gray-600;\n    }\n    &:not(.card-outline) > .card-header a.active {\n      color: $white;\n    }\n  }\n\n  .card-comments {\n    background-color: lighten($dark, 1.25%);\n    .username {\n      color: $gray-400;\n    }\n    .card-comment {\n      border-bottom-color: lighten($dark, 7.5%);\n    }\n  }\n\n  .todo-list > li {\n    background-color: lighten($dark, 5%);\n    border-color: lighten($dark, 7.5%);\n    color: $white;\n  }\n}\n", "@mixin clearfix() {\n  &::after {\n    display: block;\n    clear: both;\n    content: \"\";\n  }\n}\n", "// Lists\n\n// Unstyled keeps list items block level, just removes default browser padding and list-style\n@mixin list-unstyled() {\n  padding-left: 0;\n  list-style: none;\n}\n", "//\n// Component: Modals\n//\n\n// Overlay\n.modal-dialog {\n  .overlay {\n    background-color: $black;\n    display: block;\n    height: 100%;\n    left: 0;\n    opacity: .7;\n    position: absolute;\n    top: 0;\n    width: 100%;\n    z-index: ($zindex-modal + 2);\n  }\n}\n\n\n// BG Color Variations Fixes\n.modal-content {\n  &.bg-warning {\n    .modal-header,\n    .modal-footer {\n      border-color: $gray-800;\n    }\n  }\n\n  &.bg-primary,\n  &.bg-secondary,\n  &.bg-info,\n  &.bg-danger,\n  &.bg-success, {\n    .close {\n      color: $white;\n      text-shadow: 0 1px 0 $black;\n    }\n  }\n}\n\n.dark-mode {\n  .modal-header,\n  .modal-footer {\n    border-color: $gray-600;\n  }\n  .modal-content {\n    background-color: $dark;\n\n    &.bg-warning {\n      .modal-header,\n      .modal-footer {\n        border-color: $gray-600;\n      }\n      .close {\n        color: $dark !important;\n        text-shadow: 0 1px 0 $gray-700 !important;\n      }\n    }\n\n    &.bg-primary,\n    &.bg-secondary,\n    &.bg-info,\n    &.bg-danger,\n    &.bg-success {\n      .modal-header,\n      .modal-footer {\n        border-color: $white;\n      }\n    }\n  }\n}\n", "//\n// Component: Toasts\n//\n\n.toasts-top-right {\n  position: absolute;\n  right: 0;\n  top: 0;\n  z-index: $zindex-toasts;\n\n  &.fixed {\n    position: fixed;\n  }\n}\n\n.toasts-top-left {\n  left: 0;\n  position: absolute;\n  top: 0;\n  z-index: $zindex-toasts;\n\n  &.fixed {\n    position: fixed;\n  }\n}\n\n.toasts-bottom-right {\n  bottom: 0;\n  position: absolute;\n  right: 0;\n  z-index: $zindex-toasts;\n\n  &.fixed {\n    position: fixed;\n  }\n}\n\n.toasts-bottom-left {\n  bottom: 0;\n  left: 0;\n  position: absolute;\n  z-index: $zindex-toasts;\n\n  &.fixed {\n    position: fixed;\n  }\n}\n\n.dark-mode {\n  .toast {\n    background-color: rgba($dark, .85);\n    color: $white;\n\n    .toast-header {\n      background-color: rgba($dark, .7);\n      color: $gray-100;\n    }\n  }\n}\n\n.toast {\n  @each $name, $color in $theme-colors {\n    @include toast-variant($name, $color);\n  }\n  @each $name, $color in $colors {\n    @include toast-variant($name, $color);\n  }\n}\n", "//\n// Mixins: Toasts\n//\n\n// Toast Variant\n@mixin toast-variant($name, $color) {\n  &.bg-#{$name} {\n    background-color: rgba($color, .9) !important;\n    @if (color-yiq($color) == $yiq-text-light) {\n\n      .close {\n        color: color-yiq($color);\n        text-shadow: 0 1px 0 $black;\n      }\n    }\n\n    .toast-header {\n      background-color: rgba($color, .85);\n      color: color-yiq($color);\n    }\n  }\n}\n\n", "//\n// Component: Button\n//\n\n.btn {\n  &.disabled,\n  &:disabled {\n    cursor: not-allowed;\n  }\n\n  // Flat buttons\n  &.btn-flat {\n    @include border-radius(0);\n    border-width: 1px;\n    box-shadow: none;\n  }\n\n  // input file btn\n  &.btn-file {\n    overflow: hidden;\n    position: relative;\n\n    > input[type=\"file\"] {\n      background-color: $white;\n      cursor: inherit;\n      display: block;\n      font-size: 100px;\n      min-height: 100%;\n      min-width: 100%;\n      opacity: 0;\n      outline: none;\n      position: absolute;\n      right: 0;\n      text-align: right;\n      top: 0;\n    }\n  }\n\n  .text-sm & {\n    font-size: $font-size-sm !important;\n  }\n}\n\n// Button color variations\n.btn-default {\n  background-color: $button-default-background-color;\n  border-color: $button-default-border-color;\n  color: $button-default-color;\n\n  &:hover,\n  &:active,\n  &.hover {\n    background-color: darken($button-default-background-color, 5%);\n    color: darken($button-default-color, 10%);\n  }\n}\n\n// Application buttons\n.btn-app {\n  @include border-radius(3px);\n  background-color: $button-default-background-color;\n  border: 1px solid $button-default-border-color;\n  color: $gray-600;\n  font-size: 12px;\n  height: 60px;\n  margin: 0 0 10px 10px;\n  min-width: 80px;\n  padding: 15px 5px;\n  position: relative;\n  text-align: center;\n\n  // Icons within the btn\n  > .fa,\n  > .fas,\n  > .far,\n  > .fab,\n  > .fal,\n  > .fad,\n  > .svg-inline--fa,\n  > .ion {\n    display: block;\n    font-size: 20px;\n  }\n\n  > .svg-inline--fa {\n    margin: 0 auto;\n  }\n\n  &:hover {\n    background-color: $button-default-background-color;\n    border-color: darken($button-default-border-color, 20%);\n    color: $button-default-color;\n  }\n\n  &:active,\n  &:focus {\n    @include box-shadow(inset 0 3px 5px rgba($black, .125));\n  }\n\n  // The badge\n  > .badge {\n    font-size: 10px;\n    font-weight: 400;\n    position: absolute;\n    right: -10px;\n    top: -3px;\n  }\n}\n\n// Extra Button Size\n\n.btn-xs {\n  @include button-size($button-padding-y-xs, $button-padding-x-xs, $button-font-size-xs, $button-line-height-xs, $button-border-radius-xs);\n}\n\n.dark-mode {\n  .btn-default,\n  .btn-app {\n    background-color: lighten($dark, 2.5%);\n    color: $white;\n    border-color: $gray-600;\n\n    &:hover,\n    &:focus {\n      background-color: lighten($dark, 5%);\n      color: $gray-300;\n      border-color: lighten($gray-600, 2.5%);\n    }\n  }\n  .btn-light {\n    background-color: lighten($dark, 7.5%);\n    color: $white;\n    border-color: $gray-600;\n\n    &:hover,\n    &:focus {\n      background-color: lighten($dark, 10%);\n      color: $gray-300;\n      border-color: lighten($gray-600, 5%);\n    }\n  }\n}\n", "// Button variants\n//\n// Easily pump out default styles, as well as :hover, :focus, :active,\n// and disabled options for all buttons\n\n@mixin button-variant($background, $border, $hover-background: darken($background, 7.5%), $hover-border: darken($border, 10%), $active-background: darken($background, 10%), $active-border: darken($border, 12.5%)) {\n  color: color-yiq($background);\n  @include gradient-bg($background);\n  border-color: $border;\n  @include box-shadow($btn-box-shadow);\n\n  @include hover() {\n    color: color-yiq($hover-background);\n    @include gradient-bg($hover-background);\n    border-color: $hover-border;\n  }\n\n  &:focus,\n  &.focus {\n    color: color-yiq($hover-background);\n    @include gradient-bg($hover-background);\n    border-color: $hover-border;\n    @if $enable-shadows {\n      @include box-shadow($btn-box-shadow, 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5));\n    } @else {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      box-shadow: 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n    }\n  }\n\n  // Disabled comes first so active can properly restyle\n  &.disabled,\n  &:disabled {\n    color: color-yiq($background);\n    background-color: $background;\n    border-color: $border;\n    // Remove CSS gradients if they're enabled\n    @if $enable-gradients {\n      background-image: none;\n    }\n  }\n\n  &:not(:disabled):not(.disabled):active,\n  &:not(:disabled):not(.disabled).active,\n  .show > &.dropdown-toggle {\n    color: color-yiq($active-background);\n    background-color: $active-background;\n    @if $enable-gradients {\n      background-image: none; // Remove the gradient for the pressed/active state\n    }\n    border-color: $active-border;\n\n    &:focus {\n      @if $enable-shadows and $btn-active-box-shadow != none {\n        @include box-shadow($btn-active-box-shadow, 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5));\n      } @else {\n        // Avoid using mixin so we can pass custom focus shadow properly\n        box-shadow: 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n      }\n    }\n  }\n}\n\n@mixin button-outline-variant($color, $color-hover: color-yiq($color), $active-background: $color, $active-border: $color) {\n  color: $color;\n  border-color: $color;\n\n  @include hover() {\n    color: $color-hover;\n    background-color: $active-background;\n    border-color: $active-border;\n  }\n\n  &:focus,\n  &.focus {\n    box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);\n  }\n\n  &.disabled,\n  &:disabled {\n    color: $color;\n    background-color: transparent;\n  }\n\n  &:not(:disabled):not(.disabled):active,\n  &:not(:disabled):not(.disabled).active,\n  .show > &.dropdown-toggle {\n    color: color-yiq($active-background);\n    background-color: $active-background;\n    border-color: $active-border;\n\n    &:focus {\n      @if $enable-shadows and $btn-active-box-shadow != none {\n        @include box-shadow($btn-active-box-shadow, 0 0 0 $btn-focus-width rgba($color, .5));\n      } @else {\n        // Avoid using mixin so we can pass custom focus shadow properly\n        box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);\n      }\n    }\n  }\n}\n\n// Button sizes\n@mixin button-size($padding-y, $padding-x, $font-size, $line-height, $border-radius) {\n  padding: $padding-y $padding-x;\n  @include font-size($font-size);\n  line-height: $line-height;\n  // Manually declare to provide an override to the browser default\n  @include border-radius($border-radius, 0);\n}\n", "//\n// Component: Callout\n//\n\n// Base styles (regardless of theme)\n.callout {\n  @if $enable-rounded {\n    @include border-radius($border-radius);\n  }\n\n  @if $enable-shadows {\n    box-shadow: map-get($elevations, 1);\n  } @else {\n    border: 1px solid $gray-300;\n  }\n\n  background-color: $white;\n  border-left: 5px solid $gray-200;\n  margin-bottom: map-get($spacers, 3);\n  padding: 1rem;\n\n  a {\n    color: $gray-700;\n    text-decoration: underline;\n\n    &:hover {\n      color: $gray-200;\n    }\n  }\n\n  p:last-child {\n    margin-bottom: 0;\n  }\n\n  // Themes for different contexts\n  &.callout-danger {\n    border-left-color: darken(theme-color(\"danger\"), 10%);\n  }\n\n  &.callout-warning {\n    border-left-color: darken(theme-color(\"warning\"), 10%);\n  }\n\n  &.callout-info {\n    border-left-color: darken(theme-color(\"info\"), 10%);\n  }\n\n  &.callout-success {\n    border-left-color: darken(theme-color(\"success\"), 10%);\n  }\n}\n\n.dark-mode {\n  .callout {\n    background-color: lighten($dark, 5%);\n  }\n}\n", "//\n// Component: Alert\n//\n\n.alert {\n  .icon {\n    margin-right: 10px;\n  }\n\n  .close {\n    color: $black;\n    opacity: .2;\n\n    &:hover {\n      opacity: .5;\n    }\n  }\n\n  a {\n    color: $white;\n    text-decoration: underline;\n  }\n}\n\n//<PERSON><PERSON> Variants\n@each $color, $value in $theme-colors {\n  .alert-#{$color} {\n    color: color-yiq($value);\n    background-color: $value;\n    border-color: darken($value, 5%);\n  }\n\n  .alert-default-#{$color} {\n    @include alert-variant(theme-color-level($color, $alert-bg-level), theme-color-level($color, $alert-border-level), theme-color-level($color, $alert-color-level));\n  }\n}\n", "@mixin alert-variant($background, $border, $color) {\n  color: $color;\n  @include gradient-bg($background);\n  border-color: $border;\n\n  hr {\n    border-top-color: darken($border, 5%);\n  }\n\n  .alert-link {\n    color: darken($color, 10%);\n  }\n}\n", "//\n// Component: Table\n//\n\n.table {\n  &:not(.table-dark) {\n    color: inherit;\n  }\n\n  // fixed table head\n  &.table-head-fixed {\n    thead tr:nth-child(1) th {\n      background-color: $white;\n      border-bottom: 0;\n      box-shadow: inset 0 1px 0 $table-border-color, inset 0 -1px 0 $table-border-color;\n      position: sticky;\n      top: 0;\n      z-index: 10;\n    }\n\n    &.table-dark {\n      thead tr {\n        &:nth-child(1) th {\n          background-color: $table-dark-bg;\n          box-shadow: inset 0 1px 0 $table-dark-border-color, inset 0 -1px 0 $table-dark-border-color;\n        }\n      }\n    }\n  }\n\n  // no border\n  &.no-border {\n    &,\n    td,\n    th {\n      border: 0;\n    }\n  }\n\n  // .text-center in tables\n  &.text-center {\n    &,\n    td,\n    th {\n      text-align: center;\n    }\n  }\n\n  &.table-valign-middle {\n    thead > tr > th,\n    thead > tr > td,\n    tbody > tr > th,\n    tbody > tr > td {\n      vertical-align: middle;\n    }\n  }\n\n  .card-body.p-0 & {\n    thead > tr > th,\n    thead > tr > td,\n    tfoot > tr > th,\n    tfoot > tr > td,\n    tbody > tr > th,\n    tbody > tr > td {\n      &:first-of-type {\n        padding-left: map-get($spacers, 4);\n      }\n\n      &:last-of-type {\n        padding-right: map-get($spacers, 4);\n      }\n    }\n  }\n}\n\n// Expandable Table\n\n.table-hover tbody tr.expandable-body:hover {\n  background-color: inherit !important;\n}\n\n[data-widget=\"expandable-table\"] {\n  cursor: pointer;\n\n  i {\n    transition: transform $transition-speed linear;\n  }\n  &[aria-expanded=\"true\"] {\n    td > i {\n      // stylelint-disable selector-max-attribute\n      &[class*=\"right\"] {\n        transform: rotate(90deg);\n      }\n      &[class*=\"left\"] {\n        transform: rotate(-90deg);\n      }\n      // stylelint-enable selector-max-attribute\n    }\n  }\n}\n\n.expandable-body {\n  > td {\n    padding: 0 !important;\n    width: 100%;\n\n    > div,\n    > p {\n      padding: $table-cell-padding;\n    }\n  }\n\n  .table {\n    width: calc(100% - #{$table-cell-padding});\n    margin: 0 0 0 $table-cell-padding;\n\n    tr:first-child {\n      td,\n      th {\n        border-top: none;\n      }\n    }\n  }\n}\n\n.dark-mode {\n  .table-bordered {\n    &,\n    td,\n    th {\n      border-color: $gray-600;\n    }\n  }\n  .table-hover {\n    tbody tr:hover {\n      color: $gray-300;\n      background-color: lighten($dark, 2.5%);\n      border-color: $gray-600;\n    }\n  }\n  .table {\n    thead th {\n      border-bottom-color: $gray-600;\n    }\n    th,\n    td {\n      border-top-color: $gray-600;\n    }\n    &.table-head-fixed {\n      thead tr:nth-child(1) th {\n        background-color: lighten($dark, 5%);\n      }\n    }\n  }\n}\n", "/*!\n *   AdminLTE v3.1.0-rc\n *     Only Components\n *   Author: Colorlib\n *   Website: AdminLTE.io <https://adminlte.io>\n *   License: Open source - MIT <https://opensource.org/licenses/MIT>\n */\n.form-group.has-icon {\n  position: relative;\n}\n\n.form-group.has-icon .form-control {\n  padding-right: 35px;\n}\n\n.form-group.has-icon .form-icon {\n  background-color: transparent;\n  border: 0;\n  cursor: pointer;\n  font-size: 1rem;\n  padding: 0.375rem 0.75rem;\n  position: absolute;\n  right: 3px;\n  top: 0;\n}\n\n.btn-group-vertical .btn.btn-flat:first-of-type, .btn-group-vertical .btn.btn-flat:last-of-type {\n  border-radius: 0;\n}\n\n.form-control-feedback.fa, .form-control-feedback.fas, .form-control-feedback.far, .form-control-feedback.fab, .form-control-feedback.fal, .form-control-feedback.fad, .form-control-feedback.svg-inline--fa, .form-control-feedback.ion {\n  line-height: calc(2.25rem + 2px);\n}\n\n.input-lg + .form-control-feedback.fa, .input-lg + .form-control-feedback.fas, .input-lg + .form-control-feedback.far, .input-lg + .form-control-feedback.fab, .input-lg + .form-control-feedback.fal, .input-lg + .form-control-feedback.fad, .input-lg + .form-control-feedback.svg-inline--fa, .input-lg + .form-control-feedback.ion,\n.input-group-lg + .form-control-feedback.fa,\n.input-group-lg + .form-control-feedback.fas,\n.input-group-lg + .form-control-feedback.far,\n.input-group-lg + .form-control-feedback.fab,\n.input-group-lg + .form-control-feedback.fal,\n.input-group-lg + .form-control-feedback.fad,\n.input-group-lg + .form-control-feedback.svg-inline--fa,\n.input-group-lg + .form-control-feedback.ion {\n  line-height: calc(2.875rem + 2px);\n}\n\n.form-group-lg .form-control + .form-control-feedback.fa, .form-group-lg .form-control + .form-control-feedback.fas, .form-group-lg .form-control + .form-control-feedback.far, .form-group-lg .form-control + .form-control-feedback.fab, .form-group-lg .form-control + .form-control-feedback.fal, .form-group-lg .form-control + .form-control-feedback.fad, .form-group-lg .form-control + .form-control-feedback.svg-inline--fa, .form-group-lg .form-control + .form-control-feedback.ion {\n  line-height: calc(2.875rem + 2px);\n}\n\n.input-sm + .form-control-feedback.fa, .input-sm + .form-control-feedback.fas, .input-sm + .form-control-feedback.far, .input-sm + .form-control-feedback.fab, .input-sm + .form-control-feedback.fal, .input-sm + .form-control-feedback.fad, .input-sm + .form-control-feedback.svg-inline--fa, .input-sm + .form-control-feedback.ion,\n.input-group-sm + .form-control-feedback.fa,\n.input-group-sm + .form-control-feedback.fas,\n.input-group-sm + .form-control-feedback.far,\n.input-group-sm + .form-control-feedback.fab,\n.input-group-sm + .form-control-feedback.fal,\n.input-group-sm + .form-control-feedback.fad,\n.input-group-sm + .form-control-feedback.svg-inline--fa,\n.input-group-sm + .form-control-feedback.ion {\n  line-height: calc(1.8125rem + 2px);\n}\n\n.form-group-sm .form-control + .form-control-feedback.fa, .form-group-sm .form-control + .form-control-feedback.fas, .form-group-sm .form-control + .form-control-feedback.far, .form-group-sm .form-control + .form-control-feedback.fab, .form-group-sm .form-control + .form-control-feedback.fal, .form-group-sm .form-control + .form-control-feedback.fad, .form-group-sm .form-control + .form-control-feedback.svg-inline--fa, .form-group-sm .form-control + .form-control-feedback.ion {\n  line-height: calc(1.8125rem + 2px);\n}\n\nlabel:not(.form-check-label):not(.custom-file-label) {\n  font-weight: 700;\n}\n\n.warning-feedback {\n  font-size: 80%;\n  color: #ffc107;\n  display: none;\n  margin-top: 0.25rem;\n  width: 100%;\n}\n\n.warning-tooltip {\n  border-radius: 0.25rem;\n  font-size: 0.875rem;\n  background-color: rgba(255, 193, 7, 0.9);\n  color: #1f2d3d;\n  display: none;\n  line-height: 1.5;\n  margin-top: .1rem;\n  max-width: 100%;\n  padding: 0.25rem 0.5rem;\n  position: absolute;\n  top: 100%;\n  z-index: 5;\n}\n\n.form-control.is-warning {\n  border-color: #ffc107;\n}\n\n.form-control.is-warning:focus {\n  border-color: #ffc107;\n  box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.25);\n}\n\n.form-control.is-warning ~ .warning-feedback,\n.form-control.is-warning ~ .warning-tooltip {\n  display: block;\n}\n\ntextarea.form-control.is-warning {\n  padding-right: 2.25rem;\n  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);\n}\n\n.custom-select.is-warning {\n  border-color: #ffc107;\n}\n\n.custom-select.is-warning:focus {\n  border-color: #ffc107;\n  box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.25);\n}\n\n.custom-select.is-warning ~ .warning-feedback,\n.custom-select.is-warning ~ .warning-tooltip {\n  display: block;\n}\n\n.form-control-file.is-warning ~ .warning-feedback,\n.form-control-file.is-warning ~ .warning-tooltip {\n  display: block;\n}\n\n.form-check-input.is-warning ~ .form-check-label {\n  color: #ffc107;\n}\n\n.form-check-input.is-warning ~ .warning-feedback,\n.form-check-input.is-warning ~ .warning-tooltip {\n  display: block;\n}\n\n.custom-control-input.is-warning ~ .custom-control-label {\n  color: #ffc107;\n}\n\n.custom-control-input.is-warning ~ .custom-control-label::before {\n  border-color: #ffc107;\n}\n\n.custom-control-input.is-warning ~ .warning-feedback,\n.custom-control-input.is-warning ~ .warning-tooltip {\n  display: block;\n}\n\n.custom-control-input.is-warning:checked ~ .custom-control-label::before {\n  background-color: #ffce3a;\n  border-color: #ffce3a;\n}\n\n.custom-control-input.is-warning:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.25);\n}\n\n.custom-control-input.is-warning:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #ffc107;\n}\n\n.custom-file-input.is-warning ~ .custom-file-label {\n  border-color: #ffc107;\n}\n\n.custom-file-input.is-warning ~ .warning-feedback,\n.custom-file-input.is-warning ~ .warning-tooltip {\n  display: block;\n}\n\n.custom-file-input.is-warning:focus ~ .custom-file-label {\n  border-color: #ffc107;\n  box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.25);\n}\n\nbody.text-sm .input-group-text {\n  font-size: 0.875rem;\n}\n\n.form-control.form-control-border,\n.custom-select.form-control-border {\n  border-top: 0;\n  border-left: 0;\n  border-right: 0;\n  border-radius: 0;\n  box-shadow: inherit;\n}\n\n.form-control.form-control-border.border-width-2,\n.custom-select.form-control-border.border-width-2 {\n  border-bottom-width: 2px;\n}\n\n.form-control.form-control-border.border-width-3,\n.custom-select.form-control-border.border-width-3 {\n  border-bottom-width: 3px;\n}\n\n.custom-switch.custom-switch-off-primary .custom-control-input ~ .custom-control-label::before {\n  background-color: #007bff;\n  border-color: #004a99;\n}\n\n.custom-switch.custom-switch-off-primary .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-switch.custom-switch-off-primary .custom-control-input ~ .custom-control-label::after {\n  background-color: #003e80;\n}\n\n.custom-switch.custom-switch-on-primary .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #007bff;\n  border-color: #004a99;\n}\n\n.custom-switch.custom-switch-on-primary .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-switch.custom-switch-on-primary .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #99caff;\n}\n\n.custom-switch.custom-switch-off-secondary .custom-control-input ~ .custom-control-label::before {\n  background-color: #6c757d;\n  border-color: #3d4246;\n}\n\n.custom-switch.custom-switch-off-secondary .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-switch.custom-switch-off-secondary .custom-control-input ~ .custom-control-label::after {\n  background-color: #313539;\n}\n\n.custom-switch.custom-switch-on-secondary .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #6c757d;\n  border-color: #3d4246;\n}\n\n.custom-switch.custom-switch-on-secondary .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-switch.custom-switch-on-secondary .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #bcc1c6;\n}\n\n.custom-switch.custom-switch-off-success .custom-control-input ~ .custom-control-label::before {\n  background-color: #28a745;\n  border-color: #145523;\n}\n\n.custom-switch.custom-switch-off-success .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-switch.custom-switch-off-success .custom-control-input ~ .custom-control-label::after {\n  background-color: #0f401b;\n}\n\n.custom-switch.custom-switch-on-success .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #28a745;\n  border-color: #145523;\n}\n\n.custom-switch.custom-switch-on-success .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-switch.custom-switch-on-success .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #86e29b;\n}\n\n.custom-switch.custom-switch-off-info .custom-control-input ~ .custom-control-label::before {\n  background-color: #17a2b8;\n  border-color: #0c525d;\n}\n\n.custom-switch.custom-switch-off-info .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-switch.custom-switch-off-info .custom-control-input ~ .custom-control-label::after {\n  background-color: #093e47;\n}\n\n.custom-switch.custom-switch-on-info .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #17a2b8;\n  border-color: #0c525d;\n}\n\n.custom-switch.custom-switch-on-info .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-switch.custom-switch-on-info .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #7adeee;\n}\n\n.custom-switch.custom-switch-off-warning .custom-control-input ~ .custom-control-label::before {\n  background-color: #ffc107;\n  border-color: #a07800;\n}\n\n.custom-switch.custom-switch-off-warning .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-switch.custom-switch-off-warning .custom-control-input ~ .custom-control-label::after {\n  background-color: #876500;\n}\n\n.custom-switch.custom-switch-on-warning .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #ffc107;\n  border-color: #a07800;\n}\n\n.custom-switch.custom-switch-on-warning .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-switch.custom-switch-on-warning .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #ffe7a0;\n}\n\n.custom-switch.custom-switch-off-danger .custom-control-input ~ .custom-control-label::before {\n  background-color: #dc3545;\n  border-color: #921925;\n}\n\n.custom-switch.custom-switch-off-danger .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-switch.custom-switch-off-danger .custom-control-input ~ .custom-control-label::after {\n  background-color: #7c151f;\n}\n\n.custom-switch.custom-switch-on-danger .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #dc3545;\n  border-color: #921925;\n}\n\n.custom-switch.custom-switch-on-danger .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-switch.custom-switch-on-danger .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #f3b7bd;\n}\n\n.custom-switch.custom-switch-off-light .custom-control-input ~ .custom-control-label::before {\n  background-color: #f8f9fa;\n  border-color: #bdc6d0;\n}\n\n.custom-switch.custom-switch-off-light .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(248, 249, 250, 0.25);\n}\n\n.custom-switch.custom-switch-off-light .custom-control-input ~ .custom-control-label::after {\n  background-color: #aeb9c5;\n}\n\n.custom-switch.custom-switch-on-light .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #f8f9fa;\n  border-color: #bdc6d0;\n}\n\n.custom-switch.custom-switch-on-light .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(248, 249, 250, 0.25);\n}\n\n.custom-switch.custom-switch-on-light .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: white;\n}\n\n.custom-switch.custom-switch-off-dark .custom-control-input ~ .custom-control-label::before {\n  background-color: #343a40;\n  border-color: #060708;\n}\n\n.custom-switch.custom-switch-off-dark .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-switch.custom-switch-off-dark .custom-control-input ~ .custom-control-label::after {\n  background-color: black;\n}\n\n.custom-switch.custom-switch-on-dark .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #343a40;\n  border-color: #060708;\n}\n\n.custom-switch.custom-switch-on-dark .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-switch.custom-switch-on-dark .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #7a8793;\n}\n\n.custom-switch.custom-switch-off-lightblue .custom-control-input ~ .custom-control-label::before {\n  background-color: #3c8dbc;\n  border-color: #23536f;\n}\n\n.custom-switch.custom-switch-off-lightblue .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(60, 141, 188, 0.25);\n}\n\n.custom-switch.custom-switch-off-lightblue .custom-control-input ~ .custom-control-label::after {\n  background-color: #1d455b;\n}\n\n.custom-switch.custom-switch-on-lightblue .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #3c8dbc;\n  border-color: #23536f;\n}\n\n.custom-switch.custom-switch-on-lightblue .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(60, 141, 188, 0.25);\n}\n\n.custom-switch.custom-switch-on-lightblue .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #acd0e5;\n}\n\n.custom-switch.custom-switch-off-navy .custom-control-input ~ .custom-control-label::before {\n  background-color: #001f3f;\n  border-color: black;\n}\n\n.custom-switch.custom-switch-off-navy .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 31, 63, 0.25);\n}\n\n.custom-switch.custom-switch-off-navy .custom-control-input ~ .custom-control-label::after {\n  background-color: black;\n}\n\n.custom-switch.custom-switch-on-navy .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #001f3f;\n  border-color: black;\n}\n\n.custom-switch.custom-switch-on-navy .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 31, 63, 0.25);\n}\n\n.custom-switch.custom-switch-on-navy .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #006ad8;\n}\n\n.custom-switch.custom-switch-off-olive .custom-control-input ~ .custom-control-label::before {\n  background-color: #3d9970;\n  border-color: #20503b;\n}\n\n.custom-switch.custom-switch-off-olive .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(61, 153, 112, 0.25);\n}\n\n.custom-switch.custom-switch-off-olive .custom-control-input ~ .custom-control-label::after {\n  background-color: #193e2d;\n}\n\n.custom-switch.custom-switch-on-olive .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #3d9970;\n  border-color: #20503b;\n}\n\n.custom-switch.custom-switch-on-olive .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(61, 153, 112, 0.25);\n}\n\n.custom-switch.custom-switch-on-olive .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #99d6bb;\n}\n\n.custom-switch.custom-switch-off-lime .custom-control-input ~ .custom-control-label::before {\n  background-color: #01ff70;\n  border-color: #009a43;\n}\n\n.custom-switch.custom-switch-off-lime .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(1, 255, 112, 0.25);\n}\n\n.custom-switch.custom-switch-off-lime .custom-control-input ~ .custom-control-label::after {\n  background-color: #008138;\n}\n\n.custom-switch.custom-switch-on-lime .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #01ff70;\n  border-color: #009a43;\n}\n\n.custom-switch.custom-switch-on-lime .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(1, 255, 112, 0.25);\n}\n\n.custom-switch.custom-switch-on-lime .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #9affc6;\n}\n\n.custom-switch.custom-switch-off-fuchsia .custom-control-input ~ .custom-control-label::before {\n  background-color: #f012be;\n  border-color: #930974;\n}\n\n.custom-switch.custom-switch-off-fuchsia .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(240, 18, 190, 0.25);\n}\n\n.custom-switch.custom-switch-off-fuchsia .custom-control-input ~ .custom-control-label::after {\n  background-color: #7b0861;\n}\n\n.custom-switch.custom-switch-on-fuchsia .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #f012be;\n  border-color: #930974;\n}\n\n.custom-switch.custom-switch-on-fuchsia .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(240, 18, 190, 0.25);\n}\n\n.custom-switch.custom-switch-on-fuchsia .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #f9a2e5;\n}\n\n.custom-switch.custom-switch-off-maroon .custom-control-input ~ .custom-control-label::before {\n  background-color: #d81b60;\n  border-color: #7d1038;\n}\n\n.custom-switch.custom-switch-off-maroon .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(216, 27, 96, 0.25);\n}\n\n.custom-switch.custom-switch-off-maroon .custom-control-input ~ .custom-control-label::after {\n  background-color: #670d2e;\n}\n\n.custom-switch.custom-switch-on-maroon .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #d81b60;\n  border-color: #7d1038;\n}\n\n.custom-switch.custom-switch-on-maroon .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(216, 27, 96, 0.25);\n}\n\n.custom-switch.custom-switch-on-maroon .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #f29aba;\n}\n\n.custom-switch.custom-switch-off-blue .custom-control-input ~ .custom-control-label::before {\n  background-color: #007bff;\n  border-color: #004a99;\n}\n\n.custom-switch.custom-switch-off-blue .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-switch.custom-switch-off-blue .custom-control-input ~ .custom-control-label::after {\n  background-color: #003e80;\n}\n\n.custom-switch.custom-switch-on-blue .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #007bff;\n  border-color: #004a99;\n}\n\n.custom-switch.custom-switch-on-blue .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-switch.custom-switch-on-blue .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #99caff;\n}\n\n.custom-switch.custom-switch-off-indigo .custom-control-input ~ .custom-control-label::before {\n  background-color: #6610f2;\n  border-color: #3d0894;\n}\n\n.custom-switch.custom-switch-off-indigo .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(102, 16, 242, 0.25);\n}\n\n.custom-switch.custom-switch-off-indigo .custom-control-input ~ .custom-control-label::after {\n  background-color: #33077c;\n}\n\n.custom-switch.custom-switch-on-indigo .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #6610f2;\n  border-color: #3d0894;\n}\n\n.custom-switch.custom-switch-on-indigo .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(102, 16, 242, 0.25);\n}\n\n.custom-switch.custom-switch-on-indigo .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #c3a1fa;\n}\n\n.custom-switch.custom-switch-off-purple .custom-control-input ~ .custom-control-label::before {\n  background-color: #6f42c1;\n  border-color: #432776;\n}\n\n.custom-switch.custom-switch-off-purple .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(111, 66, 193, 0.25);\n}\n\n.custom-switch.custom-switch-off-purple .custom-control-input ~ .custom-control-label::after {\n  background-color: #382063;\n}\n\n.custom-switch.custom-switch-on-purple .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #6f42c1;\n  border-color: #432776;\n}\n\n.custom-switch.custom-switch-on-purple .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(111, 66, 193, 0.25);\n}\n\n.custom-switch.custom-switch-on-purple .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #c7b5e7;\n}\n\n.custom-switch.custom-switch-off-pink .custom-control-input ~ .custom-control-label::before {\n  background-color: #e83e8c;\n  border-color: #ac145a;\n}\n\n.custom-switch.custom-switch-off-pink .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(232, 62, 140, 0.25);\n}\n\n.custom-switch.custom-switch-off-pink .custom-control-input ~ .custom-control-label::after {\n  background-color: #95124e;\n}\n\n.custom-switch.custom-switch-on-pink .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #e83e8c;\n  border-color: #ac145a;\n}\n\n.custom-switch.custom-switch-on-pink .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(232, 62, 140, 0.25);\n}\n\n.custom-switch.custom-switch-on-pink .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #f8c7dd;\n}\n\n.custom-switch.custom-switch-off-red .custom-control-input ~ .custom-control-label::before {\n  background-color: #dc3545;\n  border-color: #921925;\n}\n\n.custom-switch.custom-switch-off-red .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-switch.custom-switch-off-red .custom-control-input ~ .custom-control-label::after {\n  background-color: #7c151f;\n}\n\n.custom-switch.custom-switch-on-red .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #dc3545;\n  border-color: #921925;\n}\n\n.custom-switch.custom-switch-on-red .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-switch.custom-switch-on-red .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #f3b7bd;\n}\n\n.custom-switch.custom-switch-off-orange .custom-control-input ~ .custom-control-label::before {\n  background-color: #fd7e14;\n  border-color: #aa4e01;\n}\n\n.custom-switch.custom-switch-off-orange .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(253, 126, 20, 0.25);\n}\n\n.custom-switch.custom-switch-off-orange .custom-control-input ~ .custom-control-label::after {\n  background-color: #904201;\n}\n\n.custom-switch.custom-switch-on-orange .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #fd7e14;\n  border-color: #aa4e01;\n}\n\n.custom-switch.custom-switch-on-orange .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(253, 126, 20, 0.25);\n}\n\n.custom-switch.custom-switch-on-orange .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #fed1ac;\n}\n\n.custom-switch.custom-switch-off-yellow .custom-control-input ~ .custom-control-label::before {\n  background-color: #ffc107;\n  border-color: #a07800;\n}\n\n.custom-switch.custom-switch-off-yellow .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-switch.custom-switch-off-yellow .custom-control-input ~ .custom-control-label::after {\n  background-color: #876500;\n}\n\n.custom-switch.custom-switch-on-yellow .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #ffc107;\n  border-color: #a07800;\n}\n\n.custom-switch.custom-switch-on-yellow .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-switch.custom-switch-on-yellow .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #ffe7a0;\n}\n\n.custom-switch.custom-switch-off-green .custom-control-input ~ .custom-control-label::before {\n  background-color: #28a745;\n  border-color: #145523;\n}\n\n.custom-switch.custom-switch-off-green .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-switch.custom-switch-off-green .custom-control-input ~ .custom-control-label::after {\n  background-color: #0f401b;\n}\n\n.custom-switch.custom-switch-on-green .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #28a745;\n  border-color: #145523;\n}\n\n.custom-switch.custom-switch-on-green .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-switch.custom-switch-on-green .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #86e29b;\n}\n\n.custom-switch.custom-switch-off-teal .custom-control-input ~ .custom-control-label::before {\n  background-color: #20c997;\n  border-color: #127155;\n}\n\n.custom-switch.custom-switch-off-teal .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(32, 201, 151, 0.25);\n}\n\n.custom-switch.custom-switch-off-teal .custom-control-input ~ .custom-control-label::after {\n  background-color: #0e5b44;\n}\n\n.custom-switch.custom-switch-on-teal .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #20c997;\n  border-color: #127155;\n}\n\n.custom-switch.custom-switch-on-teal .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(32, 201, 151, 0.25);\n}\n\n.custom-switch.custom-switch-on-teal .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #94eed3;\n}\n\n.custom-switch.custom-switch-off-cyan .custom-control-input ~ .custom-control-label::before {\n  background-color: #17a2b8;\n  border-color: #0c525d;\n}\n\n.custom-switch.custom-switch-off-cyan .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-switch.custom-switch-off-cyan .custom-control-input ~ .custom-control-label::after {\n  background-color: #093e47;\n}\n\n.custom-switch.custom-switch-on-cyan .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #17a2b8;\n  border-color: #0c525d;\n}\n\n.custom-switch.custom-switch-on-cyan .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-switch.custom-switch-on-cyan .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #7adeee;\n}\n\n.custom-switch.custom-switch-off-white .custom-control-input ~ .custom-control-label::before {\n  background-color: #fff;\n  border-color: #cccccc;\n}\n\n.custom-switch.custom-switch-off-white .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 255, 255, 0.25);\n}\n\n.custom-switch.custom-switch-off-white .custom-control-input ~ .custom-control-label::after {\n  background-color: #bfbfbf;\n}\n\n.custom-switch.custom-switch-on-white .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #fff;\n  border-color: #cccccc;\n}\n\n.custom-switch.custom-switch-on-white .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 255, 255, 0.25);\n}\n\n.custom-switch.custom-switch-on-white .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: white;\n}\n\n.custom-switch.custom-switch-off-gray .custom-control-input ~ .custom-control-label::before {\n  background-color: #6c757d;\n  border-color: #3d4246;\n}\n\n.custom-switch.custom-switch-off-gray .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-switch.custom-switch-off-gray .custom-control-input ~ .custom-control-label::after {\n  background-color: #313539;\n}\n\n.custom-switch.custom-switch-on-gray .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #6c757d;\n  border-color: #3d4246;\n}\n\n.custom-switch.custom-switch-on-gray .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-switch.custom-switch-on-gray .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #bcc1c6;\n}\n\n.custom-switch.custom-switch-off-gray-dark .custom-control-input ~ .custom-control-label::before {\n  background-color: #343a40;\n  border-color: #060708;\n}\n\n.custom-switch.custom-switch-off-gray-dark .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-switch.custom-switch-off-gray-dark .custom-control-input ~ .custom-control-label::after {\n  background-color: black;\n}\n\n.custom-switch.custom-switch-on-gray-dark .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #343a40;\n  border-color: #060708;\n}\n\n.custom-switch.custom-switch-on-gray-dark .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-switch.custom-switch-on-gray-dark .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #7a8793;\n}\n\n.custom-range.custom-range-primary:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-primary:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-range.custom-range-primary:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-range.custom-range-primary:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-range.custom-range-primary::-webkit-slider-thumb {\n  background-color: #007bff;\n}\n\n.custom-range.custom-range-primary::-webkit-slider-thumb:active {\n  background-color: #b3d7ff;\n}\n\n.custom-range.custom-range-primary::-moz-range-thumb {\n  background-color: #007bff;\n}\n\n.custom-range.custom-range-primary::-moz-range-thumb:active {\n  background-color: #b3d7ff;\n}\n\n.custom-range.custom-range-primary::-ms-thumb {\n  background-color: #007bff;\n}\n\n.custom-range.custom-range-primary::-ms-thumb:active {\n  background-color: #b3d7ff;\n}\n\n.custom-range.custom-range-secondary:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-secondary:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-range.custom-range-secondary:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-range.custom-range-secondary:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-range.custom-range-secondary::-webkit-slider-thumb {\n  background-color: #6c757d;\n}\n\n.custom-range.custom-range-secondary::-webkit-slider-thumb:active {\n  background-color: #caced1;\n}\n\n.custom-range.custom-range-secondary::-moz-range-thumb {\n  background-color: #6c757d;\n}\n\n.custom-range.custom-range-secondary::-moz-range-thumb:active {\n  background-color: #caced1;\n}\n\n.custom-range.custom-range-secondary::-ms-thumb {\n  background-color: #6c757d;\n}\n\n.custom-range.custom-range-secondary::-ms-thumb:active {\n  background-color: #caced1;\n}\n\n.custom-range.custom-range-success:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-success:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-range.custom-range-success:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-range.custom-range-success:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-range.custom-range-success::-webkit-slider-thumb {\n  background-color: #28a745;\n}\n\n.custom-range.custom-range-success::-webkit-slider-thumb:active {\n  background-color: #9be7ac;\n}\n\n.custom-range.custom-range-success::-moz-range-thumb {\n  background-color: #28a745;\n}\n\n.custom-range.custom-range-success::-moz-range-thumb:active {\n  background-color: #9be7ac;\n}\n\n.custom-range.custom-range-success::-ms-thumb {\n  background-color: #28a745;\n}\n\n.custom-range.custom-range-success::-ms-thumb:active {\n  background-color: #9be7ac;\n}\n\n.custom-range.custom-range-info:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-info:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-range.custom-range-info:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-range.custom-range-info:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-range.custom-range-info::-webkit-slider-thumb {\n  background-color: #17a2b8;\n}\n\n.custom-range.custom-range-info::-webkit-slider-thumb:active {\n  background-color: #90e4f1;\n}\n\n.custom-range.custom-range-info::-moz-range-thumb {\n  background-color: #17a2b8;\n}\n\n.custom-range.custom-range-info::-moz-range-thumb:active {\n  background-color: #90e4f1;\n}\n\n.custom-range.custom-range-info::-ms-thumb {\n  background-color: #17a2b8;\n}\n\n.custom-range.custom-range-info::-ms-thumb:active {\n  background-color: #90e4f1;\n}\n\n.custom-range.custom-range-warning:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-warning:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-range.custom-range-warning:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-range.custom-range-warning:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-range.custom-range-warning::-webkit-slider-thumb {\n  background-color: #ffc107;\n}\n\n.custom-range.custom-range-warning::-webkit-slider-thumb:active {\n  background-color: #ffeeba;\n}\n\n.custom-range.custom-range-warning::-moz-range-thumb {\n  background-color: #ffc107;\n}\n\n.custom-range.custom-range-warning::-moz-range-thumb:active {\n  background-color: #ffeeba;\n}\n\n.custom-range.custom-range-warning::-ms-thumb {\n  background-color: #ffc107;\n}\n\n.custom-range.custom-range-warning::-ms-thumb:active {\n  background-color: #ffeeba;\n}\n\n.custom-range.custom-range-danger:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-danger:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-range.custom-range-danger:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-range.custom-range-danger:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-range.custom-range-danger::-webkit-slider-thumb {\n  background-color: #dc3545;\n}\n\n.custom-range.custom-range-danger::-webkit-slider-thumb:active {\n  background-color: #f6cdd1;\n}\n\n.custom-range.custom-range-danger::-moz-range-thumb {\n  background-color: #dc3545;\n}\n\n.custom-range.custom-range-danger::-moz-range-thumb:active {\n  background-color: #f6cdd1;\n}\n\n.custom-range.custom-range-danger::-ms-thumb {\n  background-color: #dc3545;\n}\n\n.custom-range.custom-range-danger::-ms-thumb:active {\n  background-color: #f6cdd1;\n}\n\n.custom-range.custom-range-light:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-light:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(248, 249, 250, 0.25);\n}\n\n.custom-range.custom-range-light:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(248, 249, 250, 0.25);\n}\n\n.custom-range.custom-range-light:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(248, 249, 250, 0.25);\n}\n\n.custom-range.custom-range-light::-webkit-slider-thumb {\n  background-color: #f8f9fa;\n}\n\n.custom-range.custom-range-light::-webkit-slider-thumb:active {\n  background-color: white;\n}\n\n.custom-range.custom-range-light::-moz-range-thumb {\n  background-color: #f8f9fa;\n}\n\n.custom-range.custom-range-light::-moz-range-thumb:active {\n  background-color: white;\n}\n\n.custom-range.custom-range-light::-ms-thumb {\n  background-color: #f8f9fa;\n}\n\n.custom-range.custom-range-light::-ms-thumb:active {\n  background-color: white;\n}\n\n.custom-range.custom-range-dark:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-dark:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-range.custom-range-dark:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-range.custom-range-dark:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-range.custom-range-dark::-webkit-slider-thumb {\n  background-color: #343a40;\n}\n\n.custom-range.custom-range-dark::-webkit-slider-thumb:active {\n  background-color: #88939e;\n}\n\n.custom-range.custom-range-dark::-moz-range-thumb {\n  background-color: #343a40;\n}\n\n.custom-range.custom-range-dark::-moz-range-thumb:active {\n  background-color: #88939e;\n}\n\n.custom-range.custom-range-dark::-ms-thumb {\n  background-color: #343a40;\n}\n\n.custom-range.custom-range-dark::-ms-thumb:active {\n  background-color: #88939e;\n}\n\n.custom-range.custom-range-lightblue:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-lightblue:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(60, 141, 188, 0.25);\n}\n\n.custom-range.custom-range-lightblue:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(60, 141, 188, 0.25);\n}\n\n.custom-range.custom-range-lightblue:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(60, 141, 188, 0.25);\n}\n\n.custom-range.custom-range-lightblue::-webkit-slider-thumb {\n  background-color: #3c8dbc;\n}\n\n.custom-range.custom-range-lightblue::-webkit-slider-thumb:active {\n  background-color: #c0dbeb;\n}\n\n.custom-range.custom-range-lightblue::-moz-range-thumb {\n  background-color: #3c8dbc;\n}\n\n.custom-range.custom-range-lightblue::-moz-range-thumb:active {\n  background-color: #c0dbeb;\n}\n\n.custom-range.custom-range-lightblue::-ms-thumb {\n  background-color: #3c8dbc;\n}\n\n.custom-range.custom-range-lightblue::-ms-thumb:active {\n  background-color: #c0dbeb;\n}\n\n.custom-range.custom-range-navy:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-navy:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 31, 63, 0.25);\n}\n\n.custom-range.custom-range-navy:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 31, 63, 0.25);\n}\n\n.custom-range.custom-range-navy:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 31, 63, 0.25);\n}\n\n.custom-range.custom-range-navy::-webkit-slider-thumb {\n  background-color: #001f3f;\n}\n\n.custom-range.custom-range-navy::-webkit-slider-thumb:active {\n  background-color: #0077f2;\n}\n\n.custom-range.custom-range-navy::-moz-range-thumb {\n  background-color: #001f3f;\n}\n\n.custom-range.custom-range-navy::-moz-range-thumb:active {\n  background-color: #0077f2;\n}\n\n.custom-range.custom-range-navy::-ms-thumb {\n  background-color: #001f3f;\n}\n\n.custom-range.custom-range-navy::-ms-thumb:active {\n  background-color: #0077f2;\n}\n\n.custom-range.custom-range-olive:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-olive:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(61, 153, 112, 0.25);\n}\n\n.custom-range.custom-range-olive:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(61, 153, 112, 0.25);\n}\n\n.custom-range.custom-range-olive:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(61, 153, 112, 0.25);\n}\n\n.custom-range.custom-range-olive::-webkit-slider-thumb {\n  background-color: #3d9970;\n}\n\n.custom-range.custom-range-olive::-webkit-slider-thumb:active {\n  background-color: #abdec7;\n}\n\n.custom-range.custom-range-olive::-moz-range-thumb {\n  background-color: #3d9970;\n}\n\n.custom-range.custom-range-olive::-moz-range-thumb:active {\n  background-color: #abdec7;\n}\n\n.custom-range.custom-range-olive::-ms-thumb {\n  background-color: #3d9970;\n}\n\n.custom-range.custom-range-olive::-ms-thumb:active {\n  background-color: #abdec7;\n}\n\n.custom-range.custom-range-lime:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-lime:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(1, 255, 112, 0.25);\n}\n\n.custom-range.custom-range-lime:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(1, 255, 112, 0.25);\n}\n\n.custom-range.custom-range-lime:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(1, 255, 112, 0.25);\n}\n\n.custom-range.custom-range-lime::-webkit-slider-thumb {\n  background-color: #01ff70;\n}\n\n.custom-range.custom-range-lime::-webkit-slider-thumb:active {\n  background-color: #b4ffd4;\n}\n\n.custom-range.custom-range-lime::-moz-range-thumb {\n  background-color: #01ff70;\n}\n\n.custom-range.custom-range-lime::-moz-range-thumb:active {\n  background-color: #b4ffd4;\n}\n\n.custom-range.custom-range-lime::-ms-thumb {\n  background-color: #01ff70;\n}\n\n.custom-range.custom-range-lime::-ms-thumb:active {\n  background-color: #b4ffd4;\n}\n\n.custom-range.custom-range-fuchsia:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-fuchsia:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(240, 18, 190, 0.25);\n}\n\n.custom-range.custom-range-fuchsia:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(240, 18, 190, 0.25);\n}\n\n.custom-range.custom-range-fuchsia:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(240, 18, 190, 0.25);\n}\n\n.custom-range.custom-range-fuchsia::-webkit-slider-thumb {\n  background-color: #f012be;\n}\n\n.custom-range.custom-range-fuchsia::-webkit-slider-thumb:active {\n  background-color: #fbbaec;\n}\n\n.custom-range.custom-range-fuchsia::-moz-range-thumb {\n  background-color: #f012be;\n}\n\n.custom-range.custom-range-fuchsia::-moz-range-thumb:active {\n  background-color: #fbbaec;\n}\n\n.custom-range.custom-range-fuchsia::-ms-thumb {\n  background-color: #f012be;\n}\n\n.custom-range.custom-range-fuchsia::-ms-thumb:active {\n  background-color: #fbbaec;\n}\n\n.custom-range.custom-range-maroon:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-maroon:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(216, 27, 96, 0.25);\n}\n\n.custom-range.custom-range-maroon:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(216, 27, 96, 0.25);\n}\n\n.custom-range.custom-range-maroon:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(216, 27, 96, 0.25);\n}\n\n.custom-range.custom-range-maroon::-webkit-slider-thumb {\n  background-color: #d81b60;\n}\n\n.custom-range.custom-range-maroon::-webkit-slider-thumb:active {\n  background-color: #f5b0c9;\n}\n\n.custom-range.custom-range-maroon::-moz-range-thumb {\n  background-color: #d81b60;\n}\n\n.custom-range.custom-range-maroon::-moz-range-thumb:active {\n  background-color: #f5b0c9;\n}\n\n.custom-range.custom-range-maroon::-ms-thumb {\n  background-color: #d81b60;\n}\n\n.custom-range.custom-range-maroon::-ms-thumb:active {\n  background-color: #f5b0c9;\n}\n\n.custom-range.custom-range-blue:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-blue:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-range.custom-range-blue:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-range.custom-range-blue:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-range.custom-range-blue::-webkit-slider-thumb {\n  background-color: #007bff;\n}\n\n.custom-range.custom-range-blue::-webkit-slider-thumb:active {\n  background-color: #b3d7ff;\n}\n\n.custom-range.custom-range-blue::-moz-range-thumb {\n  background-color: #007bff;\n}\n\n.custom-range.custom-range-blue::-moz-range-thumb:active {\n  background-color: #b3d7ff;\n}\n\n.custom-range.custom-range-blue::-ms-thumb {\n  background-color: #007bff;\n}\n\n.custom-range.custom-range-blue::-ms-thumb:active {\n  background-color: #b3d7ff;\n}\n\n.custom-range.custom-range-indigo:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-indigo:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(102, 16, 242, 0.25);\n}\n\n.custom-range.custom-range-indigo:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(102, 16, 242, 0.25);\n}\n\n.custom-range.custom-range-indigo:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(102, 16, 242, 0.25);\n}\n\n.custom-range.custom-range-indigo::-webkit-slider-thumb {\n  background-color: #6610f2;\n}\n\n.custom-range.custom-range-indigo::-webkit-slider-thumb:active {\n  background-color: #d2b9fb;\n}\n\n.custom-range.custom-range-indigo::-moz-range-thumb {\n  background-color: #6610f2;\n}\n\n.custom-range.custom-range-indigo::-moz-range-thumb:active {\n  background-color: #d2b9fb;\n}\n\n.custom-range.custom-range-indigo::-ms-thumb {\n  background-color: #6610f2;\n}\n\n.custom-range.custom-range-indigo::-ms-thumb:active {\n  background-color: #d2b9fb;\n}\n\n.custom-range.custom-range-purple:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-purple:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(111, 66, 193, 0.25);\n}\n\n.custom-range.custom-range-purple:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(111, 66, 193, 0.25);\n}\n\n.custom-range.custom-range-purple:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(111, 66, 193, 0.25);\n}\n\n.custom-range.custom-range-purple::-webkit-slider-thumb {\n  background-color: #6f42c1;\n}\n\n.custom-range.custom-range-purple::-webkit-slider-thumb:active {\n  background-color: #d5c8ed;\n}\n\n.custom-range.custom-range-purple::-moz-range-thumb {\n  background-color: #6f42c1;\n}\n\n.custom-range.custom-range-purple::-moz-range-thumb:active {\n  background-color: #d5c8ed;\n}\n\n.custom-range.custom-range-purple::-ms-thumb {\n  background-color: #6f42c1;\n}\n\n.custom-range.custom-range-purple::-ms-thumb:active {\n  background-color: #d5c8ed;\n}\n\n.custom-range.custom-range-pink:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-pink:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(232, 62, 140, 0.25);\n}\n\n.custom-range.custom-range-pink:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(232, 62, 140, 0.25);\n}\n\n.custom-range.custom-range-pink:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(232, 62, 140, 0.25);\n}\n\n.custom-range.custom-range-pink::-webkit-slider-thumb {\n  background-color: #e83e8c;\n}\n\n.custom-range.custom-range-pink::-webkit-slider-thumb:active {\n  background-color: #fbddeb;\n}\n\n.custom-range.custom-range-pink::-moz-range-thumb {\n  background-color: #e83e8c;\n}\n\n.custom-range.custom-range-pink::-moz-range-thumb:active {\n  background-color: #fbddeb;\n}\n\n.custom-range.custom-range-pink::-ms-thumb {\n  background-color: #e83e8c;\n}\n\n.custom-range.custom-range-pink::-ms-thumb:active {\n  background-color: #fbddeb;\n}\n\n.custom-range.custom-range-red:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-red:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-range.custom-range-red:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-range.custom-range-red:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-range.custom-range-red::-webkit-slider-thumb {\n  background-color: #dc3545;\n}\n\n.custom-range.custom-range-red::-webkit-slider-thumb:active {\n  background-color: #f6cdd1;\n}\n\n.custom-range.custom-range-red::-moz-range-thumb {\n  background-color: #dc3545;\n}\n\n.custom-range.custom-range-red::-moz-range-thumb:active {\n  background-color: #f6cdd1;\n}\n\n.custom-range.custom-range-red::-ms-thumb {\n  background-color: #dc3545;\n}\n\n.custom-range.custom-range-red::-ms-thumb:active {\n  background-color: #f6cdd1;\n}\n\n.custom-range.custom-range-orange:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-orange:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(253, 126, 20, 0.25);\n}\n\n.custom-range.custom-range-orange:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(253, 126, 20, 0.25);\n}\n\n.custom-range.custom-range-orange:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(253, 126, 20, 0.25);\n}\n\n.custom-range.custom-range-orange::-webkit-slider-thumb {\n  background-color: #fd7e14;\n}\n\n.custom-range.custom-range-orange::-webkit-slider-thumb:active {\n  background-color: #ffdfc5;\n}\n\n.custom-range.custom-range-orange::-moz-range-thumb {\n  background-color: #fd7e14;\n}\n\n.custom-range.custom-range-orange::-moz-range-thumb:active {\n  background-color: #ffdfc5;\n}\n\n.custom-range.custom-range-orange::-ms-thumb {\n  background-color: #fd7e14;\n}\n\n.custom-range.custom-range-orange::-ms-thumb:active {\n  background-color: #ffdfc5;\n}\n\n.custom-range.custom-range-yellow:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-yellow:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-range.custom-range-yellow:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-range.custom-range-yellow:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-range.custom-range-yellow::-webkit-slider-thumb {\n  background-color: #ffc107;\n}\n\n.custom-range.custom-range-yellow::-webkit-slider-thumb:active {\n  background-color: #ffeeba;\n}\n\n.custom-range.custom-range-yellow::-moz-range-thumb {\n  background-color: #ffc107;\n}\n\n.custom-range.custom-range-yellow::-moz-range-thumb:active {\n  background-color: #ffeeba;\n}\n\n.custom-range.custom-range-yellow::-ms-thumb {\n  background-color: #ffc107;\n}\n\n.custom-range.custom-range-yellow::-ms-thumb:active {\n  background-color: #ffeeba;\n}\n\n.custom-range.custom-range-green:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-green:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-range.custom-range-green:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-range.custom-range-green:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-range.custom-range-green::-webkit-slider-thumb {\n  background-color: #28a745;\n}\n\n.custom-range.custom-range-green::-webkit-slider-thumb:active {\n  background-color: #9be7ac;\n}\n\n.custom-range.custom-range-green::-moz-range-thumb {\n  background-color: #28a745;\n}\n\n.custom-range.custom-range-green::-moz-range-thumb:active {\n  background-color: #9be7ac;\n}\n\n.custom-range.custom-range-green::-ms-thumb {\n  background-color: #28a745;\n}\n\n.custom-range.custom-range-green::-ms-thumb:active {\n  background-color: #9be7ac;\n}\n\n.custom-range.custom-range-teal:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-teal:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(32, 201, 151, 0.25);\n}\n\n.custom-range.custom-range-teal:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(32, 201, 151, 0.25);\n}\n\n.custom-range.custom-range-teal:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(32, 201, 151, 0.25);\n}\n\n.custom-range.custom-range-teal::-webkit-slider-thumb {\n  background-color: #20c997;\n}\n\n.custom-range.custom-range-teal::-webkit-slider-thumb:active {\n  background-color: #aaf1dc;\n}\n\n.custom-range.custom-range-teal::-moz-range-thumb {\n  background-color: #20c997;\n}\n\n.custom-range.custom-range-teal::-moz-range-thumb:active {\n  background-color: #aaf1dc;\n}\n\n.custom-range.custom-range-teal::-ms-thumb {\n  background-color: #20c997;\n}\n\n.custom-range.custom-range-teal::-ms-thumb:active {\n  background-color: #aaf1dc;\n}\n\n.custom-range.custom-range-cyan:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-cyan:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-range.custom-range-cyan:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-range.custom-range-cyan:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-range.custom-range-cyan::-webkit-slider-thumb {\n  background-color: #17a2b8;\n}\n\n.custom-range.custom-range-cyan::-webkit-slider-thumb:active {\n  background-color: #90e4f1;\n}\n\n.custom-range.custom-range-cyan::-moz-range-thumb {\n  background-color: #17a2b8;\n}\n\n.custom-range.custom-range-cyan::-moz-range-thumb:active {\n  background-color: #90e4f1;\n}\n\n.custom-range.custom-range-cyan::-ms-thumb {\n  background-color: #17a2b8;\n}\n\n.custom-range.custom-range-cyan::-ms-thumb:active {\n  background-color: #90e4f1;\n}\n\n.custom-range.custom-range-white:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-white:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 255, 255, 0.25);\n}\n\n.custom-range.custom-range-white:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 255, 255, 0.25);\n}\n\n.custom-range.custom-range-white:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 255, 255, 0.25);\n}\n\n.custom-range.custom-range-white::-webkit-slider-thumb {\n  background-color: #fff;\n}\n\n.custom-range.custom-range-white::-webkit-slider-thumb:active {\n  background-color: white;\n}\n\n.custom-range.custom-range-white::-moz-range-thumb {\n  background-color: #fff;\n}\n\n.custom-range.custom-range-white::-moz-range-thumb:active {\n  background-color: white;\n}\n\n.custom-range.custom-range-white::-ms-thumb {\n  background-color: #fff;\n}\n\n.custom-range.custom-range-white::-ms-thumb:active {\n  background-color: white;\n}\n\n.custom-range.custom-range-gray:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-gray:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-range.custom-range-gray:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-range.custom-range-gray:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-range.custom-range-gray::-webkit-slider-thumb {\n  background-color: #6c757d;\n}\n\n.custom-range.custom-range-gray::-webkit-slider-thumb:active {\n  background-color: #caced1;\n}\n\n.custom-range.custom-range-gray::-moz-range-thumb {\n  background-color: #6c757d;\n}\n\n.custom-range.custom-range-gray::-moz-range-thumb:active {\n  background-color: #caced1;\n}\n\n.custom-range.custom-range-gray::-ms-thumb {\n  background-color: #6c757d;\n}\n\n.custom-range.custom-range-gray::-ms-thumb:active {\n  background-color: #caced1;\n}\n\n.custom-range.custom-range-gray-dark:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-gray-dark:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-range.custom-range-gray-dark:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-range.custom-range-gray-dark:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-range.custom-range-gray-dark::-webkit-slider-thumb {\n  background-color: #343a40;\n}\n\n.custom-range.custom-range-gray-dark::-webkit-slider-thumb:active {\n  background-color: #88939e;\n}\n\n.custom-range.custom-range-gray-dark::-moz-range-thumb {\n  background-color: #343a40;\n}\n\n.custom-range.custom-range-gray-dark::-moz-range-thumb:active {\n  background-color: #88939e;\n}\n\n.custom-range.custom-range-gray-dark::-ms-thumb {\n  background-color: #343a40;\n}\n\n.custom-range.custom-range-gray-dark::-ms-thumb:active {\n  background-color: #88939e;\n}\n\n.custom-control-input-primary:checked ~ .custom-control-label::before {\n  border-color: #007bff;\n  background-color: #007bff;\n}\n\n.custom-control-input-primary.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23007bff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-primary.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23007bff'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-primary:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.custom-control-input-primary:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #80bdff;\n}\n\n.custom-control-input-primary:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #b3d7ff;\n  border-color: #b3d7ff;\n}\n\n.custom-control-input-secondary:checked ~ .custom-control-label::before {\n  border-color: #6c757d;\n  background-color: #6c757d;\n}\n\n.custom-control-input-secondary.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%236c757d' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-secondary.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%236c757d'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-secondary:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(108, 117, 125, 0.25);\n}\n\n.custom-control-input-secondary:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #afb5ba;\n}\n\n.custom-control-input-secondary:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #caced1;\n  border-color: #caced1;\n}\n\n.custom-control-input-success:checked ~ .custom-control-label::before {\n  border-color: #28a745;\n  background-color: #28a745;\n}\n\n.custom-control-input-success.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%2328a745' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-success.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%2328a745'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-success:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(40, 167, 69, 0.25);\n}\n\n.custom-control-input-success:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #71dd8a;\n}\n\n.custom-control-input-success:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #9be7ac;\n  border-color: #9be7ac;\n}\n\n.custom-control-input-info:checked ~ .custom-control-label::before {\n  border-color: #17a2b8;\n  background-color: #17a2b8;\n}\n\n.custom-control-input-info.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%2317a2b8' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-info.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%2317a2b8'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-info:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(23, 162, 184, 0.25);\n}\n\n.custom-control-input-info:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #63d9ec;\n}\n\n.custom-control-input-info:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #90e4f1;\n  border-color: #90e4f1;\n}\n\n.custom-control-input-warning:checked ~ .custom-control-label::before {\n  border-color: #ffc107;\n  background-color: #ffc107;\n}\n\n.custom-control-input-warning.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23ffc107' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-warning.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23ffc107'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-warning:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(255, 193, 7, 0.25);\n}\n\n.custom-control-input-warning:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #ffe187;\n}\n\n.custom-control-input-warning:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #ffeeba;\n  border-color: #ffeeba;\n}\n\n.custom-control-input-danger:checked ~ .custom-control-label::before {\n  border-color: #dc3545;\n  background-color: #dc3545;\n}\n\n.custom-control-input-danger.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23dc3545' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-danger.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23dc3545'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-danger:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n\n.custom-control-input-danger:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #efa2a9;\n}\n\n.custom-control-input-danger:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #f6cdd1;\n  border-color: #f6cdd1;\n}\n\n.custom-control-input-light:checked ~ .custom-control-label::before {\n  border-color: #f8f9fa;\n  background-color: #f8f9fa;\n}\n\n.custom-control-input-light.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23f8f9fa' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-light.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23f8f9fa'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-light:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(248, 249, 250, 0.25);\n}\n\n.custom-control-input-light:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: white;\n}\n\n.custom-control-input-light:not(:disabled):active ~ .custom-control-label::before {\n  background-color: white;\n  border-color: white;\n}\n\n.custom-control-input-dark:checked ~ .custom-control-label::before {\n  border-color: #343a40;\n  background-color: #343a40;\n}\n\n.custom-control-input-dark.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23343a40' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-dark.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23343a40'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-dark:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(52, 58, 64, 0.25);\n}\n\n.custom-control-input-dark:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #6d7a86;\n}\n\n.custom-control-input-dark:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #88939e;\n  border-color: #88939e;\n}\n\n.custom-control-input-lightblue:checked ~ .custom-control-label::before {\n  border-color: #3c8dbc;\n  background-color: #3c8dbc;\n}\n\n.custom-control-input-lightblue.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%233c8dbc' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-lightblue.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%233c8dbc'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-lightblue:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(60, 141, 188, 0.25);\n}\n\n.custom-control-input-lightblue:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #99c5de;\n}\n\n.custom-control-input-lightblue:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #c0dbeb;\n  border-color: #c0dbeb;\n}\n\n.custom-control-input-navy:checked ~ .custom-control-label::before {\n  border-color: #001f3f;\n  background-color: #001f3f;\n}\n\n.custom-control-input-navy.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23001f3f' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-navy.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23001f3f'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-navy:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(0, 31, 63, 0.25);\n}\n\n.custom-control-input-navy:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #005ebf;\n}\n\n.custom-control-input-navy:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #0077f2;\n  border-color: #0077f2;\n}\n\n.custom-control-input-olive:checked ~ .custom-control-label::before {\n  border-color: #3d9970;\n  background-color: #3d9970;\n}\n\n.custom-control-input-olive.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%233d9970' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-olive.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%233d9970'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-olive:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(61, 153, 112, 0.25);\n}\n\n.custom-control-input-olive:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #87cfaf;\n}\n\n.custom-control-input-olive:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #abdec7;\n  border-color: #abdec7;\n}\n\n.custom-control-input-lime:checked ~ .custom-control-label::before {\n  border-color: #01ff70;\n  background-color: #01ff70;\n}\n\n.custom-control-input-lime.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%2301ff70' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-lime.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%2301ff70'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-lime:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(1, 255, 112, 0.25);\n}\n\n.custom-control-input-lime:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #81ffb8;\n}\n\n.custom-control-input-lime:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #b4ffd4;\n  border-color: #b4ffd4;\n}\n\n.custom-control-input-fuchsia:checked ~ .custom-control-label::before {\n  border-color: #f012be;\n  background-color: #f012be;\n}\n\n.custom-control-input-fuchsia.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23f012be' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-fuchsia.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23f012be'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-fuchsia:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(240, 18, 190, 0.25);\n}\n\n.custom-control-input-fuchsia:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #f88adf;\n}\n\n.custom-control-input-fuchsia:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #fbbaec;\n  border-color: #fbbaec;\n}\n\n.custom-control-input-maroon:checked ~ .custom-control-label::before {\n  border-color: #d81b60;\n  background-color: #d81b60;\n}\n\n.custom-control-input-maroon.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23d81b60' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-maroon.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23d81b60'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-maroon:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(216, 27, 96, 0.25);\n}\n\n.custom-control-input-maroon:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #f083ab;\n}\n\n.custom-control-input-maroon:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #f5b0c9;\n  border-color: #f5b0c9;\n}\n\n.custom-control-input-blue:checked ~ .custom-control-label::before {\n  border-color: #007bff;\n  background-color: #007bff;\n}\n\n.custom-control-input-blue.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23007bff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-blue.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23007bff'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-blue:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.custom-control-input-blue:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #80bdff;\n}\n\n.custom-control-input-blue:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #b3d7ff;\n  border-color: #b3d7ff;\n}\n\n.custom-control-input-indigo:checked ~ .custom-control-label::before {\n  border-color: #6610f2;\n  background-color: #6610f2;\n}\n\n.custom-control-input-indigo.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%236610f2' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-indigo.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%236610f2'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-indigo:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(102, 16, 242, 0.25);\n}\n\n.custom-control-input-indigo:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #b389f9;\n}\n\n.custom-control-input-indigo:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #d2b9fb;\n  border-color: #d2b9fb;\n}\n\n.custom-control-input-purple:checked ~ .custom-control-label::before {\n  border-color: #6f42c1;\n  background-color: #6f42c1;\n}\n\n.custom-control-input-purple.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%236f42c1' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-purple.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%236f42c1'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-purple:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(111, 66, 193, 0.25);\n}\n\n.custom-control-input-purple:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #b8a2e0;\n}\n\n.custom-control-input-purple:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #d5c8ed;\n  border-color: #d5c8ed;\n}\n\n.custom-control-input-pink:checked ~ .custom-control-label::before {\n  border-color: #e83e8c;\n  background-color: #e83e8c;\n}\n\n.custom-control-input-pink.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23e83e8c' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-pink.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23e83e8c'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-pink:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(232, 62, 140, 0.25);\n}\n\n.custom-control-input-pink:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #f6b0d0;\n}\n\n.custom-control-input-pink:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #fbddeb;\n  border-color: #fbddeb;\n}\n\n.custom-control-input-red:checked ~ .custom-control-label::before {\n  border-color: #dc3545;\n  background-color: #dc3545;\n}\n\n.custom-control-input-red.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23dc3545' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-red.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23dc3545'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-red:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n\n.custom-control-input-red:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #efa2a9;\n}\n\n.custom-control-input-red:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #f6cdd1;\n  border-color: #f6cdd1;\n}\n\n.custom-control-input-orange:checked ~ .custom-control-label::before {\n  border-color: #fd7e14;\n  background-color: #fd7e14;\n}\n\n.custom-control-input-orange.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23fd7e14' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-orange.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23fd7e14'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-orange:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(253, 126, 20, 0.25);\n}\n\n.custom-control-input-orange:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #fec392;\n}\n\n.custom-control-input-orange:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #ffdfc5;\n  border-color: #ffdfc5;\n}\n\n.custom-control-input-yellow:checked ~ .custom-control-label::before {\n  border-color: #ffc107;\n  background-color: #ffc107;\n}\n\n.custom-control-input-yellow.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23ffc107' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-yellow.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23ffc107'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-yellow:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(255, 193, 7, 0.25);\n}\n\n.custom-control-input-yellow:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #ffe187;\n}\n\n.custom-control-input-yellow:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #ffeeba;\n  border-color: #ffeeba;\n}\n\n.custom-control-input-green:checked ~ .custom-control-label::before {\n  border-color: #28a745;\n  background-color: #28a745;\n}\n\n.custom-control-input-green.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%2328a745' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-green.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%2328a745'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-green:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(40, 167, 69, 0.25);\n}\n\n.custom-control-input-green:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #71dd8a;\n}\n\n.custom-control-input-green:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #9be7ac;\n  border-color: #9be7ac;\n}\n\n.custom-control-input-teal:checked ~ .custom-control-label::before {\n  border-color: #20c997;\n  background-color: #20c997;\n}\n\n.custom-control-input-teal.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%2320c997' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-teal.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%2320c997'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-teal:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(32, 201, 151, 0.25);\n}\n\n.custom-control-input-teal:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #7eeaca;\n}\n\n.custom-control-input-teal:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #aaf1dc;\n  border-color: #aaf1dc;\n}\n\n.custom-control-input-cyan:checked ~ .custom-control-label::before {\n  border-color: #17a2b8;\n  background-color: #17a2b8;\n}\n\n.custom-control-input-cyan.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%2317a2b8' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-cyan.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%2317a2b8'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-cyan:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(23, 162, 184, 0.25);\n}\n\n.custom-control-input-cyan:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #63d9ec;\n}\n\n.custom-control-input-cyan:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #90e4f1;\n  border-color: #90e4f1;\n}\n\n.custom-control-input-white:checked ~ .custom-control-label::before {\n  border-color: #fff;\n  background-color: #fff;\n}\n\n.custom-control-input-white.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-white.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23fff'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-white:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(255, 255, 255, 0.25);\n}\n\n.custom-control-input-white:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: white;\n}\n\n.custom-control-input-white:not(:disabled):active ~ .custom-control-label::before {\n  background-color: white;\n  border-color: white;\n}\n\n.custom-control-input-gray:checked ~ .custom-control-label::before {\n  border-color: #6c757d;\n  background-color: #6c757d;\n}\n\n.custom-control-input-gray.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%236c757d' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-gray.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%236c757d'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-gray:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(108, 117, 125, 0.25);\n}\n\n.custom-control-input-gray:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #afb5ba;\n}\n\n.custom-control-input-gray:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #caced1;\n  border-color: #caced1;\n}\n\n.custom-control-input-gray-dark:checked ~ .custom-control-label::before {\n  border-color: #343a40;\n  background-color: #343a40;\n}\n\n.custom-control-input-gray-dark.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23343a40' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-gray-dark.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23343a40'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-gray-dark:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(52, 58, 64, 0.25);\n}\n\n.custom-control-input-gray-dark:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #6d7a86;\n}\n\n.custom-control-input-gray-dark:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #88939e;\n  border-color: #88939e;\n}\n\n.custom-control-input-outline ~ .custom-control-label::before {\n  background-color: transparent !important;\n  box-shadow: none;\n}\n\n.custom-control-input-outline:checked ~ .custom-control-label::before {\n  background-color: transparent;\n}\n\n.dark-mode .form-control,\n.dark-mode .custom-select,\n.dark-mode .custom-file-label,\n.dark-mode .custom-file-label::after,\n.dark-mode .custom-control-label::before,\n.dark-mode .input-group-text {\n  background-color: #343a40;\n  color: #fff;\n}\n\n.dark-mode .form-control:not(.form-control-navbar):not(.is-invalid):not(:focus) {\n  border-color: #6c757d;\n}\n\n.dark-mode select {\n  background-color: #343a40;\n  color: #fff;\n  border-color: #6c757d;\n}\n\n.dark-mode .input-group-text {\n  border-color: #6c757d;\n}\n\n.dark-mode .custom-control-input:disabled ~ .custom-control-label::before,\n.dark-mode .custom-control-input[disabled] ~ .custom-control-label::before {\n  background-color: #3f474e;\n  border-color: #6c757d;\n  color: #fff;\n}\n\n.dark-mode .custom-range::-webkit-slider-runnable-track {\n  background-color: #454d55;\n}\n\n.dark-mode .custom-range::-moz-range-track {\n  background-color: #454d55;\n}\n\n.dark-mode .custom-range::-ms-track {\n  background-color: #454d55;\n}\n\n.dark-mode .navbar-dark .btn-navbar,\n.dark-mode .navbar-dark .form-control-navbar {\n  background-color: #343a40;\n  border: 1px solid #6c757d;\n}\n\n.dark-mode .navbar-dark .btn-navbar:hover {\n  background-color: #454d55;\n}\n\n.dark-mode .navbar-dark .btn-navbar:focus {\n  background-color: #4b545c;\n}\n\n.dark-mode .navbar-dark .form-control-navbar + .input-group-prepend > .btn-navbar,\n.dark-mode .navbar-dark .form-control-navbar + .input-group-append > .btn-navbar {\n  background-color: #3f474e;\n  color: #fff;\n  border: 1px solid #6c757d;\n  border-left: none;\n}\n\n.progress {\n  box-shadow: none;\n  border-radius: 1px;\n}\n\n.progress.vertical {\n  display: inline-block;\n  height: 200px;\n  margin-right: 10px;\n  position: relative;\n  width: 30px;\n}\n\n.progress.vertical > .progress-bar {\n  bottom: 0;\n  position: absolute;\n  width: 100%;\n}\n\n.progress.vertical.sm, .progress.vertical.progress-sm {\n  width: 20px;\n}\n\n.progress.vertical.xs, .progress.vertical.progress-xs {\n  width: 10px;\n}\n\n.progress.vertical.xxs, .progress.vertical.progress-xxs {\n  width: 3px;\n}\n\n.progress-group {\n  margin-bottom: 0.5rem;\n}\n\n.progress-sm {\n  height: 10px;\n}\n\n.progress-xs {\n  height: 7px;\n}\n\n.progress-xxs {\n  height: 3px;\n}\n\n.table tr > td .progress {\n  margin: 0;\n}\n\n.dark-mode .progress {\n  background: #454d55;\n}\n\n.card-primary:not(.card-outline) > .card-header {\n  background-color: #007bff;\n}\n\n.card-primary:not(.card-outline) > .card-header,\n.card-primary:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-primary:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-primary.card-outline {\n  border-top: 3px solid #007bff;\n}\n\n.card-primary.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-primary.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #007bff;\n}\n\n.bg-primary .btn-tool,\n.bg-gradient-primary .btn-tool,\n.card-primary:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-primary .btn-tool:hover,\n.bg-gradient-primary .btn-tool:hover,\n.card-primary:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-primary .bootstrap-datetimepicker-widget .table td,\n.card.bg-primary .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-primary .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-primary .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-primary .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-primary .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-primary .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #0067d6;\n  color: #fff;\n}\n\n.card.bg-primary .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-primary .bootstrap-datetimepicker-widget table td.active,\n.card.bg-primary .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #3395ff;\n  color: #fff;\n}\n\n.card-secondary:not(.card-outline) > .card-header {\n  background-color: #6c757d;\n}\n\n.card-secondary:not(.card-outline) > .card-header,\n.card-secondary:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-secondary:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-secondary.card-outline {\n  border-top: 3px solid #6c757d;\n}\n\n.card-secondary.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-secondary.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #6c757d;\n}\n\n.bg-secondary .btn-tool,\n.bg-gradient-secondary .btn-tool,\n.card-secondary:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-secondary .btn-tool:hover,\n.bg-gradient-secondary .btn-tool:hover,\n.card-secondary:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-secondary .bootstrap-datetimepicker-widget .table td,\n.card.bg-secondary .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-secondary .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-secondary .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-secondary .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-secondary .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-secondary .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #596167;\n  color: #fff;\n}\n\n.card.bg-secondary .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-secondary .bootstrap-datetimepicker-widget table td.active,\n.card.bg-secondary .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #868e96;\n  color: #fff;\n}\n\n.card-success:not(.card-outline) > .card-header {\n  background-color: #28a745;\n}\n\n.card-success:not(.card-outline) > .card-header,\n.card-success:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-success:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-success.card-outline {\n  border-top: 3px solid #28a745;\n}\n\n.card-success.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-success.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #28a745;\n}\n\n.bg-success .btn-tool,\n.bg-gradient-success .btn-tool,\n.card-success:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-success .btn-tool:hover,\n.bg-gradient-success .btn-tool:hover,\n.card-success:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-success .bootstrap-datetimepicker-widget .table td,\n.card.bg-success .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-success .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-success .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-success .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-success .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-success .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #208637;\n  color: #fff;\n}\n\n.card.bg-success .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-success .bootstrap-datetimepicker-widget table td.active,\n.card.bg-success .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #34ce57;\n  color: #fff;\n}\n\n.card-info:not(.card-outline) > .card-header {\n  background-color: #17a2b8;\n}\n\n.card-info:not(.card-outline) > .card-header,\n.card-info:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-info:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-info.card-outline {\n  border-top: 3px solid #17a2b8;\n}\n\n.card-info.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-info.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #17a2b8;\n}\n\n.bg-info .btn-tool,\n.bg-gradient-info .btn-tool,\n.card-info:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-info .btn-tool:hover,\n.bg-gradient-info .btn-tool:hover,\n.card-info:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-info .bootstrap-datetimepicker-widget .table td,\n.card.bg-info .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-info .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-info .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-info .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-info .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-info .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #128294;\n  color: #fff;\n}\n\n.card.bg-info .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-info .bootstrap-datetimepicker-widget table td.active,\n.card.bg-info .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #1fc8e3;\n  color: #fff;\n}\n\n.card-warning:not(.card-outline) > .card-header {\n  background-color: #ffc107;\n}\n\n.card-warning:not(.card-outline) > .card-header,\n.card-warning:not(.card-outline) > .card-header a {\n  color: #1f2d3d;\n}\n\n.card-warning:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-warning.card-outline {\n  border-top: 3px solid #ffc107;\n}\n\n.card-warning.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-warning.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #ffc107;\n}\n\n.bg-warning .btn-tool,\n.bg-gradient-warning .btn-tool,\n.card-warning:not(.card-outline) .btn-tool {\n  color: rgba(31, 45, 61, 0.8);\n}\n\n.bg-warning .btn-tool:hover,\n.bg-gradient-warning .btn-tool:hover,\n.card-warning:not(.card-outline) .btn-tool:hover {\n  color: #1f2d3d;\n}\n\n.card.bg-warning .bootstrap-datetimepicker-widget .table td,\n.card.bg-warning .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-warning .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-warning .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-warning .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-warning .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-warning .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #dda600;\n  color: #1f2d3d;\n}\n\n.card.bg-warning .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #1f2d3d;\n}\n\n.card.bg-warning .bootstrap-datetimepicker-widget table td.active,\n.card.bg-warning .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #ffce3a;\n  color: #1f2d3d;\n}\n\n.card-danger:not(.card-outline) > .card-header {\n  background-color: #dc3545;\n}\n\n.card-danger:not(.card-outline) > .card-header,\n.card-danger:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-danger:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-danger.card-outline {\n  border-top: 3px solid #dc3545;\n}\n\n.card-danger.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-danger.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #dc3545;\n}\n\n.bg-danger .btn-tool,\n.bg-gradient-danger .btn-tool,\n.card-danger:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-danger .btn-tool:hover,\n.bg-gradient-danger .btn-tool:hover,\n.card-danger:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-danger .bootstrap-datetimepicker-widget .table td,\n.card.bg-danger .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-danger .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-danger .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-danger .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-danger .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-danger .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #c62232;\n  color: #fff;\n}\n\n.card.bg-danger .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-danger .bootstrap-datetimepicker-widget table td.active,\n.card.bg-danger .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #e4606d;\n  color: #fff;\n}\n\n.card-light:not(.card-outline) > .card-header {\n  background-color: #f8f9fa;\n}\n\n.card-light:not(.card-outline) > .card-header,\n.card-light:not(.card-outline) > .card-header a {\n  color: #1f2d3d;\n}\n\n.card-light:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-light.card-outline {\n  border-top: 3px solid #f8f9fa;\n}\n\n.card-light.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-light.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #f8f9fa;\n}\n\n.bg-light .btn-tool,\n.bg-gradient-light .btn-tool,\n.card-light:not(.card-outline) .btn-tool {\n  color: rgba(31, 45, 61, 0.8);\n}\n\n.bg-light .btn-tool:hover,\n.bg-gradient-light .btn-tool:hover,\n.card-light:not(.card-outline) .btn-tool:hover {\n  color: #1f2d3d;\n}\n\n.card.bg-light .bootstrap-datetimepicker-widget .table td,\n.card.bg-light .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-light .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-light .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-light .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-light .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-light .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #e0e5e9;\n  color: #1f2d3d;\n}\n\n.card.bg-light .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #1f2d3d;\n}\n\n.card.bg-light .bootstrap-datetimepicker-widget table td.active,\n.card.bg-light .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: white;\n  color: #1f2d3d;\n}\n\n.card-dark:not(.card-outline) > .card-header {\n  background-color: #343a40;\n}\n\n.card-dark:not(.card-outline) > .card-header,\n.card-dark:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-dark:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-dark.card-outline {\n  border-top: 3px solid #343a40;\n}\n\n.card-dark.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-dark.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #343a40;\n}\n\n.bg-dark .btn-tool,\n.bg-gradient-dark .btn-tool,\n.card-dark:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-dark .btn-tool:hover,\n.bg-gradient-dark .btn-tool:hover,\n.card-dark:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-dark .bootstrap-datetimepicker-widget .table td,\n.card.bg-dark .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-dark .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-dark .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-dark .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-dark .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-dark .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #222629;\n  color: #fff;\n}\n\n.card.bg-dark .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-dark .bootstrap-datetimepicker-widget table td.active,\n.card.bg-dark .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #4b545c;\n  color: #fff;\n}\n\n.card-lightblue:not(.card-outline) > .card-header {\n  background-color: #3c8dbc;\n}\n\n.card-lightblue:not(.card-outline) > .card-header,\n.card-lightblue:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-lightblue:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-lightblue.card-outline {\n  border-top: 3px solid #3c8dbc;\n}\n\n.card-lightblue.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-lightblue.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #3c8dbc;\n}\n\n.bg-lightblue .btn-tool,\n.bg-gradient-lightblue .btn-tool,\n.card-lightblue:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-lightblue .btn-tool:hover,\n.bg-gradient-lightblue .btn-tool:hover,\n.card-lightblue:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-lightblue .bootstrap-datetimepicker-widget .table td,\n.card.bg-lightblue .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-lightblue .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-lightblue .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-lightblue .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-lightblue .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-lightblue .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #32769d;\n  color: #fff;\n}\n\n.card.bg-lightblue .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-lightblue .bootstrap-datetimepicker-widget table td.active,\n.card.bg-lightblue .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #5fa4cc;\n  color: #fff;\n}\n\n.card-navy:not(.card-outline) > .card-header {\n  background-color: #001f3f;\n}\n\n.card-navy:not(.card-outline) > .card-header,\n.card-navy:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-navy:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-navy.card-outline {\n  border-top: 3px solid #001f3f;\n}\n\n.card-navy.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-navy.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #001f3f;\n}\n\n.bg-navy .btn-tool,\n.bg-gradient-navy .btn-tool,\n.card-navy:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-navy .btn-tool:hover,\n.bg-gradient-navy .btn-tool:hover,\n.card-navy:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-navy .bootstrap-datetimepicker-widget .table td,\n.card.bg-navy .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-navy .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-navy .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-navy .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-navy .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-navy .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #000b16;\n  color: #fff;\n}\n\n.card.bg-navy .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-navy .bootstrap-datetimepicker-widget table td.active,\n.card.bg-navy .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #003872;\n  color: #fff;\n}\n\n.card-olive:not(.card-outline) > .card-header {\n  background-color: #3d9970;\n}\n\n.card-olive:not(.card-outline) > .card-header,\n.card-olive:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-olive:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-olive.card-outline {\n  border-top: 3px solid #3d9970;\n}\n\n.card-olive.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-olive.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #3d9970;\n}\n\n.bg-olive .btn-tool,\n.bg-gradient-olive .btn-tool,\n.card-olive:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-olive .btn-tool:hover,\n.bg-gradient-olive .btn-tool:hover,\n.card-olive:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-olive .bootstrap-datetimepicker-widget .table td,\n.card.bg-olive .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-olive .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-olive .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-olive .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-olive .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-olive .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #317c5b;\n  color: #fff;\n}\n\n.card.bg-olive .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-olive .bootstrap-datetimepicker-widget table td.active,\n.card.bg-olive .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #50b98a;\n  color: #fff;\n}\n\n.card-lime:not(.card-outline) > .card-header {\n  background-color: #01ff70;\n}\n\n.card-lime:not(.card-outline) > .card-header,\n.card-lime:not(.card-outline) > .card-header a {\n  color: #1f2d3d;\n}\n\n.card-lime:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-lime.card-outline {\n  border-top: 3px solid #01ff70;\n}\n\n.card-lime.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-lime.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #01ff70;\n}\n\n.bg-lime .btn-tool,\n.bg-gradient-lime .btn-tool,\n.card-lime:not(.card-outline) .btn-tool {\n  color: rgba(31, 45, 61, 0.8);\n}\n\n.bg-lime .btn-tool:hover,\n.bg-gradient-lime .btn-tool:hover,\n.card-lime:not(.card-outline) .btn-tool:hover {\n  color: #1f2d3d;\n}\n\n.card.bg-lime .bootstrap-datetimepicker-widget .table td,\n.card.bg-lime .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-lime .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-lime .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-lime .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-lime .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-lime .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #00d75e;\n  color: #1f2d3d;\n}\n\n.card.bg-lime .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #1f2d3d;\n}\n\n.card.bg-lime .bootstrap-datetimepicker-widget table td.active,\n.card.bg-lime .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #34ff8d;\n  color: #1f2d3d;\n}\n\n.card-fuchsia:not(.card-outline) > .card-header {\n  background-color: #f012be;\n}\n\n.card-fuchsia:not(.card-outline) > .card-header,\n.card-fuchsia:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-fuchsia:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-fuchsia.card-outline {\n  border-top: 3px solid #f012be;\n}\n\n.card-fuchsia.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-fuchsia.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #f012be;\n}\n\n.bg-fuchsia .btn-tool,\n.bg-gradient-fuchsia .btn-tool,\n.card-fuchsia:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-fuchsia .btn-tool:hover,\n.bg-gradient-fuchsia .btn-tool:hover,\n.card-fuchsia:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-fuchsia .bootstrap-datetimepicker-widget .table td,\n.card.bg-fuchsia .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #cc0da1;\n  color: #fff;\n}\n\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table td.active,\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #f342cb;\n  color: #fff;\n}\n\n.card-maroon:not(.card-outline) > .card-header {\n  background-color: #d81b60;\n}\n\n.card-maroon:not(.card-outline) > .card-header,\n.card-maroon:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-maroon:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-maroon.card-outline {\n  border-top: 3px solid #d81b60;\n}\n\n.card-maroon.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-maroon.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #d81b60;\n}\n\n.bg-maroon .btn-tool,\n.bg-gradient-maroon .btn-tool,\n.card-maroon:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-maroon .btn-tool:hover,\n.bg-gradient-maroon .btn-tool:hover,\n.card-maroon:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-maroon .bootstrap-datetimepicker-widget .table td,\n.card.bg-maroon .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-maroon .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-maroon .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-maroon .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-maroon .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-maroon .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #b41650;\n  color: #fff;\n}\n\n.card.bg-maroon .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-maroon .bootstrap-datetimepicker-widget table td.active,\n.card.bg-maroon .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #e73f7c;\n  color: #fff;\n}\n\n.card-blue:not(.card-outline) > .card-header {\n  background-color: #007bff;\n}\n\n.card-blue:not(.card-outline) > .card-header,\n.card-blue:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-blue:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-blue.card-outline {\n  border-top: 3px solid #007bff;\n}\n\n.card-blue.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-blue.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #007bff;\n}\n\n.bg-blue .btn-tool,\n.bg-gradient-blue .btn-tool,\n.card-blue:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-blue .btn-tool:hover,\n.bg-gradient-blue .btn-tool:hover,\n.card-blue:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-blue .bootstrap-datetimepicker-widget .table td,\n.card.bg-blue .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-blue .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-blue .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-blue .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-blue .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-blue .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #0067d6;\n  color: #fff;\n}\n\n.card.bg-blue .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-blue .bootstrap-datetimepicker-widget table td.active,\n.card.bg-blue .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #3395ff;\n  color: #fff;\n}\n\n.card-indigo:not(.card-outline) > .card-header {\n  background-color: #6610f2;\n}\n\n.card-indigo:not(.card-outline) > .card-header,\n.card-indigo:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-indigo:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-indigo.card-outline {\n  border-top: 3px solid #6610f2;\n}\n\n.card-indigo.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-indigo.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #6610f2;\n}\n\n.bg-indigo .btn-tool,\n.bg-gradient-indigo .btn-tool,\n.card-indigo:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-indigo .btn-tool:hover,\n.bg-gradient-indigo .btn-tool:hover,\n.card-indigo:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-indigo .bootstrap-datetimepicker-widget .table td,\n.card.bg-indigo .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-indigo .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-indigo .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-indigo .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-indigo .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-indigo .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #550bce;\n  color: #fff;\n}\n\n.card.bg-indigo .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-indigo .bootstrap-datetimepicker-widget table td.active,\n.card.bg-indigo .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #8540f5;\n  color: #fff;\n}\n\n.card-purple:not(.card-outline) > .card-header {\n  background-color: #6f42c1;\n}\n\n.card-purple:not(.card-outline) > .card-header,\n.card-purple:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-purple:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-purple.card-outline {\n  border-top: 3px solid #6f42c1;\n}\n\n.card-purple.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-purple.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #6f42c1;\n}\n\n.bg-purple .btn-tool,\n.bg-gradient-purple .btn-tool,\n.card-purple:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-purple .btn-tool:hover,\n.bg-gradient-purple .btn-tool:hover,\n.card-purple:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-purple .bootstrap-datetimepicker-widget .table td,\n.card.bg-purple .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-purple .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-purple .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-purple .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-purple .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-purple .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #5d36a4;\n  color: #fff;\n}\n\n.card.bg-purple .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-purple .bootstrap-datetimepicker-widget table td.active,\n.card.bg-purple .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #8c68ce;\n  color: #fff;\n}\n\n.card-pink:not(.card-outline) > .card-header {\n  background-color: #e83e8c;\n}\n\n.card-pink:not(.card-outline) > .card-header,\n.card-pink:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-pink:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-pink.card-outline {\n  border-top: 3px solid #e83e8c;\n}\n\n.card-pink.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-pink.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #e83e8c;\n}\n\n.bg-pink .btn-tool,\n.bg-gradient-pink .btn-tool,\n.card-pink:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-pink .btn-tool:hover,\n.bg-gradient-pink .btn-tool:hover,\n.card-pink:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-pink .bootstrap-datetimepicker-widget .table td,\n.card.bg-pink .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-pink .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-pink .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-pink .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-pink .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-pink .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #e21b76;\n  color: #fff;\n}\n\n.card.bg-pink .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-pink .bootstrap-datetimepicker-widget table td.active,\n.card.bg-pink .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #ed6ca7;\n  color: #fff;\n}\n\n.card-red:not(.card-outline) > .card-header {\n  background-color: #dc3545;\n}\n\n.card-red:not(.card-outline) > .card-header,\n.card-red:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-red:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-red.card-outline {\n  border-top: 3px solid #dc3545;\n}\n\n.card-red.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-red.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #dc3545;\n}\n\n.bg-red .btn-tool,\n.bg-gradient-red .btn-tool,\n.card-red:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-red .btn-tool:hover,\n.bg-gradient-red .btn-tool:hover,\n.card-red:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-red .bootstrap-datetimepicker-widget .table td,\n.card.bg-red .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-red .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-red .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-red .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-red .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-red .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #c62232;\n  color: #fff;\n}\n\n.card.bg-red .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-red .bootstrap-datetimepicker-widget table td.active,\n.card.bg-red .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #e4606d;\n  color: #fff;\n}\n\n.card-orange:not(.card-outline) > .card-header {\n  background-color: #fd7e14;\n}\n\n.card-orange:not(.card-outline) > .card-header,\n.card-orange:not(.card-outline) > .card-header a {\n  color: #1f2d3d;\n}\n\n.card-orange:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-orange.card-outline {\n  border-top: 3px solid #fd7e14;\n}\n\n.card-orange.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-orange.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #fd7e14;\n}\n\n.bg-orange .btn-tool,\n.bg-gradient-orange .btn-tool,\n.card-orange:not(.card-outline) .btn-tool {\n  color: rgba(31, 45, 61, 0.8);\n}\n\n.bg-orange .btn-tool:hover,\n.bg-gradient-orange .btn-tool:hover,\n.card-orange:not(.card-outline) .btn-tool:hover {\n  color: #1f2d3d;\n}\n\n.card.bg-orange .bootstrap-datetimepicker-widget .table td,\n.card.bg-orange .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-orange .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-orange .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-orange .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-orange .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-orange .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #e66a02;\n  color: #1f2d3d;\n}\n\n.card.bg-orange .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #1f2d3d;\n}\n\n.card.bg-orange .bootstrap-datetimepicker-widget table td.active,\n.card.bg-orange .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #fd9a47;\n  color: #1f2d3d;\n}\n\n.card-yellow:not(.card-outline) > .card-header {\n  background-color: #ffc107;\n}\n\n.card-yellow:not(.card-outline) > .card-header,\n.card-yellow:not(.card-outline) > .card-header a {\n  color: #1f2d3d;\n}\n\n.card-yellow:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-yellow.card-outline {\n  border-top: 3px solid #ffc107;\n}\n\n.card-yellow.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-yellow.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #ffc107;\n}\n\n.bg-yellow .btn-tool,\n.bg-gradient-yellow .btn-tool,\n.card-yellow:not(.card-outline) .btn-tool {\n  color: rgba(31, 45, 61, 0.8);\n}\n\n.bg-yellow .btn-tool:hover,\n.bg-gradient-yellow .btn-tool:hover,\n.card-yellow:not(.card-outline) .btn-tool:hover {\n  color: #1f2d3d;\n}\n\n.card.bg-yellow .bootstrap-datetimepicker-widget .table td,\n.card.bg-yellow .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-yellow .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-yellow .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-yellow .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-yellow .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-yellow .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #dda600;\n  color: #1f2d3d;\n}\n\n.card.bg-yellow .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #1f2d3d;\n}\n\n.card.bg-yellow .bootstrap-datetimepicker-widget table td.active,\n.card.bg-yellow .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #ffce3a;\n  color: #1f2d3d;\n}\n\n.card-green:not(.card-outline) > .card-header {\n  background-color: #28a745;\n}\n\n.card-green:not(.card-outline) > .card-header,\n.card-green:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-green:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-green.card-outline {\n  border-top: 3px solid #28a745;\n}\n\n.card-green.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-green.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #28a745;\n}\n\n.bg-green .btn-tool,\n.bg-gradient-green .btn-tool,\n.card-green:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-green .btn-tool:hover,\n.bg-gradient-green .btn-tool:hover,\n.card-green:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-green .bootstrap-datetimepicker-widget .table td,\n.card.bg-green .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-green .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-green .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-green .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-green .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-green .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #208637;\n  color: #fff;\n}\n\n.card.bg-green .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-green .bootstrap-datetimepicker-widget table td.active,\n.card.bg-green .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #34ce57;\n  color: #fff;\n}\n\n.card-teal:not(.card-outline) > .card-header {\n  background-color: #20c997;\n}\n\n.card-teal:not(.card-outline) > .card-header,\n.card-teal:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-teal:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-teal.card-outline {\n  border-top: 3px solid #20c997;\n}\n\n.card-teal.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-teal.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #20c997;\n}\n\n.bg-teal .btn-tool,\n.bg-gradient-teal .btn-tool,\n.card-teal:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-teal .btn-tool:hover,\n.bg-gradient-teal .btn-tool:hover,\n.card-teal:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-teal .bootstrap-datetimepicker-widget .table td,\n.card.bg-teal .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-teal .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-teal .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-teal .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-teal .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-teal .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #1aa67d;\n  color: #fff;\n}\n\n.card.bg-teal .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-teal .bootstrap-datetimepicker-widget table td.active,\n.card.bg-teal .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #3ce0af;\n  color: #fff;\n}\n\n.card-cyan:not(.card-outline) > .card-header {\n  background-color: #17a2b8;\n}\n\n.card-cyan:not(.card-outline) > .card-header,\n.card-cyan:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-cyan:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-cyan.card-outline {\n  border-top: 3px solid #17a2b8;\n}\n\n.card-cyan.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-cyan.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #17a2b8;\n}\n\n.bg-cyan .btn-tool,\n.bg-gradient-cyan .btn-tool,\n.card-cyan:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-cyan .btn-tool:hover,\n.bg-gradient-cyan .btn-tool:hover,\n.card-cyan:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-cyan .bootstrap-datetimepicker-widget .table td,\n.card.bg-cyan .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-cyan .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-cyan .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-cyan .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-cyan .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-cyan .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #128294;\n  color: #fff;\n}\n\n.card.bg-cyan .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-cyan .bootstrap-datetimepicker-widget table td.active,\n.card.bg-cyan .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #1fc8e3;\n  color: #fff;\n}\n\n.card-white:not(.card-outline) > .card-header {\n  background-color: #fff;\n}\n\n.card-white:not(.card-outline) > .card-header,\n.card-white:not(.card-outline) > .card-header a {\n  color: #1f2d3d;\n}\n\n.card-white:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-white.card-outline {\n  border-top: 3px solid #fff;\n}\n\n.card-white.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-white.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #fff;\n}\n\n.bg-white .btn-tool,\n.bg-gradient-white .btn-tool,\n.card-white:not(.card-outline) .btn-tool {\n  color: rgba(31, 45, 61, 0.8);\n}\n\n.bg-white .btn-tool:hover,\n.bg-gradient-white .btn-tool:hover,\n.card-white:not(.card-outline) .btn-tool:hover {\n  color: #1f2d3d;\n}\n\n.card.bg-white .bootstrap-datetimepicker-widget .table td,\n.card.bg-white .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-white .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-white .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-white .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-white .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-white .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #ebebeb;\n  color: #1f2d3d;\n}\n\n.card.bg-white .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #1f2d3d;\n}\n\n.card.bg-white .bootstrap-datetimepicker-widget table td.active,\n.card.bg-white .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: white;\n  color: #1f2d3d;\n}\n\n.card-gray:not(.card-outline) > .card-header {\n  background-color: #6c757d;\n}\n\n.card-gray:not(.card-outline) > .card-header,\n.card-gray:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-gray:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-gray.card-outline {\n  border-top: 3px solid #6c757d;\n}\n\n.card-gray.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-gray.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #6c757d;\n}\n\n.bg-gray .btn-tool,\n.bg-gradient-gray .btn-tool,\n.card-gray:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-gray .btn-tool:hover,\n.bg-gradient-gray .btn-tool:hover,\n.card-gray:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-gray .bootstrap-datetimepicker-widget .table td,\n.card.bg-gray .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-gray .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gray .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gray .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gray .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gray .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #596167;\n  color: #fff;\n}\n\n.card.bg-gray .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-gray .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gray .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #868e96;\n  color: #fff;\n}\n\n.card-gray-dark:not(.card-outline) > .card-header {\n  background-color: #343a40;\n}\n\n.card-gray-dark:not(.card-outline) > .card-header,\n.card-gray-dark:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-gray-dark:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-gray-dark.card-outline {\n  border-top: 3px solid #343a40;\n}\n\n.card-gray-dark.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-gray-dark.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #343a40;\n}\n\n.bg-gray-dark .btn-tool,\n.bg-gradient-gray-dark .btn-tool,\n.card-gray-dark:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-gray-dark .btn-tool:hover,\n.bg-gradient-gray-dark .btn-tool:hover,\n.card-gray-dark:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-gray-dark .bootstrap-datetimepicker-widget .table td,\n.card.bg-gray-dark .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #222629;\n  color: #fff;\n}\n\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #4b545c;\n  color: #fff;\n}\n\n.card {\n  box-shadow: 0 0 1px rgba(0, 0, 0, 0.125), 0 1px 3px rgba(0, 0, 0, 0.2);\n  margin-bottom: 1rem;\n}\n\n.card.bg-dark .card-header {\n  border-color: #383f45;\n}\n\n.card.bg-dark,\n.card.bg-dark .card-body {\n  color: #fff;\n}\n\n.card.maximized-card {\n  height: 100% !important;\n  left: 0;\n  max-height: 100% !important;\n  max-width: 100% !important;\n  position: fixed;\n  top: 0;\n  width: 100% !important;\n  z-index: 1040;\n}\n\n.card.maximized-card.was-collapsed .card-body {\n  display: block !important;\n}\n\n.card.maximized-card .card-body {\n  overflow: auto;\n}\n\n.card.maximized-card [data-widget=\"collapse\"] {\n  display: none;\n}\n\n.card.maximized-card .card-header,\n.card.maximized-card .card-footer {\n  border-radius: 0 !important;\n}\n\n.card.collapsed-card .card-body,\n.card.collapsed-card .card-footer {\n  display: none;\n}\n\n.card .nav.flex-column > li {\n  border-bottom: 1px solid rgba(0, 0, 0, 0.125);\n  margin: 0;\n}\n\n.card .nav.flex-column > li:last-of-type {\n  border-bottom: 0;\n}\n\n.card.height-control .card-body {\n  max-height: 300px;\n  overflow: auto;\n}\n\n.card .border-right {\n  border-right: 1px solid rgba(0, 0, 0, 0.125);\n}\n\n.card .border-left {\n  border-left: 1px solid rgba(0, 0, 0, 0.125);\n}\n\n.card.card-tabs:not(.card-outline) > .card-header {\n  border-bottom: 0;\n}\n\n.card.card-tabs:not(.card-outline) > .card-header .nav-item:first-child .nav-link {\n  border-left-color: transparent;\n}\n\n.card.card-tabs.card-outline .nav-item {\n  border-bottom: 0;\n}\n\n.card.card-tabs.card-outline .nav-item:first-child .nav-link {\n  border-left: 0;\n  margin-left: 0;\n}\n\n.card.card-tabs .card-tools {\n  margin: .3rem .5rem;\n}\n\n.card.card-tabs:not(.expanding-card).collapsed-card .card-header {\n  border-bottom: 0;\n}\n\n.card.card-tabs:not(.expanding-card).collapsed-card .card-header .nav-tabs {\n  border-bottom: 0;\n}\n\n.card.card-tabs:not(.expanding-card).collapsed-card .card-header .nav-tabs .nav-item {\n  margin-bottom: 0;\n}\n\n.card.card-tabs.expanding-card .card-header .nav-tabs .nav-item {\n  margin-bottom: -1px;\n}\n\n.card.card-outline-tabs {\n  border-top: 0;\n}\n\n.card.card-outline-tabs .card-header .nav-item:first-child .nav-link {\n  border-left: 0;\n  margin-left: 0;\n}\n\n.card.card-outline-tabs .card-header a {\n  border-top: 3px solid transparent;\n}\n\n.card.card-outline-tabs .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card.card-outline-tabs .card-header a.active:hover {\n  margin-top: 0;\n}\n\n.card.card-outline-tabs .card-tools {\n  margin: .5rem .5rem .3rem;\n}\n\n.card.card-outline-tabs:not(.expanding-card).collapsed-card .card-header {\n  border-bottom: 0;\n}\n\n.card.card-outline-tabs:not(.expanding-card).collapsed-card .card-header .nav-tabs {\n  border-bottom: 0;\n}\n\n.card.card-outline-tabs:not(.expanding-card).collapsed-card .card-header .nav-tabs .nav-item {\n  margin-bottom: 0;\n}\n\n.card.card-outline-tabs.expanding-card .card-header .nav-tabs .nav-item {\n  margin-bottom: -1px;\n}\n\nhtml.maximized-card {\n  overflow: hidden;\n}\n\n.card-header::after,\n.card-body::after,\n.card-footer::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.card-header {\n  background-color: transparent;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.125);\n  padding: 0.75rem 1.25rem;\n  position: relative;\n  border-top-left-radius: 0.25rem;\n  border-top-right-radius: 0.25rem;\n}\n\n.collapsed-card .card-header {\n  border-bottom: 0;\n}\n\n.card-header > .card-tools {\n  float: right;\n  margin-right: -0.625rem;\n}\n\n.card-header > .card-tools .input-group,\n.card-header > .card-tools .nav,\n.card-header > .card-tools .pagination {\n  margin-bottom: -0.3rem;\n  margin-top: -0.3rem;\n}\n\n.card-header > .card-tools [data-toggle=\"tooltip\"] {\n  position: relative;\n}\n\n.card-title {\n  float: left;\n  font-size: 1.1rem;\n  font-weight: 400;\n  margin: 0;\n}\n\n.card-text {\n  clear: both;\n}\n\n.btn-tool {\n  background-color: transparent;\n  color: #adb5bd;\n  font-size: 0.875rem;\n  margin: -0.75rem 0;\n  padding: .25rem .5rem;\n}\n\n.btn-group.show .btn-tool, .btn-tool:hover {\n  color: #495057;\n}\n\n.show .btn-tool, .btn-tool:focus {\n  box-shadow: none !important;\n}\n\n.text-sm .card-title {\n  font-size: 1rem;\n}\n\n.text-sm .nav-link {\n  padding: 0.4rem 0.8rem;\n}\n\n.card-body > .table {\n  margin-bottom: 0;\n}\n\n.card-body > .table > thead > tr > th,\n.card-body > .table > thead > tr > td {\n  border-top-width: 0;\n}\n\n.card-body .fc {\n  margin-top: 5px;\n}\n\n.card-body .full-width-chart {\n  margin: -19px;\n}\n\n.card-body.p-0 .full-width-chart {\n  margin: -9px;\n}\n\n.chart-legend {\n  padding-left: 0;\n  list-style: none;\n  margin: 10px 0;\n}\n\n@media (max-width: 576px) {\n  .chart-legend > li {\n    float: left;\n    margin-right: 10px;\n  }\n}\n\n.card-comments {\n  background-color: #f8f9fa;\n}\n\n.card-comments .card-comment {\n  border-bottom: 1px solid #e9ecef;\n  padding: 8px 0;\n}\n\n.card-comments .card-comment::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.card-comments .card-comment:last-of-type {\n  border-bottom: 0;\n}\n\n.card-comments .card-comment:first-of-type {\n  padding-top: 0;\n}\n\n.card-comments .card-comment img {\n  height: 1.875rem;\n  width: 1.875rem;\n  float: left;\n}\n\n.card-comments .comment-text {\n  color: #78838e;\n  margin-left: 40px;\n}\n\n.card-comments .username {\n  color: #495057;\n  display: block;\n  font-weight: 600;\n}\n\n.card-comments .text-muted {\n  font-size: 12px;\n  font-weight: 400;\n}\n\n.todo-list {\n  list-style: none;\n  margin: 0;\n  overflow: auto;\n  padding: 0;\n}\n\n.todo-list > li {\n  border-radius: 2px;\n  background-color: #f8f9fa;\n  border-left: 2px solid #e9ecef;\n  color: #495057;\n  margin-bottom: 2px;\n  padding: 10px;\n}\n\n.todo-list > li:last-of-type {\n  margin-bottom: 0;\n}\n\n.todo-list > li > input[type=\"checkbox\"] {\n  margin: 0 10px 0 5px;\n}\n\n.todo-list > li .text {\n  display: inline-block;\n  font-weight: 600;\n  margin-left: 5px;\n}\n\n.todo-list > li .badge {\n  font-size: .7rem;\n  margin-left: 10px;\n}\n\n.todo-list > li .tools {\n  color: #dc3545;\n  display: none;\n  float: right;\n}\n\n.todo-list > li .tools > .fa,\n.todo-list > li .tools > .fas,\n.todo-list > li .tools > .far,\n.todo-list > li .tools > .fab,\n.todo-list > li .tools > .fal,\n.todo-list > li .tools > .fad,\n.todo-list > li .tools > .svg-inline--fa,\n.todo-list > li .tools > .ion {\n  cursor: pointer;\n  margin-right: 5px;\n}\n\n.todo-list > li:hover .tools {\n  display: inline-block;\n}\n\n.todo-list > li.done {\n  color: #697582;\n}\n\n.todo-list > li.done .text {\n  font-weight: 500;\n  text-decoration: line-through;\n}\n\n.todo-list > li.done .badge {\n  background-color: #adb5bd !important;\n}\n\n.todo-list .primary {\n  border-left-color: #007bff;\n}\n\n.todo-list .secondary {\n  border-left-color: #6c757d;\n}\n\n.todo-list .success {\n  border-left-color: #28a745;\n}\n\n.todo-list .info {\n  border-left-color: #17a2b8;\n}\n\n.todo-list .warning {\n  border-left-color: #ffc107;\n}\n\n.todo-list .danger {\n  border-left-color: #dc3545;\n}\n\n.todo-list .light {\n  border-left-color: #f8f9fa;\n}\n\n.todo-list .dark {\n  border-left-color: #343a40;\n}\n\n.todo-list .lightblue {\n  border-left-color: #3c8dbc;\n}\n\n.todo-list .navy {\n  border-left-color: #001f3f;\n}\n\n.todo-list .olive {\n  border-left-color: #3d9970;\n}\n\n.todo-list .lime {\n  border-left-color: #01ff70;\n}\n\n.todo-list .fuchsia {\n  border-left-color: #f012be;\n}\n\n.todo-list .maroon {\n  border-left-color: #d81b60;\n}\n\n.todo-list .blue {\n  border-left-color: #007bff;\n}\n\n.todo-list .indigo {\n  border-left-color: #6610f2;\n}\n\n.todo-list .purple {\n  border-left-color: #6f42c1;\n}\n\n.todo-list .pink {\n  border-left-color: #e83e8c;\n}\n\n.todo-list .red {\n  border-left-color: #dc3545;\n}\n\n.todo-list .orange {\n  border-left-color: #fd7e14;\n}\n\n.todo-list .yellow {\n  border-left-color: #ffc107;\n}\n\n.todo-list .green {\n  border-left-color: #28a745;\n}\n\n.todo-list .teal {\n  border-left-color: #20c997;\n}\n\n.todo-list .cyan {\n  border-left-color: #17a2b8;\n}\n\n.todo-list .white {\n  border-left-color: #fff;\n}\n\n.todo-list .gray {\n  border-left-color: #6c757d;\n}\n\n.todo-list .gray-dark {\n  border-left-color: #343a40;\n}\n\n.todo-list .handle {\n  cursor: move;\n  display: inline-block;\n  margin: 0 5px;\n}\n\n.card-input {\n  max-width: 200px;\n}\n\n.card-default .nav-item:first-child .nav-link {\n  border-left: 0;\n}\n\n.dark-mode .card {\n  background-color: #343a40;\n  color: #fff;\n}\n\n.dark-mode .card .card {\n  background-color: #3f474e;\n  color: #fff;\n}\n\n.dark-mode .card .nav.flex-column > li {\n  border-bottom-color: #6c757d;\n}\n\n.dark-mode .card .card-footer {\n  background-color: rgba(0, 0, 0, 0.1);\n}\n\n.dark-mode .card.card-outline-tabs .card-header a:hover {\n  border-color: #6c757d;\n}\n\n.dark-mode .card:not(.card-outline) > .card-header a.active {\n  color: #fff;\n}\n\n.dark-mode .card-comments {\n  background-color: #373d44;\n}\n\n.dark-mode .card-comments .username {\n  color: #ced4da;\n}\n\n.dark-mode .card-comments .card-comment {\n  border-bottom-color: #454d55;\n}\n\n.dark-mode .todo-list > li {\n  background-color: #3f474e;\n  border-color: #454d55;\n  color: #fff;\n}\n\n.modal-dialog .overlay {\n  background-color: #000;\n  display: block;\n  height: 100%;\n  left: 0;\n  opacity: .7;\n  position: absolute;\n  top: 0;\n  width: 100%;\n  z-index: 1052;\n}\n\n.modal-content.bg-warning .modal-header,\n.modal-content.bg-warning .modal-footer {\n  border-color: #343a40;\n}\n\n.modal-content.bg-primary .close, .modal-content.bg-secondary .close, .modal-content.bg-info .close, .modal-content.bg-danger .close, .modal-content.bg-success .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.dark-mode .modal-header,\n.dark-mode .modal-footer {\n  border-color: #6c757d;\n}\n\n.dark-mode .modal-content {\n  background-color: #343a40;\n}\n\n.dark-mode .modal-content.bg-warning .modal-header,\n.dark-mode .modal-content.bg-warning .modal-footer {\n  border-color: #6c757d;\n}\n\n.dark-mode .modal-content.bg-warning .close {\n  color: #343a40 !important;\n  text-shadow: 0 1px 0 #495057 !important;\n}\n\n.dark-mode .modal-content.bg-primary .modal-header,\n.dark-mode .modal-content.bg-primary .modal-footer, .dark-mode .modal-content.bg-secondary .modal-header,\n.dark-mode .modal-content.bg-secondary .modal-footer, .dark-mode .modal-content.bg-info .modal-header,\n.dark-mode .modal-content.bg-info .modal-footer, .dark-mode .modal-content.bg-danger .modal-header,\n.dark-mode .modal-content.bg-danger .modal-footer, .dark-mode .modal-content.bg-success .modal-header,\n.dark-mode .modal-content.bg-success .modal-footer {\n  border-color: #fff;\n}\n\n.toasts-top-right {\n  position: absolute;\n  right: 0;\n  top: 0;\n  z-index: 1040;\n}\n\n.toasts-top-right.fixed {\n  position: fixed;\n}\n\n.toasts-top-left {\n  left: 0;\n  position: absolute;\n  top: 0;\n  z-index: 1040;\n}\n\n.toasts-top-left.fixed {\n  position: fixed;\n}\n\n.toasts-bottom-right {\n  bottom: 0;\n  position: absolute;\n  right: 0;\n  z-index: 1040;\n}\n\n.toasts-bottom-right.fixed {\n  position: fixed;\n}\n\n.toasts-bottom-left {\n  bottom: 0;\n  left: 0;\n  position: absolute;\n  z-index: 1040;\n}\n\n.toasts-bottom-left.fixed {\n  position: fixed;\n}\n\n.dark-mode .toast {\n  background-color: rgba(52, 58, 64, 0.85);\n  color: #fff;\n}\n\n.dark-mode .toast .toast-header {\n  background-color: rgba(52, 58, 64, 0.7);\n  color: #f8f9fa;\n}\n\n.toast.bg-primary {\n  background-color: rgba(0, 123, 255, 0.9) !important;\n}\n\n.toast.bg-primary .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-primary .toast-header {\n  background-color: rgba(0, 123, 255, 0.85);\n  color: #fff;\n}\n\n.toast.bg-secondary {\n  background-color: rgba(108, 117, 125, 0.9) !important;\n}\n\n.toast.bg-secondary .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-secondary .toast-header {\n  background-color: rgba(108, 117, 125, 0.85);\n  color: #fff;\n}\n\n.toast.bg-success {\n  background-color: rgba(40, 167, 69, 0.9) !important;\n}\n\n.toast.bg-success .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-success .toast-header {\n  background-color: rgba(40, 167, 69, 0.85);\n  color: #fff;\n}\n\n.toast.bg-info {\n  background-color: rgba(23, 162, 184, 0.9) !important;\n}\n\n.toast.bg-info .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-info .toast-header {\n  background-color: rgba(23, 162, 184, 0.85);\n  color: #fff;\n}\n\n.toast.bg-warning {\n  background-color: rgba(255, 193, 7, 0.9) !important;\n}\n\n.toast.bg-warning .toast-header {\n  background-color: rgba(255, 193, 7, 0.85);\n  color: #1f2d3d;\n}\n\n.toast.bg-danger {\n  background-color: rgba(220, 53, 69, 0.9) !important;\n}\n\n.toast.bg-danger .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-danger .toast-header {\n  background-color: rgba(220, 53, 69, 0.85);\n  color: #fff;\n}\n\n.toast.bg-light {\n  background-color: rgba(248, 249, 250, 0.9) !important;\n}\n\n.toast.bg-light .toast-header {\n  background-color: rgba(248, 249, 250, 0.85);\n  color: #1f2d3d;\n}\n\n.toast.bg-dark {\n  background-color: rgba(52, 58, 64, 0.9) !important;\n}\n\n.toast.bg-dark .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-dark .toast-header {\n  background-color: rgba(52, 58, 64, 0.85);\n  color: #fff;\n}\n\n.toast.bg-lightblue {\n  background-color: rgba(60, 141, 188, 0.9) !important;\n}\n\n.toast.bg-lightblue .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-lightblue .toast-header {\n  background-color: rgba(60, 141, 188, 0.85);\n  color: #fff;\n}\n\n.toast.bg-navy {\n  background-color: rgba(0, 31, 63, 0.9) !important;\n}\n\n.toast.bg-navy .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-navy .toast-header {\n  background-color: rgba(0, 31, 63, 0.85);\n  color: #fff;\n}\n\n.toast.bg-olive {\n  background-color: rgba(61, 153, 112, 0.9) !important;\n}\n\n.toast.bg-olive .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-olive .toast-header {\n  background-color: rgba(61, 153, 112, 0.85);\n  color: #fff;\n}\n\n.toast.bg-lime {\n  background-color: rgba(1, 255, 112, 0.9) !important;\n}\n\n.toast.bg-lime .toast-header {\n  background-color: rgba(1, 255, 112, 0.85);\n  color: #1f2d3d;\n}\n\n.toast.bg-fuchsia {\n  background-color: rgba(240, 18, 190, 0.9) !important;\n}\n\n.toast.bg-fuchsia .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-fuchsia .toast-header {\n  background-color: rgba(240, 18, 190, 0.85);\n  color: #fff;\n}\n\n.toast.bg-maroon {\n  background-color: rgba(216, 27, 96, 0.9) !important;\n}\n\n.toast.bg-maroon .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-maroon .toast-header {\n  background-color: rgba(216, 27, 96, 0.85);\n  color: #fff;\n}\n\n.toast.bg-blue {\n  background-color: rgba(0, 123, 255, 0.9) !important;\n}\n\n.toast.bg-blue .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-blue .toast-header {\n  background-color: rgba(0, 123, 255, 0.85);\n  color: #fff;\n}\n\n.toast.bg-indigo {\n  background-color: rgba(102, 16, 242, 0.9) !important;\n}\n\n.toast.bg-indigo .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-indigo .toast-header {\n  background-color: rgba(102, 16, 242, 0.85);\n  color: #fff;\n}\n\n.toast.bg-purple {\n  background-color: rgba(111, 66, 193, 0.9) !important;\n}\n\n.toast.bg-purple .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-purple .toast-header {\n  background-color: rgba(111, 66, 193, 0.85);\n  color: #fff;\n}\n\n.toast.bg-pink {\n  background-color: rgba(232, 62, 140, 0.9) !important;\n}\n\n.toast.bg-pink .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-pink .toast-header {\n  background-color: rgba(232, 62, 140, 0.85);\n  color: #fff;\n}\n\n.toast.bg-red {\n  background-color: rgba(220, 53, 69, 0.9) !important;\n}\n\n.toast.bg-red .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-red .toast-header {\n  background-color: rgba(220, 53, 69, 0.85);\n  color: #fff;\n}\n\n.toast.bg-orange {\n  background-color: rgba(253, 126, 20, 0.9) !important;\n}\n\n.toast.bg-orange .toast-header {\n  background-color: rgba(253, 126, 20, 0.85);\n  color: #1f2d3d;\n}\n\n.toast.bg-yellow {\n  background-color: rgba(255, 193, 7, 0.9) !important;\n}\n\n.toast.bg-yellow .toast-header {\n  background-color: rgba(255, 193, 7, 0.85);\n  color: #1f2d3d;\n}\n\n.toast.bg-green {\n  background-color: rgba(40, 167, 69, 0.9) !important;\n}\n\n.toast.bg-green .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-green .toast-header {\n  background-color: rgba(40, 167, 69, 0.85);\n  color: #fff;\n}\n\n.toast.bg-teal {\n  background-color: rgba(32, 201, 151, 0.9) !important;\n}\n\n.toast.bg-teal .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-teal .toast-header {\n  background-color: rgba(32, 201, 151, 0.85);\n  color: #fff;\n}\n\n.toast.bg-cyan {\n  background-color: rgba(23, 162, 184, 0.9) !important;\n}\n\n.toast.bg-cyan .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-cyan .toast-header {\n  background-color: rgba(23, 162, 184, 0.85);\n  color: #fff;\n}\n\n.toast.bg-white {\n  background-color: rgba(255, 255, 255, 0.9) !important;\n}\n\n.toast.bg-white .toast-header {\n  background-color: rgba(255, 255, 255, 0.85);\n  color: #1f2d3d;\n}\n\n.toast.bg-gray {\n  background-color: rgba(108, 117, 125, 0.9) !important;\n}\n\n.toast.bg-gray .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-gray .toast-header {\n  background-color: rgba(108, 117, 125, 0.85);\n  color: #fff;\n}\n\n.toast.bg-gray-dark {\n  background-color: rgba(52, 58, 64, 0.9) !important;\n}\n\n.toast.bg-gray-dark .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-gray-dark .toast-header {\n  background-color: rgba(52, 58, 64, 0.85);\n  color: #fff;\n}\n\n.btn.disabled, .btn:disabled {\n  cursor: not-allowed;\n}\n\n.btn.btn-flat {\n  border-radius: 0;\n  border-width: 1px;\n  box-shadow: none;\n}\n\n.btn.btn-file {\n  overflow: hidden;\n  position: relative;\n}\n\n.btn.btn-file > input[type=\"file\"] {\n  background-color: #fff;\n  cursor: inherit;\n  display: block;\n  font-size: 100px;\n  min-height: 100%;\n  min-width: 100%;\n  opacity: 0;\n  outline: none;\n  position: absolute;\n  right: 0;\n  text-align: right;\n  top: 0;\n}\n\n.text-sm .btn {\n  font-size: 0.875rem !important;\n}\n\n.btn-default {\n  background-color: #f8f9fa;\n  border-color: #ddd;\n  color: #444;\n}\n\n.btn-default:hover, .btn-default:active, .btn-default.hover {\n  background-color: #e9ecef;\n  color: #2b2b2b;\n}\n\n.btn-app {\n  border-radius: 3px;\n  background-color: #f8f9fa;\n  border: 1px solid #ddd;\n  color: #6c757d;\n  font-size: 12px;\n  height: 60px;\n  margin: 0 0 10px 10px;\n  min-width: 80px;\n  padding: 15px 5px;\n  position: relative;\n  text-align: center;\n}\n\n.btn-app > .fa,\n.btn-app > .fas,\n.btn-app > .far,\n.btn-app > .fab,\n.btn-app > .fal,\n.btn-app > .fad,\n.btn-app > .svg-inline--fa,\n.btn-app > .ion {\n  display: block;\n  font-size: 20px;\n}\n\n.btn-app > .svg-inline--fa {\n  margin: 0 auto;\n}\n\n.btn-app:hover {\n  background-color: #f8f9fa;\n  border-color: #aaaaaa;\n  color: #444;\n}\n\n.btn-app:active, .btn-app:focus {\n  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);\n}\n\n.btn-app > .badge {\n  font-size: 10px;\n  font-weight: 400;\n  position: absolute;\n  right: -10px;\n  top: -3px;\n}\n\n.btn-xs {\n  padding: 0.125rem 0.25rem;\n  font-size: 0.75rem;\n  line-height: 1.5;\n  border-radius: 0.15rem;\n}\n\n.dark-mode .btn-default,\n.dark-mode .btn-app {\n  background-color: #3a4047;\n  color: #fff;\n  border-color: #6c757d;\n}\n\n.dark-mode .btn-default:hover, .dark-mode .btn-default:focus,\n.dark-mode .btn-app:hover,\n.dark-mode .btn-app:focus {\n  background-color: #3f474e;\n  color: #dee2e6;\n  border-color: #727b84;\n}\n\n.dark-mode .btn-light {\n  background-color: #454d55;\n  color: #fff;\n  border-color: #6c757d;\n}\n\n.dark-mode .btn-light:hover, .dark-mode .btn-light:focus {\n  background-color: #4b545c;\n  color: #dee2e6;\n  border-color: #78828a;\n}\n\n.callout {\n  border-radius: 0.25rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);\n  background-color: #fff;\n  border-left: 5px solid #e9ecef;\n  margin-bottom: 1rem;\n  padding: 1rem;\n}\n\n.callout a {\n  color: #495057;\n  text-decoration: underline;\n}\n\n.callout a:hover {\n  color: #e9ecef;\n}\n\n.callout p:last-child {\n  margin-bottom: 0;\n}\n\n.callout.callout-danger {\n  border-left-color: #bd2130;\n}\n\n.callout.callout-warning {\n  border-left-color: #d39e00;\n}\n\n.callout.callout-info {\n  border-left-color: #117a8b;\n}\n\n.callout.callout-success {\n  border-left-color: #1e7e34;\n}\n\n.dark-mode .callout {\n  background-color: #3f474e;\n}\n\n.alert .icon {\n  margin-right: 10px;\n}\n\n.alert .close {\n  color: #000;\n  opacity: .2;\n}\n\n.alert .close:hover {\n  opacity: .5;\n}\n\n.alert a {\n  color: #fff;\n  text-decoration: underline;\n}\n\n.alert-primary {\n  color: #fff;\n  background-color: #007bff;\n  border-color: #006fe6;\n}\n\n.alert-default-primary {\n  color: #004085;\n  background-color: #cce5ff;\n  border-color: #b8daff;\n}\n\n.alert-default-primary hr {\n  border-top-color: #9fcdff;\n}\n\n.alert-default-primary .alert-link {\n  color: #002752;\n}\n\n.alert-secondary {\n  color: #fff;\n  background-color: #6c757d;\n  border-color: #60686f;\n}\n\n.alert-default-secondary {\n  color: #383d41;\n  background-color: #e2e3e5;\n  border-color: #d6d8db;\n}\n\n.alert-default-secondary hr {\n  border-top-color: #c8cbcf;\n}\n\n.alert-default-secondary .alert-link {\n  color: #202326;\n}\n\n.alert-success {\n  color: #fff;\n  background-color: #28a745;\n  border-color: #23923d;\n}\n\n.alert-default-success {\n  color: #155724;\n  background-color: #d4edda;\n  border-color: #c3e6cb;\n}\n\n.alert-default-success hr {\n  border-top-color: #b1dfbb;\n}\n\n.alert-default-success .alert-link {\n  color: #0b2e13;\n}\n\n.alert-info {\n  color: #fff;\n  background-color: #17a2b8;\n  border-color: #148ea1;\n}\n\n.alert-default-info {\n  color: #0c5460;\n  background-color: #d1ecf1;\n  border-color: #bee5eb;\n}\n\n.alert-default-info hr {\n  border-top-color: #abdde5;\n}\n\n.alert-default-info .alert-link {\n  color: #062c33;\n}\n\n.alert-warning {\n  color: #1f2d3d;\n  background-color: #ffc107;\n  border-color: #edb100;\n}\n\n.alert-default-warning {\n  color: #856404;\n  background-color: #fff3cd;\n  border-color: #ffeeba;\n}\n\n.alert-default-warning hr {\n  border-top-color: #ffe8a1;\n}\n\n.alert-default-warning .alert-link {\n  color: #533f03;\n}\n\n.alert-danger {\n  color: #fff;\n  background-color: #dc3545;\n  border-color: #d32535;\n}\n\n.alert-default-danger {\n  color: #721c24;\n  background-color: #f8d7da;\n  border-color: #f5c6cb;\n}\n\n.alert-default-danger hr {\n  border-top-color: #f1b0b7;\n}\n\n.alert-default-danger .alert-link {\n  color: #491217;\n}\n\n.alert-light {\n  color: #1f2d3d;\n  background-color: #f8f9fa;\n  border-color: #e9ecef;\n}\n\n.alert-default-light {\n  color: #818182;\n  background-color: #fefefe;\n  border-color: #fdfdfe;\n}\n\n.alert-default-light hr {\n  border-top-color: #ececf6;\n}\n\n.alert-default-light .alert-link {\n  color: #686868;\n}\n\n.alert-dark {\n  color: #fff;\n  background-color: #343a40;\n  border-color: #292d32;\n}\n\n.alert-default-dark {\n  color: #1b1e21;\n  background-color: #d6d8d9;\n  border-color: #c6c8ca;\n}\n\n.alert-default-dark hr {\n  border-top-color: #b9bbbe;\n}\n\n.alert-default-dark .alert-link {\n  color: #040505;\n}\n\n.table:not(.table-dark) {\n  color: inherit;\n}\n\n.table.table-head-fixed thead tr:nth-child(1) th {\n  background-color: #fff;\n  border-bottom: 0;\n  box-shadow: inset 0 1px 0 #dee2e6, inset 0 -1px 0 #dee2e6;\n  position: sticky;\n  top: 0;\n  z-index: 10;\n}\n\n.table.table-head-fixed.table-dark thead tr:nth-child(1) th {\n  background-color: #212529;\n  box-shadow: inset 0 1px 0 #383f45, inset 0 -1px 0 #383f45;\n}\n\n.table.no-border,\n.table.no-border td,\n.table.no-border th {\n  border: 0;\n}\n\n.table.text-center,\n.table.text-center td,\n.table.text-center th {\n  text-align: center;\n}\n\n.table.table-valign-middle thead > tr > th,\n.table.table-valign-middle thead > tr > td,\n.table.table-valign-middle tbody > tr > th,\n.table.table-valign-middle tbody > tr > td {\n  vertical-align: middle;\n}\n\n.card-body.p-0 .table thead > tr > th:first-of-type,\n.card-body.p-0 .table thead > tr > td:first-of-type,\n.card-body.p-0 .table tfoot > tr > th:first-of-type,\n.card-body.p-0 .table tfoot > tr > td:first-of-type,\n.card-body.p-0 .table tbody > tr > th:first-of-type,\n.card-body.p-0 .table tbody > tr > td:first-of-type {\n  padding-left: 1.5rem;\n}\n\n.card-body.p-0 .table thead > tr > th:last-of-type,\n.card-body.p-0 .table thead > tr > td:last-of-type,\n.card-body.p-0 .table tfoot > tr > th:last-of-type,\n.card-body.p-0 .table tfoot > tr > td:last-of-type,\n.card-body.p-0 .table tbody > tr > th:last-of-type,\n.card-body.p-0 .table tbody > tr > td:last-of-type {\n  padding-right: 1.5rem;\n}\n\n.table-hover tbody tr.expandable-body:hover {\n  background-color: inherit !important;\n}\n\n[data-widget=\"expandable-table\"] {\n  cursor: pointer;\n}\n\n[data-widget=\"expandable-table\"] i {\n  transition: transform 0.3s linear;\n}\n\n[data-widget=\"expandable-table\"][aria-expanded=\"true\"] td > i[class*=\"right\"] {\n  transform: rotate(90deg);\n}\n\n[data-widget=\"expandable-table\"][aria-expanded=\"true\"] td > i[class*=\"left\"] {\n  transform: rotate(-90deg);\n}\n\n.expandable-body > td {\n  padding: 0 !important;\n  width: 100%;\n}\n\n.expandable-body > td > div,\n.expandable-body > td > p {\n  padding: 0.75rem;\n}\n\n.expandable-body .table {\n  width: calc(100% - 0.75rem);\n  margin: 0 0 0 0.75rem;\n}\n\n.expandable-body .table tr:first-child td,\n.expandable-body .table tr:first-child th {\n  border-top: none;\n}\n\n.dark-mode .table-bordered,\n.dark-mode .table-bordered td,\n.dark-mode .table-bordered th {\n  border-color: #6c757d;\n}\n\n.dark-mode .table-hover tbody tr:hover {\n  color: #dee2e6;\n  background-color: #3a4047;\n  border-color: #6c757d;\n}\n\n.dark-mode .table thead th {\n  border-bottom-color: #6c757d;\n}\n\n.dark-mode .table th,\n.dark-mode .table td {\n  border-top-color: #6c757d;\n}\n\n.dark-mode .table.table-head-fixed thead tr:nth-child(1) th {\n  background-color: #3f474e;\n}\n\n.carousel-control-prev .carousel-control-custom-icon {\n  margin-left: -20px;\n}\n\n.carousel-control-next .carousel-control-custom-icon {\n  margin-right: 20px;\n}\n\n.carousel-control-custom-icon > .fa,\n.carousel-control-custom-icon > .fas,\n.carousel-control-custom-icon > .far,\n.carousel-control-custom-icon > .fab,\n.carousel-control-custom-icon > .fal,\n.carousel-control-custom-icon > .fad,\n.carousel-control-custom-icon > .svg-inline--fa,\n.carousel-control-custom-icon > .ion {\n  display: inline-block;\n  font-size: 40px;\n  margin-top: -20px;\n  position: absolute;\n  top: 50%;\n  z-index: 5;\n}\n\n.close {\n  float: right;\n  font-size: 1.5rem;\n  font-weight: 700;\n  line-height: 1;\n  color: #000;\n  text-shadow: 0 1px 0 #fff;\n  opacity: .5;\n}\n\n.close:hover {\n  color: #000;\n  text-decoration: none;\n}\n\n.close:not(:disabled):not(.disabled):hover, .close:not(:disabled):not(.disabled):focus {\n  opacity: .75;\n}\n\n.close:focus {\n  outline: none;\n}\n\nbutton.close {\n  padding: 0;\n  background-color: transparent;\n  border: 0;\n}\n\na.close.disabled {\n  pointer-events: none;\n}\n\n/*# sourceMappingURL=adminlte.components.css.map */", "//\n// Component: Carousel\n//\n\n.carousel-control-custom-icon {\n  .carousel-control-prev & {\n    margin-left: -20px;\n  }\n  .carousel-control-next & {\n    margin-right: 20px;\n  }\n\n  > .fa,\n  > .fas,\n  > .far,\n  > .fab,\n  > .fal,\n  > .fad,\n  > .svg-inline--fa,\n  > .ion {\n    display: inline-block;\n    font-size: 40px;\n    margin-top: -20px;\n    position: absolute;\n    top: 50%;\n    z-index: 5;\n  }\n}\n", ".close {\n  float: right;\n  @include font-size($close-font-size);\n  font-weight: $close-font-weight;\n  line-height: 1;\n  color: $close-color;\n  text-shadow: $close-text-shadow;\n  opacity: .5;\n\n  // Override <a>'s hover style\n  @include hover() {\n    color: $close-color;\n    text-decoration: none;\n  }\n\n  &:not(:disabled):not(.disabled) {\n    @include hover-focus() {\n      opacity: .75;\n    }\n  }\n\n  &:focus {\n    outline: none;\n  }\n}\n\n// Additional properties for button version\n// iOS requires the button element instead of an anchor tag.\n// If you want the anchor version, it requires `href=\"#\"`.\n// See https://developer.mozilla.org/en-US/docs/Web/Events/click#Safari_Mobile\n\n// stylelint-disable-next-line selector-no-qualifying-type\nbutton.close {\n  padding: 0;\n  background-color: transparent;\n  border: 0;\n}\n\n// Future-proof disabling of clicks on `<a>` elements\n\n// stylelint-disable-next-line selector-no-qualifying-type\na.close.disabled {\n  pointer-events: none;\n}\n", "// Hover mixin and `$enable-hover-media-query` are deprecated.\n//\n// Originally added during our alphas and maintained during betas, this mixin was\n// designed to prevent `:hover` stickiness on iOS-an issue where hover styles\n// would persist after initial touch.\n//\n// For backward compatibility, we've kept these mixins and updated them to\n// always return their regular pseudo-classes instead of a shimmed media query.\n//\n// Issue: https://github.com/twbs/bootstrap/issues/25195\n\n@mixin hover() {\n  &:hover { @content; }\n}\n\n@mixin hover-focus() {\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin plain-hover-focus() {\n  &,\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin hover-focus-active() {\n  &:hover,\n  &:focus,\n  &:active {\n    @content;\n  }\n}\n"]}