{"version": 3, "sources": ["../../../build/scss/parts/adminlte.components.scss", "adminlte.components.css", "../../../build/scss/_forms.scss", "../../../build/scss/_bootstrap-variables.scss", "../../../node_modules/bootstrap/scss/mixins/_border-radius.scss", "../../../node_modules/bootstrap/scss/vendor/_rfs.scss", "../../../node_modules/bootstrap/scss/mixins/_gradients.scss", "../../../build/scss/mixins/_custom-forms.scss", "../../../build/scss/_variables.scss", "../../../build/scss/_progress-bars.scss", "../../../node_modules/bootstrap/scss/mixins/_box-shadow.scss", "../../../build/scss/mixins/_cards.scss", "../../../build/scss/_cards.scss", "../../../node_modules/bootstrap/scss/mixins/_clearfix.scss", "../../../node_modules/bootstrap/scss/mixins/_lists.scss", "../../../build/scss/_modals.scss", "../../../build/scss/_toasts.scss", "../../../build/scss/mixins/_toasts.scss", "../../../build/scss/_buttons.scss", "../../../node_modules/bootstrap/scss/mixins/_buttons.scss", "../../../build/scss/_callout.scss", "../../../build/scss/_alerts.scss", "../../../node_modules/bootstrap/scss/mixins/_alert.scss", "../../../node_modules/bootstrap/scss/_functions.scss", "../../../build/scss/_table.scss", "../../../build/scss/_carousel.scss", "../../../build/scss/_close.scss", "../../../node_modules/bootstrap/scss/mixins/_hover.scss"], "names": [], "mappings": "AAAA;;;;;;ECME;ACFF;EAEI,kBAAkB;ADGtB;;ACLA;EAKM,mBAAmB;ADIzB;;ACTA;EASM,6BAA6B;EAC7B,SAAS;EACT,eAAe;EACf,eAAe;EAEf,yBCuT8B;EDtT9B,kBAAkB;EAClB,UAAU;EACV,MAAM;ADGZ;;ACGA;EETI,gBFa0B;ADH9B;;ACSA;EASI,gCC0X0F;AFxY9F;;ACkBA;;;;;;;;;EAUI,iCCkX6F;AFlYjG;;ACoBA;EAUM,iCCoW2F;AF9XjG;;AC+BA;;;;;;;;;EAUI,kCCkV6F;AF/WjG;;ACiCA;EAUM,kCCoU2F;AF3WjG;;AC4CA;EACE,gBCsI+B;AF/KjC;;AC4CA;EGxCI,cAAW;EH0Cb,cCxEe;EDyEf,aAAa;EACb,mBC8T4C;ED7T5C,WAAW;ADzCb;;AC4CA;EEtGI,sBDmMgC;EEtF9B,mBAtCY;EHkChB,wCCjFe;EDkFf,cC/BqB;EDgCrB,aAAa;EACb,gBCwH+B;EDvH/B,iBAAiB;EACjB,eAAe;EACf,uBCqjBiC;EDpjBjC,kBAAkB;EAClB,SAAS;EACT,UAAU;ADzCZ;;AC4CA;EAEI,qBC/Fa;AFqDjB;;ACwCA;EAaM,qBC1GW;ED2GX,2CC3GW;AF0DjB;;ACmCA;;EAmBM,cAAc;ADjDpB;;ACuDA;EAGM,sBCiQyG;EDhQzG,kFCkQ+F;AFxTrG;;AC2DA;EAEI,qBCjIa;AFwEjB;;ACuDA;EAUM,qBCzIW;ED0IX,2CC1IW;AF6EjB;;ACkDA;;EAgBM,cAAc;AD7DpB;;ACmEA;;EAIM,cAAc;ADlEpB;;ACuEA;EAGM,cCjKW;AF2FjB;;ACmEA;;EAQM,cAAc;ADtEpB;;AC2EA;EAEI,cC7Ka;AFoGjB;;ACuEA;EAKM,qBChLW;AFwGjB;;ACmEA;;EAWI,cAAc;ADzElB;;AC8DA;EI/MI,yBJ+N2D;EACzD,qBAAkD;AD1ExD;;ACyDA;EAuBM,2CClMW;AFsHjB;;ACqDA;EA2BM,qBCtMW;AF0HjB;;ACkFA;EAGM,qBC/MW;AF8HjB;;AC8EA;;EAQM,cAAc;ADjFpB;;ACyEA;EAaQ,qBCzNS;ED0NT,2CC1NS;AFwIjB;;ACyFA;EAEI,mBC9BkD;AFzDtD;;AC4FA;;EAGI,aAAa;EACb,cAAc;EACd,eAAe;EACf,gBAAgB;EAChB,mBAAmB;AD1FvB;;ACmFA;;EAUM,wBAAwB;ADxF9B;;AC8EA;;EAaM,wBAAwB;ADtF9B;;AMlMI;EACE,yBAAkB;EAClB,qBAAiC;ANqMvC;;AMlMI;EACE,6DJuBW;AF8KjB;;AMlMI;EACE,yBAAqC;ANqM3C;;AMhMI;EACE,yBAAkB;EAClB,qBAAiC;ANmMvC;;AMhMI;EACE,6DJQW;AF2LjB;;AMhMI;EACE,yBAAsC;ANmM5C;;AM5NI;EACE,yBAAkB;EAClB,qBAAiC;AN+NvC;;AM5NI;EACE,+DJIY;AF2NlB;;AM5NI;EACE,yBAAqC;AN+N3C;;AM1NI;EACE,yBAAkB;EAClB,qBAAiC;AN6NvC;;AM1NI;EACE,+DJXY;AFwOlB;;AM1NI;EACE,yBAAsC;AN6N5C;;AMtPI;EACE,yBAAkB;EAClB,qBAAiC;ANyPvC;;AMtPI;EACE,6DJ8BW;AF2NjB;;AMtPI;EACE,yBAAqC;ANyP3C;;AMpPI;EACE,yBAAkB;EAClB,qBAAiC;ANuPvC;;AMpPI;EACE,6DJeW;AFwOjB;;AMpPI;EACE,yBAAsC;ANuP5C;;AMhRI;EACE,yBAAkB;EAClB,qBAAiC;ANmRvC;;AMhRI;EACE,8DJgCW;AFmPjB;;AMhRI;EACE,yBAAqC;ANmR3C;;AM9QI;EACE,yBAAkB;EAClB,qBAAiC;ANiRvC;;AM9QI;EACE,8DJiBW;AFgQjB;;AM9QI;EACE,yBAAsC;ANiR5C;;AM1SI;EACE,yBAAkB;EAClB,qBAAiC;AN6SvC;;AM1SI;EACE,6DJ6BW;AFgRjB;;AM1SI;EACE,yBAAqC;AN6S3C;;AMxSI;EACE,yBAAkB;EAClB,qBAAiC;AN2SvC;;AMxSI;EACE,6DJcW;AF6RjB;;AMxSI;EACE,yBAAsC;AN2S5C;;AMpUI;EACE,yBAAkB;EAClB,qBAAiC;ANuUvC;;AMpUI;EACE,6DJ2BW;AF4SjB;;AMpUI;EACE,yBAAqC;ANuU3C;;AMlUI;EACE,yBAAkB;EAClB,qBAAiC;ANqUvC;;AMlUI;EACE,6DJYW;AFyTjB;;AMlUI;EACE,yBAAsC;ANqU5C;;AM9VI;EACE,yBAAkB;EAClB,qBAAiC;ANiWvC;;AM9VI;EACE,+DJDY;AFkWlB;;AM9VI;EACE,yBAAqC;ANiW3C;;AM5VI;EACE,yBAAkB;EAClB,qBAAiC;AN+VvC;;AM5VI;EACE,+DJhBY;AF+WlB;;AM5VI;EACE,uBAAsC;AN+V5C;;AMxXI;EACE,yBAAkB;EAClB,qBAAiC;AN2XvC;;AMxXI;EACE,4DJMY;AFqXlB;;AMxXI;EACE,uBAAqC;AN2X3C;;AMtXI;EACE,yBAAkB;EAClB,qBAAiC;ANyXvC;;AMtXI;EACE,4DJTY;AFkYlB;;AMtXI;EACE,yBAAsC;ANyX5C;;AMlZI;EACE,yBAAkB;EAClB,qBAAiC;ANqZvC;;AMlZI;EACE,8DCNa;AP2ZnB;;AMlZI;EACE,yBAAqC;ANqZ3C;;AMhZI;EACE,yBAAkB;EAClB,qBAAiC;ANmZvC;;AMhZI;EACE,8DCrBa;APwanB;;AMhZI;EACE,yBAAsC;ANmZ5C;;AM5aI;EACE,yBAAkB;EAClB,mBAAiC;AN+avC;;AM5aI;EACE,2DCLQ;APobd;;AM5aI;EACE,uBAAqC;AN+a3C;;AM1aI;EACE,yBAAkB;EAClB,mBAAiC;AN6avC;;AM1aI;EACE,2DCpBQ;APicd;;AM1aI;EACE,yBAAsC;AN6a5C;;AMtcI;EACE,yBAAkB;EAClB,qBAAiC;ANycvC;;AMtcI;EACE,8DCHS;AP4cf;;AMtcI;EACE,yBAAqC;ANyc3C;;AMpcI;EACE,yBAAkB;EAClB,qBAAiC;ANucvC;;AMpcI;EACE,8DClBS;APydf;;AMpcI;EACE,yBAAsC;ANuc5C;;AMheI;EACE,yBAAkB;EAClB,qBAAiC;ANmevC;;AMheI;EACE,6DCFQ;APqed;;AMheI;EACE,yBAAqC;ANme3C;;AM9dI;EACE,yBAAkB;EAClB,qBAAiC;ANievC;;AM9dI;EACE,6DCjBQ;APkfd;;AM9dI;EACE,yBAAsC;ANie5C;;AM1fI;EACE,yBAAkB;EAClB,qBAAiC;AN6fvC;;AM1fI;EACE,8DCAW;AP6fjB;;AM1fI;EACE,yBAAqC;AN6f3C;;AMxfI;EACE,yBAAkB;EAClB,qBAAiC;AN2fvC;;AMxfI;EACE,8DCfW;AP0gBjB;;AMxfI;EACE,yBAAsC;AN2f5C;;AMphBI;EACE,yBAAkB;EAClB,qBAAiC;ANuhBvC;;AMphBI;EACE,6DCEU;APqhBhB;;AMphBI;EACE,yBAAqC;ANuhB3C;;AMlhBI;EACE,yBAAkB;EAClB,qBAAiC;ANqhBvC;;AMlhBI;EACE,6DCbU;APkiBhB;;AMlhBI;EACE,yBAAsC;ANqhB5C;;AM9iBI;EACE,yBAAkB;EAClB,qBAAiC;ANijBvC;;AM9iBI;EACE,6DJuBW;AF0hBjB;;AM9iBI;EACE,yBAAqC;ANijB3C;;AM5iBI;EACE,yBAAkB;EAClB,qBAAiC;AN+iBvC;;AM5iBI;EACE,6DJQW;AFuiBjB;;AM5iBI;EACE,yBAAsC;AN+iB5C;;AMxkBI;EACE,yBAAkB;EAClB,qBAAiC;AN2kBvC;;AMxkBI;EACE,8DJwBW;AFmjBjB;;AMxkBI;EACE,yBAAqC;AN2kB3C;;AMtkBI;EACE,yBAAkB;EAClB,qBAAiC;ANykBvC;;AMtkBI;EACE,8DJSW;AFgkBjB;;AMtkBI;EACE,yBAAsC;ANykB5C;;AMlmBI;EACE,yBAAkB;EAClB,qBAAiC;ANqmBvC;;AMlmBI;EACE,8DJyBW;AF4kBjB;;AMlmBI;EACE,yBAAqC;ANqmB3C;;AMhmBI;EACE,yBAAkB;EAClB,qBAAiC;ANmmBvC;;AMhmBI;EACE,8DJUW;AFylBjB;;AMhmBI;EACE,yBAAsC;ANmmB5C;;AM5nBI;EACE,yBAAkB;EAClB,qBAAiC;AN+nBvC;;AM5nBI;EACE,8DJ0BW;AFqmBjB;;AM5nBI;EACE,yBAAqC;AN+nB3C;;AM1nBI;EACE,yBAAkB;EAClB,qBAAiC;AN6nBvC;;AM1nBI;EACE,8DJWW;AFknBjB;;AM1nBI;EACE,yBAAsC;AN6nB5C;;AMtpBI;EACE,yBAAkB;EAClB,qBAAiC;ANypBvC;;AMtpBI;EACE,6DJ2BW;AF8nBjB;;AMtpBI;EACE,yBAAqC;ANypB3C;;AMppBI;EACE,yBAAkB;EAClB,qBAAiC;ANupBvC;;AMppBI;EACE,6DJYW;AF2oBjB;;AMppBI;EACE,yBAAsC;ANupB5C;;AMhrBI;EACE,yBAAkB;EAClB,qBAAiC;ANmrBvC;;AMhrBI;EACE,8DJ4BW;AFupBjB;;AMhrBI;EACE,yBAAqC;ANmrB3C;;AM9qBI;EACE,yBAAkB;EAClB,qBAAiC;ANirBvC;;AM9qBI;EACE,8DJaW;AFoqBjB;;AM9qBI;EACE,yBAAsC;ANirB5C;;AM1sBI;EACE,yBAAkB;EAClB,qBAAiC;AN6sBvC;;AM1sBI;EACE,6DJ6BW;AFgrBjB;;AM1sBI;EACE,yBAAqC;AN6sB3C;;AMxsBI;EACE,yBAAkB;EAClB,qBAAiC;AN2sBvC;;AMxsBI;EACE,6DJcW;AF6rBjB;;AMxsBI;EACE,yBAAsC;AN2sB5C;;AMpuBI;EACE,yBAAkB;EAClB,qBAAiC;ANuuBvC;;AMpuBI;EACE,6DJ8BW;AFysBjB;;AMpuBI;EACE,yBAAqC;ANuuB3C;;AMluBI;EACE,yBAAkB;EAClB,qBAAiC;ANquBvC;;AMluBI;EACE,6DJeW;AFstBjB;;AMluBI;EACE,yBAAsC;ANquB5C;;AM9vBI;EACE,yBAAkB;EAClB,qBAAiC;ANiwBvC;;AM9vBI;EACE,8DJ+BW;AFkuBjB;;AM9vBI;EACE,yBAAqC;ANiwB3C;;AM5vBI;EACE,yBAAkB;EAClB,qBAAiC;AN+vBvC;;AM5vBI;EACE,8DJgBW;AF+uBjB;;AM5vBI;EACE,yBAAsC;AN+vB5C;;AMxxBI;EACE,yBAAkB;EAClB,qBAAiC;AN2xBvC;;AMxxBI;EACE,8DJgCW;AF2vBjB;;AMxxBI;EACE,yBAAqC;AN2xB3C;;AMtxBI;EACE,yBAAkB;EAClB,qBAAiC;ANyxBvC;;AMtxBI;EACE,8DJiBW;AFwwBjB;;AMtxBI;EACE,yBAAsC;ANyxB5C;;AMlzBI;EACE,sBAAkB;EAClB,qBAAiC;ANqzBvC;;AMlzBI;EACE,+DJFS;AFuzBf;;AMlzBI;EACE,yBAAqC;ANqzB3C;;AMhzBI;EACE,sBAAkB;EAClB,qBAAiC;ANmzBvC;;AMhzBI;EACE,+DJjBS;AFo0Bf;;AMhzBI;EACE,uBAAsC;ANmzB5C;;AM50BI;EACE,yBAAkB;EAClB,qBAAiC;AN+0BvC;;AM50BI;EACE,+DJIY;AF20BlB;;AM50BI;EACE,yBAAqC;AN+0B3C;;AM10BI;EACE,yBAAkB;EAClB,qBAAiC;AN60BvC;;AM10BI;EACE,+DJXY;AFw1BlB;;AM10BI;EACE,yBAAsC;AN60B5C;;AMt2BI;EACE,yBAAkB;EAClB,qBAAiC;ANy2BvC;;AMt2BI;EACE,4DJMY;AFm2BlB;;AMt2BI;EACE,uBAAqC;ANy2B3C;;AMp2BI;EACE,yBAAkB;EAClB,qBAAiC;ANu2BvC;;AMp2BI;EACE,4DJTY;AFg3BlB;;AMp2BI;EACE,yBAAsC;ANu2B5C;;AM/1BI;EACE,aAAa;ANk2BnB;;AMh2BM;EACE,6DJRS;AF22BjB;;AMh2BM;EACE,6DJZS;AF+2BjB;;AMh2BM;EACE,6DJhBS;AFm3BjB;;AM/1BI;EACE,yBJrBW;AFu3BjB;;AMh2BM;EACE,yBAAsC;ANm2B9C;;AM/1BI;EACE,yBJ7BW;AF+3BjB;;AMh2BM;EACE,yBAAsC;ANm2B9C;;AM/1BI;EACE,yBJrCW;AFu4BjB;;AMh2BM;EACE,yBAAsC;ANm2B9C;;AMv4BI;EACE,aAAa;AN04BnB;;AMx4BM;EACE,+DJ3BU;AFs6BlB;;AMx4BM;EACE,+DJ/BU;AF06BlB;;AMx4BM;EACE,+DJnCU;AF86BlB;;AMv4BI;EACE,yBJxCY;AFk7BlB;;AMx4BM;EACE,yBAAsC;AN24B9C;;AMv4BI;EACE,yBJhDY;AF07BlB;;AMx4BM;EACE,yBAAsC;AN24B9C;;AMv4BI;EACE,yBJxDY;AFk8BlB;;AMx4BM;EACE,yBAAsC;AN24B9C;;AM/6BI;EACE,aAAa;ANk7BnB;;AMh7BM;EACE,6DJDS;AFo7BjB;;AMh7BM;EACE,6DJLS;AFw7BjB;;AMh7BM;EACE,6DJTS;AF47BjB;;AM/6BI;EACE,yBJdW;AFg8BjB;;AMh7BM;EACE,yBAAsC;ANm7B9C;;AM/6BI;EACE,yBJtBW;AFw8BjB;;AMh7BM;EACE,yBAAsC;ANm7B9C;;AM/6BI;EACE,yBJ9BW;AFg9BjB;;AMh7BM;EACE,yBAAsC;ANm7B9C;;AMv9BI;EACE,aAAa;AN09BnB;;AMx9BM;EACE,8DJCS;AF09BjB;;AMx9BM;EACE,8DJHS;AF89BjB;;AMx9BM;EACE,8DJPS;AFk+BjB;;AMv9BI;EACE,yBJZW;AFs+BjB;;AMx9BM;EACE,yBAAsC;AN29B9C;;AMv9BI;EACE,yBJpBW;AF8+BjB;;AMx9BM;EACE,yBAAsC;AN29B9C;;AMv9BI;EACE,yBJ5BW;AFs/BjB;;AMx9BM;EACE,yBAAsC;AN29B9C;;AM//BI;EACE,aAAa;ANkgCnB;;AMhgCM;EACE,6DJFS;AFqgCjB;;AMhgCM;EACE,6DJNS;AFygCjB;;AMhgCM;EACE,6DJVS;AF6gCjB;;AM//BI;EACE,yBJfW;AFihCjB;;AMhgCM;EACE,yBAAsC;ANmgC9C;;AM//BI;EACE,yBJvBW;AFyhCjB;;AMhgCM;EACE,yBAAsC;ANmgC9C;;AM//BI;EACE,yBJ/BW;AFiiCjB;;AMhgCM;EACE,yBAAsC;ANmgC9C;;AMviCI;EACE,aAAa;AN0iCnB;;AMxiCM;EACE,6DJJS;AF+iCjB;;AMxiCM;EACE,6DJRS;AFmjCjB;;AMxiCM;EACE,6DJZS;AFujCjB;;AMviCI;EACE,yBJjBW;AF2jCjB;;AMxiCM;EACE,yBAAsC;AN2iC9C;;AMviCI;EACE,yBJzBW;AFmkCjB;;AMxiCM;EACE,yBAAsC;AN2iC9C;;AMviCI;EACE,yBJjCW;AF2kCjB;;AMxiCM;EACE,yBAAsC;AN2iC9C;;AM/kCI;EACE,aAAa;ANklCnB;;AMhlCM;EACE,+DJhCU;AFmnClB;;AMhlCM;EACE,+DJpCU;AFunClB;;AMhlCM;EACE,+DJxCU;AF2nClB;;AM/kCI;EACE,yBJ7CY;AF+nClB;;AMhlCM;EACE,uBAAsC;ANmlC9C;;AM/kCI;EACE,yBJrDY;AFuoClB;;AMhlCM;EACE,uBAAsC;ANmlC9C;;AM/kCI;EACE,yBJ7DY;AF+oClB;;AMhlCM;EACE,uBAAsC;ANmlC9C;;AMvnCI;EACE,aAAa;AN0nCnB;;AMxnCM;EACE,4DJzBU;AFopClB;;AMxnCM;EACE,4DJ7BU;AFwpClB;;AMxnCM;EACE,4DJjCU;AF4pClB;;AMvnCI;EACE,yBJtCY;AFgqClB;;AMxnCM;EACE,yBAAsC;AN2nC9C;;AMvnCI;EACE,yBJ9CY;AFwqClB;;AMxnCM;EACE,yBAAsC;AN2nC9C;;AMvnCI;EACE,yBJtDY;AFgrClB;;AMxnCM;EACE,yBAAsC;AN2nC9C;;AM/pCI;EACE,aAAa;ANkqCnB;;AMhqCM;EACE,8DCrCW;APwsCnB;;AMhqCM;EACE,8DCzCW;AP4sCnB;;AMhqCM;EACE,8DC7CW;APgtCnB;;AM/pCI;EACE,yBClDa;APotCnB;;AMhqCM;EACE,yBAAsC;ANmqC9C;;AM/pCI;EACE,yBC1Da;AP4tCnB;;AMhqCM;EACE,yBAAsC;ANmqC9C;;AM/pCI;EACE,yBClEa;APouCnB;;AMhqCM;EACE,yBAAsC;ANmqC9C;;AMvsCI;EACE,aAAa;AN0sCnB;;AMxsCM;EACE,2DCpCM;AP+uCd;;AMxsCM;EACE,2DCxCM;APmvCd;;AMxsCM;EACE,2DC5CM;APuvCd;;AMvsCI;EACE,yBCjDQ;AP2vCd;;AMxsCM;EACE,yBAAsC;AN2sC9C;;AMvsCI;EACE,yBCzDQ;APmwCd;;AMxsCM;EACE,yBAAsC;AN2sC9C;;AMvsCI;EACE,yBCjEQ;AP2wCd;;AMxsCM;EACE,yBAAsC;AN2sC9C;;AM/uCI;EACE,aAAa;ANkvCnB;;AMhvCM;EACE,8DClCO;APqxCf;;AMhvCM;EACE,8DCtCO;APyxCf;;AMhvCM;EACE,8DC1CO;AP6xCf;;AM/uCI;EACE,yBC/CS;APiyCf;;AMhvCM;EACE,yBAAsC;ANmvC9C;;AM/uCI;EACE,yBCvDS;APyyCf;;AMhvCM;EACE,yBAAsC;ANmvC9C;;AM/uCI;EACE,yBC/DS;APizCf;;AMhvCM;EACE,yBAAsC;ANmvC9C;;AMvxCI;EACE,aAAa;AN0xCnB;;AMxxCM;EACE,6DCjCM;AP4zCd;;AMxxCM;EACE,6DCrCM;APg0Cd;;AMxxCM;EACE,6DCzCM;APo0Cd;;AMvxCI;EACE,yBC9CQ;APw0Cd;;AMxxCM;EACE,yBAAsC;AN2xC9C;;AMvxCI;EACE,yBCtDQ;APg1Cd;;AMxxCM;EACE,yBAAsC;AN2xC9C;;AMvxCI;EACE,yBC9DQ;APw1Cd;;AMxxCM;EACE,yBAAsC;AN2xC9C;;AM/zCI;EACE,aAAa;ANk0CnB;;AMh0CM;EACE,8DC/BS;APk2CjB;;AMh0CM;EACE,8DCnCS;APs2CjB;;AMh0CM;EACE,8DCvCS;AP02CjB;;AM/zCI;EACE,yBC5CW;AP82CjB;;AMh0CM;EACE,yBAAsC;ANm0C9C;;AM/zCI;EACE,yBCpDW;APs3CjB;;AMh0CM;EACE,yBAAsC;ANm0C9C;;AM/zCI;EACE,yBC5DW;AP83CjB;;AMh0CM;EACE,yBAAsC;ANm0C9C;;AMv2CI;EACE,aAAa;AN02CnB;;AMx2CM;EACE,6DC7BQ;APw4ChB;;AMx2CM;EACE,6DCjCQ;AP44ChB;;AMx2CM;EACE,6DCrCQ;APg5ChB;;AMv2CI;EACE,yBC1CU;APo5ChB;;AMx2CM;EACE,yBAAsC;AN22C9C;;AMv2CI;EACE,yBClDU;AP45ChB;;AMx2CM;EACE,yBAAsC;AN22C9C;;AMv2CI;EACE,yBC1DU;APo6ChB;;AMx2CM;EACE,yBAAsC;AN22C9C;;AM/4CI;EACE,aAAa;ANk5CnB;;AMh5CM;EACE,6DJRS;AF25CjB;;AMh5CM;EACE,6DJZS;AF+5CjB;;AMh5CM;EACE,6DJhBS;AFm6CjB;;AM/4CI;EACE,yBJrBW;AFu6CjB;;AMh5CM;EACE,yBAAsC;ANm5C9C;;AM/4CI;EACE,yBJ7BW;AF+6CjB;;AMh5CM;EACE,yBAAsC;ANm5C9C;;AM/4CI;EACE,yBJrCW;AFu7CjB;;AMh5CM;EACE,yBAAsC;ANm5C9C;;AMv7CI;EACE,aAAa;AN07CnB;;AMx7CM;EACE,8DJPS;AFk8CjB;;AMx7CM;EACE,8DJXS;AFs8CjB;;AMx7CM;EACE,8DJfS;AF08CjB;;AMv7CI;EACE,yBJpBW;AF88CjB;;AMx7CM;EACE,yBAAsC;AN27C9C;;AMv7CI;EACE,yBJ5BW;AFs9CjB;;AMx7CM;EACE,yBAAsC;AN27C9C;;AMv7CI;EACE,yBJpCW;AF89CjB;;AMx7CM;EACE,yBAAsC;AN27C9C;;AM/9CI;EACE,aAAa;ANk+CnB;;AMh+CM;EACE,8DJNS;AFy+CjB;;AMh+CM;EACE,8DJVS;AF6+CjB;;AMh+CM;EACE,8DJdS;AFi/CjB;;AM/9CI;EACE,yBJnBW;AFq/CjB;;AMh+CM;EACE,yBAAsC;ANm+C9C;;AM/9CI;EACE,yBJ3BW;AF6/CjB;;AMh+CM;EACE,yBAAsC;ANm+C9C;;AM/9CI;EACE,yBJnCW;AFqgDjB;;AMh+CM;EACE,yBAAsC;ANm+C9C;;AMvgDI;EACE,aAAa;AN0gDnB;;AMxgDM;EACE,8DJLS;AFghDjB;;AMxgDM;EACE,8DJTS;AFohDjB;;AMxgDM;EACE,8DJbS;AFwhDjB;;AMvgDI;EACE,yBJlBW;AF4hDjB;;AMxgDM;EACE,yBAAsC;AN2gD9C;;AMvgDI;EACE,yBJ1BW;AFoiDjB;;AMxgDM;EACE,yBAAsC;AN2gD9C;;AMvgDI;EACE,yBJlCW;AF4iDjB;;AMxgDM;EACE,yBAAsC;AN2gD9C;;AM/iDI;EACE,aAAa;ANkjDnB;;AMhjDM;EACE,6DJJS;AFujDjB;;AMhjDM;EACE,6DJRS;AF2jDjB;;AMhjDM;EACE,6DJZS;AF+jDjB;;AM/iDI;EACE,yBJjBW;AFmkDjB;;AMhjDM;EACE,yBAAsC;ANmjD9C;;AM/iDI;EACE,yBJzBW;AF2kDjB;;AMhjDM;EACE,yBAAsC;ANmjD9C;;AM/iDI;EACE,yBJjCW;AFmlDjB;;AMhjDM;EACE,yBAAsC;ANmjD9C;;AMvlDI;EACE,aAAa;AN0lDnB;;AMxlDM;EACE,8DJHS;AF8lDjB;;AMxlDM;EACE,8DJPS;AFkmDjB;;AMxlDM;EACE,8DJXS;AFsmDjB;;AMvlDI;EACE,yBJhBW;AF0mDjB;;AMxlDM;EACE,yBAAsC;AN2lD9C;;AMvlDI;EACE,yBJxBW;AFknDjB;;AMxlDM;EACE,yBAAsC;AN2lD9C;;AMvlDI;EACE,yBJhCW;AF0nDjB;;AMxlDM;EACE,yBAAsC;AN2lD9C;;AM/nDI;EACE,aAAa;ANkoDnB;;AMhoDM;EACE,6DJFS;AFqoDjB;;AMhoDM;EACE,6DJNS;AFyoDjB;;AMhoDM;EACE,6DJVS;AF6oDjB;;AM/nDI;EACE,yBJfW;AFipDjB;;AMhoDM;EACE,yBAAsC;ANmoD9C;;AM/nDI;EACE,yBJvBW;AFypDjB;;AMhoDM;EACE,yBAAsC;ANmoD9C;;AM/nDI;EACE,yBJ/BW;AFiqDjB;;AMhoDM;EACE,yBAAsC;ANmoD9C;;AMvqDI;EACE,aAAa;AN0qDnB;;AMxqDM;EACE,6DJDS;AF4qDjB;;AMxqDM;EACE,6DJLS;AFgrDjB;;AMxqDM;EACE,6DJTS;AForDjB;;AMvqDI;EACE,yBJdW;AFwrDjB;;AMxqDM;EACE,yBAAsC;AN2qD9C;;AMvqDI;EACE,yBJtBW;AFgsDjB;;AMxqDM;EACE,yBAAsC;AN2qD9C;;AMvqDI;EACE,yBJ9BW;AFwsDjB;;AMxqDM;EACE,yBAAsC;AN2qD9C;;AM/sDI;EACE,aAAa;ANktDnB;;AMhtDM;EACE,8DJAS;AFmtDjB;;AMhtDM;EACE,8DJJS;AFutDjB;;AMhtDM;EACE,8DJRS;AF2tDjB;;AM/sDI;EACE,yBJbW;AF+tDjB;;AMhtDM;EACE,yBAAsC;ANmtD9C;;AM/sDI;EACE,yBJrBW;AFuuDjB;;AMhtDM;EACE,yBAAsC;ANmtD9C;;AM/sDI;EACE,yBJ7BW;AF+uDjB;;AMhtDM;EACE,yBAAsC;ANmtD9C;;AMvvDI;EACE,aAAa;AN0vDnB;;AMxvDM;EACE,8DJCS;AF0vDjB;;AMxvDM;EACE,8DJHS;AF8vDjB;;AMxvDM;EACE,8DJPS;AFkwDjB;;AMvvDI;EACE,yBJZW;AFswDjB;;AMxvDM;EACE,yBAAsC;AN2vD9C;;AMvvDI;EACE,yBJpBW;AF8wDjB;;AMxvDM;EACE,yBAAsC;AN2vD9C;;AMvvDI;EACE,yBJ5BW;AFsxDjB;;AMxvDM;EACE,yBAAsC;AN2vD9C;;AM/xDI;EACE,aAAa;ANkyDnB;;AMhyDM;EACE,+DJjCO;AFo0Df;;AMhyDM;EACE,+DJrCO;AFw0Df;;AMhyDM;EACE,+DJzCO;AF40Df;;AM/xDI;EACE,sBJ9CS;AFg1Df;;AMhyDM;EACE,uBAAsC;ANmyD9C;;AM/xDI;EACE,sBJtDS;AFw1Df;;AMhyDM;EACE,uBAAsC;ANmyD9C;;AM/xDI;EACE,sBJ9DS;AFg2Df;;AMhyDM;EACE,uBAAsC;ANmyD9C;;AMv0DI;EACE,aAAa;AN00DnB;;AMx0DM;EACE,+DJ3BU;AFs2DlB;;AMx0DM;EACE,+DJ/BU;AF02DlB;;AMx0DM;EACE,+DJnCU;AF82DlB;;AMv0DI;EACE,yBJxCY;AFk3DlB;;AMx0DM;EACE,yBAAsC;AN20D9C;;AMv0DI;EACE,yBJhDY;AF03DlB;;AMx0DM;EACE,yBAAsC;AN20D9C;;AMv0DI;EACE,yBJxDY;AFk4DlB;;AMx0DM;EACE,yBAAsC;AN20D9C;;AM/2DI;EACE,aAAa;ANk3DnB;;AMh3DM;EACE,4DJzBU;AF44DlB;;AMh3DM;EACE,4DJ7BU;AFg5DlB;;AMh3DM;EACE,4DJjCU;AFo5DlB;;AM/2DI;EACE,yBJtCY;AFw5DlB;;AMh3DM;EACE,yBAAsC;ANm3D9C;;AM/2DI;EACE,yBJ9CY;AFg6DlB;;AMh3DM;EACE,yBAAsC;ANm3D9C;;AM/2DI;EACE,yBJtDY;AFw6DlB;;AMh3DM;EACE,yBAAsC;ANm3D9C;;AMt2DE;EAEI,qBJvDW;EG9Bb,yBH8Ba;AFg6DjB;;AM32DE;EAQM,wOAAoE;ANu2D5E;;AM/2DE;EAWM,kLAAiE;ANw2DzE;;AMn3DE;EAkBM,8EJvES;AF46DjB;;AMv3DE;EAyBI,qBAAkC;ANk2DxC;;AM33DE;EA6BI,yBAAsC;EACtC,qBAAkC;ANk2DxC;;AMh4DE;EAEI,qBJ1EY;EGXd,yBHWc;AF68DlB;;AMr4DE;EAQM,wOAAoE;ANi4D5E;;AMz4DE;EAWM,kLAAiE;ANk4DzE;;AM74DE;EAkBM,gFJ1FU;AFy9DlB;;AMj5DE;EAyBI,qBAAkC;AN43DxC;;AMr5DE;EA6BI,yBAAsC;EACtC,qBAAkC;AN43DxC;;AM15DE;EAEI,qBJhDW;EGrCb,yBHqCa;AF68DjB;;AM/5DE;EAQM,wOAAoE;AN25D5E;;AMn6DE;EAWM,kLAAiE;AN45DzE;;AMv6DE;EAkBM,8EJhES;AFy9DjB;;AM36DE;EAyBI,qBAAkC;ANs5DxC;;AM/6DE;EA6BI,yBAAsC;EACtC,qBAAkC;ANs5DxC;;AMp7DE;EAEI,qBJ9CW;EGvCb,yBHuCa;AFq+DjB;;AMz7DE;EAQM,wOAAoE;ANq7D5E;;AM77DE;EAWM,kLAAiE;ANs7DzE;;AMj8DE;EAkBM,+EJ9DS;AFi/DjB;;AMr8DE;EAyBI,qBAAkC;ANg7DxC;;AMz8DE;EA6BI,yBAAsC;EACtC,qBAAkC;ANg7DxC;;AM98DE;EAEI,qBJjDW;EGpCb,yBHoCa;AFkgEjB;;AMn9DE;EAQM,wOAAoE;AN+8D5E;;AMv9DE;EAWM,kLAAiE;ANg9DzE;;AM39DE;EAkBM,8EJjES;AF8gEjB;;AM/9DE;EAyBI,qBAAkC;AN08DxC;;AMn+DE;EA6BI,yBAAsC;EACtC,qBAAkC;AN08DxC;;AMx+DE;EAEI,qBJnDW;EGlCb,yBHkCa;AF8hEjB;;AM7+DE;EAQM,wOAAoE;ANy+D5E;;AMj/DE;EAWM,kLAAiE;AN0+DzE;;AMr/DE;EAkBM,8EJnES;AF0iEjB;;AMz/DE;EAyBI,qBAAkC;ANo+DxC;;AM7/DE;EA6BI,yBAAsC;EACtC,qBAAkC;ANo+DxC;;AMlgEE;EAEI,qBJ/EY;EGNd,yBHMc;AFolElB;;AMvgEE;EAQM,wOAAoE;ANmgE5E;;AM3gEE;EAWM,kLAAiE;ANogEzE;;AM/gEE;EAkBM,gFJ/FU;AFgmElB;;AMnhEE;EAyBI,mBAAkC;AN8/DxC;;AMvhEE;EA6BI,uBAAsC;EACtC,mBAAkC;AN8/DxC;;AM5hEE;EAEI,qBJxEY;EGbd,yBHac;AFumElB;;AMjiEE;EAQM,wOAAoE;AN6hE5E;;AMriEE;EAWM,kLAAiE;AN8hEzE;;AMziEE;EAkBM,6EJxFU;AFmnElB;;AM7iEE;EAyBI,qBAAkC;ANwhExC;;AMjjEE;EA6BI,yBAAsC;EACtC,qBAAkC;ANwhExC;;AMtjEE;EAEI,qBCpFa;EFDf,yBECe;AP6oEnB;;AM3jEE;EAQM,wOAAoE;ANujE5E;;AM/jEE;EAWM,kLAAiE;ANwjEzE;;AMnkEE;EAkBM,+ECpGW;APypEnB;;AMvkEE;EAyBI,qBAAkC;ANkjExC;;AM3kEE;EA6BI,yBAAsC;EACtC,qBAAkC;ANkjExC;;AMhlEE;EAEI,qBCnFQ;EFFV,yBEEU;APsqEd;;AMrlEE;EAQM,wOAAoE;ANilE5E;;AMzlEE;EAWM,kLAAiE;ANklEzE;;AM7lEE;EAkBM,4ECnGM;APkrEd;;AMjmEE;EAyBI,qBAAkC;AN4kExC;;AMrmEE;EA6BI,yBAAsC;EACtC,qBAAkC;AN4kExC;;AM1mEE;EAEI,qBCjFS;EFJX,yBEIW;AP8rEf;;AM/mEE;EAQM,wOAAoE;AN2mE5E;;AMnnEE;EAWM,kLAAiE;AN4mEzE;;AMvnEE;EAkBM,+ECjGO;AP0sEf;;AM3nEE;EAyBI,qBAAkC;ANsmExC;;AM/nEE;EA6BI,yBAAsC;EACtC,qBAAkC;ANsmExC;;AMpoEE;EAEI,qBChFQ;EFLV,yBEKU;APutEd;;AMzoEE;EAQM,wOAAoE;ANqoE5E;;AM7oEE;EAWM,kLAAiE;ANsoEzE;;AMjpEE;EAkBM,8EChGM;APmuEd;;AMrpEE;EAyBI,qBAAkC;ANgoExC;;AMzpEE;EA6BI,yBAAsC;EACtC,qBAAkC;ANgoExC;;AM9pEE;EAEI,qBC9EW;EFPb,yBEOa;AP+uEjB;;AMnqEE;EAQM,wOAAoE;AN+pE5E;;AMvqEE;EAWM,kLAAiE;ANgqEzE;;AM3qEE;EAkBM,+EC9FS;AP2vEjB;;AM/qEE;EAyBI,qBAAkC;AN0pExC;;AMnrEE;EA6BI,yBAAsC;EACtC,qBAAkC;AN0pExC;;AMxrEE;EAEI,qBC5EU;EFTZ,yBESY;APuwEhB;;AM7rEE;EAQM,wOAAoE;ANyrE5E;;AMjsEE;EAWM,kLAAiE;AN0rEzE;;AMrsEE;EAkBM,8EC5FQ;APmxEhB;;AMzsEE;EAyBI,qBAAkC;ANorExC;;AM7sEE;EA6BI,yBAAsC;EACtC,qBAAkC;ANorExC;;AMltEE;EAEI,qBJvDW;EG9Bb,yBH8Ba;AF4wEjB;;AMvtEE;EAQM,wOAAoE;ANmtE5E;;AM3tEE;EAWM,kLAAiE;ANotEzE;;AM/tEE;EAkBM,8EJvES;AFwxEjB;;AMnuEE;EAyBI,qBAAkC;AN8sExC;;AMvuEE;EA6BI,yBAAsC;EACtC,qBAAkC;AN8sExC;;AM5uEE;EAEI,qBJtDW;EG/Bb,yBH+Ba;AFqyEjB;;AMjvEE;EAQM,wOAAoE;AN6uE5E;;AMrvEE;EAWM,kLAAiE;AN8uEzE;;AMzvEE;EAkBM,+EJtES;AFizEjB;;AM7vEE;EAyBI,qBAAkC;ANwuExC;;AMjwEE;EA6BI,yBAAsC;EACtC,qBAAkC;ANwuExC;;AMtwEE;EAEI,qBJrDW;EGhCb,yBHgCa;AF8zEjB;;AM3wEE;EAQM,wOAAoE;ANuwE5E;;AM/wEE;EAWM,kLAAiE;ANwwEzE;;AMnxEE;EAkBM,+EJrES;AF00EjB;;AMvxEE;EAyBI,qBAAkC;ANkwExC;;AM3xEE;EA6BI,yBAAsC;EACtC,qBAAkC;ANkwExC;;AMhyEE;EAEI,qBJpDW;EGjCb,yBHiCa;AFu1EjB;;AMryEE;EAQM,wOAAoE;ANiyE5E;;AMzyEE;EAWM,kLAAiE;ANkyEzE;;AM7yEE;EAkBM,+EJpES;AFm2EjB;;AMjzEE;EAyBI,qBAAkC;AN4xExC;;AMrzEE;EA6BI,yBAAsC;EACtC,qBAAkC;AN4xExC;;AM1zEE;EAEI,qBJnDW;EGlCb,yBHkCa;AFg3EjB;;AM/zEE;EAQM,wOAAoE;AN2zE5E;;AMn0EE;EAWM,kLAAiE;AN4zEzE;;AMv0EE;EAkBM,8EJnES;AF43EjB;;AM30EE;EAyBI,qBAAkC;ANszExC;;AM/0EE;EA6BI,yBAAsC;EACtC,qBAAkC;ANszExC;;AMp1EE;EAEI,qBJlDW;EGnCb,yBHmCa;AFy4EjB;;AMz1EE;EAQM,wOAAoE;ANq1E5E;;AM71EE;EAWM,kLAAiE;ANs1EzE;;AMj2EE;EAkBM,+EJlES;AFq5EjB;;AMr2EE;EAyBI,qBAAkC;ANg1ExC;;AMz2EE;EA6BI,yBAAsC;EACtC,qBAAkC;ANg1ExC;;AM92EE;EAEI,qBJjDW;EGpCb,yBHoCa;AFk6EjB;;AMn3EE;EAQM,wOAAoE;AN+2E5E;;AMv3EE;EAWM,kLAAiE;ANg3EzE;;AM33EE;EAkBM,8EJjES;AF86EjB;;AM/3EE;EAyBI,qBAAkC;AN02ExC;;AMn4EE;EA6BI,yBAAsC;EACtC,qBAAkC;AN02ExC;;AMx4EE;EAEI,qBJhDW;EGrCb,yBHqCa;AF27EjB;;AM74EE;EAQM,wOAAoE;ANy4E5E;;AMj5EE;EAWM,kLAAiE;AN04EzE;;AMr5EE;EAkBM,8EJhES;AFu8EjB;;AMz5EE;EAyBI,qBAAkC;ANo4ExC;;AM75EE;EA6BI,yBAAsC;EACtC,qBAAkC;ANo4ExC;;AMl6EE;EAEI,qBJ/CW;EGtCb,yBHsCa;AFo9EjB;;AMv6EE;EAQM,wOAAoE;ANm6E5E;;AM36EE;EAWM,kLAAiE;ANo6EzE;;AM/6EE;EAkBM,+EJ/DS;AFg+EjB;;AMn7EE;EAyBI,qBAAkC;AN85ExC;;AMv7EE;EA6BI,yBAAsC;EACtC,qBAAkC;AN85ExC;;AM57EE;EAEI,qBJ9CW;EGvCb,yBHuCa;AF6+EjB;;AMj8EE;EAQM,wOAAoE;AN67E5E;;AMr8EE;EAWM,kLAAiE;AN87EzE;;AMz8EE;EAkBM,+EJ9DS;AFy/EjB;;AM78EE;EAyBI,qBAAkC;ANw7ExC;;AMj9EE;EA6BI,yBAAsC;EACtC,qBAAkC;ANw7ExC;;AMt9EE;EAEI,kBJhFS;EGLX,sBHKW;AFyiFf;;AM39EE;EAQM,qOAAoE;ANu9E5E;;AM/9EE;EAWM,+KAAiE;ANw9EzE;;AMn+EE;EAkBM,gFJhGO;AFqjFf;;AMv+EE;EAyBI,mBAAkC;ANk9ExC;;AM3+EE;EA6BI,uBAAsC;EACtC,mBAAkC;ANk9ExC;;AMh/EE;EAEI,qBJ1EY;EGXd,yBHWc;AF6jFlB;;AMr/EE;EAQM,wOAAoE;ANi/E5E;;AMz/EE;EAWM,kLAAiE;ANk/EzE;;AM7/EE;EAkBM,gFJ1FU;AFykFlB;;AMjgFE;EAyBI,qBAAkC;AN4+ExC;;AMrgFE;EA6BI,yBAAsC;EACtC,qBAAkC;AN4+ExC;;AM1gFE;EAEI,qBJxEY;EGbd,yBHac;AFqlFlB;;AM/gFE;EAQM,wOAAoE;AN2gF5E;;AMnhFE;EAWM,kLAAiE;AN4gFzE;;AMvhFE;EAkBM,6EJxFU;AFimFlB;;AM3hFE;EAyBI,qBAAkC;ANsgFxC;;AM/hFE;EA6BI,yBAAsC;EACtC,qBAAkC;ANsgFxC;;AC1zEA;EAEI,wCAAwC;EACxC,gBAAgB;AD4zEpB;;AC/zEA;EI7TI,6BJmUgC;AD6zEpC;;ACzzEA;;;;;;EAOI,yBCjUc;EDkUd,WC1UW;AFqoFf;;ACn0EA;EAWI,qBCvUc;AFmoFlB;;ACv0EA;EAcI,yBCxUc;EDyUd,WCjVW;EDkVX,qBC5Uc;AFyoFlB;;AC70EA;EAoBI,qBChVc;AF6oFlB;;ACj1EA;;EAyBI,yBAAoC;EACpC,qBCtVc;EDuVd,WC7VW;AF0pFf;;ACx1EA;EAgCM,yBAAsC;AD4zE5C;;AC51EA;EAmCM,yBAAsC;AD6zE5C;;ACh2EA;EAsCM,yBAAsC;AD8zE5C;;ACp2EA;;EA6CM,yBCvWY;EDwWZ,yBC1WY;AFsqFlB;;AC12EA;EAkDQ,yBAAiD;AD4zEzD;;AC92EA;EAqDQ,yBAAgD;AD6zExD;;ACl3EA;;EA4DQ,yBAAoC;EACpC,WC/XO;EDgYP,yBC1XU;ED2XV,iBAAiB;AD2zEzB;;AQlsFA;ECWM,gBDVoB;ELYtB,kBIoJ4B;APsiFhC;;AQvsFA;EAMI,qBAAqB;EACrB,aAAa;EACb,kBAAkB;EAClB,kBAAkB;EAClB,WAAW;ARqsFf;;AQ/sFA;EAaM,SAAS;EACT,kBAAkB;EAClB,WAAW;ARssFjB;;AQrtFA;EAqBM,WAAW;ARosFjB;;AQztFA;EA0BM,WAAW;ARmsFjB;;AQ7tFA;EA+BM,UAAU;ARksFhB;;AQ7rFA;EACE,qBNoFiB;AF4mFnB;;AQ5rFA;EACE,YAAY;AR+rFd;;AQ5rFA;EACE,WAAW;AR+rFb;;AQ5rFA;EACE,WAAW;AR+rFb;;AQ3rFA;EAGM,SAAS;AR4rFf;;AQvrFA;EAEI,mBAAgC;ARyrFpC;;AUzvFE;EAGM,yBR4BS;AF8tFjB;;AU7vFE;;EAOQ,WRDK;AF4vFf;;AUlwFE;EAWQ,cR6Ea;AF8qFvB;;AUtwFE;EAiBI,6BRcW;AF2uFjB;;AU1wFE;EAwBU,6BRfM;AFqwFlB;;AU9wFE;EA4BU,6BRGK;AFmvFjB;;AU/uFE;;;EAII,+BRjCS;AFkxFf;;AUrvFE;;;EAOM,WRpCO;AFwxFf;;AU/uFE;;;;EAKM,YAAY;AVivFpB;;AUtvFE;;;;;;;;;;EAaM,yBAAoC;EACpC,WRvDO;AF6yFf;;AUpwFE;;EAkBM,yBR3DO;AFkzFf;;AUzwFE;;;;EAuBM,yBAAsC;EACtC,WRjEO;AF0zFf;;AUh0FE;EAGM,yBRSU;AFwzFlB;;AUp0FE;;EAOQ,WRDK;AFm0Ff;;AUz0FE;EAWQ,cR6Ea;AFqvFvB;;AU70FE;EAiBI,6BRLY;AFq0FlB;;AUj1FE;EAwBU,6BRfM;AF40FlB;;AUr1FE;EA4BU,6BRhBM;AF60FlB;;AUtzFE;;;EAII,+BRjCS;AFy1Ff;;AU5zFE;;;EAOM,WRpCO;AF+1Ff;;AUtzFE;;;;EAKM,YAAY;AVwzFpB;;AU7zFE;;;;;;;;;;EAaM,yBAAoC;EACpC,WRvDO;AFo3Ff;;AU30FE;;EAkBM,yBR3DO;AFy3Ff;;AUh1FE;;;;EAuBM,yBAAsC;EACtC,WRjEO;AFi4Ff;;AUv4FE;EAGM,yBRmCS;AFq2FjB;;AU34FE;;EAOQ,WRDK;AF04Ff;;AUh5FE;EAWQ,cR6Ea;AF4zFvB;;AUp5FE;EAiBI,6BRqBW;AFk3FjB;;AUx5FE;EAwBU,6BRfM;AFm5FlB;;AU55FE;EA4BU,6BRUK;AF03FjB;;AU73FE;;;EAII,+BRjCS;AFg6Ff;;AUn4FE;;;EAOM,WRpCO;AFs6Ff;;AU73FE;;;;EAKM,YAAY;AV+3FpB;;AUp4FE;;;;;;;;;;EAaM,yBAAoC;EACpC,WRvDO;AF27Ff;;AUl5FE;;EAkBM,yBR3DO;AFg8Ff;;AUv5FE;;;;EAuBM,yBAAsC;EACtC,WRjEO;AFw8Ff;;AU98FE;EAGM,yBRqCS;AF06FjB;;AUl9FE;;EAOQ,WRDK;AFi9Ff;;AUv9FE;EAWQ,cR6Ea;AFm4FvB;;AU39FE;EAiBI,6BRuBW;AFu7FjB;;AU/9FE;EAwBU,6BRfM;AF09FlB;;AUn+FE;EA4BU,6BRYK;AF+7FjB;;AUp8FE;;;EAII,+BRjCS;AFu+Ff;;AU18FE;;;EAOM,WRpCO;AF6+Ff;;AUp8FE;;;;EAKM,YAAY;AVs8FpB;;AU38FE;;;;;;;;;;EAaM,yBAAoC;EACpC,WRvDO;AFkgGf;;AUz9FE;;EAkBM,yBR3DO;AFugGf;;AU99FE;;;;EAuBM,yBAAsC;EACtC,WRjEO;AF+gGf;;AUrhGE;EAGM,yBRkCS;AFo/FjB;;AUzhGE;;EAOQ,cRiFa;AFs8FvB;;AU9hGE;EAWQ,cR6Ea;AF08FvB;;AUliGE;EAiBI,6BRoBW;AFigGjB;;AUtiGE;EAwBU,6BRfM;AFiiGlB;;AU1iGE;EA4BU,6BRSK;AFygGjB;;AU3gGE;;;EAII,4BRiDiB;AF49FvB;;AUjhGE;;;EAOM,cR8Ce;AFk+FvB;;AU3gGE;;;;EAKM,YAAY;AV6gGpB;;AUlhGE;;;;;;;;;;EAaM,yBAAoC;EACpC,cR2Be;AFu/FvB;;AUhiGE;;EAkBM,4BRuBe;AF4/FvB;;AUriGE;;;;EAuBM,yBAAsC;EACtC,cRiBe;AFogGvB;;AU5lGE;EAGM,yBRgCS;AF6jGjB;;AUhmGE;;EAOQ,WRDK;AF+lGf;;AUrmGE;EAWQ,cR6Ea;AFihGvB;;AUzmGE;EAiBI,6BRkBW;AF0kGjB;;AU7mGE;EAwBU,6BRfM;AFwmGlB;;AUjnGE;EA4BU,6BROK;AFklGjB;;AUllGE;;;EAII,+BRjCS;AFqnGf;;AUxlGE;;;EAOM,WRpCO;AF2nGf;;AUllGE;;;;EAKM,YAAY;AVolGpB;;AUzlGE;;;;;;;;;;EAaM,yBAAoC;EACpC,WRvDO;AFgpGf;;AUvmGE;;EAkBM,yBR3DO;AFqpGf;;AU5mGE;;;;EAuBM,yBAAsC;EACtC,WRjEO;AF6pGf;;AUnqGE;EAGM,yBRIU;AFgqGlB;;AUvqGE;;EAOQ,cRiFa;AFolGvB;;AU5qGE;EAWQ,cR6Ea;AFwlGvB;;AUhrGE;EAiBI,6BRVY;AF6qGlB;;AUprGE;EAwBU,6BRfM;AF+qGlB;;AUxrGE;EA4BU,6BRrBM;AFqrGlB;;AUzpGE;;;EAII,4BRiDiB;AF0mGvB;;AU/pGE;;;EAOM,cR8Ce;AFgnGvB;;AUzpGE;;;;EAKM,YAAY;AV2pGpB;;AUhqGE;;;;;;;;;;EAaM,yBAAoC;EACpC,cR2Be;AFqoGvB;;AU9qGE;;EAkBM,4BRuBe;AF0oGvB;;AUnrGE;;;;EAuBM,uBAAsC;EACtC,cRiBe;AFkpGvB;;AU1uGE;EAGM,yBRWU;AFguGlB;;AU9uGE;;EAOQ,WRDK;AF6uGf;;AUnvGE;EAWQ,cR6Ea;AF+pGvB;;AUvvGE;EAiBI,6BRHY;AF6uGlB;;AU3vGE;EAwBU,6BRfM;AFsvGlB;;AU/vGE;EA4BU,6BRdM;AFqvGlB;;AUhuGE;;;EAII,+BRjCS;AFmwGf;;AUtuGE;;;EAOM,WRpCO;AFywGf;;AUhuGE;;;;EAKM,YAAY;AVkuGpB;;AUvuGE;;;;;;;;;;EAaM,yBAAoC;EACpC,WRvDO;AF8xGf;;AUrvGE;;EAkBM,yBR3DO;AFmyGf;;AU1vGE;;;;EAuBM,yBAAsC;EACtC,WRjEO;AF2yGf;;AUjzGE;EAGM,yBHDW;APmzGnB;;AUrzGE;;EAOQ,WRDK;AFozGf;;AU1zGE;EAWQ,cR6Ea;AFsuGvB;;AU9zGE;EAiBI,6BHfa;APg0GnB;;AUl0GE;EAwBU,6BRfM;AF6zGlB;;AUt0GE;EA4BU,6BH1BO;APw0GnB;;AUvyGE;;;EAII,+BRjCS;AF00Gf;;AU7yGE;;;EAOM,WRpCO;AFg1Gf;;AUvyGE;;;;EAKM,YAAY;AVyyGpB;;AU9yGE;;;;;;;;;;EAaM,yBAAoC;EACpC,WRvDO;AFq2Gf;;AU5zGE;;EAkBM,yBR3DO;AF02Gf;;AUj0GE;;;;EAuBM,yBAAsC;EACtC,WRjEO;AFk3Gf;;AUx3GE;EAGM,yBHAM;APy3Gd;;AU53GE;;EAOQ,WRDK;AF23Gf;;AUj4GE;EAWQ,cR6Ea;AF6yGvB;;AUr4GE;EAiBI,6BHdQ;APs4Gd;;AUz4GE;EAwBU,6BRfM;AFo4GlB;;AU74GE;EA4BU,6BHzBE;AP84Gd;;AU92GE;;;EAII,+BRjCS;AFi5Gf;;AUp3GE;;;EAOM,WRpCO;AFu5Gf;;AU92GE;;;;EAKM,YAAY;AVg3GpB;;AUr3GE;;;;;;;;;;EAaM,yBAAoC;EACpC,WRvDO;AF46Gf;;AUn4GE;;EAkBM,yBR3DO;AFi7Gf;;AUx4GE;;;;EAuBM,yBAAsC;EACtC,WRjEO;AFy7Gf;;AU/7GE;EAGM,yBHEO;AP87Gf;;AUn8GE;;EAOQ,WRDK;AFk8Gf;;AUx8GE;EAWQ,cR6Ea;AFo3GvB;;AU58GE;EAiBI,6BHZS;AP28Gf;;AUh9GE;EAwBU,6BRfM;AF28GlB;;AUp9GE;EA4BU,6BHvBG;APm9Gf;;AUr7GE;;;EAII,+BRjCS;AFw9Gf;;AU37GE;;;EAOM,WRpCO;AF89Gf;;AUr7GE;;;;EAKM,YAAY;AVu7GpB;;AU57GE;;;;;;;;;;EAaM,yBAAoC;EACpC,WRvDO;AFm/Gf;;AU18GE;;EAkBM,yBR3DO;AFw/Gf;;AU/8GE;;;;EAuBM,yBAAsC;EACtC,WRjEO;AFggHf;;AUtgHE;EAGM,yBHGM;APogHd;;AU1gHE;;EAOQ,cRiFa;AFu7GvB;;AU/gHE;EAWQ,cR6Ea;AF27GvB;;AUnhHE;EAiBI,6BHXQ;APihHd;;AUvhHE;EAwBU,6BRfM;AFkhHlB;;AU3hHE;EA4BU,6BHtBE;APyhHd;;AU5/GE;;;EAII,4BRiDiB;AF68GvB;;AUlgHE;;;EAOM,cR8Ce;AFm9GvB;;AU5/GE;;;;EAKM,YAAY;AV8/GpB;;AUngHE;;;;;;;;;;EAaM,yBAAoC;EACpC,cR2Be;AFw+GvB;;AUjhHE;;EAkBM,4BRuBe;AF6+GvB;;AUthHE;;;;EAuBM,yBAAsC;EACtC,cRiBe;AFq/GvB;;AU7kHE;EAGM,yBHKS;APykHjB;;AUjlHE;;EAOQ,WRDK;AFglHf;;AUtlHE;EAWQ,cR6Ea;AFkgHvB;;AU1lHE;EAiBI,6BHTW;APslHjB;;AU9lHE;EAwBU,6BRfM;AFylHlB;;AUlmHE;EA4BU,6BHpBK;AP8lHjB;;AUnkHE;;;EAII,+BRjCS;AFsmHf;;AUzkHE;;;EAOM,WRpCO;AF4mHf;;AUnkHE;;;;EAKM,YAAY;AVqkHpB;;AU1kHE;;;;;;;;;;EAaM,yBAAoC;EACpC,WRvDO;AFioHf;;AUxlHE;;EAkBM,yBR3DO;AFsoHf;;AU7lHE;;;;EAuBM,yBAAsC;EACtC,WRjEO;AF8oHf;;AUppHE;EAGM,yBHOQ;AP8oHhB;;AUxpHE;;EAOQ,WRDK;AFupHf;;AU7pHE;EAWQ,cR6Ea;AFykHvB;;AUjqHE;EAiBI,6BHPU;AP2pHhB;;AUrqHE;EAwBU,6BRfM;AFgqHlB;;AUzqHE;EA4BU,6BHlBI;APmqHhB;;AU1oHE;;;EAII,+BRjCS;AF6qHf;;AUhpHE;;;EAOM,WRpCO;AFmrHf;;AU1oHE;;;;EAKM,YAAY;AV4oHpB;;AUjpHE;;;;;;;;;;EAaM,yBAAoC;EACpC,WRvDO;AFwsHf;;AU/pHE;;EAkBM,yBR3DO;AF6sHf;;AUpqHE;;;;EAuBM,yBAAsC;EACtC,WRjEO;AFqtHf;;AU3tHE;EAGM,yBR4BS;AFgsHjB;;AU/tHE;;EAOQ,WRDK;AF8tHf;;AUpuHE;EAWQ,cR6Ea;AFgpHvB;;AUxuHE;EAiBI,6BRcW;AF6sHjB;;AU5uHE;EAwBU,6BRfM;AFuuHlB;;AUhvHE;EA4BU,6BRGK;AFqtHjB;;AUjtHE;;;EAII,+BRjCS;AFovHf;;AUvtHE;;;EAOM,WRpCO;AF0vHf;;AUjtHE;;;;EAKM,YAAY;AVmtHpB;;AUxtHE;;;;;;;;;;EAaM,yBAAoC;EACpC,WRvDO;AF+wHf;;AUtuHE;;EAkBM,yBR3DO;AFoxHf;;AU3uHE;;;;EAuBM,yBAAsC;EACtC,WRjEO;AF4xHf;;AUlyHE;EAGM,yBR6BS;AFswHjB;;AUtyHE;;EAOQ,WRDK;AFqyHf;;AU3yHE;EAWQ,cR6Ea;AFutHvB;;AU/yHE;EAiBI,6BReW;AFmxHjB;;AUnzHE;EAwBU,6BRfM;AF8yHlB;;AUvzHE;EA4BU,6BRIK;AF2xHjB;;AUxxHE;;;EAII,+BRjCS;AF2zHf;;AU9xHE;;;EAOM,WRpCO;AFi0Hf;;AUxxHE;;;;EAKM,YAAY;AV0xHpB;;AU/xHE;;;;;;;;;;EAaM,yBAAoC;EACpC,WRvDO;AFs1Hf;;AU7yHE;;EAkBM,yBR3DO;AF21Hf;;AUlzHE;;;;EAuBM,yBAAsC;EACtC,WRjEO;AFm2Hf;;AUz2HE;EAGM,yBR8BS;AF40HjB;;AU72HE;;EAOQ,WRDK;AF42Hf;;AUl3HE;EAWQ,cR6Ea;AF8xHvB;;AUt3HE;EAiBI,6BRgBW;AFy1HjB;;AU13HE;EAwBU,6BRfM;AFq3HlB;;AU93HE;EA4BU,6BRKK;AFi2HjB;;AU/1HE;;;EAII,+BRjCS;AFk4Hf;;AUr2HE;;;EAOM,WRpCO;AFw4Hf;;AU/1HE;;;;EAKM,YAAY;AVi2HpB;;AUt2HE;;;;;;;;;;EAaM,yBAAoC;EACpC,WRvDO;AF65Hf;;AUp3HE;;EAkBM,yBR3DO;AFk6Hf;;AUz3HE;;;;EAuBM,yBAAsC;EACtC,WRjEO;AF06Hf;;AUh7HE;EAGM,yBR+BS;AFk5HjB;;AUp7HE;;EAOQ,WRDK;AFm7Hf;;AUz7HE;EAWQ,cR6Ea;AFq2HvB;;AU77HE;EAiBI,6BRiBW;AF+5HjB;;AUj8HE;EAwBU,6BRfM;AF47HlB;;AUr8HE;EA4BU,6BRMK;AFu6HjB;;AUt6HE;;;EAII,+BRjCS;AFy8Hf;;AU56HE;;;EAOM,WRpCO;AF+8Hf;;AUt6HE;;;;EAKM,YAAY;AVw6HpB;;AU76HE;;;;;;;;;;EAaM,yBAAoC;EACpC,WRvDO;AFo+Hf;;AU37HE;;EAkBM,yBR3DO;AFy+Hf;;AUh8HE;;;;EAuBM,yBAAsC;EACtC,WRjEO;AFi/Hf;;AUv/HE;EAGM,yBRgCS;AFw9HjB;;AU3/HE;;EAOQ,WRDK;AF0/Hf;;AUhgIE;EAWQ,cR6Ea;AF46HvB;;AUpgIE;EAiBI,6BRkBW;AFq+HjB;;AUxgIE;EAwBU,6BRfM;AFmgIlB;;AU5gIE;EA4BU,6BROK;AF6+HjB;;AU7+HE;;;EAII,+BRjCS;AFghIf;;AUn/HE;;;EAOM,WRpCO;AFshIf;;AU7+HE;;;;EAKM,YAAY;AV++HpB;;AUp/HE;;;;;;;;;;EAaM,yBAAoC;EACpC,WRvDO;AF2iIf;;AUlgIE;;EAkBM,yBR3DO;AFgjIf;;AUvgIE;;;;EAuBM,yBAAsC;EACtC,WRjEO;AFwjIf;;AU9jIE;EAGM,yBRiCS;AF8hIjB;;AUlkIE;;EAOQ,cRiFa;AF++HvB;;AUvkIE;EAWQ,cR6Ea;AFm/HvB;;AU3kIE;EAiBI,6BRmBW;AF2iIjB;;AU/kIE;EAwBU,6BRfM;AF0kIlB;;AUnlIE;EA4BU,6BRQK;AFmjIjB;;AUpjIE;;;EAII,4BRiDiB;AFqgIvB;;AU1jIE;;;EAOM,cR8Ce;AF2gIvB;;AUpjIE;;;;EAKM,YAAY;AVsjIpB;;AU3jIE;;;;;;;;;;EAaM,yBAAoC;EACpC,cR2Be;AFgiIvB;;AUzkIE;;EAkBM,4BRuBe;AFqiIvB;;AU9kIE;;;;EAuBM,yBAAsC;EACtC,cRiBe;AF6iIvB;;AUroIE;EAGM,yBRkCS;AFomIjB;;AUzoIE;;EAOQ,cRiFa;AFsjIvB;;AU9oIE;EAWQ,cR6Ea;AF0jIvB;;AUlpIE;EAiBI,6BRoBW;AFinIjB;;AUtpIE;EAwBU,6BRfM;AFipIlB;;AU1pIE;EA4BU,6BRSK;AFynIjB;;AU3nIE;;;EAII,4BRiDiB;AF4kIvB;;AUjoIE;;;EAOM,cR8Ce;AFklIvB;;AU3nIE;;;;EAKM,YAAY;AV6nIpB;;AUloIE;;;;;;;;;;EAaM,yBAAoC;EACpC,cR2Be;AFumIvB;;AUhpIE;;EAkBM,4BRuBe;AF4mIvB;;AUrpIE;;;;EAuBM,yBAAsC;EACtC,cRiBe;AFonIvB;;AU5sIE;EAGM,yBRmCS;AF0qIjB;;AUhtIE;;EAOQ,WRDK;AF+sIf;;AUrtIE;EAWQ,cR6Ea;AFioIvB;;AUztIE;EAiBI,6BRqBW;AFurIjB;;AU7tIE;EAwBU,6BRfM;AFwtIlB;;AUjuIE;EA4BU,6BRUK;AF+rIjB;;AUlsIE;;;EAII,+BRjCS;AFquIf;;AUxsIE;;;EAOM,WRpCO;AF2uIf;;AUlsIE;;;;EAKM,YAAY;AVosIpB;;AUzsIE;;;;;;;;;;EAaM,yBAAoC;EACpC,WRvDO;AFgwIf;;AUvtIE;;EAkBM,yBR3DO;AFqwIf;;AU5tIE;;;;EAuBM,yBAAsC;EACtC,WRjEO;AF6wIf;;AUnxIE;EAGM,yBRoCS;AFgvIjB;;AUvxIE;;EAOQ,WRDK;AFsxIf;;AU5xIE;EAWQ,cR6Ea;AFwsIvB;;AUhyIE;EAiBI,6BRsBW;AF6vIjB;;AUpyIE;EAwBU,6BRfM;AF+xIlB;;AUxyIE;EA4BU,6BRWK;AFqwIjB;;AUzwIE;;;EAII,+BRjCS;AF4yIf;;AU/wIE;;;EAOM,WRpCO;AFkzIf;;AUzwIE;;;;EAKM,YAAY;AV2wIpB;;AUhxIE;;;;;;;;;;EAaM,yBAAoC;EACpC,WRvDO;AFu0If;;AU9xIE;;EAkBM,yBR3DO;AF40If;;AUnyIE;;;;EAuBM,yBAAsC;EACtC,WRjEO;AFo1If;;AU11IE;EAGM,yBRqCS;AFszIjB;;AU91IE;;EAOQ,WRDK;AF61If;;AUn2IE;EAWQ,cR6Ea;AF+wIvB;;AUv2IE;EAiBI,6BRuBW;AFm0IjB;;AU32IE;EAwBU,6BRfM;AFs2IlB;;AU/2IE;EA4BU,6BRYK;AF20IjB;;AUh1IE;;;EAII,+BRjCS;AFm3If;;AUt1IE;;;EAOM,WRpCO;AFy3If;;AUh1IE;;;;EAKM,YAAY;AVk1IpB;;AUv1IE;;;;;;;;;;EAaM,yBAAoC;EACpC,WRvDO;AF84If;;AUr2IE;;EAkBM,yBR3DO;AFm5If;;AU12IE;;;;EAuBM,yBAAsC;EACtC,WRjEO;AF25If;;AUj6IE;EAGM,sBRGO;AF+5If;;AUr6IE;;EAOQ,cRiFa;AFk1IvB;;AU16IE;EAWQ,cR6Ea;AFs1IvB;;AU96IE;EAiBI,0BRXS;AF46If;;AUl7IE;EAwBU,6BRfM;AF66IlB;;AUt7IE;EA4BU,0BRtBG;AFo7If;;AUv5IE;;;EAII,4BRiDiB;AFw2IvB;;AU75IE;;;EAOM,cR8Ce;AF82IvB;;AUv5IE;;;;EAKM,YAAY;AVy5IpB;;AU95IE;;;;;;;;;;EAaM,yBAAoC;EACpC,cR2Be;AFm4IvB;;AU56IE;;EAkBM,4BRuBe;AFw4IvB;;AUj7IE;;;;EAuBM,uBAAsC;EACtC,cRiBe;AFg5IvB;;AUx+IE;EAGM,yBRSU;AFg+IlB;;AU5+IE;;EAOQ,WRDK;AF2+If;;AUj/IE;EAWQ,cR6Ea;AF65IvB;;AUr/IE;EAiBI,6BRLY;AF6+IlB;;AUz/IE;EAwBU,6BRfM;AFo/IlB;;AU7/IE;EA4BU,6BRhBM;AFq/IlB;;AU99IE;;;EAII,+BRjCS;AFigJf;;AUp+IE;;;EAOM,WRpCO;AFugJf;;AU99IE;;;;EAKM,YAAY;AVg+IpB;;AUr+IE;;;;;;;;;;EAaM,yBAAoC;EACpC,WRvDO;AF4hJf;;AUn/IE;;EAkBM,yBR3DO;AFiiJf;;AUx/IE;;;;EAuBM,yBAAsC;EACtC,WRjEO;AFyiJf;;AU/iJE;EAGM,yBRWU;AFqiJlB;;AUnjJE;;EAOQ,WRDK;AFkjJf;;AUxjJE;EAWQ,cR6Ea;AFo+IvB;;AU5jJE;EAiBI,6BRHY;AFkjJlB;;AUhkJE;EAwBU,6BRfM;AF2jJlB;;AUpkJE;EA4BU,6BRdM;AF0jJlB;;AUriJE;;;EAII,+BRjCS;AFwkJf;;AU3iJE;;;EAOM,WRpCO;AF8kJf;;AUriJE;;;;EAKM,YAAY;AVuiJpB;;AU5iJE;;;;;;;;;;EAaM,yBAAoC;EACpC,WRvDO;AFmmJf;;AU1jJE;;EAkBM,yBR3DO;AFwmJf;;AU/jJE;;;;EAuBM,yBAAsC;EACtC,WRjEO;AFgnJf;;AW9mJA;EFGM,sEF4IgE;EI7IpE,mBT0GW;AFugJb;;AWnnJA;EAMM,qBJwI0C;APy+IhD;;AWvnJA;;EAWM,WTbS;AF8nJf;;AW5nJA;EAgBI,uBAAuB;EACvB,OAAO;EACP,2BAA2B;EAC3B,0BAA0B;EAC1B,eAAe;EACf,MAAM;EACN,sBAAsB;EACtB,aTyhBoC;AFulIxC;;AWvoJA;EA0BM,yBAAyB;AXinJ/B;;AW3oJA;EA8BM,cAAc;AXinJpB;;AW/oJA;EAkCM,aAAa;AXinJnB;;AWnpJA;;ERKI,2BQkCqC;AXinJzC;;AWxpJA;;EA+CM,aAAa;AX8mJnB;;AW7pJA;EAqDM,6CT7CS;ES8CT,SAAS;AX4mJf;;AWlqJA;EAyDQ,gBAAgB;AX6mJxB;;AWtqJA;EAiEM,iBAAiB;EACjB,cAAc;AXymJpB;;AW3qJA;EAuEI,4CT/DW;AFuqJf;;AW/qJA;EA2EI,2CTnEW;AF2qJf;;AWnrJA;EAiFQ,gBAAgB;AXsmJxB;;AWvrJA;EAqFY,8BAA8B;AXsmJ1C;;AW3rJA;EA6FQ,gBAAgB;AXkmJxB;;AW/rJA;EAgGU,cAAc;EACd,cAAc;AXmmJxB;;AWpsJA;EAuGM,mBAAmB;AXimJzB;;AWxsJA;EA4GQ,gBAAgB;AXgmJxB;;AW5sJA;EA+GU,gBAAgB;AXimJ1B;;AWhtJA;EAkHY,gBAAgB;AXkmJ5B;;AWptJA;EA4HY,mBAAmB;AX4lJ/B;;AWxtJA;EAoII,aAAa;AXwlJjB;;AW5tJA;EAyIU,cAAc;EACd,cAAc;AXulJxB;;AWjuJA;EA+IQ,iCAAiC;AXslJzC;;AWruJA;EAkJU,6BTjJQ;AFwuJlB;;AWzuJA;EAuJY,aAAa;AXslJzB;;AW7uJA;EA8JM,yBAAyB;AXmlJ/B;;AWjvJA;EAkKM,gBAAgB;AXmlJtB;;AWrvJA;EAqKQ,gBAAgB;AXolJxB;;AWzvJA;EAwKU,gBAAgB;AXqlJ1B;;AW7vJA;EAiLY,mBAAmB;AXglJ/B;;AWtkJA;EACE,gBAAgB;AXykJlB;;AYjxJE;;;EACE,cAAc;EACd,WAAW;EACX,WAAW;AZsxJf;;AWtkJA;EACE,6BAA6B;EAC7B,6CTjMa;ESkMb,wBTocyC;ESnczC,kBAAkB;ER7LhB,+BD0LgC;ECzLhC,gCDyLgC;AF8kJpC;;AWrkJE;EACE,gBAAgB;AXwkJpB;;AWnlJA;EAeI,YAAY;EACZ,uBAAiC;AXwkJrC;;AWxlJA;;;EAqBM,sBAAoC;EACpC,mBAAiC;AXykJvC;;AW/lJA;EA0BM,kBAAkB;AXykJxB;;AWpkJA;EACE,WAAW;EACX,iBJxF2B;EIyF3B,gBTJ+B;ESK/B,SAAS;AXukJX;;AWpkJA;EACE,WAAW;AXukJb;;AWlkJA;EACE,6BAA6B;EAC7B,cTlPgB;ESmPhB,mBTpBoD;ESqBpD,kBAAqC;EACrC,qBAAqB;AXqkJvB;;AWnkJE;EAEE,cTvPc;AF4zJlB;;AWlkJE;EAEE,2BAA2B;AXokJ/B;;AWhkJA;EAEI,eJtH0B;APwrJ9B;;AWpkJA;EAMI,sBJvH8B;APyrJlC;;AW7jJA;EAQI,gBAAgB;AXyjJpB;;AWjkJA;;EAYM,mBAAmB;AX0jJzB;;AWtkJA;EAkBI,eAAe;AXwjJnB;;AW1kJA;EAsBI,aAAa;AXwjJjB;;AW9kJA;EA0BI,YAAY;AXwjJhB;;AWpjJA;EEvTE,eAAe;EACf,gBAAgB;EFwThB,cAAc;AXwjJhB;;AWrjJI;EALJ;IAMM,WAAW;IACX,kBAAkB;EXyjJtB;AACF;;AWpjJA;EACE,yBT7TgB;AFo3JlB;;AWxjJA;EAKI,gCThUc;ESiUd,cAAc;AXujJlB;;AYp4JE;EACE,cAAc;EACd,WAAW;EACX,WAAW;AZu4Jf;;AWnkJA;EASM,gBAAgB;AX8jJtB;;AWvkJA;EAaM,cAAc;AX8jJpB;;AW3kJA;EAiBM,gBJ5RgB;EI6RhB,eJ7RgB;EI8RhB,WAAW;AX8jJjB;;AWjlJA;EAwBI,cAA8B;EAC9B,iBAAiB;AX6jJrB;;AWtlJA;EA6BI,cTnVc;ESoVd,cAAc;EACd,gBAAgB;AX6jJpB;;AW5lJA;EAmCI,eAAe;EACf,gBAAgB;AX6jJpB;;AWrjJA;EACE,gBAAgB;EAChB,SAAS;EACT,cAAc;EACd,UAAU;AXwjJZ;;AW5jJA;ERlWI,kBQ0W0B;EAC1B,yBTjXc;ESkXd,8BTjXc;ESkXd,cT7Wc;ES8Wd,kBAAkB;EAClB,aAAa;AXwjJjB;;AWrkJA;EAgBM,gBAAgB;AXyjJtB;;AWzkJA;EAoBM,oBAAoB;AXyjJ1B;;AW7kJA;EAwBM,qBAAqB;EACrB,gBAAgB;EAChB,gBAAgB;AXyjJtB;;AWnlJA;EA+BM,gBAAgB;EAChB,iBAAiB;AXwjJvB;;AWxlJA;EAqCM,cTjXW;ESkXX,aAAa;EACb,YAAY;AXujJlB;;AW9lJA;;;;;;;;EAkDQ,eAAe;EACf,iBAAiB;AXujJzB;;AW1mJA;EAwDM,qBAAqB;AXsjJ3B;;AW9mJA;EA4DM,cAA6B;AXsjJnC;;AWlnJA;EA+DQ,gBAAgB;EAChB,6BAA6B;AXujJrC;;AWvnJA;EAoEQ,oCAAsC;AXujJ9C;;AW3nJA;EA4EM,0BT5ZW;AF+8JjB;;AW/nJA;EA4EM,0BT/aY;AFs+JlB;;AWnoJA;EA4EM,0BTrZW;AFg9JjB;;AWvoJA;EA4EM,0BTnZW;AFk9JjB;;AW3oJA;EA4EM,0BTtZW;AFy9JjB;;AW/oJA;EA4EM,0BTxZW;AF+9JjB;;AWnpJA;EA4EM,0BTpbY;AF+/JlB;;AWvpJA;EA4EM,0BT7aY;AF4/JlB;;AW3pJA;EAkFM,0BJ/ba;AP4gKnB;;AW/pJA;EAkFM,0BJ9bQ;AP+gKd;;AWnqJA;EAkFM,0BJ5bS;APihKf;;AWvqJA;EAkFM,0BJ3bQ;APohKd;;AW3qJA;EAkFM,0BJzbW;APshKjB;;AW/qJA;EAkFM,0BJvbU;APwhKhB;;AWnrJA;EAkFM,0BTlaW;AFugKjB;;AWvrJA;EAkFM,0BTjaW;AF0gKjB;;AW3rJA;EAkFM,0BThaW;AF6gKjB;;AW/rJA;EAkFM,0BT/ZW;AFghKjB;;AWnsJA;EAkFM,0BT9ZW;AFmhKjB;;AWvsJA;EAkFM,0BT7ZW;AFshKjB;;AW3sJA;EAkFM,0BT5ZW;AFyhKjB;;AW/sJA;EAkFM,0BT3ZW;AF4hKjB;;AWntJA;EAkFM,0BT1ZW;AF+hKjB;;AWvtJA;EAkFM,0BTzZW;AFkiKjB;;AW3tJA;EAkFM,uBT3bS;AFwkKf;;AW/tJA;EAkFM,0BTrbY;AFskKlB;;AWnuJA;EAkFM,0BTnbY;AFwkKlB;;AWvuJA;EAuFI,YAAY;EACZ,qBAAqB;EACrB,aAAa;AXopJjB;;AW7oJA;EACE,gBAAgB;AXgpJlB;;AW5oJA;EAGM,cAAc;AX6oJpB;;AWxoJA;EAGI,yBTjdc;ESkdd,WT1dW;AFmmKf;;AW7oJA;EAOM,yBAAoC;EACpC,WT9dS;AFwmKf;;AWlpJA;EAWM,4BT3dY;AFsmKlB;;AWtpJA;EAcM,oCT1dS;AFsmKf;;AW1pJA;EAiBM,qBTjeY;AF8mKlB;;AW9pJA;EAoBM,WT1eS;AFwnKf;;AWlqJA;EAyBI,yBAAuC;AX6oJ3C;;AWtqJA;EA2BM,cT7eY;AF4nKlB;;AW1qJA;EA8BM,4BAAyC;AXgpJ/C;;AW9qJA;EAmCI,yBAAoC;EACpC,qBAAkC;EAClC,WT3fW;AF0oKf;;AchpKA;EAEI,sBZcW;EYbX,cAAc;EACd,YAAY;EACZ,OAAO;EACP,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,WAAW;EACX,aZ+iBoC;AFmmJxC;;Ac5oKA;;EAIM,qBZNY;AFmpKlB;;AcjpKA;EAcM,WZxBS;EYyBT,yBZfS;AFspKf;;AcloKA;;EAGI,qBZ3Bc;AF+pKlB;;AcvoKA;EAMI,yBZ5Bc;AFiqKlB;;Ac3oKA;;EAWQ,qBZnCU;AFwqKlB;;AchpKA;EAcQ,yBAAuB;EACvB,uCAAyC;AdsoKjD;;AcrpKA;;;;;;EA0BQ,kBZxDO;AF4rKf;;AensKA;EACE,kBAAkB;EAClB,QAAQ;EACR,MAAM;EACN,abojBsC;AFkpJxC;;Ae1sKA;EAOI,eAAe;AfusKnB;;AensKA;EACE,OAAO;EACP,kBAAkB;EAClB,MAAM;EACN,abyiBsC;AF6pJxC;;Ae1sKA;EAOI,eAAe;AfusKnB;;AensKA;EACE,SAAS;EACT,kBAAkB;EAClB,QAAQ;EACR,ab8hBsC;AFwqJxC;;Ae1sKA;EAOI,eAAe;AfusKnB;;AensKA;EACE,SAAS;EACT,OAAO;EACP,kBAAkB;EAClB,abmhBsC;AFmrJxC;;Ae1sKA;EAOI,eAAe;AfusKnB;;AensKA;EAEI,wCb/Bc;EagCd,WbxCW;AF6uKf;;AexsKA;EAMM,uCbnCY;EaoCZ,cb3CY;AFivKlB;;AgBvvKE;EACE,mDAA6C;AhB0vKjD;;AgBvvKM;EACE,WdAO;EcCP,yBdSO;AFivKf;;AgBtvKI;EACE,yCdmBW;EclBX,WdPS;AFgwKf;;AgBrwKE;EACE,qDAA6C;AhBwwKjD;;AgBrwKM;EACE,WdAO;EcCP,yBdSO;AF+vKf;;AgBpwKI;EACE,2CdAY;EcCZ,WdPS;AF8wKf;;AgBnxKE;EACE,mDAA6C;AhBsxKjD;;AgBnxKM;EACE,WdAO;EcCP,yBdSO;AF6wKf;;AgBlxKI;EACE,yCd0BW;EczBX,WdPS;AF4xKf;;AgBjyKE;EACE,oDAA6C;AhBoyKjD;;AgBjyKM;EACE,WdAO;EcCP,yBdSO;AF2xKf;;AgBhyKI;EACE,0Cd4BW;Ec3BX,WdPS;AF0yKf;;AgB/yKE;EACE,mDAA6C;AhBkzKjD;;AgBzyKI;EACE,yCdyBW;EcxBX,cd2EiB;AFiuKvB;;AgBxzKE;EACE,mDAA6C;AhB2zKjD;;AgBxzKM;EACE,WdAO;EcCP,yBdSO;AFkzKf;;AgBvzKI;EACE,yCduBW;EctBX,WdPS;AFi0Kf;;AgBt0KE;EACE,qDAA6C;AhBy0KjD;;AgBh0KI;EACE,2CdLY;EcMZ,cd2EiB;AFwvKvB;;AgB/0KE;EACE,kDAA6C;AhBk1KjD;;AgB/0KM;EACE,WdAO;EcCP,yBdSO;AFy0Kf;;AgB90KI;EACE,wCdEY;EcDZ,WdPS;AFw1Kf;;AgB71KE;EACE,oDAA6C;AhBg2KjD;;AgB71KM;EACE,WdAO;EcCP,yBdSO;AFu1Kf;;AgB51KI;EACE,0CTVa;ESWb,WdPS;AFs2Kf;;AgB32KE;EACE,iDAA6C;AhB82KjD;;AgB32KM;EACE,WdAO;EcCP,yBdSO;AFq2Kf;;AgB12KI;EACE,uCTTQ;ESUR,WdPS;AFo3Kf;;AgBz3KE;EACE,oDAA6C;AhB43KjD;;AgBz3KM;EACE,WdAO;EcCP,yBdSO;AFm3Kf;;AgBx3KI;EACE,0CTPS;ESQT,WdPS;AFk4Kf;;AgBv4KE;EACE,mDAA6C;AhB04KjD;;AgBj4KI;EACE,yCTNQ;ESOR,cd2EiB;AFyzKvB;;AgBh5KE;EACE,oDAA6C;AhBm5KjD;;AgBh5KM;EACE,WdAO;EcCP,yBdSO;AF04Kf;;AgB/4KI;EACE,0CTJW;ESKX,WdPS;AFy5Kf;;AgB95KE;EACE,mDAA6C;AhBi6KjD;;AgB95KM;EACE,WdAO;EcCP,yBdSO;AFw5Kf;;AgB75KI;EACE,yCTFU;ESGV,WdPS;AFu6Kf;;AgB56KE;EACE,mDAA6C;AhB+6KjD;;AgB56KM;EACE,WdAO;EcCP,yBdSO;AFs6Kf;;AgB36KI;EACE,yCdmBW;EclBX,WdPS;AFq7Kf;;AgB17KE;EACE,oDAA6C;AhB67KjD;;AgB17KM;EACE,WdAO;EcCP,yBdSO;AFo7Kf;;AgBz7KI;EACE,0CdoBW;EcnBX,WdPS;AFm8Kf;;AgBx8KE;EACE,oDAA6C;AhB28KjD;;AgBx8KM;EACE,WdAO;EcCP,yBdSO;AFk8Kf;;AgBv8KI;EACE,0CdqBW;EcpBX,WdPS;AFi9Kf;;AgBt9KE;EACE,oDAA6C;AhBy9KjD;;AgBt9KM;EACE,WdAO;EcCP,yBdSO;AFg9Kf;;AgBr9KI;EACE,0CdsBW;EcrBX,WdPS;AF+9Kf;;AgBp+KE;EACE,mDAA6C;AhBu+KjD;;AgBp+KM;EACE,WdAO;EcCP,yBdSO;AF89Kf;;AgBn+KI;EACE,yCduBW;EctBX,WdPS;AF6+Kf;;AgBl/KE;EACE,oDAA6C;AhBq/KjD;;AgB5+KI;EACE,0CdwBW;EcvBX,cd2EiB;AFo6KvB;;AgB3/KE;EACE,mDAA6C;AhB8/KjD;;AgBr/KI;EACE,yCdyBW;EcxBX,cd2EiB;AF66KvB;;AgBpgLE;EACE,mDAA6C;AhBugLjD;;AgBpgLM;EACE,WdAO;EcCP,yBdSO;AF8/Kf;;AgBngLI;EACE,yCd0BW;EczBX,WdPS;AF6gLf;;AgBlhLE;EACE,oDAA6C;AhBqhLjD;;AgBlhLM;EACE,WdAO;EcCP,yBdSO;AF4gLf;;AgBjhLI;EACE,0Cd2BW;Ec1BX,WdPS;AF2hLf;;AgBhiLE;EACE,oDAA6C;AhBmiLjD;;AgBhiLM;EACE,WdAO;EcCP,yBdSO;AF0hLf;;AgB/hLI;EACE,0Cd4BW;Ec3BX,WdPS;AFyiLf;;AgB9iLE;EACE,qDAA6C;AhBijLjD;;AgBxiLI;EACE,2CdNS;EcOT,cd2EiB;AFg+KvB;;AgBvjLE;EACE,qDAA6C;AhB0jLjD;;AgBvjLM;EACE,WdAO;EcCP,yBdSO;AFijLf;;AgBtjLI;EACE,2CdAY;EcCZ,WdPS;AFgkLf;;AgBrkLE;EACE,kDAA6C;AhBwkLjD;;AgBrkLM;EACE,WdAO;EcCP,yBdSO;AF+jLf;;AgBpkLI;EACE,wCdEY;EcDZ,WdPS;AF8kLf;;AiBrlLA;EAGI,mBAAmB;AjBslLvB;;AiBzlLA;EdcI,gBcNwB;EACxB,iBAAiB;EACjB,gBAAgB;AjBqlLpB;;AiB/lLA;EAeI,gBAAgB;EAChB,kBAAkB;AjBolLtB;;AiBpmLA;EAmBM,sBfZS;EeaT,eAAe;EACf,cAAc;EACd,gBAAgB;EAChB,gBAAgB;EAChB,eAAe;EACf,UAAU;EACV,aAAa;EACb,kBAAkB;EAClB,QAAQ;EACR,iBAAiB;EACjB,MAAM;AjBqlLZ;;AiBjlLE;EACE,8BAAmC;AjBolLvC;;AiB/kLA;EACE,yBfjCgB;EekChB,kBViKgC;EUhKhC,WV+JyB;APm7K3B;;AiBrlLA;EAQI,yBAA8D;EAC9D,cAAyC;AjBilL7C;;AiB5kLA;EdxCI,kBcyCwB;EAC1B,yBfhDgB;EeiDhB,sBVkJgC;EUjJhC,cf7CgB;Ee8ChB,eAAe;EACf,YAAY;EACZ,qBAAqB;EACrB,eAAe;EACf,iBAAiB;EACjB,kBAAkB;EAClB,kBAAkB;AjB+kLpB;;AiB1lLA;;;;;;;;EAsBI,cAAc;EACd,eAAe;AjB+kLnB;;AiBtmLA;EA2BI,cAAc;AjB+kLlB;;AiB1mLA;EA+BI,yBf7Ec;Ee8Ed,qBAAuD;EACvD,WVmHuB;AP49K3B;;AiBhnLA;ER1CM,gDPKS;AFypLf;;AiBpnLA;EA2CI,eAAe;EACf,gBAAgB;EAChB,kBAAkB;EAClB,YAAY;EACZ,SAAS;AjB6kLb;;AiBvkLA;ECPE,yBX0G0B;EHnFtB,kBAtCY;EciBhB,gBhBsG+B;EC9L7B,sBImM4B;APw+KhC;;AiB1kLA;;EAGI,yBAAsC;EACtC,Wf5GW;Ee6GX,qBfvGc;AFmrLlB;;AiBjlLA;;;EASM,yBAAoC;EACpC,cf/GY;EegHZ,qBAAsC;AjB8kL5C;;AiBzlLA;EAeI,yBAAsC;EACtC,WfxHW;EeyHX,qBfnHc;AFisLlB;;AiB/lLA;EAqBM,yBAAqC;EACrC,cf3HY;Ee4HZ,qBAAoC;AjB8kL1C;;AmBntLA;EhBaI,sBDmMgC;EiB1MhC,wEZkNiF;EY7MnF,sBjBLa;EiBMb,8BjBJgB;EiBKhB,mBjBuGW;EiBtGX,aAAa;AnB8sLf;;AmB5tLA;EAiBI,cjBJc;EiBKd,0BAA0B;AnB+sL9B;;AmBjuLA;EAqBM,cjBbY;AF6tLlB;;AmBruLA;EA0BI,gBAAgB;AnB+sLpB;;AmBzuLA;EA+BI,0BAAqD;AnB8sLzD;;AmB7uLA;EAmCI,0BAAsD;AnB8sL1D;;AmBjvLA;EAuCI,0BAAmD;AnB8sLvD;;AmBrvLA;EA2CI,0BAAsD;AnB8sL1D;;AmB1sLA;EAEI,yBAAoC;AnB4sLxC;;AoB9vLA;EAEI,kBAAkB;ApBgwLtB;;AoBlwLA;EAMI,WlBWW;EkBVX,WAAW;ApBgwLf;;AoBvwLA;EAUM,WAAW;ApBiwLjB;;AoB3wLA;EAeI,WlBRW;EkBSX,0BAA0B;ApBgwL9B;;AoB1vLE;EACE,WlBhBW;EkBiBX,yBlBQa;EkBPb,qBAAgC;ApB6vLpC;;AoB1vLE;EC/BA,cCwGgE;EjBnG9D,yBiBmG8D;EDtGhE,qBCsGgE;AtBurLlE;;AqB3xLE;EACE,yBAAqC;ArB8xLzC;;AqB3xLE;EACE,cAA0B;ArB8xL9B;;AoB9wLE;EACE,WlBhBW;EkBiBX,yBlBXc;EkBYd,qBAAgC;ApBixLpC;;AoB9wLE;EC/BA,cCwGgE;EjBnG9D,yBiBmG8D;EDtGhE,qBCsGgE;AtB2sLlE;;AqB/yLE;EACE,yBAAqC;ArBkzLzC;;AqB/yLE;EACE,cAA0B;ArBkzL9B;;AoBlyLE;EACE,WlBhBW;EkBiBX,yBlBea;EkBdb,qBAAgC;ApBqyLpC;;AoBlyLE;EC/BA,cCwGgE;EjBnG9D,yBiBmG8D;EDtGhE,qBCsGgE;AtB+tLlE;;AqBn0LE;EACE,yBAAqC;ArBs0LzC;;AqBn0LE;EACE,cAA0B;ArBs0L9B;;AoBtzLE;EACE,WlBhBW;EkBiBX,yBlBiBa;EkBhBb,qBAAgC;ApByzLpC;;AoBtzLE;EC/BA,cCwGgE;EjBnG9D,yBiBmG8D;EDtGhE,qBCsGgE;AtBmvLlE;;AqBv1LE;EACE,yBAAqC;ArB01LzC;;AqBv1LE;EACE,cAA0B;ArB01L9B;;AoB10LE;EACE,clBkEmB;EkBjEnB,yBlBca;EkBbb,qBAAgC;ApB60LpC;;AoB10LE;EC/BA,cCwGgE;EjBnG9D,yBiBmG8D;EDtGhE,qBCsGgE;AtBuwLlE;;AqB32LE;EACE,yBAAqC;ArB82LzC;;AqB32LE;EACE,cAA0B;ArB82L9B;;AoB91LE;EACE,WlBhBW;EkBiBX,yBlBYa;EkBXb,qBAAgC;ApBi2LpC;;AoB91LE;EC/BA,cCwGgE;EjBnG9D,yBiBmG8D;EDtGhE,qBCsGgE;AtB2xLlE;;AqB/3LE;EACE,yBAAqC;ArBk4LzC;;AqB/3LE;EACE,cAA0B;ArBk4L9B;;AoBl3LE;EACE,clBkEmB;EkBjEnB,yBlBhBc;EkBiBd,qBAAgC;ApBq3LpC;;AoBl3LE;EC/BA,cCwGgE;EjBnG9D,yBiBmG8D;EDtGhE,qBCsGgE;AtB+yLlE;;AqBn5LE;EACE,yBAAqC;ArBs5LzC;;AqBn5LE;EACE,cAA0B;ArBs5L9B;;AoBt4LE;EACE,WlBhBW;EkBiBX,yBlBTc;EkBUd,qBAAgC;ApBy4LpC;;AoBt4LE;EC/BA,cCwGgE;EjBnG9D,yBiBmG8D;EDtGhE,qBCsGgE;AtBm0LlE;;AqBv6LE;EACE,yBAAqC;ArB06LzC;;AqBv6LE;EACE,cAA0B;ArB06L9B;;AuBh7LA;EAEI,cAAc;AvBk7LlB;;AuBp7LA;EAQM,sBrBDS;EqBET,gBAAgB;EAChB,yDrBAY;EqBCZ,wBAAgB;EAAhB,gBAAgB;EAChB,MAAM;EACN,WAAW;AvBg7LjB;;AuB77LA;EAmBU,yBrBHQ;EqBIR,yDrBwS2C;AFsoLrD;;AuBl8LA;;;EA+BM,SAAS;AvBy6Lf;;AuBx8LA;;;EAwCM,kBAAkB;AvBs6LxB;;AuB98LA;;;;EAiDM,sBAAsB;AvBo6L5B;;AuBh6LE;;;;;;EAQM,oBrB+DY;AFk2LpB;;AuBz6LE;;;;;;EAYM,qBrB2DY;AF22LpB;;AuB95LA;EACE,oCAAoC;AvBi6LtC;;AAEA;EuB/5LE,eAAe;AvBi6LjB;;AAEA;EuBh6LI,iCAA8C;AvBk6LlD;;AAEA;EuB95LQ,wBAAwB;AvBg6LhC;;AAEA;EuB/5LQ,yBAAyB;AvBi6LjC;;AuB15LA;EAEI,qBAAqB;EACrB,WAAW;AvB45Lf;;AuB/5LA;;EAOM,gBrBmM8B;AF0tLpC;;AuBp6LA;EAYI,2BAA0C;EAC1C,qBrB6LgC;AF+tLpC;;AuBz6LA;;EAkBQ,gBAAgB;AvB45LxB;;AuBt5LA;;;EAKM,qBrBjHY;AFwgMlB;;AuB55LA;EAUM,crBzHY;EqB0HZ,yBAAsC;EACtC,qBrBxHY;AF8gMlB;;AuBl6LA;EAiBM,4BrB7HY;AFkhMlB;;AuBt6LA;;EAqBM,yBrBjIY;AFuhMlB;;AuB36LA;EAyBQ,yBAAoC;AvBs5L5C;;AwBviME;EACE,kBAAkB;AxB0iMtB;;AwBxiME;EACE,kBAAkB;AxB2iMtB;;AwBhjMA;;;;;;;;EAgBI,qBAAqB;EACrB,eAAe;EACf,iBAAiB;EACjB,kBAAkB;EAClB,QAAQ;EACR,UAAU;AxB2iMd;;AyBpkMA;EACE,YAAY;ErB8HR,iBAtCY;EqBtFhB,gBvBgP+B;EuB/O/B,cAAc;EACd,WvBgBa;EuBfb,yBvBKa;EuBJb,WAAW;AzBukMb;;A0BlkME;EDDE,WvBUW;EuBTX,qBAAqB;AzBukMzB;;A0BnkME;EDCI,YAAY;AzBskMlB;;AyBvlMA;EAsBI,aAAa;AzBqkMjB;;AyB3jMA;EACE,UAAU;EACV,6BAA6B;EAC7B,SAAS;AzB8jMX;;AyBxjMA;EACE,oBAAoB;AzB2jMtB", "file": "adminlte.components.css", "sourcesContent": ["/*!\n *   AdminLTE v3.1.0-rc\n *     Only Components\n *   Author: Colorlib\n *   Website: AdminLTE.io <https://adminlte.io>\n *   License: Open source - MIT <https://opensource.org/licenses/MIT>\n */\n\n// Bootstrap\n// ---------------------------------------------------\n@import \"~bootstrap/scss/functions\";\n@import \"../bootstrap-variables\";\n@import \"~bootstrap/scss/mixins\";\n\n// Variables and Mixins\n// ---------------------------------------------------\n@import \"../variables\";\n@import \"../mixins\";\n\n@import \"components\";\n", "/*!\n *   AdminLTE v3.1.0-rc\n *     Only Components\n *   Author: Colorlib\n *   Website: AdminLTE.io <https://adminlte.io>\n *   License: Open source - MIT <https://opensource.org/licenses/MIT>\n */\n.form-group.has-icon {\n  position: relative;\n}\n\n.form-group.has-icon .form-control {\n  padding-right: 35px;\n}\n\n.form-group.has-icon .form-icon {\n  background-color: transparent;\n  border: 0;\n  cursor: pointer;\n  font-size: 1rem;\n  padding: 0.375rem 0.75rem;\n  position: absolute;\n  right: 3px;\n  top: 0;\n}\n\n.btn-group-vertical .btn.btn-flat:first-of-type, .btn-group-vertical .btn.btn-flat:last-of-type {\n  border-radius: 0;\n}\n\n.form-control-feedback.fa, .form-control-feedback.fas, .form-control-feedback.far, .form-control-feedback.fab, .form-control-feedback.fal, .form-control-feedback.fad, .form-control-feedback.svg-inline--fa, .form-control-feedback.ion {\n  line-height: calc(2.25rem + 2px);\n}\n\n.input-lg + .form-control-feedback.fa, .input-lg + .form-control-feedback.fas, .input-lg + .form-control-feedback.far, .input-lg + .form-control-feedback.fab, .input-lg + .form-control-feedback.fal, .input-lg + .form-control-feedback.fad, .input-lg + .form-control-feedback.svg-inline--fa, .input-lg + .form-control-feedback.ion,\n.input-group-lg + .form-control-feedback.fa,\n.input-group-lg + .form-control-feedback.fas,\n.input-group-lg + .form-control-feedback.far,\n.input-group-lg + .form-control-feedback.fab,\n.input-group-lg + .form-control-feedback.fal,\n.input-group-lg + .form-control-feedback.fad,\n.input-group-lg + .form-control-feedback.svg-inline--fa,\n.input-group-lg + .form-control-feedback.ion {\n  line-height: calc(2.875rem + 2px);\n}\n\n.form-group-lg .form-control + .form-control-feedback.fa, .form-group-lg .form-control + .form-control-feedback.fas, .form-group-lg .form-control + .form-control-feedback.far, .form-group-lg .form-control + .form-control-feedback.fab, .form-group-lg .form-control + .form-control-feedback.fal, .form-group-lg .form-control + .form-control-feedback.fad, .form-group-lg .form-control + .form-control-feedback.svg-inline--fa, .form-group-lg .form-control + .form-control-feedback.ion {\n  line-height: calc(2.875rem + 2px);\n}\n\n.input-sm + .form-control-feedback.fa, .input-sm + .form-control-feedback.fas, .input-sm + .form-control-feedback.far, .input-sm + .form-control-feedback.fab, .input-sm + .form-control-feedback.fal, .input-sm + .form-control-feedback.fad, .input-sm + .form-control-feedback.svg-inline--fa, .input-sm + .form-control-feedback.ion,\n.input-group-sm + .form-control-feedback.fa,\n.input-group-sm + .form-control-feedback.fas,\n.input-group-sm + .form-control-feedback.far,\n.input-group-sm + .form-control-feedback.fab,\n.input-group-sm + .form-control-feedback.fal,\n.input-group-sm + .form-control-feedback.fad,\n.input-group-sm + .form-control-feedback.svg-inline--fa,\n.input-group-sm + .form-control-feedback.ion {\n  line-height: calc(1.8125rem + 2px);\n}\n\n.form-group-sm .form-control + .form-control-feedback.fa, .form-group-sm .form-control + .form-control-feedback.fas, .form-group-sm .form-control + .form-control-feedback.far, .form-group-sm .form-control + .form-control-feedback.fab, .form-group-sm .form-control + .form-control-feedback.fal, .form-group-sm .form-control + .form-control-feedback.fad, .form-group-sm .form-control + .form-control-feedback.svg-inline--fa, .form-group-sm .form-control + .form-control-feedback.ion {\n  line-height: calc(1.8125rem + 2px);\n}\n\nlabel:not(.form-check-label):not(.custom-file-label) {\n  font-weight: 700;\n}\n\n.warning-feedback {\n  font-size: 80%;\n  color: #ffc107;\n  display: none;\n  margin-top: 0.25rem;\n  width: 100%;\n}\n\n.warning-tooltip {\n  border-radius: 0.25rem;\n  font-size: 0.875rem;\n  background-color: rgba(255, 193, 7, 0.9);\n  color: #1f2d3d;\n  display: none;\n  line-height: 1.5;\n  margin-top: .1rem;\n  max-width: 100%;\n  padding: 0.25rem 0.5rem;\n  position: absolute;\n  top: 100%;\n  z-index: 5;\n}\n\n.form-control.is-warning {\n  border-color: #ffc107;\n}\n\n.form-control.is-warning:focus {\n  border-color: #ffc107;\n  box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.25);\n}\n\n.form-control.is-warning ~ .warning-feedback,\n.form-control.is-warning ~ .warning-tooltip {\n  display: block;\n}\n\ntextarea.form-control.is-warning {\n  padding-right: 2.25rem;\n  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);\n}\n\n.custom-select.is-warning {\n  border-color: #ffc107;\n}\n\n.custom-select.is-warning:focus {\n  border-color: #ffc107;\n  box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.25);\n}\n\n.custom-select.is-warning ~ .warning-feedback,\n.custom-select.is-warning ~ .warning-tooltip {\n  display: block;\n}\n\n.form-control-file.is-warning ~ .warning-feedback,\n.form-control-file.is-warning ~ .warning-tooltip {\n  display: block;\n}\n\n.form-check-input.is-warning ~ .form-check-label {\n  color: #ffc107;\n}\n\n.form-check-input.is-warning ~ .warning-feedback,\n.form-check-input.is-warning ~ .warning-tooltip {\n  display: block;\n}\n\n.custom-control-input.is-warning ~ .custom-control-label {\n  color: #ffc107;\n}\n\n.custom-control-input.is-warning ~ .custom-control-label::before {\n  border-color: #ffc107;\n}\n\n.custom-control-input.is-warning ~ .warning-feedback,\n.custom-control-input.is-warning ~ .warning-tooltip {\n  display: block;\n}\n\n.custom-control-input.is-warning:checked ~ .custom-control-label::before {\n  background-color: #ffce3a;\n  border-color: #ffce3a;\n}\n\n.custom-control-input.is-warning:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.25);\n}\n\n.custom-control-input.is-warning:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #ffc107;\n}\n\n.custom-file-input.is-warning ~ .custom-file-label {\n  border-color: #ffc107;\n}\n\n.custom-file-input.is-warning ~ .warning-feedback,\n.custom-file-input.is-warning ~ .warning-tooltip {\n  display: block;\n}\n\n.custom-file-input.is-warning:focus ~ .custom-file-label {\n  border-color: #ffc107;\n  box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.25);\n}\n\nbody.text-sm .input-group-text {\n  font-size: 0.875rem;\n}\n\n.form-control.form-control-border,\n.custom-select.form-control-border {\n  border-top: 0;\n  border-left: 0;\n  border-right: 0;\n  border-radius: 0;\n  box-shadow: inherit;\n}\n\n.form-control.form-control-border.border-width-2,\n.custom-select.form-control-border.border-width-2 {\n  border-bottom-width: 2px;\n}\n\n.form-control.form-control-border.border-width-3,\n.custom-select.form-control-border.border-width-3 {\n  border-bottom-width: 3px;\n}\n\n.custom-switch.custom-switch-off-primary .custom-control-input ~ .custom-control-label::before {\n  background-color: #007bff;\n  border-color: #004a99;\n}\n\n.custom-switch.custom-switch-off-primary .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-switch.custom-switch-off-primary .custom-control-input ~ .custom-control-label::after {\n  background-color: #003e80;\n}\n\n.custom-switch.custom-switch-on-primary .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #007bff;\n  border-color: #004a99;\n}\n\n.custom-switch.custom-switch-on-primary .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-switch.custom-switch-on-primary .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #99caff;\n}\n\n.custom-switch.custom-switch-off-secondary .custom-control-input ~ .custom-control-label::before {\n  background-color: #6c757d;\n  border-color: #3d4246;\n}\n\n.custom-switch.custom-switch-off-secondary .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-switch.custom-switch-off-secondary .custom-control-input ~ .custom-control-label::after {\n  background-color: #313539;\n}\n\n.custom-switch.custom-switch-on-secondary .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #6c757d;\n  border-color: #3d4246;\n}\n\n.custom-switch.custom-switch-on-secondary .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-switch.custom-switch-on-secondary .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #bcc1c6;\n}\n\n.custom-switch.custom-switch-off-success .custom-control-input ~ .custom-control-label::before {\n  background-color: #28a745;\n  border-color: #145523;\n}\n\n.custom-switch.custom-switch-off-success .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-switch.custom-switch-off-success .custom-control-input ~ .custom-control-label::after {\n  background-color: #0f401b;\n}\n\n.custom-switch.custom-switch-on-success .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #28a745;\n  border-color: #145523;\n}\n\n.custom-switch.custom-switch-on-success .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-switch.custom-switch-on-success .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #86e29b;\n}\n\n.custom-switch.custom-switch-off-info .custom-control-input ~ .custom-control-label::before {\n  background-color: #17a2b8;\n  border-color: #0c525d;\n}\n\n.custom-switch.custom-switch-off-info .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-switch.custom-switch-off-info .custom-control-input ~ .custom-control-label::after {\n  background-color: #093e47;\n}\n\n.custom-switch.custom-switch-on-info .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #17a2b8;\n  border-color: #0c525d;\n}\n\n.custom-switch.custom-switch-on-info .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-switch.custom-switch-on-info .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #7adeee;\n}\n\n.custom-switch.custom-switch-off-warning .custom-control-input ~ .custom-control-label::before {\n  background-color: #ffc107;\n  border-color: #a07800;\n}\n\n.custom-switch.custom-switch-off-warning .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-switch.custom-switch-off-warning .custom-control-input ~ .custom-control-label::after {\n  background-color: #876500;\n}\n\n.custom-switch.custom-switch-on-warning .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #ffc107;\n  border-color: #a07800;\n}\n\n.custom-switch.custom-switch-on-warning .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-switch.custom-switch-on-warning .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #ffe7a0;\n}\n\n.custom-switch.custom-switch-off-danger .custom-control-input ~ .custom-control-label::before {\n  background-color: #dc3545;\n  border-color: #921925;\n}\n\n.custom-switch.custom-switch-off-danger .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-switch.custom-switch-off-danger .custom-control-input ~ .custom-control-label::after {\n  background-color: #7c151f;\n}\n\n.custom-switch.custom-switch-on-danger .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #dc3545;\n  border-color: #921925;\n}\n\n.custom-switch.custom-switch-on-danger .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-switch.custom-switch-on-danger .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #f3b7bd;\n}\n\n.custom-switch.custom-switch-off-light .custom-control-input ~ .custom-control-label::before {\n  background-color: #f8f9fa;\n  border-color: #bdc6d0;\n}\n\n.custom-switch.custom-switch-off-light .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(248, 249, 250, 0.25);\n}\n\n.custom-switch.custom-switch-off-light .custom-control-input ~ .custom-control-label::after {\n  background-color: #aeb9c5;\n}\n\n.custom-switch.custom-switch-on-light .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #f8f9fa;\n  border-color: #bdc6d0;\n}\n\n.custom-switch.custom-switch-on-light .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(248, 249, 250, 0.25);\n}\n\n.custom-switch.custom-switch-on-light .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: white;\n}\n\n.custom-switch.custom-switch-off-dark .custom-control-input ~ .custom-control-label::before {\n  background-color: #343a40;\n  border-color: #060708;\n}\n\n.custom-switch.custom-switch-off-dark .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-switch.custom-switch-off-dark .custom-control-input ~ .custom-control-label::after {\n  background-color: black;\n}\n\n.custom-switch.custom-switch-on-dark .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #343a40;\n  border-color: #060708;\n}\n\n.custom-switch.custom-switch-on-dark .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-switch.custom-switch-on-dark .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #7a8793;\n}\n\n.custom-switch.custom-switch-off-lightblue .custom-control-input ~ .custom-control-label::before {\n  background-color: #3c8dbc;\n  border-color: #23536f;\n}\n\n.custom-switch.custom-switch-off-lightblue .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(60, 141, 188, 0.25);\n}\n\n.custom-switch.custom-switch-off-lightblue .custom-control-input ~ .custom-control-label::after {\n  background-color: #1d455b;\n}\n\n.custom-switch.custom-switch-on-lightblue .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #3c8dbc;\n  border-color: #23536f;\n}\n\n.custom-switch.custom-switch-on-lightblue .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(60, 141, 188, 0.25);\n}\n\n.custom-switch.custom-switch-on-lightblue .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #acd0e5;\n}\n\n.custom-switch.custom-switch-off-navy .custom-control-input ~ .custom-control-label::before {\n  background-color: #001f3f;\n  border-color: black;\n}\n\n.custom-switch.custom-switch-off-navy .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 31, 63, 0.25);\n}\n\n.custom-switch.custom-switch-off-navy .custom-control-input ~ .custom-control-label::after {\n  background-color: black;\n}\n\n.custom-switch.custom-switch-on-navy .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #001f3f;\n  border-color: black;\n}\n\n.custom-switch.custom-switch-on-navy .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 31, 63, 0.25);\n}\n\n.custom-switch.custom-switch-on-navy .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #006ad8;\n}\n\n.custom-switch.custom-switch-off-olive .custom-control-input ~ .custom-control-label::before {\n  background-color: #3d9970;\n  border-color: #20503b;\n}\n\n.custom-switch.custom-switch-off-olive .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(61, 153, 112, 0.25);\n}\n\n.custom-switch.custom-switch-off-olive .custom-control-input ~ .custom-control-label::after {\n  background-color: #193e2d;\n}\n\n.custom-switch.custom-switch-on-olive .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #3d9970;\n  border-color: #20503b;\n}\n\n.custom-switch.custom-switch-on-olive .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(61, 153, 112, 0.25);\n}\n\n.custom-switch.custom-switch-on-olive .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #99d6bb;\n}\n\n.custom-switch.custom-switch-off-lime .custom-control-input ~ .custom-control-label::before {\n  background-color: #01ff70;\n  border-color: #009a43;\n}\n\n.custom-switch.custom-switch-off-lime .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(1, 255, 112, 0.25);\n}\n\n.custom-switch.custom-switch-off-lime .custom-control-input ~ .custom-control-label::after {\n  background-color: #008138;\n}\n\n.custom-switch.custom-switch-on-lime .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #01ff70;\n  border-color: #009a43;\n}\n\n.custom-switch.custom-switch-on-lime .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(1, 255, 112, 0.25);\n}\n\n.custom-switch.custom-switch-on-lime .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #9affc6;\n}\n\n.custom-switch.custom-switch-off-fuchsia .custom-control-input ~ .custom-control-label::before {\n  background-color: #f012be;\n  border-color: #930974;\n}\n\n.custom-switch.custom-switch-off-fuchsia .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(240, 18, 190, 0.25);\n}\n\n.custom-switch.custom-switch-off-fuchsia .custom-control-input ~ .custom-control-label::after {\n  background-color: #7b0861;\n}\n\n.custom-switch.custom-switch-on-fuchsia .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #f012be;\n  border-color: #930974;\n}\n\n.custom-switch.custom-switch-on-fuchsia .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(240, 18, 190, 0.25);\n}\n\n.custom-switch.custom-switch-on-fuchsia .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #f9a2e5;\n}\n\n.custom-switch.custom-switch-off-maroon .custom-control-input ~ .custom-control-label::before {\n  background-color: #d81b60;\n  border-color: #7d1038;\n}\n\n.custom-switch.custom-switch-off-maroon .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(216, 27, 96, 0.25);\n}\n\n.custom-switch.custom-switch-off-maroon .custom-control-input ~ .custom-control-label::after {\n  background-color: #670d2e;\n}\n\n.custom-switch.custom-switch-on-maroon .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #d81b60;\n  border-color: #7d1038;\n}\n\n.custom-switch.custom-switch-on-maroon .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(216, 27, 96, 0.25);\n}\n\n.custom-switch.custom-switch-on-maroon .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #f29aba;\n}\n\n.custom-switch.custom-switch-off-blue .custom-control-input ~ .custom-control-label::before {\n  background-color: #007bff;\n  border-color: #004a99;\n}\n\n.custom-switch.custom-switch-off-blue .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-switch.custom-switch-off-blue .custom-control-input ~ .custom-control-label::after {\n  background-color: #003e80;\n}\n\n.custom-switch.custom-switch-on-blue .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #007bff;\n  border-color: #004a99;\n}\n\n.custom-switch.custom-switch-on-blue .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-switch.custom-switch-on-blue .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #99caff;\n}\n\n.custom-switch.custom-switch-off-indigo .custom-control-input ~ .custom-control-label::before {\n  background-color: #6610f2;\n  border-color: #3d0894;\n}\n\n.custom-switch.custom-switch-off-indigo .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(102, 16, 242, 0.25);\n}\n\n.custom-switch.custom-switch-off-indigo .custom-control-input ~ .custom-control-label::after {\n  background-color: #33077c;\n}\n\n.custom-switch.custom-switch-on-indigo .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #6610f2;\n  border-color: #3d0894;\n}\n\n.custom-switch.custom-switch-on-indigo .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(102, 16, 242, 0.25);\n}\n\n.custom-switch.custom-switch-on-indigo .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #c3a1fa;\n}\n\n.custom-switch.custom-switch-off-purple .custom-control-input ~ .custom-control-label::before {\n  background-color: #6f42c1;\n  border-color: #432776;\n}\n\n.custom-switch.custom-switch-off-purple .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(111, 66, 193, 0.25);\n}\n\n.custom-switch.custom-switch-off-purple .custom-control-input ~ .custom-control-label::after {\n  background-color: #382063;\n}\n\n.custom-switch.custom-switch-on-purple .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #6f42c1;\n  border-color: #432776;\n}\n\n.custom-switch.custom-switch-on-purple .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(111, 66, 193, 0.25);\n}\n\n.custom-switch.custom-switch-on-purple .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #c7b5e7;\n}\n\n.custom-switch.custom-switch-off-pink .custom-control-input ~ .custom-control-label::before {\n  background-color: #e83e8c;\n  border-color: #ac145a;\n}\n\n.custom-switch.custom-switch-off-pink .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(232, 62, 140, 0.25);\n}\n\n.custom-switch.custom-switch-off-pink .custom-control-input ~ .custom-control-label::after {\n  background-color: #95124e;\n}\n\n.custom-switch.custom-switch-on-pink .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #e83e8c;\n  border-color: #ac145a;\n}\n\n.custom-switch.custom-switch-on-pink .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(232, 62, 140, 0.25);\n}\n\n.custom-switch.custom-switch-on-pink .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #f8c7dd;\n}\n\n.custom-switch.custom-switch-off-red .custom-control-input ~ .custom-control-label::before {\n  background-color: #dc3545;\n  border-color: #921925;\n}\n\n.custom-switch.custom-switch-off-red .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-switch.custom-switch-off-red .custom-control-input ~ .custom-control-label::after {\n  background-color: #7c151f;\n}\n\n.custom-switch.custom-switch-on-red .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #dc3545;\n  border-color: #921925;\n}\n\n.custom-switch.custom-switch-on-red .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-switch.custom-switch-on-red .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #f3b7bd;\n}\n\n.custom-switch.custom-switch-off-orange .custom-control-input ~ .custom-control-label::before {\n  background-color: #fd7e14;\n  border-color: #aa4e01;\n}\n\n.custom-switch.custom-switch-off-orange .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(253, 126, 20, 0.25);\n}\n\n.custom-switch.custom-switch-off-orange .custom-control-input ~ .custom-control-label::after {\n  background-color: #904201;\n}\n\n.custom-switch.custom-switch-on-orange .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #fd7e14;\n  border-color: #aa4e01;\n}\n\n.custom-switch.custom-switch-on-orange .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(253, 126, 20, 0.25);\n}\n\n.custom-switch.custom-switch-on-orange .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #fed1ac;\n}\n\n.custom-switch.custom-switch-off-yellow .custom-control-input ~ .custom-control-label::before {\n  background-color: #ffc107;\n  border-color: #a07800;\n}\n\n.custom-switch.custom-switch-off-yellow .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-switch.custom-switch-off-yellow .custom-control-input ~ .custom-control-label::after {\n  background-color: #876500;\n}\n\n.custom-switch.custom-switch-on-yellow .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #ffc107;\n  border-color: #a07800;\n}\n\n.custom-switch.custom-switch-on-yellow .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-switch.custom-switch-on-yellow .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #ffe7a0;\n}\n\n.custom-switch.custom-switch-off-green .custom-control-input ~ .custom-control-label::before {\n  background-color: #28a745;\n  border-color: #145523;\n}\n\n.custom-switch.custom-switch-off-green .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-switch.custom-switch-off-green .custom-control-input ~ .custom-control-label::after {\n  background-color: #0f401b;\n}\n\n.custom-switch.custom-switch-on-green .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #28a745;\n  border-color: #145523;\n}\n\n.custom-switch.custom-switch-on-green .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-switch.custom-switch-on-green .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #86e29b;\n}\n\n.custom-switch.custom-switch-off-teal .custom-control-input ~ .custom-control-label::before {\n  background-color: #20c997;\n  border-color: #127155;\n}\n\n.custom-switch.custom-switch-off-teal .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(32, 201, 151, 0.25);\n}\n\n.custom-switch.custom-switch-off-teal .custom-control-input ~ .custom-control-label::after {\n  background-color: #0e5b44;\n}\n\n.custom-switch.custom-switch-on-teal .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #20c997;\n  border-color: #127155;\n}\n\n.custom-switch.custom-switch-on-teal .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(32, 201, 151, 0.25);\n}\n\n.custom-switch.custom-switch-on-teal .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #94eed3;\n}\n\n.custom-switch.custom-switch-off-cyan .custom-control-input ~ .custom-control-label::before {\n  background-color: #17a2b8;\n  border-color: #0c525d;\n}\n\n.custom-switch.custom-switch-off-cyan .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-switch.custom-switch-off-cyan .custom-control-input ~ .custom-control-label::after {\n  background-color: #093e47;\n}\n\n.custom-switch.custom-switch-on-cyan .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #17a2b8;\n  border-color: #0c525d;\n}\n\n.custom-switch.custom-switch-on-cyan .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-switch.custom-switch-on-cyan .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #7adeee;\n}\n\n.custom-switch.custom-switch-off-white .custom-control-input ~ .custom-control-label::before {\n  background-color: #fff;\n  border-color: #cccccc;\n}\n\n.custom-switch.custom-switch-off-white .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 255, 255, 0.25);\n}\n\n.custom-switch.custom-switch-off-white .custom-control-input ~ .custom-control-label::after {\n  background-color: #bfbfbf;\n}\n\n.custom-switch.custom-switch-on-white .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #fff;\n  border-color: #cccccc;\n}\n\n.custom-switch.custom-switch-on-white .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 255, 255, 0.25);\n}\n\n.custom-switch.custom-switch-on-white .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: white;\n}\n\n.custom-switch.custom-switch-off-gray .custom-control-input ~ .custom-control-label::before {\n  background-color: #6c757d;\n  border-color: #3d4246;\n}\n\n.custom-switch.custom-switch-off-gray .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-switch.custom-switch-off-gray .custom-control-input ~ .custom-control-label::after {\n  background-color: #313539;\n}\n\n.custom-switch.custom-switch-on-gray .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #6c757d;\n  border-color: #3d4246;\n}\n\n.custom-switch.custom-switch-on-gray .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-switch.custom-switch-on-gray .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #bcc1c6;\n}\n\n.custom-switch.custom-switch-off-gray-dark .custom-control-input ~ .custom-control-label::before {\n  background-color: #343a40;\n  border-color: #060708;\n}\n\n.custom-switch.custom-switch-off-gray-dark .custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-switch.custom-switch-off-gray-dark .custom-control-input ~ .custom-control-label::after {\n  background-color: black;\n}\n\n.custom-switch.custom-switch-on-gray-dark .custom-control-input:checked ~ .custom-control-label::before {\n  background-color: #343a40;\n  border-color: #060708;\n}\n\n.custom-switch.custom-switch-on-gray-dark .custom-control-input:checked:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-switch.custom-switch-on-gray-dark .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #7a8793;\n}\n\n.custom-range.custom-range-primary:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-primary:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-range.custom-range-primary:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-range.custom-range-primary:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-range.custom-range-primary::-webkit-slider-thumb {\n  background-color: #007bff;\n}\n\n.custom-range.custom-range-primary::-webkit-slider-thumb:active {\n  background-color: #b3d7ff;\n}\n\n.custom-range.custom-range-primary::-moz-range-thumb {\n  background-color: #007bff;\n}\n\n.custom-range.custom-range-primary::-moz-range-thumb:active {\n  background-color: #b3d7ff;\n}\n\n.custom-range.custom-range-primary::-ms-thumb {\n  background-color: #007bff;\n}\n\n.custom-range.custom-range-primary::-ms-thumb:active {\n  background-color: #b3d7ff;\n}\n\n.custom-range.custom-range-secondary:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-secondary:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-range.custom-range-secondary:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-range.custom-range-secondary:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-range.custom-range-secondary::-webkit-slider-thumb {\n  background-color: #6c757d;\n}\n\n.custom-range.custom-range-secondary::-webkit-slider-thumb:active {\n  background-color: #caced1;\n}\n\n.custom-range.custom-range-secondary::-moz-range-thumb {\n  background-color: #6c757d;\n}\n\n.custom-range.custom-range-secondary::-moz-range-thumb:active {\n  background-color: #caced1;\n}\n\n.custom-range.custom-range-secondary::-ms-thumb {\n  background-color: #6c757d;\n}\n\n.custom-range.custom-range-secondary::-ms-thumb:active {\n  background-color: #caced1;\n}\n\n.custom-range.custom-range-success:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-success:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-range.custom-range-success:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-range.custom-range-success:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-range.custom-range-success::-webkit-slider-thumb {\n  background-color: #28a745;\n}\n\n.custom-range.custom-range-success::-webkit-slider-thumb:active {\n  background-color: #9be7ac;\n}\n\n.custom-range.custom-range-success::-moz-range-thumb {\n  background-color: #28a745;\n}\n\n.custom-range.custom-range-success::-moz-range-thumb:active {\n  background-color: #9be7ac;\n}\n\n.custom-range.custom-range-success::-ms-thumb {\n  background-color: #28a745;\n}\n\n.custom-range.custom-range-success::-ms-thumb:active {\n  background-color: #9be7ac;\n}\n\n.custom-range.custom-range-info:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-info:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-range.custom-range-info:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-range.custom-range-info:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-range.custom-range-info::-webkit-slider-thumb {\n  background-color: #17a2b8;\n}\n\n.custom-range.custom-range-info::-webkit-slider-thumb:active {\n  background-color: #90e4f1;\n}\n\n.custom-range.custom-range-info::-moz-range-thumb {\n  background-color: #17a2b8;\n}\n\n.custom-range.custom-range-info::-moz-range-thumb:active {\n  background-color: #90e4f1;\n}\n\n.custom-range.custom-range-info::-ms-thumb {\n  background-color: #17a2b8;\n}\n\n.custom-range.custom-range-info::-ms-thumb:active {\n  background-color: #90e4f1;\n}\n\n.custom-range.custom-range-warning:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-warning:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-range.custom-range-warning:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-range.custom-range-warning:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-range.custom-range-warning::-webkit-slider-thumb {\n  background-color: #ffc107;\n}\n\n.custom-range.custom-range-warning::-webkit-slider-thumb:active {\n  background-color: #ffeeba;\n}\n\n.custom-range.custom-range-warning::-moz-range-thumb {\n  background-color: #ffc107;\n}\n\n.custom-range.custom-range-warning::-moz-range-thumb:active {\n  background-color: #ffeeba;\n}\n\n.custom-range.custom-range-warning::-ms-thumb {\n  background-color: #ffc107;\n}\n\n.custom-range.custom-range-warning::-ms-thumb:active {\n  background-color: #ffeeba;\n}\n\n.custom-range.custom-range-danger:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-danger:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-range.custom-range-danger:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-range.custom-range-danger:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-range.custom-range-danger::-webkit-slider-thumb {\n  background-color: #dc3545;\n}\n\n.custom-range.custom-range-danger::-webkit-slider-thumb:active {\n  background-color: #f6cdd1;\n}\n\n.custom-range.custom-range-danger::-moz-range-thumb {\n  background-color: #dc3545;\n}\n\n.custom-range.custom-range-danger::-moz-range-thumb:active {\n  background-color: #f6cdd1;\n}\n\n.custom-range.custom-range-danger::-ms-thumb {\n  background-color: #dc3545;\n}\n\n.custom-range.custom-range-danger::-ms-thumb:active {\n  background-color: #f6cdd1;\n}\n\n.custom-range.custom-range-light:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-light:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(248, 249, 250, 0.25);\n}\n\n.custom-range.custom-range-light:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(248, 249, 250, 0.25);\n}\n\n.custom-range.custom-range-light:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(248, 249, 250, 0.25);\n}\n\n.custom-range.custom-range-light::-webkit-slider-thumb {\n  background-color: #f8f9fa;\n}\n\n.custom-range.custom-range-light::-webkit-slider-thumb:active {\n  background-color: white;\n}\n\n.custom-range.custom-range-light::-moz-range-thumb {\n  background-color: #f8f9fa;\n}\n\n.custom-range.custom-range-light::-moz-range-thumb:active {\n  background-color: white;\n}\n\n.custom-range.custom-range-light::-ms-thumb {\n  background-color: #f8f9fa;\n}\n\n.custom-range.custom-range-light::-ms-thumb:active {\n  background-color: white;\n}\n\n.custom-range.custom-range-dark:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-dark:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-range.custom-range-dark:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-range.custom-range-dark:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-range.custom-range-dark::-webkit-slider-thumb {\n  background-color: #343a40;\n}\n\n.custom-range.custom-range-dark::-webkit-slider-thumb:active {\n  background-color: #88939e;\n}\n\n.custom-range.custom-range-dark::-moz-range-thumb {\n  background-color: #343a40;\n}\n\n.custom-range.custom-range-dark::-moz-range-thumb:active {\n  background-color: #88939e;\n}\n\n.custom-range.custom-range-dark::-ms-thumb {\n  background-color: #343a40;\n}\n\n.custom-range.custom-range-dark::-ms-thumb:active {\n  background-color: #88939e;\n}\n\n.custom-range.custom-range-lightblue:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-lightblue:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(60, 141, 188, 0.25);\n}\n\n.custom-range.custom-range-lightblue:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(60, 141, 188, 0.25);\n}\n\n.custom-range.custom-range-lightblue:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(60, 141, 188, 0.25);\n}\n\n.custom-range.custom-range-lightblue::-webkit-slider-thumb {\n  background-color: #3c8dbc;\n}\n\n.custom-range.custom-range-lightblue::-webkit-slider-thumb:active {\n  background-color: #c0dbeb;\n}\n\n.custom-range.custom-range-lightblue::-moz-range-thumb {\n  background-color: #3c8dbc;\n}\n\n.custom-range.custom-range-lightblue::-moz-range-thumb:active {\n  background-color: #c0dbeb;\n}\n\n.custom-range.custom-range-lightblue::-ms-thumb {\n  background-color: #3c8dbc;\n}\n\n.custom-range.custom-range-lightblue::-ms-thumb:active {\n  background-color: #c0dbeb;\n}\n\n.custom-range.custom-range-navy:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-navy:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 31, 63, 0.25);\n}\n\n.custom-range.custom-range-navy:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 31, 63, 0.25);\n}\n\n.custom-range.custom-range-navy:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 31, 63, 0.25);\n}\n\n.custom-range.custom-range-navy::-webkit-slider-thumb {\n  background-color: #001f3f;\n}\n\n.custom-range.custom-range-navy::-webkit-slider-thumb:active {\n  background-color: #0077f2;\n}\n\n.custom-range.custom-range-navy::-moz-range-thumb {\n  background-color: #001f3f;\n}\n\n.custom-range.custom-range-navy::-moz-range-thumb:active {\n  background-color: #0077f2;\n}\n\n.custom-range.custom-range-navy::-ms-thumb {\n  background-color: #001f3f;\n}\n\n.custom-range.custom-range-navy::-ms-thumb:active {\n  background-color: #0077f2;\n}\n\n.custom-range.custom-range-olive:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-olive:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(61, 153, 112, 0.25);\n}\n\n.custom-range.custom-range-olive:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(61, 153, 112, 0.25);\n}\n\n.custom-range.custom-range-olive:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(61, 153, 112, 0.25);\n}\n\n.custom-range.custom-range-olive::-webkit-slider-thumb {\n  background-color: #3d9970;\n}\n\n.custom-range.custom-range-olive::-webkit-slider-thumb:active {\n  background-color: #abdec7;\n}\n\n.custom-range.custom-range-olive::-moz-range-thumb {\n  background-color: #3d9970;\n}\n\n.custom-range.custom-range-olive::-moz-range-thumb:active {\n  background-color: #abdec7;\n}\n\n.custom-range.custom-range-olive::-ms-thumb {\n  background-color: #3d9970;\n}\n\n.custom-range.custom-range-olive::-ms-thumb:active {\n  background-color: #abdec7;\n}\n\n.custom-range.custom-range-lime:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-lime:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(1, 255, 112, 0.25);\n}\n\n.custom-range.custom-range-lime:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(1, 255, 112, 0.25);\n}\n\n.custom-range.custom-range-lime:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(1, 255, 112, 0.25);\n}\n\n.custom-range.custom-range-lime::-webkit-slider-thumb {\n  background-color: #01ff70;\n}\n\n.custom-range.custom-range-lime::-webkit-slider-thumb:active {\n  background-color: #b4ffd4;\n}\n\n.custom-range.custom-range-lime::-moz-range-thumb {\n  background-color: #01ff70;\n}\n\n.custom-range.custom-range-lime::-moz-range-thumb:active {\n  background-color: #b4ffd4;\n}\n\n.custom-range.custom-range-lime::-ms-thumb {\n  background-color: #01ff70;\n}\n\n.custom-range.custom-range-lime::-ms-thumb:active {\n  background-color: #b4ffd4;\n}\n\n.custom-range.custom-range-fuchsia:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-fuchsia:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(240, 18, 190, 0.25);\n}\n\n.custom-range.custom-range-fuchsia:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(240, 18, 190, 0.25);\n}\n\n.custom-range.custom-range-fuchsia:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(240, 18, 190, 0.25);\n}\n\n.custom-range.custom-range-fuchsia::-webkit-slider-thumb {\n  background-color: #f012be;\n}\n\n.custom-range.custom-range-fuchsia::-webkit-slider-thumb:active {\n  background-color: #fbbaec;\n}\n\n.custom-range.custom-range-fuchsia::-moz-range-thumb {\n  background-color: #f012be;\n}\n\n.custom-range.custom-range-fuchsia::-moz-range-thumb:active {\n  background-color: #fbbaec;\n}\n\n.custom-range.custom-range-fuchsia::-ms-thumb {\n  background-color: #f012be;\n}\n\n.custom-range.custom-range-fuchsia::-ms-thumb:active {\n  background-color: #fbbaec;\n}\n\n.custom-range.custom-range-maroon:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-maroon:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(216, 27, 96, 0.25);\n}\n\n.custom-range.custom-range-maroon:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(216, 27, 96, 0.25);\n}\n\n.custom-range.custom-range-maroon:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(216, 27, 96, 0.25);\n}\n\n.custom-range.custom-range-maroon::-webkit-slider-thumb {\n  background-color: #d81b60;\n}\n\n.custom-range.custom-range-maroon::-webkit-slider-thumb:active {\n  background-color: #f5b0c9;\n}\n\n.custom-range.custom-range-maroon::-moz-range-thumb {\n  background-color: #d81b60;\n}\n\n.custom-range.custom-range-maroon::-moz-range-thumb:active {\n  background-color: #f5b0c9;\n}\n\n.custom-range.custom-range-maroon::-ms-thumb {\n  background-color: #d81b60;\n}\n\n.custom-range.custom-range-maroon::-ms-thumb:active {\n  background-color: #f5b0c9;\n}\n\n.custom-range.custom-range-blue:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-blue:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-range.custom-range-blue:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-range.custom-range-blue:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.custom-range.custom-range-blue::-webkit-slider-thumb {\n  background-color: #007bff;\n}\n\n.custom-range.custom-range-blue::-webkit-slider-thumb:active {\n  background-color: #b3d7ff;\n}\n\n.custom-range.custom-range-blue::-moz-range-thumb {\n  background-color: #007bff;\n}\n\n.custom-range.custom-range-blue::-moz-range-thumb:active {\n  background-color: #b3d7ff;\n}\n\n.custom-range.custom-range-blue::-ms-thumb {\n  background-color: #007bff;\n}\n\n.custom-range.custom-range-blue::-ms-thumb:active {\n  background-color: #b3d7ff;\n}\n\n.custom-range.custom-range-indigo:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-indigo:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(102, 16, 242, 0.25);\n}\n\n.custom-range.custom-range-indigo:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(102, 16, 242, 0.25);\n}\n\n.custom-range.custom-range-indigo:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(102, 16, 242, 0.25);\n}\n\n.custom-range.custom-range-indigo::-webkit-slider-thumb {\n  background-color: #6610f2;\n}\n\n.custom-range.custom-range-indigo::-webkit-slider-thumb:active {\n  background-color: #d2b9fb;\n}\n\n.custom-range.custom-range-indigo::-moz-range-thumb {\n  background-color: #6610f2;\n}\n\n.custom-range.custom-range-indigo::-moz-range-thumb:active {\n  background-color: #d2b9fb;\n}\n\n.custom-range.custom-range-indigo::-ms-thumb {\n  background-color: #6610f2;\n}\n\n.custom-range.custom-range-indigo::-ms-thumb:active {\n  background-color: #d2b9fb;\n}\n\n.custom-range.custom-range-purple:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-purple:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(111, 66, 193, 0.25);\n}\n\n.custom-range.custom-range-purple:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(111, 66, 193, 0.25);\n}\n\n.custom-range.custom-range-purple:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(111, 66, 193, 0.25);\n}\n\n.custom-range.custom-range-purple::-webkit-slider-thumb {\n  background-color: #6f42c1;\n}\n\n.custom-range.custom-range-purple::-webkit-slider-thumb:active {\n  background-color: #d5c8ed;\n}\n\n.custom-range.custom-range-purple::-moz-range-thumb {\n  background-color: #6f42c1;\n}\n\n.custom-range.custom-range-purple::-moz-range-thumb:active {\n  background-color: #d5c8ed;\n}\n\n.custom-range.custom-range-purple::-ms-thumb {\n  background-color: #6f42c1;\n}\n\n.custom-range.custom-range-purple::-ms-thumb:active {\n  background-color: #d5c8ed;\n}\n\n.custom-range.custom-range-pink:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-pink:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(232, 62, 140, 0.25);\n}\n\n.custom-range.custom-range-pink:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(232, 62, 140, 0.25);\n}\n\n.custom-range.custom-range-pink:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(232, 62, 140, 0.25);\n}\n\n.custom-range.custom-range-pink::-webkit-slider-thumb {\n  background-color: #e83e8c;\n}\n\n.custom-range.custom-range-pink::-webkit-slider-thumb:active {\n  background-color: #fbddeb;\n}\n\n.custom-range.custom-range-pink::-moz-range-thumb {\n  background-color: #e83e8c;\n}\n\n.custom-range.custom-range-pink::-moz-range-thumb:active {\n  background-color: #fbddeb;\n}\n\n.custom-range.custom-range-pink::-ms-thumb {\n  background-color: #e83e8c;\n}\n\n.custom-range.custom-range-pink::-ms-thumb:active {\n  background-color: #fbddeb;\n}\n\n.custom-range.custom-range-red:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-red:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-range.custom-range-red:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-range.custom-range-red:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.custom-range.custom-range-red::-webkit-slider-thumb {\n  background-color: #dc3545;\n}\n\n.custom-range.custom-range-red::-webkit-slider-thumb:active {\n  background-color: #f6cdd1;\n}\n\n.custom-range.custom-range-red::-moz-range-thumb {\n  background-color: #dc3545;\n}\n\n.custom-range.custom-range-red::-moz-range-thumb:active {\n  background-color: #f6cdd1;\n}\n\n.custom-range.custom-range-red::-ms-thumb {\n  background-color: #dc3545;\n}\n\n.custom-range.custom-range-red::-ms-thumb:active {\n  background-color: #f6cdd1;\n}\n\n.custom-range.custom-range-orange:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-orange:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(253, 126, 20, 0.25);\n}\n\n.custom-range.custom-range-orange:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(253, 126, 20, 0.25);\n}\n\n.custom-range.custom-range-orange:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(253, 126, 20, 0.25);\n}\n\n.custom-range.custom-range-orange::-webkit-slider-thumb {\n  background-color: #fd7e14;\n}\n\n.custom-range.custom-range-orange::-webkit-slider-thumb:active {\n  background-color: #ffdfc5;\n}\n\n.custom-range.custom-range-orange::-moz-range-thumb {\n  background-color: #fd7e14;\n}\n\n.custom-range.custom-range-orange::-moz-range-thumb:active {\n  background-color: #ffdfc5;\n}\n\n.custom-range.custom-range-orange::-ms-thumb {\n  background-color: #fd7e14;\n}\n\n.custom-range.custom-range-orange::-ms-thumb:active {\n  background-color: #ffdfc5;\n}\n\n.custom-range.custom-range-yellow:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-yellow:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-range.custom-range-yellow:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-range.custom-range-yellow:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 193, 7, 0.25);\n}\n\n.custom-range.custom-range-yellow::-webkit-slider-thumb {\n  background-color: #ffc107;\n}\n\n.custom-range.custom-range-yellow::-webkit-slider-thumb:active {\n  background-color: #ffeeba;\n}\n\n.custom-range.custom-range-yellow::-moz-range-thumb {\n  background-color: #ffc107;\n}\n\n.custom-range.custom-range-yellow::-moz-range-thumb:active {\n  background-color: #ffeeba;\n}\n\n.custom-range.custom-range-yellow::-ms-thumb {\n  background-color: #ffc107;\n}\n\n.custom-range.custom-range-yellow::-ms-thumb:active {\n  background-color: #ffeeba;\n}\n\n.custom-range.custom-range-green:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-green:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-range.custom-range-green:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-range.custom-range-green:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.custom-range.custom-range-green::-webkit-slider-thumb {\n  background-color: #28a745;\n}\n\n.custom-range.custom-range-green::-webkit-slider-thumb:active {\n  background-color: #9be7ac;\n}\n\n.custom-range.custom-range-green::-moz-range-thumb {\n  background-color: #28a745;\n}\n\n.custom-range.custom-range-green::-moz-range-thumb:active {\n  background-color: #9be7ac;\n}\n\n.custom-range.custom-range-green::-ms-thumb {\n  background-color: #28a745;\n}\n\n.custom-range.custom-range-green::-ms-thumb:active {\n  background-color: #9be7ac;\n}\n\n.custom-range.custom-range-teal:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-teal:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(32, 201, 151, 0.25);\n}\n\n.custom-range.custom-range-teal:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(32, 201, 151, 0.25);\n}\n\n.custom-range.custom-range-teal:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(32, 201, 151, 0.25);\n}\n\n.custom-range.custom-range-teal::-webkit-slider-thumb {\n  background-color: #20c997;\n}\n\n.custom-range.custom-range-teal::-webkit-slider-thumb:active {\n  background-color: #aaf1dc;\n}\n\n.custom-range.custom-range-teal::-moz-range-thumb {\n  background-color: #20c997;\n}\n\n.custom-range.custom-range-teal::-moz-range-thumb:active {\n  background-color: #aaf1dc;\n}\n\n.custom-range.custom-range-teal::-ms-thumb {\n  background-color: #20c997;\n}\n\n.custom-range.custom-range-teal::-ms-thumb:active {\n  background-color: #aaf1dc;\n}\n\n.custom-range.custom-range-cyan:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-cyan:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-range.custom-range-cyan:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-range.custom-range-cyan:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(23, 162, 184, 0.25);\n}\n\n.custom-range.custom-range-cyan::-webkit-slider-thumb {\n  background-color: #17a2b8;\n}\n\n.custom-range.custom-range-cyan::-webkit-slider-thumb:active {\n  background-color: #90e4f1;\n}\n\n.custom-range.custom-range-cyan::-moz-range-thumb {\n  background-color: #17a2b8;\n}\n\n.custom-range.custom-range-cyan::-moz-range-thumb:active {\n  background-color: #90e4f1;\n}\n\n.custom-range.custom-range-cyan::-ms-thumb {\n  background-color: #17a2b8;\n}\n\n.custom-range.custom-range-cyan::-ms-thumb:active {\n  background-color: #90e4f1;\n}\n\n.custom-range.custom-range-white:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-white:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 255, 255, 0.25);\n}\n\n.custom-range.custom-range-white:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 255, 255, 0.25);\n}\n\n.custom-range.custom-range-white:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(255, 255, 255, 0.25);\n}\n\n.custom-range.custom-range-white::-webkit-slider-thumb {\n  background-color: #fff;\n}\n\n.custom-range.custom-range-white::-webkit-slider-thumb:active {\n  background-color: white;\n}\n\n.custom-range.custom-range-white::-moz-range-thumb {\n  background-color: #fff;\n}\n\n.custom-range.custom-range-white::-moz-range-thumb:active {\n  background-color: white;\n}\n\n.custom-range.custom-range-white::-ms-thumb {\n  background-color: #fff;\n}\n\n.custom-range.custom-range-white::-ms-thumb:active {\n  background-color: white;\n}\n\n.custom-range.custom-range-gray:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-gray:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-range.custom-range-gray:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-range.custom-range-gray:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(108, 117, 125, 0.25);\n}\n\n.custom-range.custom-range-gray::-webkit-slider-thumb {\n  background-color: #6c757d;\n}\n\n.custom-range.custom-range-gray::-webkit-slider-thumb:active {\n  background-color: #caced1;\n}\n\n.custom-range.custom-range-gray::-moz-range-thumb {\n  background-color: #6c757d;\n}\n\n.custom-range.custom-range-gray::-moz-range-thumb:active {\n  background-color: #caced1;\n}\n\n.custom-range.custom-range-gray::-ms-thumb {\n  background-color: #6c757d;\n}\n\n.custom-range.custom-range-gray::-ms-thumb:active {\n  background-color: #caced1;\n}\n\n.custom-range.custom-range-gray-dark:focus {\n  outline: none;\n}\n\n.custom-range.custom-range-gray-dark:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-range.custom-range-gray-dark:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-range.custom-range-gray-dark:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 2px rgba(52, 58, 64, 0.25);\n}\n\n.custom-range.custom-range-gray-dark::-webkit-slider-thumb {\n  background-color: #343a40;\n}\n\n.custom-range.custom-range-gray-dark::-webkit-slider-thumb:active {\n  background-color: #88939e;\n}\n\n.custom-range.custom-range-gray-dark::-moz-range-thumb {\n  background-color: #343a40;\n}\n\n.custom-range.custom-range-gray-dark::-moz-range-thumb:active {\n  background-color: #88939e;\n}\n\n.custom-range.custom-range-gray-dark::-ms-thumb {\n  background-color: #343a40;\n}\n\n.custom-range.custom-range-gray-dark::-ms-thumb:active {\n  background-color: #88939e;\n}\n\n.custom-control-input-primary:checked ~ .custom-control-label::before {\n  border-color: #007bff;\n  background-color: #007bff;\n}\n\n.custom-control-input-primary.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23007bff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-primary.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23007bff'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-primary:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.custom-control-input-primary:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #80bdff;\n}\n\n.custom-control-input-primary:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #b3d7ff;\n  border-color: #b3d7ff;\n}\n\n.custom-control-input-secondary:checked ~ .custom-control-label::before {\n  border-color: #6c757d;\n  background-color: #6c757d;\n}\n\n.custom-control-input-secondary.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%236c757d' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-secondary.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%236c757d'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-secondary:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(108, 117, 125, 0.25);\n}\n\n.custom-control-input-secondary:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #afb5ba;\n}\n\n.custom-control-input-secondary:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #caced1;\n  border-color: #caced1;\n}\n\n.custom-control-input-success:checked ~ .custom-control-label::before {\n  border-color: #28a745;\n  background-color: #28a745;\n}\n\n.custom-control-input-success.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%2328a745' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-success.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%2328a745'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-success:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(40, 167, 69, 0.25);\n}\n\n.custom-control-input-success:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #71dd8a;\n}\n\n.custom-control-input-success:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #9be7ac;\n  border-color: #9be7ac;\n}\n\n.custom-control-input-info:checked ~ .custom-control-label::before {\n  border-color: #17a2b8;\n  background-color: #17a2b8;\n}\n\n.custom-control-input-info.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%2317a2b8' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-info.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%2317a2b8'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-info:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(23, 162, 184, 0.25);\n}\n\n.custom-control-input-info:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #63d9ec;\n}\n\n.custom-control-input-info:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #90e4f1;\n  border-color: #90e4f1;\n}\n\n.custom-control-input-warning:checked ~ .custom-control-label::before {\n  border-color: #ffc107;\n  background-color: #ffc107;\n}\n\n.custom-control-input-warning.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23ffc107' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-warning.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23ffc107'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-warning:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(255, 193, 7, 0.25);\n}\n\n.custom-control-input-warning:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #ffe187;\n}\n\n.custom-control-input-warning:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #ffeeba;\n  border-color: #ffeeba;\n}\n\n.custom-control-input-danger:checked ~ .custom-control-label::before {\n  border-color: #dc3545;\n  background-color: #dc3545;\n}\n\n.custom-control-input-danger.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23dc3545' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-danger.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23dc3545'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-danger:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n\n.custom-control-input-danger:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #efa2a9;\n}\n\n.custom-control-input-danger:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #f6cdd1;\n  border-color: #f6cdd1;\n}\n\n.custom-control-input-light:checked ~ .custom-control-label::before {\n  border-color: #f8f9fa;\n  background-color: #f8f9fa;\n}\n\n.custom-control-input-light.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23f8f9fa' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-light.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23f8f9fa'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-light:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(248, 249, 250, 0.25);\n}\n\n.custom-control-input-light:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: white;\n}\n\n.custom-control-input-light:not(:disabled):active ~ .custom-control-label::before {\n  background-color: white;\n  border-color: white;\n}\n\n.custom-control-input-dark:checked ~ .custom-control-label::before {\n  border-color: #343a40;\n  background-color: #343a40;\n}\n\n.custom-control-input-dark.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23343a40' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-dark.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23343a40'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-dark:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(52, 58, 64, 0.25);\n}\n\n.custom-control-input-dark:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #6d7a86;\n}\n\n.custom-control-input-dark:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #88939e;\n  border-color: #88939e;\n}\n\n.custom-control-input-lightblue:checked ~ .custom-control-label::before {\n  border-color: #3c8dbc;\n  background-color: #3c8dbc;\n}\n\n.custom-control-input-lightblue.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%233c8dbc' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-lightblue.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%233c8dbc'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-lightblue:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(60, 141, 188, 0.25);\n}\n\n.custom-control-input-lightblue:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #99c5de;\n}\n\n.custom-control-input-lightblue:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #c0dbeb;\n  border-color: #c0dbeb;\n}\n\n.custom-control-input-navy:checked ~ .custom-control-label::before {\n  border-color: #001f3f;\n  background-color: #001f3f;\n}\n\n.custom-control-input-navy.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23001f3f' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-navy.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23001f3f'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-navy:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(0, 31, 63, 0.25);\n}\n\n.custom-control-input-navy:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #005ebf;\n}\n\n.custom-control-input-navy:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #0077f2;\n  border-color: #0077f2;\n}\n\n.custom-control-input-olive:checked ~ .custom-control-label::before {\n  border-color: #3d9970;\n  background-color: #3d9970;\n}\n\n.custom-control-input-olive.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%233d9970' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-olive.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%233d9970'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-olive:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(61, 153, 112, 0.25);\n}\n\n.custom-control-input-olive:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #87cfaf;\n}\n\n.custom-control-input-olive:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #abdec7;\n  border-color: #abdec7;\n}\n\n.custom-control-input-lime:checked ~ .custom-control-label::before {\n  border-color: #01ff70;\n  background-color: #01ff70;\n}\n\n.custom-control-input-lime.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%2301ff70' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-lime.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%2301ff70'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-lime:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(1, 255, 112, 0.25);\n}\n\n.custom-control-input-lime:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #81ffb8;\n}\n\n.custom-control-input-lime:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #b4ffd4;\n  border-color: #b4ffd4;\n}\n\n.custom-control-input-fuchsia:checked ~ .custom-control-label::before {\n  border-color: #f012be;\n  background-color: #f012be;\n}\n\n.custom-control-input-fuchsia.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23f012be' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-fuchsia.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23f012be'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-fuchsia:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(240, 18, 190, 0.25);\n}\n\n.custom-control-input-fuchsia:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #f88adf;\n}\n\n.custom-control-input-fuchsia:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #fbbaec;\n  border-color: #fbbaec;\n}\n\n.custom-control-input-maroon:checked ~ .custom-control-label::before {\n  border-color: #d81b60;\n  background-color: #d81b60;\n}\n\n.custom-control-input-maroon.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23d81b60' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-maroon.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23d81b60'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-maroon:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(216, 27, 96, 0.25);\n}\n\n.custom-control-input-maroon:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #f083ab;\n}\n\n.custom-control-input-maroon:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #f5b0c9;\n  border-color: #f5b0c9;\n}\n\n.custom-control-input-blue:checked ~ .custom-control-label::before {\n  border-color: #007bff;\n  background-color: #007bff;\n}\n\n.custom-control-input-blue.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23007bff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-blue.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23007bff'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-blue:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.custom-control-input-blue:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #80bdff;\n}\n\n.custom-control-input-blue:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #b3d7ff;\n  border-color: #b3d7ff;\n}\n\n.custom-control-input-indigo:checked ~ .custom-control-label::before {\n  border-color: #6610f2;\n  background-color: #6610f2;\n}\n\n.custom-control-input-indigo.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%236610f2' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-indigo.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%236610f2'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-indigo:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(102, 16, 242, 0.25);\n}\n\n.custom-control-input-indigo:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #b389f9;\n}\n\n.custom-control-input-indigo:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #d2b9fb;\n  border-color: #d2b9fb;\n}\n\n.custom-control-input-purple:checked ~ .custom-control-label::before {\n  border-color: #6f42c1;\n  background-color: #6f42c1;\n}\n\n.custom-control-input-purple.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%236f42c1' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-purple.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%236f42c1'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-purple:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(111, 66, 193, 0.25);\n}\n\n.custom-control-input-purple:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #b8a2e0;\n}\n\n.custom-control-input-purple:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #d5c8ed;\n  border-color: #d5c8ed;\n}\n\n.custom-control-input-pink:checked ~ .custom-control-label::before {\n  border-color: #e83e8c;\n  background-color: #e83e8c;\n}\n\n.custom-control-input-pink.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23e83e8c' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-pink.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23e83e8c'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-pink:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(232, 62, 140, 0.25);\n}\n\n.custom-control-input-pink:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #f6b0d0;\n}\n\n.custom-control-input-pink:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #fbddeb;\n  border-color: #fbddeb;\n}\n\n.custom-control-input-red:checked ~ .custom-control-label::before {\n  border-color: #dc3545;\n  background-color: #dc3545;\n}\n\n.custom-control-input-red.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23dc3545' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-red.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23dc3545'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-red:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n\n.custom-control-input-red:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #efa2a9;\n}\n\n.custom-control-input-red:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #f6cdd1;\n  border-color: #f6cdd1;\n}\n\n.custom-control-input-orange:checked ~ .custom-control-label::before {\n  border-color: #fd7e14;\n  background-color: #fd7e14;\n}\n\n.custom-control-input-orange.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23fd7e14' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-orange.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23fd7e14'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-orange:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(253, 126, 20, 0.25);\n}\n\n.custom-control-input-orange:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #fec392;\n}\n\n.custom-control-input-orange:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #ffdfc5;\n  border-color: #ffdfc5;\n}\n\n.custom-control-input-yellow:checked ~ .custom-control-label::before {\n  border-color: #ffc107;\n  background-color: #ffc107;\n}\n\n.custom-control-input-yellow.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23ffc107' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-yellow.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23ffc107'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-yellow:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(255, 193, 7, 0.25);\n}\n\n.custom-control-input-yellow:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #ffe187;\n}\n\n.custom-control-input-yellow:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #ffeeba;\n  border-color: #ffeeba;\n}\n\n.custom-control-input-green:checked ~ .custom-control-label::before {\n  border-color: #28a745;\n  background-color: #28a745;\n}\n\n.custom-control-input-green.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%2328a745' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-green.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%2328a745'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-green:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(40, 167, 69, 0.25);\n}\n\n.custom-control-input-green:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #71dd8a;\n}\n\n.custom-control-input-green:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #9be7ac;\n  border-color: #9be7ac;\n}\n\n.custom-control-input-teal:checked ~ .custom-control-label::before {\n  border-color: #20c997;\n  background-color: #20c997;\n}\n\n.custom-control-input-teal.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%2320c997' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-teal.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%2320c997'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-teal:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(32, 201, 151, 0.25);\n}\n\n.custom-control-input-teal:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #7eeaca;\n}\n\n.custom-control-input-teal:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #aaf1dc;\n  border-color: #aaf1dc;\n}\n\n.custom-control-input-cyan:checked ~ .custom-control-label::before {\n  border-color: #17a2b8;\n  background-color: #17a2b8;\n}\n\n.custom-control-input-cyan.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%2317a2b8' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-cyan.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%2317a2b8'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-cyan:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(23, 162, 184, 0.25);\n}\n\n.custom-control-input-cyan:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #63d9ec;\n}\n\n.custom-control-input-cyan:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #90e4f1;\n  border-color: #90e4f1;\n}\n\n.custom-control-input-white:checked ~ .custom-control-label::before {\n  border-color: #fff;\n  background-color: #fff;\n}\n\n.custom-control-input-white.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-white.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23fff'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-white:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(255, 255, 255, 0.25);\n}\n\n.custom-control-input-white:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: white;\n}\n\n.custom-control-input-white:not(:disabled):active ~ .custom-control-label::before {\n  background-color: white;\n  border-color: white;\n}\n\n.custom-control-input-gray:checked ~ .custom-control-label::before {\n  border-color: #6c757d;\n  background-color: #6c757d;\n}\n\n.custom-control-input-gray.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%236c757d' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-gray.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%236c757d'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-gray:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(108, 117, 125, 0.25);\n}\n\n.custom-control-input-gray:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #afb5ba;\n}\n\n.custom-control-input-gray:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #caced1;\n  border-color: #caced1;\n}\n\n.custom-control-input-gray-dark:checked ~ .custom-control-label::before {\n  border-color: #343a40;\n  background-color: #343a40;\n}\n\n.custom-control-input-gray-dark.custom-control-input-outline:checked[type=\"checkbox\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23343a40' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-gray-dark.custom-control-input-outline:checked[type=\"radio\"] ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23343a40'/%3E%3C/svg%3E\") !important;\n}\n\n.custom-control-input-gray-dark:focus ~ .custom-control-label::before {\n  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0.2rem rgba(52, 58, 64, 0.25);\n}\n\n.custom-control-input-gray-dark:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #6d7a86;\n}\n\n.custom-control-input-gray-dark:not(:disabled):active ~ .custom-control-label::before {\n  background-color: #88939e;\n  border-color: #88939e;\n}\n\n.custom-control-input-outline ~ .custom-control-label::before {\n  background-color: transparent !important;\n  box-shadow: none;\n}\n\n.custom-control-input-outline:checked ~ .custom-control-label::before {\n  background-color: transparent;\n}\n\n.dark-mode .form-control,\n.dark-mode .custom-select,\n.dark-mode .custom-file-label,\n.dark-mode .custom-file-label::after,\n.dark-mode .custom-control-label::before,\n.dark-mode .input-group-text {\n  background-color: #343a40;\n  color: #fff;\n}\n\n.dark-mode .form-control:not(.form-control-navbar):not(.is-invalid):not(:focus) {\n  border-color: #6c757d;\n}\n\n.dark-mode select {\n  background-color: #343a40;\n  color: #fff;\n  border-color: #6c757d;\n}\n\n.dark-mode .input-group-text {\n  border-color: #6c757d;\n}\n\n.dark-mode .custom-control-input:disabled ~ .custom-control-label::before,\n.dark-mode .custom-control-input[disabled] ~ .custom-control-label::before {\n  background-color: #3f474e;\n  border-color: #6c757d;\n  color: #fff;\n}\n\n.dark-mode .custom-range::-webkit-slider-runnable-track {\n  background-color: #454d55;\n}\n\n.dark-mode .custom-range::-moz-range-track {\n  background-color: #454d55;\n}\n\n.dark-mode .custom-range::-ms-track {\n  background-color: #454d55;\n}\n\n.dark-mode .navbar-dark .btn-navbar,\n.dark-mode .navbar-dark .form-control-navbar {\n  background-color: #343a40;\n  border: 1px solid #6c757d;\n}\n\n.dark-mode .navbar-dark .btn-navbar:hover {\n  background-color: #454d55;\n}\n\n.dark-mode .navbar-dark .btn-navbar:focus {\n  background-color: #4b545c;\n}\n\n.dark-mode .navbar-dark .form-control-navbar + .input-group-prepend > .btn-navbar,\n.dark-mode .navbar-dark .form-control-navbar + .input-group-append > .btn-navbar {\n  background-color: #3f474e;\n  color: #fff;\n  border: 1px solid #6c757d;\n  border-left: none;\n}\n\n.progress {\n  box-shadow: none;\n  border-radius: 1px;\n}\n\n.progress.vertical {\n  display: inline-block;\n  height: 200px;\n  margin-right: 10px;\n  position: relative;\n  width: 30px;\n}\n\n.progress.vertical > .progress-bar {\n  bottom: 0;\n  position: absolute;\n  width: 100%;\n}\n\n.progress.vertical.sm, .progress.vertical.progress-sm {\n  width: 20px;\n}\n\n.progress.vertical.xs, .progress.vertical.progress-xs {\n  width: 10px;\n}\n\n.progress.vertical.xxs, .progress.vertical.progress-xxs {\n  width: 3px;\n}\n\n.progress-group {\n  margin-bottom: 0.5rem;\n}\n\n.progress-sm {\n  height: 10px;\n}\n\n.progress-xs {\n  height: 7px;\n}\n\n.progress-xxs {\n  height: 3px;\n}\n\n.table tr > td .progress {\n  margin: 0;\n}\n\n.dark-mode .progress {\n  background: #454d55;\n}\n\n.card-primary:not(.card-outline) > .card-header {\n  background-color: #007bff;\n}\n\n.card-primary:not(.card-outline) > .card-header,\n.card-primary:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-primary:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-primary.card-outline {\n  border-top: 3px solid #007bff;\n}\n\n.card-primary.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-primary.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #007bff;\n}\n\n.bg-primary .btn-tool,\n.bg-gradient-primary .btn-tool,\n.card-primary:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-primary .btn-tool:hover,\n.bg-gradient-primary .btn-tool:hover,\n.card-primary:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-primary .bootstrap-datetimepicker-widget .table td,\n.card.bg-primary .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-primary .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-primary .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-primary .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-primary .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-primary .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #0067d6;\n  color: #fff;\n}\n\n.card.bg-primary .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-primary .bootstrap-datetimepicker-widget table td.active,\n.card.bg-primary .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-primary .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #3395ff;\n  color: #fff;\n}\n\n.card-secondary:not(.card-outline) > .card-header {\n  background-color: #6c757d;\n}\n\n.card-secondary:not(.card-outline) > .card-header,\n.card-secondary:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-secondary:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-secondary.card-outline {\n  border-top: 3px solid #6c757d;\n}\n\n.card-secondary.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-secondary.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #6c757d;\n}\n\n.bg-secondary .btn-tool,\n.bg-gradient-secondary .btn-tool,\n.card-secondary:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-secondary .btn-tool:hover,\n.bg-gradient-secondary .btn-tool:hover,\n.card-secondary:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-secondary .bootstrap-datetimepicker-widget .table td,\n.card.bg-secondary .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-secondary .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-secondary .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-secondary .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-secondary .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-secondary .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #596167;\n  color: #fff;\n}\n\n.card.bg-secondary .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-secondary .bootstrap-datetimepicker-widget table td.active,\n.card.bg-secondary .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-secondary .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #868e96;\n  color: #fff;\n}\n\n.card-success:not(.card-outline) > .card-header {\n  background-color: #28a745;\n}\n\n.card-success:not(.card-outline) > .card-header,\n.card-success:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-success:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-success.card-outline {\n  border-top: 3px solid #28a745;\n}\n\n.card-success.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-success.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #28a745;\n}\n\n.bg-success .btn-tool,\n.bg-gradient-success .btn-tool,\n.card-success:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-success .btn-tool:hover,\n.bg-gradient-success .btn-tool:hover,\n.card-success:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-success .bootstrap-datetimepicker-widget .table td,\n.card.bg-success .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-success .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-success .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-success .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-success .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-success .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #208637;\n  color: #fff;\n}\n\n.card.bg-success .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-success .bootstrap-datetimepicker-widget table td.active,\n.card.bg-success .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-success .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #34ce57;\n  color: #fff;\n}\n\n.card-info:not(.card-outline) > .card-header {\n  background-color: #17a2b8;\n}\n\n.card-info:not(.card-outline) > .card-header,\n.card-info:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-info:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-info.card-outline {\n  border-top: 3px solid #17a2b8;\n}\n\n.card-info.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-info.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #17a2b8;\n}\n\n.bg-info .btn-tool,\n.bg-gradient-info .btn-tool,\n.card-info:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-info .btn-tool:hover,\n.bg-gradient-info .btn-tool:hover,\n.card-info:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-info .bootstrap-datetimepicker-widget .table td,\n.card.bg-info .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-info .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-info .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-info .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-info .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-info .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #128294;\n  color: #fff;\n}\n\n.card.bg-info .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-info .bootstrap-datetimepicker-widget table td.active,\n.card.bg-info .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-info .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #1fc8e3;\n  color: #fff;\n}\n\n.card-warning:not(.card-outline) > .card-header {\n  background-color: #ffc107;\n}\n\n.card-warning:not(.card-outline) > .card-header,\n.card-warning:not(.card-outline) > .card-header a {\n  color: #1f2d3d;\n}\n\n.card-warning:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-warning.card-outline {\n  border-top: 3px solid #ffc107;\n}\n\n.card-warning.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-warning.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #ffc107;\n}\n\n.bg-warning .btn-tool,\n.bg-gradient-warning .btn-tool,\n.card-warning:not(.card-outline) .btn-tool {\n  color: rgba(31, 45, 61, 0.8);\n}\n\n.bg-warning .btn-tool:hover,\n.bg-gradient-warning .btn-tool:hover,\n.card-warning:not(.card-outline) .btn-tool:hover {\n  color: #1f2d3d;\n}\n\n.card.bg-warning .bootstrap-datetimepicker-widget .table td,\n.card.bg-warning .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-warning .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-warning .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-warning .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-warning .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-warning .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #dda600;\n  color: #1f2d3d;\n}\n\n.card.bg-warning .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #1f2d3d;\n}\n\n.card.bg-warning .bootstrap-datetimepicker-widget table td.active,\n.card.bg-warning .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-warning .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #ffce3a;\n  color: #1f2d3d;\n}\n\n.card-danger:not(.card-outline) > .card-header {\n  background-color: #dc3545;\n}\n\n.card-danger:not(.card-outline) > .card-header,\n.card-danger:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-danger:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-danger.card-outline {\n  border-top: 3px solid #dc3545;\n}\n\n.card-danger.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-danger.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #dc3545;\n}\n\n.bg-danger .btn-tool,\n.bg-gradient-danger .btn-tool,\n.card-danger:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-danger .btn-tool:hover,\n.bg-gradient-danger .btn-tool:hover,\n.card-danger:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-danger .bootstrap-datetimepicker-widget .table td,\n.card.bg-danger .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-danger .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-danger .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-danger .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-danger .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-danger .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #c62232;\n  color: #fff;\n}\n\n.card.bg-danger .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-danger .bootstrap-datetimepicker-widget table td.active,\n.card.bg-danger .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-danger .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #e4606d;\n  color: #fff;\n}\n\n.card-light:not(.card-outline) > .card-header {\n  background-color: #f8f9fa;\n}\n\n.card-light:not(.card-outline) > .card-header,\n.card-light:not(.card-outline) > .card-header a {\n  color: #1f2d3d;\n}\n\n.card-light:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-light.card-outline {\n  border-top: 3px solid #f8f9fa;\n}\n\n.card-light.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-light.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #f8f9fa;\n}\n\n.bg-light .btn-tool,\n.bg-gradient-light .btn-tool,\n.card-light:not(.card-outline) .btn-tool {\n  color: rgba(31, 45, 61, 0.8);\n}\n\n.bg-light .btn-tool:hover,\n.bg-gradient-light .btn-tool:hover,\n.card-light:not(.card-outline) .btn-tool:hover {\n  color: #1f2d3d;\n}\n\n.card.bg-light .bootstrap-datetimepicker-widget .table td,\n.card.bg-light .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-light .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-light .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-light .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-light .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-light .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #e0e5e9;\n  color: #1f2d3d;\n}\n\n.card.bg-light .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #1f2d3d;\n}\n\n.card.bg-light .bootstrap-datetimepicker-widget table td.active,\n.card.bg-light .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-light .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: white;\n  color: #1f2d3d;\n}\n\n.card-dark:not(.card-outline) > .card-header {\n  background-color: #343a40;\n}\n\n.card-dark:not(.card-outline) > .card-header,\n.card-dark:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-dark:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-dark.card-outline {\n  border-top: 3px solid #343a40;\n}\n\n.card-dark.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-dark.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #343a40;\n}\n\n.bg-dark .btn-tool,\n.bg-gradient-dark .btn-tool,\n.card-dark:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-dark .btn-tool:hover,\n.bg-gradient-dark .btn-tool:hover,\n.card-dark:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-dark .bootstrap-datetimepicker-widget .table td,\n.card.bg-dark .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-dark .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-dark .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-dark .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-dark .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-dark .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #222629;\n  color: #fff;\n}\n\n.card.bg-dark .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-dark .bootstrap-datetimepicker-widget table td.active,\n.card.bg-dark .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-dark .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #4b545c;\n  color: #fff;\n}\n\n.card-lightblue:not(.card-outline) > .card-header {\n  background-color: #3c8dbc;\n}\n\n.card-lightblue:not(.card-outline) > .card-header,\n.card-lightblue:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-lightblue:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-lightblue.card-outline {\n  border-top: 3px solid #3c8dbc;\n}\n\n.card-lightblue.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-lightblue.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #3c8dbc;\n}\n\n.bg-lightblue .btn-tool,\n.bg-gradient-lightblue .btn-tool,\n.card-lightblue:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-lightblue .btn-tool:hover,\n.bg-gradient-lightblue .btn-tool:hover,\n.card-lightblue:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-lightblue .bootstrap-datetimepicker-widget .table td,\n.card.bg-lightblue .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-lightblue .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-lightblue .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-lightblue .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-lightblue .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-lightblue .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #32769d;\n  color: #fff;\n}\n\n.card.bg-lightblue .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-lightblue .bootstrap-datetimepicker-widget table td.active,\n.card.bg-lightblue .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-lightblue .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #5fa4cc;\n  color: #fff;\n}\n\n.card-navy:not(.card-outline) > .card-header {\n  background-color: #001f3f;\n}\n\n.card-navy:not(.card-outline) > .card-header,\n.card-navy:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-navy:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-navy.card-outline {\n  border-top: 3px solid #001f3f;\n}\n\n.card-navy.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-navy.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #001f3f;\n}\n\n.bg-navy .btn-tool,\n.bg-gradient-navy .btn-tool,\n.card-navy:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-navy .btn-tool:hover,\n.bg-gradient-navy .btn-tool:hover,\n.card-navy:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-navy .bootstrap-datetimepicker-widget .table td,\n.card.bg-navy .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-navy .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-navy .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-navy .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-navy .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-navy .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #000b16;\n  color: #fff;\n}\n\n.card.bg-navy .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-navy .bootstrap-datetimepicker-widget table td.active,\n.card.bg-navy .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-navy .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #003872;\n  color: #fff;\n}\n\n.card-olive:not(.card-outline) > .card-header {\n  background-color: #3d9970;\n}\n\n.card-olive:not(.card-outline) > .card-header,\n.card-olive:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-olive:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-olive.card-outline {\n  border-top: 3px solid #3d9970;\n}\n\n.card-olive.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-olive.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #3d9970;\n}\n\n.bg-olive .btn-tool,\n.bg-gradient-olive .btn-tool,\n.card-olive:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-olive .btn-tool:hover,\n.bg-gradient-olive .btn-tool:hover,\n.card-olive:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-olive .bootstrap-datetimepicker-widget .table td,\n.card.bg-olive .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-olive .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-olive .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-olive .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-olive .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-olive .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #317c5b;\n  color: #fff;\n}\n\n.card.bg-olive .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-olive .bootstrap-datetimepicker-widget table td.active,\n.card.bg-olive .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-olive .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #50b98a;\n  color: #fff;\n}\n\n.card-lime:not(.card-outline) > .card-header {\n  background-color: #01ff70;\n}\n\n.card-lime:not(.card-outline) > .card-header,\n.card-lime:not(.card-outline) > .card-header a {\n  color: #1f2d3d;\n}\n\n.card-lime:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-lime.card-outline {\n  border-top: 3px solid #01ff70;\n}\n\n.card-lime.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-lime.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #01ff70;\n}\n\n.bg-lime .btn-tool,\n.bg-gradient-lime .btn-tool,\n.card-lime:not(.card-outline) .btn-tool {\n  color: rgba(31, 45, 61, 0.8);\n}\n\n.bg-lime .btn-tool:hover,\n.bg-gradient-lime .btn-tool:hover,\n.card-lime:not(.card-outline) .btn-tool:hover {\n  color: #1f2d3d;\n}\n\n.card.bg-lime .bootstrap-datetimepicker-widget .table td,\n.card.bg-lime .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-lime .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-lime .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-lime .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-lime .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-lime .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #00d75e;\n  color: #1f2d3d;\n}\n\n.card.bg-lime .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #1f2d3d;\n}\n\n.card.bg-lime .bootstrap-datetimepicker-widget table td.active,\n.card.bg-lime .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-lime .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #34ff8d;\n  color: #1f2d3d;\n}\n\n.card-fuchsia:not(.card-outline) > .card-header {\n  background-color: #f012be;\n}\n\n.card-fuchsia:not(.card-outline) > .card-header,\n.card-fuchsia:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-fuchsia:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-fuchsia.card-outline {\n  border-top: 3px solid #f012be;\n}\n\n.card-fuchsia.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-fuchsia.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #f012be;\n}\n\n.bg-fuchsia .btn-tool,\n.bg-gradient-fuchsia .btn-tool,\n.card-fuchsia:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-fuchsia .btn-tool:hover,\n.bg-gradient-fuchsia .btn-tool:hover,\n.card-fuchsia:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-fuchsia .bootstrap-datetimepicker-widget .table td,\n.card.bg-fuchsia .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #cc0da1;\n  color: #fff;\n}\n\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table td.active,\n.card.bg-fuchsia .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-fuchsia .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #f342cb;\n  color: #fff;\n}\n\n.card-maroon:not(.card-outline) > .card-header {\n  background-color: #d81b60;\n}\n\n.card-maroon:not(.card-outline) > .card-header,\n.card-maroon:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-maroon:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-maroon.card-outline {\n  border-top: 3px solid #d81b60;\n}\n\n.card-maroon.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-maroon.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #d81b60;\n}\n\n.bg-maroon .btn-tool,\n.bg-gradient-maroon .btn-tool,\n.card-maroon:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-maroon .btn-tool:hover,\n.bg-gradient-maroon .btn-tool:hover,\n.card-maroon:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-maroon .bootstrap-datetimepicker-widget .table td,\n.card.bg-maroon .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-maroon .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-maroon .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-maroon .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-maroon .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-maroon .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #b41650;\n  color: #fff;\n}\n\n.card.bg-maroon .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-maroon .bootstrap-datetimepicker-widget table td.active,\n.card.bg-maroon .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-maroon .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #e73f7c;\n  color: #fff;\n}\n\n.card-blue:not(.card-outline) > .card-header {\n  background-color: #007bff;\n}\n\n.card-blue:not(.card-outline) > .card-header,\n.card-blue:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-blue:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-blue.card-outline {\n  border-top: 3px solid #007bff;\n}\n\n.card-blue.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-blue.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #007bff;\n}\n\n.bg-blue .btn-tool,\n.bg-gradient-blue .btn-tool,\n.card-blue:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-blue .btn-tool:hover,\n.bg-gradient-blue .btn-tool:hover,\n.card-blue:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-blue .bootstrap-datetimepicker-widget .table td,\n.card.bg-blue .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-blue .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-blue .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-blue .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-blue .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-blue .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #0067d6;\n  color: #fff;\n}\n\n.card.bg-blue .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-blue .bootstrap-datetimepicker-widget table td.active,\n.card.bg-blue .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-blue .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #3395ff;\n  color: #fff;\n}\n\n.card-indigo:not(.card-outline) > .card-header {\n  background-color: #6610f2;\n}\n\n.card-indigo:not(.card-outline) > .card-header,\n.card-indigo:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-indigo:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-indigo.card-outline {\n  border-top: 3px solid #6610f2;\n}\n\n.card-indigo.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-indigo.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #6610f2;\n}\n\n.bg-indigo .btn-tool,\n.bg-gradient-indigo .btn-tool,\n.card-indigo:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-indigo .btn-tool:hover,\n.bg-gradient-indigo .btn-tool:hover,\n.card-indigo:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-indigo .bootstrap-datetimepicker-widget .table td,\n.card.bg-indigo .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-indigo .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-indigo .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-indigo .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-indigo .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-indigo .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #550bce;\n  color: #fff;\n}\n\n.card.bg-indigo .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-indigo .bootstrap-datetimepicker-widget table td.active,\n.card.bg-indigo .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-indigo .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #8540f5;\n  color: #fff;\n}\n\n.card-purple:not(.card-outline) > .card-header {\n  background-color: #6f42c1;\n}\n\n.card-purple:not(.card-outline) > .card-header,\n.card-purple:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-purple:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-purple.card-outline {\n  border-top: 3px solid #6f42c1;\n}\n\n.card-purple.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-purple.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #6f42c1;\n}\n\n.bg-purple .btn-tool,\n.bg-gradient-purple .btn-tool,\n.card-purple:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-purple .btn-tool:hover,\n.bg-gradient-purple .btn-tool:hover,\n.card-purple:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-purple .bootstrap-datetimepicker-widget .table td,\n.card.bg-purple .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-purple .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-purple .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-purple .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-purple .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-purple .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #5d36a4;\n  color: #fff;\n}\n\n.card.bg-purple .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-purple .bootstrap-datetimepicker-widget table td.active,\n.card.bg-purple .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-purple .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #8c68ce;\n  color: #fff;\n}\n\n.card-pink:not(.card-outline) > .card-header {\n  background-color: #e83e8c;\n}\n\n.card-pink:not(.card-outline) > .card-header,\n.card-pink:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-pink:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-pink.card-outline {\n  border-top: 3px solid #e83e8c;\n}\n\n.card-pink.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-pink.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #e83e8c;\n}\n\n.bg-pink .btn-tool,\n.bg-gradient-pink .btn-tool,\n.card-pink:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-pink .btn-tool:hover,\n.bg-gradient-pink .btn-tool:hover,\n.card-pink:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-pink .bootstrap-datetimepicker-widget .table td,\n.card.bg-pink .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-pink .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-pink .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-pink .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-pink .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-pink .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #e21b76;\n  color: #fff;\n}\n\n.card.bg-pink .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-pink .bootstrap-datetimepicker-widget table td.active,\n.card.bg-pink .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-pink .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #ed6ca7;\n  color: #fff;\n}\n\n.card-red:not(.card-outline) > .card-header {\n  background-color: #dc3545;\n}\n\n.card-red:not(.card-outline) > .card-header,\n.card-red:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-red:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-red.card-outline {\n  border-top: 3px solid #dc3545;\n}\n\n.card-red.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-red.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #dc3545;\n}\n\n.bg-red .btn-tool,\n.bg-gradient-red .btn-tool,\n.card-red:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-red .btn-tool:hover,\n.bg-gradient-red .btn-tool:hover,\n.card-red:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-red .bootstrap-datetimepicker-widget .table td,\n.card.bg-red .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-red .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-red .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-red .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-red .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-red .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #c62232;\n  color: #fff;\n}\n\n.card.bg-red .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-red .bootstrap-datetimepicker-widget table td.active,\n.card.bg-red .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-red .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #e4606d;\n  color: #fff;\n}\n\n.card-orange:not(.card-outline) > .card-header {\n  background-color: #fd7e14;\n}\n\n.card-orange:not(.card-outline) > .card-header,\n.card-orange:not(.card-outline) > .card-header a {\n  color: #1f2d3d;\n}\n\n.card-orange:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-orange.card-outline {\n  border-top: 3px solid #fd7e14;\n}\n\n.card-orange.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-orange.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #fd7e14;\n}\n\n.bg-orange .btn-tool,\n.bg-gradient-orange .btn-tool,\n.card-orange:not(.card-outline) .btn-tool {\n  color: rgba(31, 45, 61, 0.8);\n}\n\n.bg-orange .btn-tool:hover,\n.bg-gradient-orange .btn-tool:hover,\n.card-orange:not(.card-outline) .btn-tool:hover {\n  color: #1f2d3d;\n}\n\n.card.bg-orange .bootstrap-datetimepicker-widget .table td,\n.card.bg-orange .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-orange .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-orange .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-orange .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-orange .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-orange .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #e66a02;\n  color: #1f2d3d;\n}\n\n.card.bg-orange .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #1f2d3d;\n}\n\n.card.bg-orange .bootstrap-datetimepicker-widget table td.active,\n.card.bg-orange .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-orange .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #fd9a47;\n  color: #1f2d3d;\n}\n\n.card-yellow:not(.card-outline) > .card-header {\n  background-color: #ffc107;\n}\n\n.card-yellow:not(.card-outline) > .card-header,\n.card-yellow:not(.card-outline) > .card-header a {\n  color: #1f2d3d;\n}\n\n.card-yellow:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-yellow.card-outline {\n  border-top: 3px solid #ffc107;\n}\n\n.card-yellow.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-yellow.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #ffc107;\n}\n\n.bg-yellow .btn-tool,\n.bg-gradient-yellow .btn-tool,\n.card-yellow:not(.card-outline) .btn-tool {\n  color: rgba(31, 45, 61, 0.8);\n}\n\n.bg-yellow .btn-tool:hover,\n.bg-gradient-yellow .btn-tool:hover,\n.card-yellow:not(.card-outline) .btn-tool:hover {\n  color: #1f2d3d;\n}\n\n.card.bg-yellow .bootstrap-datetimepicker-widget .table td,\n.card.bg-yellow .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-yellow .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-yellow .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-yellow .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-yellow .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-yellow .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #dda600;\n  color: #1f2d3d;\n}\n\n.card.bg-yellow .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #1f2d3d;\n}\n\n.card.bg-yellow .bootstrap-datetimepicker-widget table td.active,\n.card.bg-yellow .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-yellow .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #ffce3a;\n  color: #1f2d3d;\n}\n\n.card-green:not(.card-outline) > .card-header {\n  background-color: #28a745;\n}\n\n.card-green:not(.card-outline) > .card-header,\n.card-green:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-green:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-green.card-outline {\n  border-top: 3px solid #28a745;\n}\n\n.card-green.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-green.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #28a745;\n}\n\n.bg-green .btn-tool,\n.bg-gradient-green .btn-tool,\n.card-green:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-green .btn-tool:hover,\n.bg-gradient-green .btn-tool:hover,\n.card-green:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-green .bootstrap-datetimepicker-widget .table td,\n.card.bg-green .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-green .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-green .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-green .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-green .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-green .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #208637;\n  color: #fff;\n}\n\n.card.bg-green .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-green .bootstrap-datetimepicker-widget table td.active,\n.card.bg-green .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-green .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #34ce57;\n  color: #fff;\n}\n\n.card-teal:not(.card-outline) > .card-header {\n  background-color: #20c997;\n}\n\n.card-teal:not(.card-outline) > .card-header,\n.card-teal:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-teal:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-teal.card-outline {\n  border-top: 3px solid #20c997;\n}\n\n.card-teal.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-teal.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #20c997;\n}\n\n.bg-teal .btn-tool,\n.bg-gradient-teal .btn-tool,\n.card-teal:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-teal .btn-tool:hover,\n.bg-gradient-teal .btn-tool:hover,\n.card-teal:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-teal .bootstrap-datetimepicker-widget .table td,\n.card.bg-teal .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-teal .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-teal .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-teal .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-teal .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-teal .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #1aa67d;\n  color: #fff;\n}\n\n.card.bg-teal .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-teal .bootstrap-datetimepicker-widget table td.active,\n.card.bg-teal .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-teal .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #3ce0af;\n  color: #fff;\n}\n\n.card-cyan:not(.card-outline) > .card-header {\n  background-color: #17a2b8;\n}\n\n.card-cyan:not(.card-outline) > .card-header,\n.card-cyan:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-cyan:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-cyan.card-outline {\n  border-top: 3px solid #17a2b8;\n}\n\n.card-cyan.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-cyan.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #17a2b8;\n}\n\n.bg-cyan .btn-tool,\n.bg-gradient-cyan .btn-tool,\n.card-cyan:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-cyan .btn-tool:hover,\n.bg-gradient-cyan .btn-tool:hover,\n.card-cyan:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-cyan .bootstrap-datetimepicker-widget .table td,\n.card.bg-cyan .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-cyan .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-cyan .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-cyan .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-cyan .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-cyan .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #128294;\n  color: #fff;\n}\n\n.card.bg-cyan .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-cyan .bootstrap-datetimepicker-widget table td.active,\n.card.bg-cyan .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-cyan .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #1fc8e3;\n  color: #fff;\n}\n\n.card-white:not(.card-outline) > .card-header {\n  background-color: #fff;\n}\n\n.card-white:not(.card-outline) > .card-header,\n.card-white:not(.card-outline) > .card-header a {\n  color: #1f2d3d;\n}\n\n.card-white:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-white.card-outline {\n  border-top: 3px solid #fff;\n}\n\n.card-white.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-white.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #fff;\n}\n\n.bg-white .btn-tool,\n.bg-gradient-white .btn-tool,\n.card-white:not(.card-outline) .btn-tool {\n  color: rgba(31, 45, 61, 0.8);\n}\n\n.bg-white .btn-tool:hover,\n.bg-gradient-white .btn-tool:hover,\n.card-white:not(.card-outline) .btn-tool:hover {\n  color: #1f2d3d;\n}\n\n.card.bg-white .bootstrap-datetimepicker-widget .table td,\n.card.bg-white .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-white .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-white .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-white .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-white .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-white .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #ebebeb;\n  color: #1f2d3d;\n}\n\n.card.bg-white .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #1f2d3d;\n}\n\n.card.bg-white .bootstrap-datetimepicker-widget table td.active,\n.card.bg-white .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-white .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: white;\n  color: #1f2d3d;\n}\n\n.card-gray:not(.card-outline) > .card-header {\n  background-color: #6c757d;\n}\n\n.card-gray:not(.card-outline) > .card-header,\n.card-gray:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-gray:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-gray.card-outline {\n  border-top: 3px solid #6c757d;\n}\n\n.card-gray.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-gray.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #6c757d;\n}\n\n.bg-gray .btn-tool,\n.bg-gradient-gray .btn-tool,\n.card-gray:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-gray .btn-tool:hover,\n.bg-gradient-gray .btn-tool:hover,\n.card-gray:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-gray .bootstrap-datetimepicker-widget .table td,\n.card.bg-gray .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-gray .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gray .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gray .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gray .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gray .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #596167;\n  color: #fff;\n}\n\n.card.bg-gray .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-gray .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gray .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-gray .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #868e96;\n  color: #fff;\n}\n\n.card-gray-dark:not(.card-outline) > .card-header {\n  background-color: #343a40;\n}\n\n.card-gray-dark:not(.card-outline) > .card-header,\n.card-gray-dark:not(.card-outline) > .card-header a {\n  color: #fff;\n}\n\n.card-gray-dark:not(.card-outline) > .card-header a.active {\n  color: #1f2d3d;\n}\n\n.card-gray-dark.card-outline {\n  border-top: 3px solid #343a40;\n}\n\n.card-gray-dark.card-outline-tabs > .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card-gray-dark.card-outline-tabs > .card-header a.active {\n  border-top: 3px solid #343a40;\n}\n\n.bg-gray-dark .btn-tool,\n.bg-gradient-gray-dark .btn-tool,\n.card-gray-dark:not(.card-outline) .btn-tool {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.bg-gray-dark .btn-tool:hover,\n.bg-gradient-gray-dark .btn-tool:hover,\n.card-gray-dark:not(.card-outline) .btn-tool:hover {\n  color: #fff;\n}\n\n.card.bg-gray-dark .bootstrap-datetimepicker-widget .table td,\n.card.bg-gray-dark .bootstrap-datetimepicker-widget .table th,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget .table td,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget .table th {\n  border: none;\n}\n\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table td.second:hover,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table thead tr:first-child th:hover,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table td.day:hover,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table td.hour:hover,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table td.minute:hover,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table td.second:hover {\n  background-color: #222629;\n  color: #fff;\n}\n\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table td.today::before,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table td.today::before {\n  border-bottom-color: #fff;\n}\n\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gray-dark .bootstrap-datetimepicker-widget table td.active:hover,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table td.active,\n.card.bg-gradient-gray-dark .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #4b545c;\n  color: #fff;\n}\n\n.card {\n  box-shadow: 0 0 1px rgba(0, 0, 0, 0.125), 0 1px 3px rgba(0, 0, 0, 0.2);\n  margin-bottom: 1rem;\n}\n\n.card.bg-dark .card-header {\n  border-color: #383f45;\n}\n\n.card.bg-dark,\n.card.bg-dark .card-body {\n  color: #fff;\n}\n\n.card.maximized-card {\n  height: 100% !important;\n  left: 0;\n  max-height: 100% !important;\n  max-width: 100% !important;\n  position: fixed;\n  top: 0;\n  width: 100% !important;\n  z-index: 1040;\n}\n\n.card.maximized-card.was-collapsed .card-body {\n  display: block !important;\n}\n\n.card.maximized-card .card-body {\n  overflow: auto;\n}\n\n.card.maximized-card [data-widget=\"collapse\"] {\n  display: none;\n}\n\n.card.maximized-card .card-header,\n.card.maximized-card .card-footer {\n  border-radius: 0 !important;\n}\n\n.card.collapsed-card .card-body,\n.card.collapsed-card .card-footer {\n  display: none;\n}\n\n.card .nav.flex-column > li {\n  border-bottom: 1px solid rgba(0, 0, 0, 0.125);\n  margin: 0;\n}\n\n.card .nav.flex-column > li:last-of-type {\n  border-bottom: 0;\n}\n\n.card.height-control .card-body {\n  max-height: 300px;\n  overflow: auto;\n}\n\n.card .border-right {\n  border-right: 1px solid rgba(0, 0, 0, 0.125);\n}\n\n.card .border-left {\n  border-left: 1px solid rgba(0, 0, 0, 0.125);\n}\n\n.card.card-tabs:not(.card-outline) > .card-header {\n  border-bottom: 0;\n}\n\n.card.card-tabs:not(.card-outline) > .card-header .nav-item:first-child .nav-link {\n  border-left-color: transparent;\n}\n\n.card.card-tabs.card-outline .nav-item {\n  border-bottom: 0;\n}\n\n.card.card-tabs.card-outline .nav-item:first-child .nav-link {\n  border-left: 0;\n  margin-left: 0;\n}\n\n.card.card-tabs .card-tools {\n  margin: .3rem .5rem;\n}\n\n.card.card-tabs:not(.expanding-card).collapsed-card .card-header {\n  border-bottom: 0;\n}\n\n.card.card-tabs:not(.expanding-card).collapsed-card .card-header .nav-tabs {\n  border-bottom: 0;\n}\n\n.card.card-tabs:not(.expanding-card).collapsed-card .card-header .nav-tabs .nav-item {\n  margin-bottom: 0;\n}\n\n.card.card-tabs.expanding-card .card-header .nav-tabs .nav-item {\n  margin-bottom: -1px;\n}\n\n.card.card-outline-tabs {\n  border-top: 0;\n}\n\n.card.card-outline-tabs .card-header .nav-item:first-child .nav-link {\n  border-left: 0;\n  margin-left: 0;\n}\n\n.card.card-outline-tabs .card-header a {\n  border-top: 3px solid transparent;\n}\n\n.card.card-outline-tabs .card-header a:hover {\n  border-top: 3px solid #dee2e6;\n}\n\n.card.card-outline-tabs .card-header a.active:hover {\n  margin-top: 0;\n}\n\n.card.card-outline-tabs .card-tools {\n  margin: .5rem .5rem .3rem;\n}\n\n.card.card-outline-tabs:not(.expanding-card).collapsed-card .card-header {\n  border-bottom: 0;\n}\n\n.card.card-outline-tabs:not(.expanding-card).collapsed-card .card-header .nav-tabs {\n  border-bottom: 0;\n}\n\n.card.card-outline-tabs:not(.expanding-card).collapsed-card .card-header .nav-tabs .nav-item {\n  margin-bottom: 0;\n}\n\n.card.card-outline-tabs.expanding-card .card-header .nav-tabs .nav-item {\n  margin-bottom: -1px;\n}\n\nhtml.maximized-card {\n  overflow: hidden;\n}\n\n.card-header::after,\n.card-body::after,\n.card-footer::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.card-header {\n  background-color: transparent;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.125);\n  padding: 0.75rem 1.25rem;\n  position: relative;\n  border-top-left-radius: 0.25rem;\n  border-top-right-radius: 0.25rem;\n}\n\n.collapsed-card .card-header {\n  border-bottom: 0;\n}\n\n.card-header > .card-tools {\n  float: right;\n  margin-right: -0.625rem;\n}\n\n.card-header > .card-tools .input-group,\n.card-header > .card-tools .nav,\n.card-header > .card-tools .pagination {\n  margin-bottom: -0.3rem;\n  margin-top: -0.3rem;\n}\n\n.card-header > .card-tools [data-toggle=\"tooltip\"] {\n  position: relative;\n}\n\n.card-title {\n  float: left;\n  font-size: 1.1rem;\n  font-weight: 400;\n  margin: 0;\n}\n\n.card-text {\n  clear: both;\n}\n\n.btn-tool {\n  background-color: transparent;\n  color: #adb5bd;\n  font-size: 0.875rem;\n  margin: -0.75rem 0;\n  padding: .25rem .5rem;\n}\n\n.btn-group.show .btn-tool, .btn-tool:hover {\n  color: #495057;\n}\n\n.show .btn-tool, .btn-tool:focus {\n  box-shadow: none !important;\n}\n\n.text-sm .card-title {\n  font-size: 1rem;\n}\n\n.text-sm .nav-link {\n  padding: 0.4rem 0.8rem;\n}\n\n.card-body > .table {\n  margin-bottom: 0;\n}\n\n.card-body > .table > thead > tr > th,\n.card-body > .table > thead > tr > td {\n  border-top-width: 0;\n}\n\n.card-body .fc {\n  margin-top: 5px;\n}\n\n.card-body .full-width-chart {\n  margin: -19px;\n}\n\n.card-body.p-0 .full-width-chart {\n  margin: -9px;\n}\n\n.chart-legend {\n  padding-left: 0;\n  list-style: none;\n  margin: 10px 0;\n}\n\n@media (max-width: 576px) {\n  .chart-legend > li {\n    float: left;\n    margin-right: 10px;\n  }\n}\n\n.card-comments {\n  background-color: #f8f9fa;\n}\n\n.card-comments .card-comment {\n  border-bottom: 1px solid #e9ecef;\n  padding: 8px 0;\n}\n\n.card-comments .card-comment::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.card-comments .card-comment:last-of-type {\n  border-bottom: 0;\n}\n\n.card-comments .card-comment:first-of-type {\n  padding-top: 0;\n}\n\n.card-comments .card-comment img {\n  height: 1.875rem;\n  width: 1.875rem;\n  float: left;\n}\n\n.card-comments .comment-text {\n  color: #78838e;\n  margin-left: 40px;\n}\n\n.card-comments .username {\n  color: #495057;\n  display: block;\n  font-weight: 600;\n}\n\n.card-comments .text-muted {\n  font-size: 12px;\n  font-weight: 400;\n}\n\n.todo-list {\n  list-style: none;\n  margin: 0;\n  overflow: auto;\n  padding: 0;\n}\n\n.todo-list > li {\n  border-radius: 2px;\n  background-color: #f8f9fa;\n  border-left: 2px solid #e9ecef;\n  color: #495057;\n  margin-bottom: 2px;\n  padding: 10px;\n}\n\n.todo-list > li:last-of-type {\n  margin-bottom: 0;\n}\n\n.todo-list > li > input[type=\"checkbox\"] {\n  margin: 0 10px 0 5px;\n}\n\n.todo-list > li .text {\n  display: inline-block;\n  font-weight: 600;\n  margin-left: 5px;\n}\n\n.todo-list > li .badge {\n  font-size: .7rem;\n  margin-left: 10px;\n}\n\n.todo-list > li .tools {\n  color: #dc3545;\n  display: none;\n  float: right;\n}\n\n.todo-list > li .tools > .fa,\n.todo-list > li .tools > .fas,\n.todo-list > li .tools > .far,\n.todo-list > li .tools > .fab,\n.todo-list > li .tools > .fal,\n.todo-list > li .tools > .fad,\n.todo-list > li .tools > .svg-inline--fa,\n.todo-list > li .tools > .ion {\n  cursor: pointer;\n  margin-right: 5px;\n}\n\n.todo-list > li:hover .tools {\n  display: inline-block;\n}\n\n.todo-list > li.done {\n  color: #697582;\n}\n\n.todo-list > li.done .text {\n  font-weight: 500;\n  text-decoration: line-through;\n}\n\n.todo-list > li.done .badge {\n  background-color: #adb5bd !important;\n}\n\n.todo-list .primary {\n  border-left-color: #007bff;\n}\n\n.todo-list .secondary {\n  border-left-color: #6c757d;\n}\n\n.todo-list .success {\n  border-left-color: #28a745;\n}\n\n.todo-list .info {\n  border-left-color: #17a2b8;\n}\n\n.todo-list .warning {\n  border-left-color: #ffc107;\n}\n\n.todo-list .danger {\n  border-left-color: #dc3545;\n}\n\n.todo-list .light {\n  border-left-color: #f8f9fa;\n}\n\n.todo-list .dark {\n  border-left-color: #343a40;\n}\n\n.todo-list .lightblue {\n  border-left-color: #3c8dbc;\n}\n\n.todo-list .navy {\n  border-left-color: #001f3f;\n}\n\n.todo-list .olive {\n  border-left-color: #3d9970;\n}\n\n.todo-list .lime {\n  border-left-color: #01ff70;\n}\n\n.todo-list .fuchsia {\n  border-left-color: #f012be;\n}\n\n.todo-list .maroon {\n  border-left-color: #d81b60;\n}\n\n.todo-list .blue {\n  border-left-color: #007bff;\n}\n\n.todo-list .indigo {\n  border-left-color: #6610f2;\n}\n\n.todo-list .purple {\n  border-left-color: #6f42c1;\n}\n\n.todo-list .pink {\n  border-left-color: #e83e8c;\n}\n\n.todo-list .red {\n  border-left-color: #dc3545;\n}\n\n.todo-list .orange {\n  border-left-color: #fd7e14;\n}\n\n.todo-list .yellow {\n  border-left-color: #ffc107;\n}\n\n.todo-list .green {\n  border-left-color: #28a745;\n}\n\n.todo-list .teal {\n  border-left-color: #20c997;\n}\n\n.todo-list .cyan {\n  border-left-color: #17a2b8;\n}\n\n.todo-list .white {\n  border-left-color: #fff;\n}\n\n.todo-list .gray {\n  border-left-color: #6c757d;\n}\n\n.todo-list .gray-dark {\n  border-left-color: #343a40;\n}\n\n.todo-list .handle {\n  cursor: move;\n  display: inline-block;\n  margin: 0 5px;\n}\n\n.card-input {\n  max-width: 200px;\n}\n\n.card-default .nav-item:first-child .nav-link {\n  border-left: 0;\n}\n\n.dark-mode .card {\n  background-color: #343a40;\n  color: #fff;\n}\n\n.dark-mode .card .card {\n  background-color: #3f474e;\n  color: #fff;\n}\n\n.dark-mode .card .nav.flex-column > li {\n  border-bottom-color: #6c757d;\n}\n\n.dark-mode .card .card-footer {\n  background-color: rgba(0, 0, 0, 0.1);\n}\n\n.dark-mode .card.card-outline-tabs .card-header a:hover {\n  border-color: #6c757d;\n}\n\n.dark-mode .card:not(.card-outline) > .card-header a.active {\n  color: #fff;\n}\n\n.dark-mode .card-comments {\n  background-color: #373d44;\n}\n\n.dark-mode .card-comments .username {\n  color: #ced4da;\n}\n\n.dark-mode .card-comments .card-comment {\n  border-bottom-color: #454d55;\n}\n\n.dark-mode .todo-list > li {\n  background-color: #3f474e;\n  border-color: #454d55;\n  color: #fff;\n}\n\n.modal-dialog .overlay {\n  background-color: #000;\n  display: block;\n  height: 100%;\n  left: 0;\n  opacity: .7;\n  position: absolute;\n  top: 0;\n  width: 100%;\n  z-index: 1052;\n}\n\n.modal-content.bg-warning .modal-header,\n.modal-content.bg-warning .modal-footer {\n  border-color: #343a40;\n}\n\n.modal-content.bg-primary .close, .modal-content.bg-secondary .close, .modal-content.bg-info .close, .modal-content.bg-danger .close, .modal-content.bg-success .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.dark-mode .modal-header,\n.dark-mode .modal-footer {\n  border-color: #6c757d;\n}\n\n.dark-mode .modal-content {\n  background-color: #343a40;\n}\n\n.dark-mode .modal-content.bg-warning .modal-header,\n.dark-mode .modal-content.bg-warning .modal-footer {\n  border-color: #6c757d;\n}\n\n.dark-mode .modal-content.bg-warning .close {\n  color: #343a40 !important;\n  text-shadow: 0 1px 0 #495057 !important;\n}\n\n.dark-mode .modal-content.bg-primary .modal-header,\n.dark-mode .modal-content.bg-primary .modal-footer, .dark-mode .modal-content.bg-secondary .modal-header,\n.dark-mode .modal-content.bg-secondary .modal-footer, .dark-mode .modal-content.bg-info .modal-header,\n.dark-mode .modal-content.bg-info .modal-footer, .dark-mode .modal-content.bg-danger .modal-header,\n.dark-mode .modal-content.bg-danger .modal-footer, .dark-mode .modal-content.bg-success .modal-header,\n.dark-mode .modal-content.bg-success .modal-footer {\n  border-color: #fff;\n}\n\n.toasts-top-right {\n  position: absolute;\n  right: 0;\n  top: 0;\n  z-index: 1040;\n}\n\n.toasts-top-right.fixed {\n  position: fixed;\n}\n\n.toasts-top-left {\n  left: 0;\n  position: absolute;\n  top: 0;\n  z-index: 1040;\n}\n\n.toasts-top-left.fixed {\n  position: fixed;\n}\n\n.toasts-bottom-right {\n  bottom: 0;\n  position: absolute;\n  right: 0;\n  z-index: 1040;\n}\n\n.toasts-bottom-right.fixed {\n  position: fixed;\n}\n\n.toasts-bottom-left {\n  bottom: 0;\n  left: 0;\n  position: absolute;\n  z-index: 1040;\n}\n\n.toasts-bottom-left.fixed {\n  position: fixed;\n}\n\n.dark-mode .toast {\n  background-color: rgba(52, 58, 64, 0.85);\n  color: #fff;\n}\n\n.dark-mode .toast .toast-header {\n  background-color: rgba(52, 58, 64, 0.7);\n  color: #f8f9fa;\n}\n\n.toast.bg-primary {\n  background-color: rgba(0, 123, 255, 0.9) !important;\n}\n\n.toast.bg-primary .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-primary .toast-header {\n  background-color: rgba(0, 123, 255, 0.85);\n  color: #fff;\n}\n\n.toast.bg-secondary {\n  background-color: rgba(108, 117, 125, 0.9) !important;\n}\n\n.toast.bg-secondary .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-secondary .toast-header {\n  background-color: rgba(108, 117, 125, 0.85);\n  color: #fff;\n}\n\n.toast.bg-success {\n  background-color: rgba(40, 167, 69, 0.9) !important;\n}\n\n.toast.bg-success .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-success .toast-header {\n  background-color: rgba(40, 167, 69, 0.85);\n  color: #fff;\n}\n\n.toast.bg-info {\n  background-color: rgba(23, 162, 184, 0.9) !important;\n}\n\n.toast.bg-info .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-info .toast-header {\n  background-color: rgba(23, 162, 184, 0.85);\n  color: #fff;\n}\n\n.toast.bg-warning {\n  background-color: rgba(255, 193, 7, 0.9) !important;\n}\n\n.toast.bg-warning .toast-header {\n  background-color: rgba(255, 193, 7, 0.85);\n  color: #1f2d3d;\n}\n\n.toast.bg-danger {\n  background-color: rgba(220, 53, 69, 0.9) !important;\n}\n\n.toast.bg-danger .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-danger .toast-header {\n  background-color: rgba(220, 53, 69, 0.85);\n  color: #fff;\n}\n\n.toast.bg-light {\n  background-color: rgba(248, 249, 250, 0.9) !important;\n}\n\n.toast.bg-light .toast-header {\n  background-color: rgba(248, 249, 250, 0.85);\n  color: #1f2d3d;\n}\n\n.toast.bg-dark {\n  background-color: rgba(52, 58, 64, 0.9) !important;\n}\n\n.toast.bg-dark .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-dark .toast-header {\n  background-color: rgba(52, 58, 64, 0.85);\n  color: #fff;\n}\n\n.toast.bg-lightblue {\n  background-color: rgba(60, 141, 188, 0.9) !important;\n}\n\n.toast.bg-lightblue .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-lightblue .toast-header {\n  background-color: rgba(60, 141, 188, 0.85);\n  color: #fff;\n}\n\n.toast.bg-navy {\n  background-color: rgba(0, 31, 63, 0.9) !important;\n}\n\n.toast.bg-navy .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-navy .toast-header {\n  background-color: rgba(0, 31, 63, 0.85);\n  color: #fff;\n}\n\n.toast.bg-olive {\n  background-color: rgba(61, 153, 112, 0.9) !important;\n}\n\n.toast.bg-olive .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-olive .toast-header {\n  background-color: rgba(61, 153, 112, 0.85);\n  color: #fff;\n}\n\n.toast.bg-lime {\n  background-color: rgba(1, 255, 112, 0.9) !important;\n}\n\n.toast.bg-lime .toast-header {\n  background-color: rgba(1, 255, 112, 0.85);\n  color: #1f2d3d;\n}\n\n.toast.bg-fuchsia {\n  background-color: rgba(240, 18, 190, 0.9) !important;\n}\n\n.toast.bg-fuchsia .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-fuchsia .toast-header {\n  background-color: rgba(240, 18, 190, 0.85);\n  color: #fff;\n}\n\n.toast.bg-maroon {\n  background-color: rgba(216, 27, 96, 0.9) !important;\n}\n\n.toast.bg-maroon .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-maroon .toast-header {\n  background-color: rgba(216, 27, 96, 0.85);\n  color: #fff;\n}\n\n.toast.bg-blue {\n  background-color: rgba(0, 123, 255, 0.9) !important;\n}\n\n.toast.bg-blue .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-blue .toast-header {\n  background-color: rgba(0, 123, 255, 0.85);\n  color: #fff;\n}\n\n.toast.bg-indigo {\n  background-color: rgba(102, 16, 242, 0.9) !important;\n}\n\n.toast.bg-indigo .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-indigo .toast-header {\n  background-color: rgba(102, 16, 242, 0.85);\n  color: #fff;\n}\n\n.toast.bg-purple {\n  background-color: rgba(111, 66, 193, 0.9) !important;\n}\n\n.toast.bg-purple .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-purple .toast-header {\n  background-color: rgba(111, 66, 193, 0.85);\n  color: #fff;\n}\n\n.toast.bg-pink {\n  background-color: rgba(232, 62, 140, 0.9) !important;\n}\n\n.toast.bg-pink .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-pink .toast-header {\n  background-color: rgba(232, 62, 140, 0.85);\n  color: #fff;\n}\n\n.toast.bg-red {\n  background-color: rgba(220, 53, 69, 0.9) !important;\n}\n\n.toast.bg-red .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-red .toast-header {\n  background-color: rgba(220, 53, 69, 0.85);\n  color: #fff;\n}\n\n.toast.bg-orange {\n  background-color: rgba(253, 126, 20, 0.9) !important;\n}\n\n.toast.bg-orange .toast-header {\n  background-color: rgba(253, 126, 20, 0.85);\n  color: #1f2d3d;\n}\n\n.toast.bg-yellow {\n  background-color: rgba(255, 193, 7, 0.9) !important;\n}\n\n.toast.bg-yellow .toast-header {\n  background-color: rgba(255, 193, 7, 0.85);\n  color: #1f2d3d;\n}\n\n.toast.bg-green {\n  background-color: rgba(40, 167, 69, 0.9) !important;\n}\n\n.toast.bg-green .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-green .toast-header {\n  background-color: rgba(40, 167, 69, 0.85);\n  color: #fff;\n}\n\n.toast.bg-teal {\n  background-color: rgba(32, 201, 151, 0.9) !important;\n}\n\n.toast.bg-teal .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-teal .toast-header {\n  background-color: rgba(32, 201, 151, 0.85);\n  color: #fff;\n}\n\n.toast.bg-cyan {\n  background-color: rgba(23, 162, 184, 0.9) !important;\n}\n\n.toast.bg-cyan .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-cyan .toast-header {\n  background-color: rgba(23, 162, 184, 0.85);\n  color: #fff;\n}\n\n.toast.bg-white {\n  background-color: rgba(255, 255, 255, 0.9) !important;\n}\n\n.toast.bg-white .toast-header {\n  background-color: rgba(255, 255, 255, 0.85);\n  color: #1f2d3d;\n}\n\n.toast.bg-gray {\n  background-color: rgba(108, 117, 125, 0.9) !important;\n}\n\n.toast.bg-gray .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-gray .toast-header {\n  background-color: rgba(108, 117, 125, 0.85);\n  color: #fff;\n}\n\n.toast.bg-gray-dark {\n  background-color: rgba(52, 58, 64, 0.9) !important;\n}\n\n.toast.bg-gray-dark .close {\n  color: #fff;\n  text-shadow: 0 1px 0 #000;\n}\n\n.toast.bg-gray-dark .toast-header {\n  background-color: rgba(52, 58, 64, 0.85);\n  color: #fff;\n}\n\n.btn.disabled, .btn:disabled {\n  cursor: not-allowed;\n}\n\n.btn.btn-flat {\n  border-radius: 0;\n  border-width: 1px;\n  box-shadow: none;\n}\n\n.btn.btn-file {\n  overflow: hidden;\n  position: relative;\n}\n\n.btn.btn-file > input[type=\"file\"] {\n  background-color: #fff;\n  cursor: inherit;\n  display: block;\n  font-size: 100px;\n  min-height: 100%;\n  min-width: 100%;\n  opacity: 0;\n  outline: none;\n  position: absolute;\n  right: 0;\n  text-align: right;\n  top: 0;\n}\n\n.text-sm .btn {\n  font-size: 0.875rem !important;\n}\n\n.btn-default {\n  background-color: #f8f9fa;\n  border-color: #ddd;\n  color: #444;\n}\n\n.btn-default:hover, .btn-default:active, .btn-default.hover {\n  background-color: #e9ecef;\n  color: #2b2b2b;\n}\n\n.btn-app {\n  border-radius: 3px;\n  background-color: #f8f9fa;\n  border: 1px solid #ddd;\n  color: #6c757d;\n  font-size: 12px;\n  height: 60px;\n  margin: 0 0 10px 10px;\n  min-width: 80px;\n  padding: 15px 5px;\n  position: relative;\n  text-align: center;\n}\n\n.btn-app > .fa,\n.btn-app > .fas,\n.btn-app > .far,\n.btn-app > .fab,\n.btn-app > .fal,\n.btn-app > .fad,\n.btn-app > .svg-inline--fa,\n.btn-app > .ion {\n  display: block;\n  font-size: 20px;\n}\n\n.btn-app > .svg-inline--fa {\n  margin: 0 auto;\n}\n\n.btn-app:hover {\n  background-color: #f8f9fa;\n  border-color: #aaaaaa;\n  color: #444;\n}\n\n.btn-app:active, .btn-app:focus {\n  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);\n}\n\n.btn-app > .badge {\n  font-size: 10px;\n  font-weight: 400;\n  position: absolute;\n  right: -10px;\n  top: -3px;\n}\n\n.btn-xs {\n  padding: 0.125rem 0.25rem;\n  font-size: 0.75rem;\n  line-height: 1.5;\n  border-radius: 0.15rem;\n}\n\n.dark-mode .btn-default,\n.dark-mode .btn-app {\n  background-color: #3a4047;\n  color: #fff;\n  border-color: #6c757d;\n}\n\n.dark-mode .btn-default:hover, .dark-mode .btn-default:focus,\n.dark-mode .btn-app:hover,\n.dark-mode .btn-app:focus {\n  background-color: #3f474e;\n  color: #dee2e6;\n  border-color: #727b84;\n}\n\n.dark-mode .btn-light {\n  background-color: #454d55;\n  color: #fff;\n  border-color: #6c757d;\n}\n\n.dark-mode .btn-light:hover, .dark-mode .btn-light:focus {\n  background-color: #4b545c;\n  color: #dee2e6;\n  border-color: #78828a;\n}\n\n.callout {\n  border-radius: 0.25rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);\n  background-color: #fff;\n  border-left: 5px solid #e9ecef;\n  margin-bottom: 1rem;\n  padding: 1rem;\n}\n\n.callout a {\n  color: #495057;\n  text-decoration: underline;\n}\n\n.callout a:hover {\n  color: #e9ecef;\n}\n\n.callout p:last-child {\n  margin-bottom: 0;\n}\n\n.callout.callout-danger {\n  border-left-color: #bd2130;\n}\n\n.callout.callout-warning {\n  border-left-color: #d39e00;\n}\n\n.callout.callout-info {\n  border-left-color: #117a8b;\n}\n\n.callout.callout-success {\n  border-left-color: #1e7e34;\n}\n\n.dark-mode .callout {\n  background-color: #3f474e;\n}\n\n.alert .icon {\n  margin-right: 10px;\n}\n\n.alert .close {\n  color: #000;\n  opacity: .2;\n}\n\n.alert .close:hover {\n  opacity: .5;\n}\n\n.alert a {\n  color: #fff;\n  text-decoration: underline;\n}\n\n.alert-primary {\n  color: #fff;\n  background-color: #007bff;\n  border-color: #006fe6;\n}\n\n.alert-default-primary {\n  color: #004085;\n  background-color: #cce5ff;\n  border-color: #b8daff;\n}\n\n.alert-default-primary hr {\n  border-top-color: #9fcdff;\n}\n\n.alert-default-primary .alert-link {\n  color: #002752;\n}\n\n.alert-secondary {\n  color: #fff;\n  background-color: #6c757d;\n  border-color: #60686f;\n}\n\n.alert-default-secondary {\n  color: #383d41;\n  background-color: #e2e3e5;\n  border-color: #d6d8db;\n}\n\n.alert-default-secondary hr {\n  border-top-color: #c8cbcf;\n}\n\n.alert-default-secondary .alert-link {\n  color: #202326;\n}\n\n.alert-success {\n  color: #fff;\n  background-color: #28a745;\n  border-color: #23923d;\n}\n\n.alert-default-success {\n  color: #155724;\n  background-color: #d4edda;\n  border-color: #c3e6cb;\n}\n\n.alert-default-success hr {\n  border-top-color: #b1dfbb;\n}\n\n.alert-default-success .alert-link {\n  color: #0b2e13;\n}\n\n.alert-info {\n  color: #fff;\n  background-color: #17a2b8;\n  border-color: #148ea1;\n}\n\n.alert-default-info {\n  color: #0c5460;\n  background-color: #d1ecf1;\n  border-color: #bee5eb;\n}\n\n.alert-default-info hr {\n  border-top-color: #abdde5;\n}\n\n.alert-default-info .alert-link {\n  color: #062c33;\n}\n\n.alert-warning {\n  color: #1f2d3d;\n  background-color: #ffc107;\n  border-color: #edb100;\n}\n\n.alert-default-warning {\n  color: #856404;\n  background-color: #fff3cd;\n  border-color: #ffeeba;\n}\n\n.alert-default-warning hr {\n  border-top-color: #ffe8a1;\n}\n\n.alert-default-warning .alert-link {\n  color: #533f03;\n}\n\n.alert-danger {\n  color: #fff;\n  background-color: #dc3545;\n  border-color: #d32535;\n}\n\n.alert-default-danger {\n  color: #721c24;\n  background-color: #f8d7da;\n  border-color: #f5c6cb;\n}\n\n.alert-default-danger hr {\n  border-top-color: #f1b0b7;\n}\n\n.alert-default-danger .alert-link {\n  color: #491217;\n}\n\n.alert-light {\n  color: #1f2d3d;\n  background-color: #f8f9fa;\n  border-color: #e9ecef;\n}\n\n.alert-default-light {\n  color: #818182;\n  background-color: #fefefe;\n  border-color: #fdfdfe;\n}\n\n.alert-default-light hr {\n  border-top-color: #ececf6;\n}\n\n.alert-default-light .alert-link {\n  color: #686868;\n}\n\n.alert-dark {\n  color: #fff;\n  background-color: #343a40;\n  border-color: #292d32;\n}\n\n.alert-default-dark {\n  color: #1b1e21;\n  background-color: #d6d8d9;\n  border-color: #c6c8ca;\n}\n\n.alert-default-dark hr {\n  border-top-color: #b9bbbe;\n}\n\n.alert-default-dark .alert-link {\n  color: #040505;\n}\n\n.table:not(.table-dark) {\n  color: inherit;\n}\n\n.table.table-head-fixed thead tr:nth-child(1) th {\n  background-color: #fff;\n  border-bottom: 0;\n  box-shadow: inset 0 1px 0 #dee2e6, inset 0 -1px 0 #dee2e6;\n  position: sticky;\n  top: 0;\n  z-index: 10;\n}\n\n.table.table-head-fixed.table-dark thead tr:nth-child(1) th {\n  background-color: #212529;\n  box-shadow: inset 0 1px 0 #383f45, inset 0 -1px 0 #383f45;\n}\n\n.table.no-border,\n.table.no-border td,\n.table.no-border th {\n  border: 0;\n}\n\n.table.text-center,\n.table.text-center td,\n.table.text-center th {\n  text-align: center;\n}\n\n.table.table-valign-middle thead > tr > th,\n.table.table-valign-middle thead > tr > td,\n.table.table-valign-middle tbody > tr > th,\n.table.table-valign-middle tbody > tr > td {\n  vertical-align: middle;\n}\n\n.card-body.p-0 .table thead > tr > th:first-of-type,\n.card-body.p-0 .table thead > tr > td:first-of-type,\n.card-body.p-0 .table tfoot > tr > th:first-of-type,\n.card-body.p-0 .table tfoot > tr > td:first-of-type,\n.card-body.p-0 .table tbody > tr > th:first-of-type,\n.card-body.p-0 .table tbody > tr > td:first-of-type {\n  padding-left: 1.5rem;\n}\n\n.card-body.p-0 .table thead > tr > th:last-of-type,\n.card-body.p-0 .table thead > tr > td:last-of-type,\n.card-body.p-0 .table tfoot > tr > th:last-of-type,\n.card-body.p-0 .table tfoot > tr > td:last-of-type,\n.card-body.p-0 .table tbody > tr > th:last-of-type,\n.card-body.p-0 .table tbody > tr > td:last-of-type {\n  padding-right: 1.5rem;\n}\n\n.table-hover tbody tr.expandable-body:hover {\n  background-color: inherit !important;\n}\n\n[data-widget=\"expandable-table\"] {\n  cursor: pointer;\n}\n\n[data-widget=\"expandable-table\"] i {\n  transition: transform 0.3s linear;\n}\n\n[data-widget=\"expandable-table\"][aria-expanded=\"true\"] td > i[class*=\"right\"] {\n  transform: rotate(90deg);\n}\n\n[data-widget=\"expandable-table\"][aria-expanded=\"true\"] td > i[class*=\"left\"] {\n  transform: rotate(-90deg);\n}\n\n.expandable-body > td {\n  padding: 0 !important;\n  width: 100%;\n}\n\n.expandable-body > td > div,\n.expandable-body > td > p {\n  padding: 0.75rem;\n}\n\n.expandable-body .table {\n  width: calc(100% - 0.75rem);\n  margin: 0 0 0 0.75rem;\n}\n\n.expandable-body .table tr:first-child td,\n.expandable-body .table tr:first-child th {\n  border-top: none;\n}\n\n.dark-mode .table-bordered,\n.dark-mode .table-bordered td,\n.dark-mode .table-bordered th {\n  border-color: #6c757d;\n}\n\n.dark-mode .table-hover tbody tr:hover {\n  color: #dee2e6;\n  background-color: #3a4047;\n  border-color: #6c757d;\n}\n\n.dark-mode .table thead th {\n  border-bottom-color: #6c757d;\n}\n\n.dark-mode .table th,\n.dark-mode .table td {\n  border-top-color: #6c757d;\n}\n\n.dark-mode .table.table-head-fixed thead tr:nth-child(1) th {\n  background-color: #3f474e;\n}\n\n.carousel-control-prev .carousel-control-custom-icon {\n  margin-left: -20px;\n}\n\n.carousel-control-next .carousel-control-custom-icon {\n  margin-right: 20px;\n}\n\n.carousel-control-custom-icon > .fa,\n.carousel-control-custom-icon > .fas,\n.carousel-control-custom-icon > .far,\n.carousel-control-custom-icon > .fab,\n.carousel-control-custom-icon > .fal,\n.carousel-control-custom-icon > .fad,\n.carousel-control-custom-icon > .svg-inline--fa,\n.carousel-control-custom-icon > .ion {\n  display: inline-block;\n  font-size: 40px;\n  margin-top: -20px;\n  position: absolute;\n  top: 50%;\n  z-index: 5;\n}\n\n.close {\n  float: right;\n  font-size: 1.5rem;\n  font-weight: 700;\n  line-height: 1;\n  color: #000;\n  text-shadow: 0 1px 0 #fff;\n  opacity: .5;\n}\n\n.close:hover {\n  color: #000;\n  text-decoration: none;\n}\n\n.close:not(:disabled):not(.disabled):hover, .close:not(:disabled):not(.disabled):focus {\n  opacity: .75;\n}\n\n.close:focus {\n  outline: none;\n}\n\nbutton.close {\n  padding: 0;\n  background-color: transparent;\n  border: 0;\n}\n\na.close.disabled {\n  pointer-events: none;\n}\n\n/*# sourceMappingURL=adminlte.components.css.map */", "//\n// Component: Forms\n//\n\n.form-group {\n  &.has-icon {\n    position: relative;\n\n    .form-control {\n      padding-right: 35px;\n    }\n\n    .form-icon {\n      background-color: transparent;\n      border: 0;\n      cursor: pointer;\n      font-size: 1rem;\n      // margin-top: -3px;\n      padding: $input-btn-padding-y $input-btn-padding-x;\n      position: absolute;\n      right: 3px;\n      top: 0;\n    }\n  }\n}\n\n// Button groups\n.btn-group-vertical {\n  .btn {\n    &.btn-flat:first-of-type,\n    &.btn-flat:last-of-type {\n      @include border-radius(0);\n    }\n  }\n}\n\n// Support icons in form-control\n.form-control-feedback {\n  &.fa,\n  &.fas,\n  &.far,\n  &.fab,\n  &.fal,\n  &.fad,\n  &.svg-inline--fa,\n  &.ion {\n    line-height: $input-height;\n  }\n}\n\n.input-lg + .form-control-feedback,\n.input-group-lg + .form-control-feedback {\n  &.fa,\n  &.fas,\n  &.far,\n  &.fab,\n  &.fal,\n  &.fad,\n  &.svg-inline--fa,\n  &.ion {\n    line-height: $input-height-lg;\n  }\n}\n\n.form-group-lg {\n  .form-control + .form-control-feedback {\n    &.fa,\n    &.fas,\n    &.far,\n    &.fab,\n    &.fal,\n    &.fad,\n    &.svg-inline--fa,\n    &.ion {\n      line-height: $input-height-lg;\n    }\n  }\n}\n\n.input-sm + .form-control-feedback,\n.input-group-sm + .form-control-feedback {\n  &.fa,\n  &.fas,\n  &.far,\n  &.fab,\n  &.fal,\n  &.fad,\n  &.svg-inline--fa,\n  &.ion {\n    line-height: $input-height-sm;\n  }\n}\n\n.form-group-sm {\n  .form-control + .form-control-feedback {\n    &.fa,\n    &.fas,\n    &.far,\n    &.fab,\n    &.fal,\n    &.fad,\n    &.svg-inline--fa,\n    &.ion {\n      line-height: $input-height-sm;\n    }\n  }\n}\n\nlabel:not(.form-check-label):not(.custom-file-label) {\n  font-weight: $font-weight-bold;\n}\n\n.warning-feedback {\n  @include font-size($form-feedback-font-size);\n  color: theme-color(\"warning\");\n  display: none;\n  margin-top: $form-feedback-margin-top;\n  width: 100%;\n}\n\n.warning-tooltip {\n  @include border-radius($form-feedback-tooltip-border-radius);\n  @include font-size($form-feedback-tooltip-font-size);\n  background-color: rgba(theme-color(\"warning\"), $form-feedback-tooltip-opacity);\n  color: color-yiq(theme-color(\"warning\"));\n  display: none;\n  line-height: $form-feedback-tooltip-line-height;\n  margin-top: .1rem;\n  max-width: 100%; // Contain to parent when possible\n  padding: $form-feedback-tooltip-padding-y $form-feedback-tooltip-padding-x;\n  position: absolute;\n  top: 100%;\n  z-index: 5;\n}\n\n.form-control {\n  &.is-warning {\n    border-color: theme-color(\"warning\");\n\n    @if $enable-validation-icons {\n      // padding-right: $input-height-inner;\n      // background-image: none;\n      // background-repeat: no-repeat;\n      // background-position: center right $input-height-inner-quarter;\n      // background-size: $input-height-inner-half $input-height-inner-half;\n    }\n\n    &:focus {\n      border-color: theme-color(\"warning\");\n      box-shadow: 0 0 0 $input-focus-width rgba(theme-color(\"warning\"), .25);\n    }\n\n    ~ .warning-feedback,\n    ~ .warning-tooltip {\n      display: block;\n    }\n  }\n}\n\n// stylelint-disable-next-line selector-no-qualifying-type\ntextarea.form-control {\n  &.is-warning {\n    @if $enable-validation-icons {\n      padding-right: $input-height-inner;\n      background-position: top $input-height-inner-quarter right $input-height-inner-quarter;\n    }\n  }\n}\n\n.custom-select {\n  &.is-warning {\n    border-color: theme-color(\"warning\");\n\n    @if $enable-validation-icons {\n      // padding-right: $custom-select-feedback-icon-padding-right;\n      // background: $custom-select-background, none $custom-select-bg no-repeat $custom-select-feedback-icon-position / $custom-select-feedback-icon-size;\n    }\n\n    &:focus {\n      border-color: theme-color(\"warning\");\n      box-shadow: 0 0 0 $input-focus-width rgba(theme-color(\"warning\"), .25);\n    }\n\n    ~ .warning-feedback,\n    ~ .warning-tooltip {\n      display: block;\n    }\n  }\n}\n\n\n.form-control-file {\n  &.is-warning {\n    ~ .warning-feedback,\n    ~ .warning-tooltip {\n      display: block;\n    }\n  }\n}\n\n.form-check-input {\n  &.is-warning {\n    ~ .form-check-label {\n      color: theme-color(\"warning\");\n    }\n\n    ~ .warning-feedback,\n    ~ .warning-tooltip {\n      display: block;\n    }\n  }\n}\n\n.custom-control-input.is-warning {\n  ~ .custom-control-label {\n    color: theme-color(\"warning\");\n\n    &::before {\n      border-color: theme-color(\"warning\");\n    }\n  }\n\n  ~ .warning-feedback,\n  ~ .warning-tooltip {\n    display: block;\n  }\n\n  &:checked {\n    ~ .custom-control-label::before {\n      @include gradient-bg(lighten(theme-color(\"warning\"), 10%));\n      border-color: lighten(theme-color(\"warning\"), 10%);\n    }\n  }\n\n  &:focus {\n    ~ .custom-control-label::before {\n      box-shadow: 0 0 0 $input-focus-width rgba(theme-color(\"warning\"), .25);\n    }\n\n    &:not(:checked) ~ .custom-control-label::before {\n      border-color: theme-color(\"warning\");\n    }\n  }\n}\n\n// custom file\n.custom-file-input {\n  &.is-warning {\n    ~ .custom-file-label {\n      border-color: theme-color(\"warning\");\n    }\n\n    ~ .warning-feedback,\n    ~ .warning-tooltip {\n      display: block;\n    }\n\n    &:focus {\n      ~ .custom-file-label {\n        border-color: theme-color(\"warning\");\n        box-shadow: 0 0 0 $input-focus-width rgba(theme-color(\"warning\"), .25);\n      }\n    }\n  }\n}\n\n// body.text-sm support\nbody.text-sm {\n  .input-group-text {\n    font-size: $font-size-sm;\n  }\n}\n\n// custom .form-control styles\n.form-control,\n.custom-select {\n  &.form-control-border {\n    border-top: 0;\n    border-left: 0;\n    border-right: 0;\n    border-radius: 0;\n    box-shadow: inherit;\n\n    &.border-width-2 {\n      border-bottom-width: 2px;\n    }\n    &.border-width-3 {\n      border-bottom-width: 3px;\n    }\n  }\n}\n\n// custom switch color variations\n.custom-switch {\n  @each $name, $color in $theme-colors {\n    @include custom-switch-variant($name, $color);\n  }\n\n  @each $name, $color in $colors {\n    @include custom-switch-variant($name, $color);\n  }\n}\n\n// custom range color variations\n.custom-range {\n  @each $name, $color in $theme-colors {\n    @include custom-range-variant($name, $color);\n  }\n\n  @each $name, $color in $colors {\n    @include custom-range-variant($name, $color);\n  }\n}\n\n// custom control input variations\n@each $name, $color in $theme-colors {\n  @include custom-control-input-variant($name, $color);\n}\n\n@each $name, $color in $colors {\n  @include custom-control-input-variant($name, $color);\n}\n\n.custom-control-input-outline {\n  ~ .custom-control-label::before {\n    background-color: transparent !important;\n    box-shadow: none;\n  }\n  &:checked ~ .custom-control-label::before {\n    @include gradient-bg(transparent);\n  }\n}\n\n.dark-mode {\n  .form-control,\n  .custom-select,\n  .custom-file-label,\n  .custom-file-label::after,\n  .custom-control-label::before,\n  .input-group-text {\n    background-color: $dark;\n    color: $white;\n  }\n  .form-control:not(.form-control-navbar):not(.is-invalid):not(:focus) {\n    border-color: $gray-600;\n  }\n  select {\n    background-color: $dark;\n    color: $white;\n    border-color: $gray-600;\n  }\n\n  .input-group-text {\n    border-color: $gray-600;\n  }\n\n  .custom-control-input:disabled ~ .custom-control-label::before,\n  .custom-control-input[disabled] ~ .custom-control-label::before {\n    background-color: lighten($dark, 5%);\n    border-color: $gray-600;\n    color: $white;\n  }\n\n  .custom-range {\n    &::-webkit-slider-runnable-track {\n      background-color: lighten($dark, 7.5%);\n    }\n    &::-moz-range-track {\n      background-color: lighten($dark, 7.5%);\n    }\n    &::-ms-track {\n      background-color: lighten($dark, 7.5%);\n    }\n  }\n\n  .navbar-dark {\n    .btn-navbar,\n    .form-control-navbar {\n      background-color: $dark;\n      border: $input-border-width solid $gray-600;\n    }\n    .btn-navbar {\n      &:hover {\n        background-color: lighten($sidebar-dark-bg, 7.5%);\n      }\n      &:focus {\n        background-color: lighten($sidebar-dark-bg, 10%);\n      }\n    }\n\n    .form-control-navbar + .input-group-prepend,\n    .form-control-navbar + .input-group-append {\n      > .btn-navbar {\n        background-color: lighten($dark, 5%);\n        color: $white;\n        border: $input-border-width solid $gray-600;\n        border-left: none;\n      }\n    }\n  }\n}\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n\n//\n// Color system\n//\n\n// stylelint-disable\n$white:    #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n\n$grays: () !default;\n$grays: map-merge((\n  \"100\": $gray-100,\n  \"200\": $gray-200,\n  \"300\": $gray-300,\n  \"400\": $gray-400,\n  \"500\": $gray-500,\n  \"600\": $gray-600,\n  \"700\": $gray-700,\n  \"800\": $gray-800,\n  \"900\": $gray-900\n), $grays);\n\n$blue:    #007bff !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #e83e8c !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #28a745 !default;\n$teal:    #20c997 !default;\n$cyan:    #17a2b8 !default;\n\n$colors: () !default;\n$colors: map-merge((\n  \"blue\":       $blue,\n  \"indigo\":     $indigo,\n  \"purple\":     $purple,\n  \"pink\":       $pink,\n  \"red\":        $red,\n  \"orange\":     $orange,\n  \"yellow\":     $yellow,\n  \"green\":      $green,\n  \"teal\":       $teal,\n  \"cyan\":       $cyan,\n  \"white\":      $white,\n  \"gray\":       $gray-600,\n  \"gray-dark\":  $gray-800\n), $colors);\n\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-800 !default;\n\n$theme-colors: () !default;\n$theme-colors: map-merge((\n  \"primary\":    $primary,\n  \"secondary\":  $secondary,\n  \"success\":    $success,\n  \"info\":       $info,\n  \"warning\":    $warning,\n  \"danger\":     $danger,\n  \"light\":      $light,\n  \"dark\":       $dark\n), $theme-colors);\n// stylelint-enable\n\n// Set a specific jump point for requesting color jumps\n$theme-color-interval:      8% !default;\n\n// The yiq lightness value that determines when the lightness of color changes from \"dark\" to \"light\". Acceptable values are between 0 and 255.\n$yiq-contrasted-threshold: 150 !default;\n\n// Customize the light and dark text colors for use in our YIQ color contrast function.\n$yiq-text-dark: #1f2d3d !default;\n$yiq-text-light: $white !default;\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                                true !default;\n$enable-rounded:                              true !default;\n$enable-shadows:                              true !default;\n$enable-gradients:                            false !default;\n$enable-transitions:                          true !default;\n$enable-prefers-reduced-motion-media-query:   true !default;\n$enable-hover-media-query:                    false !default; // Deprecated, no longer affects any compiled CSS\n$enable-grid-classes:                         true !default;\n$enable-pointer-cursor-for-buttons:           true !default;\n$enable-print-styles:                         true !default;\n$enable-responsive-font-sizes:                false !default;\n$enable-validation-icons:                     true !default;\n$enable-deprecation-messages:                 true !default;\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// stylelint-disable\n$spacer: 1rem !default;\n$spacers: () !default;\n$spacers: map-merge((\n  0: 0,\n  1: ($spacer * .25),\n  2: ($spacer * .5),\n  3: $spacer,\n  4: ($spacer * 1.5),\n  5: ($spacer * 3)\n), $spacers);\n\n// This variable affects the `.h-*` and `.w-*` classes.\n$sizes: () !default;\n$sizes: map-merge((\n  25: 25%,\n  50: 50%,\n  75: 75%,\n  100: 100%\n), $sizes);\n// stylelint-enable\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   $white !default;\n$body-color:                $gray-900 !default;\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                theme-color(\"primary\") !default;\n$link-decoration:           none !default;\n$link-hover-color:          darken($link-color, 15%) !default;\n$link-hover-decoration:     none !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px\n) !default;\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints);\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px\n) !default;\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           15px !default;\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n$line-height-lg:              1.5 !default;\n$line-height-sm:              1.5 !default;\n\n$border-width:                1px !default;\n$border-color:                $gray-300 !default;\n\n$border-radius:               .25rem !default;\n$border-radius-lg:            .3rem !default;\n$border-radius-sm:            .2rem !default;\n\n$component-active-color:      $white !default;\n$component-active-bg:         theme-color(\"primary\") !default;\n\n$caret-width:                 .3em !default;\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n$transition-collapse:         height .35s ease !default;\n\n\n// Fonts\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      \"Source Sans Pro\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n$font-family-base:            $font-family-sans-serif !default;\n// stylelint-enable value-keyword-case\n\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-lg:                ($font-size-base * 1.25) !default;\n$font-size-sm:                ($font-size-base * .875) !default;\n\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-bold:            700 !default;\n\n$font-weight-base:            $font-weight-normal !default;\n$line-height-base:            1.5 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n\n$headings-margin-bottom:      ($spacer / 2) !default;\n$headings-font-family:        inherit !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              inherit !default;\n\n$display1-size:               6rem !default;\n$display2-size:               5.5rem !default;\n$display3-size:               4.5rem !default;\n$display4-size:               3.5rem !default;\n\n$display1-weight:             300 !default;\n$display2-weight:             300 !default;\n$display3-weight:             300 !default;\n$display4-weight:             300 !default;\n$display-line-height:         $headings-line-height !default;\n\n$lead-font-size:              ($font-size-base * 1.25) !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             80% !default;\n\n$text-muted:                  $gray-600 !default;\n\n$blockquote-small-color:      $gray-600 !default;\n$blockquote-font-size:        ($font-size-base * 1.25) !default;\n\n$hr-border-color:             rgba($black, .1) !default;\n$hr-border-width:             $border-width !default;\n\n$mark-padding:                .2em !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$kbd-box-shadow:              inset 0 -.1rem 0 rgba($black, .25) !default;\n$nested-kbd-font-weight:      $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-bg:                     #fcf8e3 !default;\n\n$hr-margin-y:                 $spacer !default;\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n$table-cell-padding:          .75rem !default;\n$table-cell-padding-sm:       .3rem !default;\n\n$table-bg:                    transparent !default;\n$table-accent-bg:             rgba($black, .05) !default;\n$table-hover-bg:              rgba($black, .075) !default;\n$table-active-bg:             $table-hover-bg !default;\n\n$table-border-width:          $border-width !default;\n$table-border-color:          $gray-300 !default;\n\n$table-head-bg:               $gray-200 !default;\n$table-head-color:            $gray-700 !default;\n\n$table-dark-bg:               $gray-900 !default;\n$table-dark-accent-bg:        rgba($white, .05) !default;\n$table-dark-hover-bg:         rgba($white, .075) !default;\n$table-dark-border-color:     lighten($gray-900, 10%) !default;\n$table-dark-color:            $body-bg !default;\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:       .2rem !default;\n$input-btn-focus-color:       rgba($component-active-bg, .25) !default;\n$input-btn-focus-box-shadow:  0 0 0 $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-line-height-sm:    $line-height-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-line-height-lg:    $line-height-lg !default;\n\n$input-btn-border-width:      $border-width !default;\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-line-height:             $input-btn-line-height !default;\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-line-height-sm:          $input-btn-line-height-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-line-height-lg:          $input-btn-line-height-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              none !default;\n$btn-focus-width:             0 !default;\n$btn-focus-box-shadow:        none !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       none !default;\n\n$btn-link-disabled-color:     $gray-600 !default;\n\n$btn-block-spacing-y:         .5rem !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius !default;\n$btn-border-radius-lg:        $border-radius-lg !default;\n$btn-border-radius-sm:        $border-radius-sm !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n\n// Forms\n\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-line-height-sm:                  $input-btn-line-height-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-line-height-lg:                  $input-btn-line-height-lg !default;\n\n$input-bg:                              $white !default;\n$input-disabled-bg:                     $gray-200 !default;\n\n$input-color:                           $gray-700 !default;\n$input-border-color:                    $gray-400 !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      inset 0 0 0 rgba($black, 0) !default;\n\n$input-border-radius:                   $border-radius !default;\n$input-border-radius-lg:                $border-radius-lg !default;\n$input-border-radius-sm:                $border-radius-sm !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              lighten($component-active-bg, 25%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     0 !default;\n$input-focus-box-shadow:                none !default;\n\n$input-placeholder-color:               lighten($gray-600, 15%) !default;\n\n$input-height-border:                   $input-border-width * 2 !default;\n\n$input-height-inner:                    ($font-size-base * $input-btn-line-height) + ($input-btn-padding-y * 2) !default;\n$input-height-inner-half:               calc(#{$input-line-height * .5em} + #{$input-padding-y}) !default;\n$input-height-inner-quarter:            calc(#{$input-line-height * .25em} + #{$input-padding-y / 2}) !default;\n\n$input-height:                          calc(#{$input-height-inner} + #{$input-height-border}) !default;\n\n$input-height-inner-sm:                 ($font-size-sm * $input-btn-line-height-sm) + ($input-btn-padding-y-sm * 2) !default;\n$input-height-sm:                       calc(#{$input-height-inner-sm} + #{$input-height-border}) !default;\n\n$input-height-inner-lg:                 ($font-size-lg * $input-btn-line-height-lg) + ($input-btn-padding-y-lg * 2) !default;\n$input-height-lg:                       calc(#{$input-height-inner-lg} + #{$input-height-border}) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-text-margin-top:                  .25rem !default;\n\n$form-check-input-gutter:               1.25rem !default;\n$form-check-input-margin-y:             .3rem !default;\n$form-check-input-margin-x:             .25rem !default;\n\n$form-check-inline-margin-x:            .75rem !default;\n$form-check-inline-input-margin-x:      .3125rem !default;\n\n$form-group-margin-bottom:              1rem !default;\n\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  $gray-200 !default;\n$input-group-addon-border-color:        $input-border-color !default;\n\n$custom-control-gutter:                 .5rem !default;\n$custom-control-spacer-x:               1rem !default;\n\n$custom-control-indicator-size:         1rem !default;\n$custom-control-indicator-bg:           $gray-300 !default;\n$custom-control-indicator-bg-size:      50% 50% !default;\n$custom-control-indicator-box-shadow:   inset 0 .25rem .25rem rgba($black, .1) !default;\n\n$custom-control-indicator-disabled-bg:          $gray-200 !default;\n$custom-control-label-disabled-color:           $gray-600 !default;\n\n$custom-control-indicator-checked-color:        $component-active-color !default;\n$custom-control-indicator-checked-bg:           $component-active-bg !default;\n$custom-control-indicator-checked-disabled-bg:  rgba(theme-color(\"primary\"), .5) !default;\n$custom-control-indicator-checked-box-shadow:   none !default;\n\n$custom-control-indicator-focus-box-shadow:     0 0 0 1px $body-bg, $input-btn-focus-box-shadow !default;\n\n$custom-control-indicator-active-color:         $component-active-color !default;\n$custom-control-indicator-active-bg:            lighten($component-active-bg, 35%) !default;\n$custom-control-indicator-active-box-shadow:    none !default;\n\n$custom-checkbox-indicator-border-radius:       $border-radius !default;\n$custom-checkbox-indicator-icon-checked:        str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='#{$custom-control-indicator-checked-color}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$custom-checkbox-indicator-indeterminate-bg:    $component-active-bg !default;\n$custom-checkbox-indicator-indeterminate-color: $custom-control-indicator-checked-color !default;\n$custom-checkbox-indicator-icon-indeterminate:  str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath stroke='#{$custom-checkbox-indicator-indeterminate-color}' d='M0 2h4'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$custom-checkbox-indicator-indeterminate-box-shadow: none !default;\n\n$custom-radio-indicator-border-radius:          50% !default;\n$custom-radio-indicator-icon-checked:           str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='#{$custom-control-indicator-checked-color}'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$custom-select-padding-y:           .375rem !default;\n$custom-select-padding-x:          .75rem !default;\n$custom-select-height:              $input-height !default;\n$custom-select-indicator-padding:   1rem !default; // Extra padding to account for the presence of the background-image based indicator\n$custom-select-line-height:         $input-btn-line-height !default;\n$custom-select-color:               $input-color !default;\n$custom-select-disabled-color:      $gray-600 !default;\n$custom-select-bg:                  $white !default;\n$custom-select-disabled-bg:         $gray-200 !default;\n$custom-select-bg-size:             8px 10px !default; // In pixels because image dimensions\n$custom-select-indicator-color:     $gray-800 !default;\n$custom-select-indicator:           str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='#{$custom-select-indicator-color}' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$custom-select-border-width:        $input-btn-border-width !default;\n$custom-select-border-color:        $input-border-color !default;\n$custom-select-border-radius:       $border-radius !default;\n\n$custom-select-focus-border-color:  $input-focus-border-color !default;\n$custom-select-focus-box-shadow:    none !default;\n\n$custom-select-font-size-sm:        75% !default;\n$custom-select-height-sm:           $input-height-sm !default;\n\n$custom-select-font-size-lg:        125% !default;\n$custom-select-height-lg:           $input-height-lg !default;\n\n$custom-file-height:                $input-height !default;\n$custom-file-focus-border-color:    $input-focus-border-color !default;\n$custom-file-focus-box-shadow:      $custom-select-focus-box-shadow !default;\n\n$custom-file-padding-y:             $input-btn-padding-y !default;\n$custom-file-padding-x:             $input-btn-padding-x !default;\n$custom-file-line-height:           $input-btn-line-height !default;\n$custom-file-color:                 $input-color !default;\n$custom-file-bg:                    $input-bg !default;\n$custom-file-border-width:          $input-btn-border-width !default;\n$custom-file-border-color:          $input-border-color !default;\n$custom-file-border-radius:         $input-border-radius !default;\n$custom-file-box-shadow:            $custom-select-focus-box-shadow !default;\n$custom-file-button-color:          $custom-file-color !default;\n$custom-file-button-bg:             $input-group-addon-bg !default;\n$custom-file-text: (\n  en: \"Browse\"\n) !default;\n\n$custom-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-btn-focus-box-shadow !default;\n\n\n// Form validation\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $small-font-size !default;\n$form-feedback-valid-color:         theme-color(\"success\") !default;\n$form-feedback-invalid-color:       theme-color(\"danger\") !default;\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-bg:                       $white !default;\n$dropdown-border-color:             rgba($black, .15) !default;\n$dropdown-border-radius:            $border-radius !default;\n$dropdown-border-width:             $border-width !default;\n$dropdown-divider-bg:               $gray-200 !default;\n$dropdown-box-shadow:               0 .5rem 1rem rgba($black, .175) !default;\n\n$dropdown-link-color:               $gray-900 !default;\n$dropdown-link-hover-color:         darken($gray-900, 5%) !default;\n$dropdown-link-hover-bg:            $gray-100 !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      $gray-600 !default;\n\n$dropdown-item-padding-y:           .25rem !default;\n$dropdown-item-padding-x:           1rem !default;\n\n$dropdown-header-color:             $gray-600 !default;\n\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-modal-backdrop:             1040 !default;\n$zindex-modal:                      1050 !default;\n$zindex-popover:                    1060 !default;\n$zindex-tooltip:                    1070 !default;\n\n// Navs\n\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-disabled-color:           $gray-600 !default;\n\n$nav-tabs-border-color:             $gray-300 !default;\n$nav-tabs-border-width:             $border-width !default;\n$nav-tabs-border-radius:            $border-radius !default;\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        $gray-700 !default;\n$nav-tabs-link-active-bg:           $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           $border-radius !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n\n// Navbar\n\n$navbar-padding-y:                  ($spacer / 2) !default;\n$navbar-padding-x:                  ($spacer / 2) !default;\n\n$navbar-nav-link-padding-x:         1rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   ($font-size-base * $line-height-base + $nav-link-padding-y * 2) !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) / 2 !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n\n$navbar-dark-color:                 rgba($white, .75) !default;\n$navbar-dark-hover-color:           rgba($white, 1) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg:       str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#{$navbar-dark-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n\n$navbar-light-color:                rgba($black, .5) !default;\n$navbar-light-hover-color:          rgba($black, .7) !default;\n$navbar-light-active-color:         rgba($black, .9) !default;\n$navbar-light-disabled-color:       rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg:      str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#{$navbar-light-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n// Pagination\n\n$pagination-padding-y:              .5rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n$pagination-line-height:            1.25 !default;\n\n$pagination-color:                  $link-color !default;\n$pagination-bg:                     $white !default;\n$pagination-border-width:           $border-width !default;\n$pagination-border-color:           $gray-300 !default;\n\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n\n$pagination-hover-color:            $link-hover-color !default;\n$pagination-hover-bg:               $gray-200 !default;\n$pagination-hover-border-color:     $gray-300 !default;\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $pagination-active-bg !default;\n\n$pagination-disabled-color:         $gray-600 !default;\n$pagination-disabled-bg:            $white !default;\n$pagination-disabled-border-color:  $gray-300 !default;\n\n\n// Jumbotron\n\n$jumbotron-padding:                 2rem !default;\n$jumbotron-bg:                      $gray-200 !default;\n\n\n// Cards\n\n$card-spacer-y:                     .75rem !default;\n$card-spacer-x:                     1.25rem !default;\n$card-border-width:                 0 !default; //$border-width !default;\n$card-border-radius:                $border-radius !default;\n$card-border-color:                 rgba($black, .125) !default;\n$card-inner-border-radius:          calc(#{$card-border-radius} - #{$card-border-width}) !default;\n$card-cap-bg:                       rgba($black, .03) !default;\n$card-bg:                           $white !default;\n\n$card-img-overlay-padding:          1.25rem !default;\n\n$card-group-margin:                 ($grid-gutter-width / 2) !default;\n$card-deck-margin:                  $card-group-margin !default;\n\n$card-columns-count:                3 !default;\n$card-columns-gap:                  1.25rem !default;\n$card-columns-margin:               $card-spacer-y !default;\n\n\n// Tooltips\n\n$tooltip-font-size:           $font-size-sm !default;\n$tooltip-max-width:           200px !default;\n$tooltip-color:               $white !default;\n$tooltip-bg:                  $black !default;\n$tooltip-border-radius:        $border-radius !default;\n$tooltip-opacity:             .9 !default;\n$tooltip-padding-y:           .25rem !default;\n$tooltip-padding-x:           .5rem !default;\n$tooltip-margin:              0 !default;\n\n$tooltip-arrow-width:         .8rem !default;\n$tooltip-arrow-height:        .4rem !default;\n$tooltip-arrow-color:         $tooltip-bg !default;\n\n// Form tooltips must come after regular tooltips\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   $line-height-base !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n\n// Popovers\n\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        $white !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              $border-width !default;\n$popover-border-color:              rgba($black, .2) !default;\n$popover-border-radius:             $border-radius-lg !default;\n$popover-box-shadow:                0 .25rem .5rem rgba($black, .2) !default;\n\n$popover-header-bg:                 darken($popover-bg, 3%) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          .75rem !default;\n\n$popover-body-color:                $body-color !default;\n$popover-body-padding-y:            $popover-header-padding-y !default;\n$popover-body-padding-x:            $popover-header-padding-x !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n$popover-arrow-color:               $popover-bg !default;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05) !default;\n\n\n// Badges\n\n$badge-font-size:                   75% !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-padding-y:                   .25em !default;\n$badge-padding-x:                   .4em !default;\n$badge-border-radius:               $border-radius !default;\n\n$badge-pill-padding-x:              .6em !default;\n// Use a higher than normal value to ensure completely rounded edges when\n// customizing padding or font-size on labels.\n$badge-pill-border-radius:          10rem !default;\n\n\n// Modals\n\n// Padding applied to the modal body\n$modal-inner-padding:         1rem !default;\n\n$modal-dialog-margin:         .5rem !default;\n$modal-dialog-margin-y-sm-up: 1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-bg:               $white !default;\n$modal-content-border-color:     rgba($black, .2) !default;\n$modal-content-border-width:     $border-width !default;\n$modal-content-box-shadow-xs:    0 .25rem .5rem rgba($black, .5) !default;\n$modal-content-box-shadow-sm-up: 0 .5rem 1rem rgba($black, .5) !default;\n\n$modal-backdrop-bg:           $black !default;\n$modal-backdrop-opacity:      .5 !default;\n$modal-header-border-color:   $gray-200 !default;\n$modal-footer-border-color:   $modal-header-border-color !default;\n$modal-header-border-width:   $modal-content-border-width !default;\n$modal-footer-border-width:   $modal-header-border-width !default;\n$modal-header-padding:        1rem !default;\n\n$modal-lg:                          800px !default;\n$modal-md:                          500px !default;\n$modal-sm:                          300px !default;\n\n$modal-transition:                  transform .3s ease-out !default;\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n$alert-padding-y:                   .75rem !default;\n$alert-padding-x:                   1.25rem !default;\n$alert-margin-bottom:               1rem !default;\n$alert-border-radius:               $border-radius !default;\n$alert-link-font-weight:            $font-weight-bold !default;\n$alert-border-width:                $border-width !default;\n\n$alert-bg-level:                    -10 !default;\n$alert-border-level:                -9 !default;\n$alert-color-level:                 6 !default;\n\n\n// Progress bars\n\n$progress-height:                   1rem !default;\n$progress-font-size:                ($font-size-base * .75) !default;\n$progress-bg:                       $gray-200 !default;\n$progress-border-radius:            $border-radius !default;\n$progress-box-shadow:               inset 0 .1rem .1rem rgba($black, .1) !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   theme-color(\"primary\") !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n\n// List group\n\n$list-group-bg:                     $white !default;\n$list-group-border-color:           rgba($black, .125) !default;\n$list-group-border-width:           $border-width !default;\n$list-group-border-radius:          $border-radius !default;\n\n$list-group-item-padding-y:         .75rem !default;\n$list-group-item-padding-x:         1.25rem !default;\n\n$list-group-hover-bg:               $gray-100 !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         $gray-600 !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           $gray-700 !default;\n$list-group-action-hover-color:     $list-group-action-color !default;\n\n$list-group-action-active-color:    $body-color !default;\n$list-group-action-active-bg:       $gray-200 !default;\n\n\n// Image thumbnails\n\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      $body-bg !default;\n$thumbnail-border-width:            $border-width !default;\n$thumbnail-border-color:            $gray-300 !default;\n$thumbnail-border-radius:           $border-radius !default;\n$thumbnail-box-shadow:              0 1px 2px rgba($black, .075) !default;\n\n\n// Figures\n\n$figure-caption-font-size:          90% !default;\n$figure-caption-color:              $gray-600 !default;\n\n\n// Breadcrumbs\n\n$breadcrumb-padding-y:              .75rem !default;\n$breadcrumb-padding-x:              1rem !default;\n$breadcrumb-item-padding:           .5rem !default;\n\n$breadcrumb-margin-bottom:          1rem !default;\n\n$breadcrumb-bg:                     $gray-200 !default;\n$breadcrumb-divider-color:          $gray-600 !default;\n$breadcrumb-active-color:           $gray-600 !default;\n$breadcrumb-divider:                \"/\" !default;\n\n\n// Carousel\n\n$carousel-control-color:            $white !default;\n$carousel-control-width:            15% !default;\n$carousel-control-opacity:          .5 !default;\n\n$carousel-indicator-width:          30px !default;\n$carousel-indicator-height:         3px !default;\n$carousel-indicator-spacer:         3px !default;\n$carousel-indicator-active-bg:      $white !default;\n\n$carousel-caption-width:            70% !default;\n$carousel-caption-color:            $white !default;\n\n$carousel-control-icon-width:       20px !default;\n\n$carousel-control-prev-icon-bg:     str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3E%3Cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$carousel-control-next-icon-bg:     str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3E%3Cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$carousel-transition:               transform .6s ease !default;\n\n\n// Close\n\n$close-font-size:                   $font-size-base * 1.5 !default;\n$close-font-weight:                 $font-weight-bold !default;\n$close-color:                       $black !default;\n$close-text-shadow:                 0 1px 0 $white !default;\n\n// Code\n\n$code-font-size:                    87.5% !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .2rem !default;\n$kbd-padding-x:                     .4rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         $white !default;\n$kbd-bg:                            $gray-900 !default;\n\n$pre-color:                         $gray-900 !default;\n$pre-scrollable-max-height:         340px !default;\n\n\n// Printing\n$print-page-size:                   a3 !default;\n$print-body-min-width:              map-get($grid-breakpoints, \"lg\") !default;\n", "// stylelint-disable property-disallowed-list\n// Single side border-radius\n\n// Helper function to replace negative values with 0\n@function valid-radius($radius) {\n  $return: ();\n  @each $value in $radius {\n    @if type-of($value) == number {\n      $return: append($return, max($value, 0));\n    } @else {\n      $return: append($return, $value);\n    }\n  }\n  @return $return;\n}\n\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: valid-radius($radius);\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-right-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-left-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n", "// stylelint-disable property-blacklist, scss/dollar-variable-default\n\n// SCSS RFS mixin\n//\n// Automated font-resizing\n//\n// See https://github.com/twbs/rfs\n\n// Configuration\n\n// Base font size\n$rfs-base-font-size: 1.25rem !default;\n$rfs-font-size-unit: rem !default;\n\n// Breakpoint at where font-size starts decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n// Resize font-size based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != \"number\" or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-responsive-font-sizes to false\n$enable-responsive-font-sizes: true !default;\n\n// Cache $rfs-base-font-size unit\n$rfs-base-font-size-unit: unit($rfs-base-font-size);\n\n// Remove px-unit from $rfs-base-font-size for calculations\n@if $rfs-base-font-size-unit == \"px\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1);\n}\n@else if $rfs-base-font-size-unit == \"rem\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1 / $rfs-rem-value);\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == \"px\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == \"rem\" or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1 / $rfs-rem-value);\n}\n\n// Responsive font-size mixin\n@mixin rfs($fs, $important: false) {\n  // Cache $fs unit\n  $fs-unit: if(type-of($fs) == \"number\", unit($fs), false);\n\n  // Add !important suffix if needed\n  $rfs-suffix: if($important, \" !important\", \"\");\n\n  // If $fs isn't a number (like inherit) or $fs has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n  @if not $fs-unit or $fs-unit != \"\" and $fs-unit != \"px\" and $fs-unit != \"rem\" or $fs == 0 {\n    font-size: #{$fs}#{$rfs-suffix};\n  }\n  @else {\n    // Variables for storing static and fluid rescaling\n    $rfs-static: null;\n    $rfs-fluid: null;\n\n    // Remove px-unit from $fs for calculations\n    @if $fs-unit == \"px\" {\n      $fs: $fs / ($fs * 0 + 1);\n    }\n    @else if $fs-unit == \"rem\" {\n      $fs: $fs / ($fs * 0 + 1 / $rfs-rem-value);\n    }\n\n    // Set default font-size\n    @if $rfs-font-size-unit == rem {\n      $rfs-static: #{$fs / $rfs-rem-value}rem#{$rfs-suffix};\n    }\n    @else if $rfs-font-size-unit == px {\n      $rfs-static: #{$fs}px#{$rfs-suffix};\n    }\n    @else {\n      @error \"`#{$rfs-font-size-unit}` is not a valid unit for $rfs-font-size-unit. Use `px` or `rem`.\";\n    }\n\n    // Only add media query if font-size is bigger as the minimum font-size\n    // If $rfs-factor == 1, no rescaling will take place\n    @if $fs > $rfs-base-font-size and $enable-responsive-font-sizes {\n      $min-width: null;\n      $variable-unit: null;\n\n      // Calculate minimum font-size for given font-size\n      $fs-min: $rfs-base-font-size + ($fs - $rfs-base-font-size) / $rfs-factor;\n\n      // Calculate difference between given font-size and minimum font-size for given font-size\n      $fs-diff: $fs - $fs-min;\n\n      // Base font-size formatting\n      // No need to check if the unit is valid, because we did that before\n      $min-width: if($rfs-font-size-unit == rem, #{$fs-min / $rfs-rem-value}rem, #{$fs-min}px);\n\n      // If two-dimensional, use smallest of screen width and height\n      $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n      // Calculate the variable width between 0 and $rfs-breakpoint\n      $variable-width: #{$fs-diff * 100 / $rfs-breakpoint}#{$variable-unit};\n\n      // Set the calculated font-size.\n      $rfs-fluid: calc(#{$min-width} + #{$variable-width}) #{$rfs-suffix};\n    }\n\n    // Rendering\n    @if $rfs-fluid == null {\n      // Only render static font-size if no fluid font-size is available\n      font-size: $rfs-static;\n    }\n    @else {\n      $mq-value: null;\n\n      // RFS breakpoint formatting\n      @if $rfs-breakpoint-unit == em or $rfs-breakpoint-unit == rem {\n        $mq-value: #{$rfs-breakpoint / $rfs-rem-value}#{$rfs-breakpoint-unit};\n      }\n      @else if $rfs-breakpoint-unit == px {\n        $mq-value: #{$rfs-breakpoint}px;\n      }\n      @else {\n        @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n      }\n\n      @if $rfs-class == \"disable\" {\n        // Adding an extra class increases specificity,\n        // which prevents the media query to override the font size\n        &,\n        .disable-responsive-font-size &,\n        &.disable-responsive-font-size {\n          font-size: $rfs-static;\n        }\n      }\n      @else {\n        font-size: $rfs-static;\n      }\n\n      @if $rfs-two-dimensional {\n        @media (max-width: #{$mq-value}), (max-height: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n      @else {\n        @media (max-width: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n    }\n  }\n}\n\n// The font-size & responsive-font-size mixin uses RFS to rescale font sizes\n@mixin font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n\n@mixin responsive-font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n", "// Gradients\n\n@mixin gradient-bg($color) {\n  @if $enable-gradients {\n    background: $color linear-gradient(180deg, mix($body-bg, $color, 15%), $color) repeat-x;\n  } @else {\n    background-color: $color;\n  }\n}\n\n// Horizontal gradient, from left to right\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-x($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to right, $start-color $start-percent, $end-color $end-percent);\n  background-repeat: repeat-x;\n}\n\n// Vertical gradient, from top to bottom\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-y($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to bottom, $start-color $start-percent, $end-color $end-percent);\n  background-repeat: repeat-x;\n}\n\n@mixin gradient-directional($start-color: $gray-700, $end-color: $gray-800, $deg: 45deg) {\n  background-image: linear-gradient($deg, $start-color, $end-color);\n  background-repeat: repeat-x;\n}\n@mixin gradient-x-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient(to right, $start-color, $mid-color $color-stop, $end-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-y-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient($start-color, $mid-color $color-stop, $end-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-radial($inner-color: $gray-700, $outer-color: $gray-800) {\n  background-image: radial-gradient(circle, $inner-color, $outer-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-striped($color: rgba($white, .15), $angle: 45deg) {\n  background-image: linear-gradient($angle, $color 25%, transparent 25%, transparent 50%, $color 50%, $color 75%, transparent 75%, transparent);\n}\n", "//\n// Mixins: Custom Forms\n//\n\n// Custom Switch Variant\n@mixin custom-switch-variant($name, $color) {\n  &.custom-switch-off-#{$name} {\n    .custom-control-input ~ .custom-control-label::before {\n      background-color: #{$color};\n      border-color: darken($color, 20%);\n    }\n\n    .custom-control-input:focus ~ .custom-control-label::before {\n      box-shadow: 0 0 0 1px $body-bg, 0 0 0 2px rgba($color, .25);\n    }\n\n    .custom-control-input ~ .custom-control-label::after {\n      background-color: darken($color, 25%);\n    }\n  }\n\n  &.custom-switch-on-#{$name} {\n    .custom-control-input:checked ~ .custom-control-label::before {\n      background-color: #{$color};\n      border-color: darken($color, 20%);\n    }\n\n    .custom-control-input:checked:focus ~ .custom-control-label::before {\n      box-shadow: 0 0 0 1px $body-bg, 0 0 0 2px rgba($color, .25);\n    }\n\n    .custom-control-input:checked ~ .custom-control-label::after {\n      background-color: lighten($color, 30%);\n    }\n  }\n}\n\n// Custom Range Variant\n@mixin custom-range-variant($name, $color) {\n  &.custom-range-#{$name} {\n    &:focus {\n      outline: none;\n\n      &::-webkit-slider-thumb {\n        box-shadow: 0 0 0 1px $body-bg, 0 0 0 2px rgba($color, .25);\n      }\n\n      &::-moz-range-thumb     {\n        box-shadow: 0 0 0 1px $body-bg, 0 0 0 2px rgba($color, .25);\n      }\n\n      &::-ms-thumb            {\n        box-shadow: 0 0 0 1px $body-bg, 0 0 0 2px rgba($color, .25);\n      }\n    }\n\n    &::-webkit-slider-thumb {\n      background-color: $color;\n\n      &:active {\n        background-color: lighten($color, 35%);\n      }\n    }\n\n    &::-moz-range-thumb {\n      background-color: $color;\n\n      &:active {\n        background-color: lighten($color, 35%);\n      }\n    }\n\n    &::-ms-thumb {\n      background-color: $color;\n\n      &:active {\n        background-color: lighten($color, 35%);\n      }\n    }\n  }\n}\n\n\n// Custom Control Input Variant\n@mixin custom-control-input-variant($name, $color) {\n  $custom-control-indicator-checked-color: $color;\n  $custom-checkbox-indicator-icon-checked: str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='#{$custom-control-indicator-checked-color}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\"), \"#\", \"%23\");\n  $custom-radio-indicator-icon-checked: str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='#{$custom-control-indicator-checked-color}'/%3E%3C/svg%3E\"), \"#\", \"%23\");\n\n  .custom-control-input-#{$name} {\n    &:checked ~ .custom-control-label::before {\n      border-color: $color;\n      @include gradient-bg($color);\n    }\n\n    &.custom-control-input-outline:checked {\n      &[type=\"checkbox\"] ~ .custom-control-label::after {\n        background-image: $custom-checkbox-indicator-icon-checked !important;\n      }\n      &[type=\"radio\"] ~ .custom-control-label::after {\n        background-image: $custom-radio-indicator-icon-checked !important;\n      }\n    }\n\n    &:focus ~ .custom-control-label::before {\n      // the mixin is not used here to make sure there is feedback\n      @if $enable-shadows {\n        box-shadow: $input-box-shadow, 0 0 0 $input-btn-focus-width rgba($color, .25);\n      } @else {\n        box-shadow: 0 0 0 $input-btn-focus-width rgba($color, .25);\n      }\n    }\n\n    &:focus:not(:checked) ~ .custom-control-label::before {\n      border-color: lighten($color, 25%);\n    }\n\n    &:not(:disabled):active ~ .custom-control-label::before {\n      background-color: lighten($color, 35%);\n      border-color: lighten($color, 35%);\n    }\n  }\n}\n", "//\n// Core: Variables\n//\n\n// COLORS\n// --------------------------------------------------------\n$blue: #0073b7 !default;\n$lightblue: #3c8dbc !default;\n$navy: #001f3f !default;\n$teal: #39cccc !default;\n$olive: #3d9970 !default;\n$lime: #01ff70 !default;\n$orange: #ff851b !default;\n$fuchsia: #f012be !default;\n$purple: #605ca8 !default;\n$maroon: #d81b60 !default;\n$black: #111 !default;\n$gray-x-light: #d2d6de !default;\n\n$colors: map-merge(\n  (\n    \"lightblue\": $lightblue,\n    \"navy\": $navy,\n    \"olive\": $olive,\n    \"lime\": $lime,\n    \"fuchsia\": $fuchsia,\n    \"maroon\": $maroon,\n  ),\n  $colors\n);\n\n// LAYOUT\n// --------------------------------------------------------\n\n$font-size-root: 1rem !default;\n\n// Sidebar\n$sidebar-width: 250px !default;\n$sidebar-padding-x: .5rem !default;\n$sidebar-padding-y: 0 !default;\n$sidebar-custom-height: 4rem !default;\n$sidebar-custom-height-lg: 6rem !default;\n$sidebar-custom-height-xl: 8rem !default;\n$sidebar-custom-padding-x: .85rem !default;\n$sidebar-custom-padding-y: .5rem !default;\n\n// Boxed layout maximum width\n$boxed-layout-max-width: 1250px !default;\n\n// When to show the smaller logo\n$screen-header-collapse: map-get($grid-breakpoints, md) !default;\n\n// Body background (Affects main content background only)\n$main-bg: #f4f6f9 !default;\n\n// Content padding\n$content-padding-y: 0 !default;\n$content-padding-x: $navbar-padding-x !default;\n\n// IMAGE SIZES\n// --------------------------------------------------------\n$img-size-sm: 1.875rem !default;\n$img-size-md: 3.75rem !default;\n$img-size-lg: 6.25rem !default;\n$img-size-push: .625rem !default;\n\n// MAIN HEADER\n// --------------------------------------------------------\n$main-header-bottom-border-width: $border-width !default;\n$main-header-bottom-border-color: $gray-300 !default;\n$main-header-bottom-border: $main-header-bottom-border-width solid $main-header-bottom-border-color !default;\n$main-header-link-padding-y: $navbar-padding-y !default;\n$main-header-link-padding-x: $navbar-padding-x !default;\n$main-header-brand-padding-y: $navbar-brand-padding-y !default;\n$main-header-brand-padding-x: $navbar-padding-x !default;\n$main-header-height-inner: ($nav-link-height + ($main-header-link-padding-y * 2)) !default;\n$main-header-height: calc(#{$main-header-height-inner} + #{$main-header-bottom-border-width}) !default;\n$nav-link-sm-padding-y: .35rem !default;\n$nav-link-sm-height: ($font-size-sm * $line-height-sm + $nav-link-sm-padding-y * 1.785) !default;\n$main-header-height-sm-inner: ($nav-link-sm-height + ($main-header-link-padding-y * 2)) !default;\n$main-header-height-sm: calc(#{$main-header-height-sm-inner} + #{$main-header-bottom-border-width}) !default;\n\n\n// Main header skins\n$main-header-dark-form-control-bg: rgba(255, 255, 255, .2) !default;\n$main-header-dark-form-control-focused-bg: rgba(255, 255, 255, .6) !default;\n$main-header-dark-form-control-focused-color: $gray-800 !default;\n$main-header-dark-form-control-border: 0 !default;\n$main-header-dark-form-control-focused-border: 0 !default;\n$main-header-dark-placeholder-color: rgba(255, 255, 255, .6) !default;\n\n$main-header-light-form-control-bg: darken($gray-100, 2%) !default;\n$main-header-light-form-control-focused-bg: $gray-200 !default;\n$main-header-light-form-control-focused-color: $gray-800 !default;\n$main-header-light-form-control-border: 0 !default;\n$main-header-light-form-control-focused-border: 0 !default;\n$main-header-light-placeholder-color: rgba(0, 0, 0, .6) !default;\n\n// MAIN FOOTER\n// --------------------------------------------------------\n$main-footer-padding: 1rem !default;\n$main-footer-padding-sm: $main-footer-padding * .812 !default;\n$main-footer-border-top-width: 1px !default;\n$main-footer-border-top-color: $gray-300 !default;\n$main-footer-border-top: $main-footer-border-top-width solid $main-footer-border-top-color !default;\n$main-footer-height-inner: (($font-size-root * $line-height-base) + ($main-footer-padding * 2)) !default;\n$main-footer-height: calc(#{$main-footer-height-inner} + #{$main-footer-border-top-width}) !default;\n$main-footer-height-sm-inner: (($font-size-sm * $line-height-base) + ($main-footer-padding-sm * 2)) !default;\n$main-footer-height-sm: calc(#{$main-footer-height-sm-inner} + #{$main-footer-border-top-width}) !default;\n$main-footer-bg: $white !default;\n\n// SIDEBAR SKINS\n// --------------------------------------------------------\n\n// Dark sidebar\n$sidebar-dark-bg: $dark !default;\n$sidebar-dark-hover-bg: rgba(255, 255, 255, .1) !default;\n$sidebar-dark-color: #c2c7d0 !default;\n$sidebar-dark-hover-color: $white !default;\n$sidebar-dark-active-color: $white !default;\n$sidebar-dark-submenu-bg: transparent !default;\n$sidebar-dark-submenu-color: #c2c7d0 !default;\n$sidebar-dark-submenu-hover-color: $white !default;\n$sidebar-dark-submenu-hover-bg: $sidebar-dark-hover-bg !default;\n$sidebar-dark-submenu-active-color: $sidebar-dark-bg !default;\n$sidebar-dark-submenu-active-bg: rgba(255, 255, 255, .9) !default;\n$sidebar-dark-header-color: $white !default;\n\n// Light sidebar\n$sidebar-light-bg: $white !default;\n$sidebar-light-hover-bg: rgba($black, .1) !default;\n$sidebar-light-color: $gray-800 !default;\n$sidebar-light-hover-color: $gray-900 !default;\n$sidebar-light-active-color: $black !default;\n$sidebar-light-submenu-bg: transparent !default;\n$sidebar-light-submenu-color: #777 !default;\n$sidebar-light-submenu-hover-color: #000 !default;\n$sidebar-light-submenu-hover-bg: $sidebar-light-hover-bg !default;\n$sidebar-light-submenu-active-color: $sidebar-light-hover-color !default;\n$sidebar-light-submenu-active-bg: $sidebar-light-submenu-hover-bg !default;\n$sidebar-light-header-color: $gray-800 !default;\n\n// SIDEBAR MINI\n// --------------------------------------------------------\n$sidebar-mini-width: ($nav-link-padding-x + $sidebar-padding-x + .8rem) * 2 !default;\n$sidebar-nav-icon-width: $sidebar-mini-width - (($sidebar-padding-x + $nav-link-padding-x) * 2) !default;\n$sidebar-user-image-width: $sidebar-nav-icon-width + ($nav-link-padding-x / 2) !default;\n\n// CONTROL SIDEBAR\n// --------------------------------------------------------\n$control-sidebar-width: $sidebar-width !default;\n\n// Cards\n// --------------------------------------------------------\n$card-border-color: $gray-100 !default;\n$card-dark-border-color: lighten($gray-900, 10%) !default;\n$card-shadow: 0 0 1px rgba(0, 0, 0, .125), 0 1px 3px rgba(0, 0, 0, .2) !default;\n$card-title-font-size: 1.1rem !default;\n$card-title-font-size-sm: 1rem !default;\n$card-title-font-weight: $font-weight-normal !default;\n$card-nav-link-padding-sm-y: .4rem !default;\n$card-nav-link-padding-sm-x: .8rem !default;\n$card-img-size: $img-size-sm !default;\n\n// PROGRESS BARS\n// --------------------------------------------------------\n$progress-bar-border-radius: 1px !default;\n$progress-bar-sm-border-radius: 1px !default;\n$progress-bar-xs-border-radius: 1px !default;\n\n// DIRECT CHAT\n// --------------------------------------------------------\n$direct-chat-height: 250px !default;\n$direct-chat-default-msg-bg: $gray-x-light !default;\n$direct-chat-default-font-color: #444 !default;\n$direct-chat-default-msg-border-color: $gray-x-light !default;\n\n// CHAT WIDGET\n// --------------------------------------------------------\n$attachment-border-radius: 3px !default;\n\n// Z-INDEX\n// --------------------------------------------------------\n$zindex-main-header: $zindex-fixed + 4 !default;\n$zindex-main-sidebar: $zindex-fixed + 8 !default;\n$zindex-main-footer: $zindex-fixed + 2 !default;\n$zindex-control-sidebar: $zindex-fixed + 1 !default;\n$zindex-sidebar-mini-links: 010 !default;\n$zindex-toasts: $zindex-main-sidebar + 2 !default;\n\n// TRANSITIONS SETTINGS\n// --------------------------------------------------------\n\n// Transition global options\n$transition-speed: .3s !default;\n$transition-fn: ease-in-out !default;\n\n// TEXT\n// --------------------------------------------------------\n$font-size-xs: ($font-size-base * .75) !default;\n$font-size-xl: ($font-size-base * 2) !default;\n\n\n// BUTTON\n// --------------------------------------------------------\n$button-default-background-color: $gray-100 !default;\n$button-default-color: #444 !default;\n$button-default-border-color: #ddd !default;\n\n$button-padding-y-xs: .125rem !default;\n$button-padding-x-xs: .25rem !default;\n$button-line-height-xs: $line-height-sm !default;\n$button-font-size-xs: ($font-size-base * .75) !default;\n$button-border-radius-xs: .15rem !default;\n\n\n// ELEVATION\n// --------------------------------------------------------\n$elevations: ();\n$elevations: map-merge(\n  (\n    1: unquote(\"0 1px 3px \" + rgba($black, .12) + \", 0 1px 2px \" + rgba($black, .24)),\n    2: unquote(\"0 3px 6px \" + rgba($black, .16) + \", 0 3px 6px \" + rgba($black, .23)),\n    3: unquote(\"0 10px 20px \" + rgba($black, .19) + \", 0 6px 6px \" + rgba($black, .23)),\n    4: unquote(\"0 14px 28px \" + rgba($black, .25) + \", 0 10px 10px \" + rgba($black, .22)),\n    5: unquote(\"0 19px 38px \" + rgba($black, .3) + \", 0 15px 12px \" + rgba($black, .22)),\n  ),\n  $elevations\n);\n\n// RIBBON\n// --------------------------------------------------------\n$ribbon-border-size: 3px !default;\n$ribbon-line-height: 100% !default;\n$ribbon-padding: .375rem 0 !default;\n$ribbon-font-size: .8rem !default;\n$ribbon-width: 90px !default;\n$ribbon-wrapper-size: 70px !default;\n$ribbon-top: 10px !default;\n$ribbon-right: -2px !default;\n$ribbon-lg-wrapper-size: 120px !default;\n$ribbon-lg-width: 160px !default;\n$ribbon-lg-top: 26px !default;\n$ribbon-lg-right: 0 !default;\n$ribbon-xl-wrapper-size: 180px !default;\n$ribbon-xl-width: 240px !default;\n$ribbon-xl-top: 47px !default;\n$ribbon-xl-right: 4px !default;\n", "//\n// Component: Progress Bar\n//\n\n//General CSS\n.progress {\n  @include box-shadow(none);\n  @include border-radius($progress-bar-border-radius);\n\n  // Vertical bars\n  &.vertical {\n    display: inline-block;\n    height: 200px;\n    margin-right: 10px;\n    position: relative;\n    width: 30px;\n\n    > .progress-bar {\n      bottom: 0;\n      position: absolute;\n      width: 100%;\n    }\n\n    //Sizes\n    &.sm,\n    &.progress-sm {\n      width: 20px;\n    }\n\n    &.xs,\n    &.progress-xs {\n      width: 10px;\n    }\n\n    &.xxs,\n    &.progress-xxs {\n      width: 3px;\n    }\n  }\n}\n\n.progress-group {\n  margin-bottom: map-get($spacers, 2);\n}\n\n// size variation\n.progress-sm {\n  height: 10px;\n}\n\n.progress-xs {\n  height: 7px;\n}\n\n.progress-xxs {\n  height: 3px;\n}\n\n// Remove margins from progress bars when put in a table\n.table {\n  tr > td {\n    .progress {\n      margin: 0;\n    }\n  }\n}\n\n.dark-mode {\n  .progress {\n    background: lighten($dark, 7.5%);\n  }\n}\n", "@mixin box-shadow($shadow...) {\n  @if $enable-shadows {\n    $result: ();\n\n    @if (length($shadow) == 1) {\n      // We can pass `@include box-shadow(none);`\n      $result: $shadow;\n    } @else {\n      // Filter to avoid invalid properties for example `box-shadow: none, 1px 1px black;`\n      @for $i from 1 through length($shadow) {\n        @if nth($shadow, $i) != \"none\" {\n          $result: append($result, nth($shadow, $i), \"comma\");\n        }\n      }\n    }\n    @if (length($result) > 0) {\n      box-shadow: $result;\n    }\n  }\n}\n", "//\n// Mixins: Cards Variant\n//\n\n@mixin cards-variant($name, $color) {\n  .card-#{$name} {\n    &:not(.card-outline) {\n      > .card-header {\n        background-color: $color;\n\n        &,\n        a {\n          color: color-yiq($color);\n        }\n\n        a.active {\n          color: color-yiq($white);\n        }\n      }\n    }\n\n    &.card-outline {\n      border-top: 3px solid $color;\n    }\n\n    &.card-outline-tabs {\n      > .card-header {\n        a {\n          &:hover {\n            border-top: 3px solid $nav-tabs-border-color;\n          }\n\n          &.active {\n            border-top: 3px solid $color;\n          }\n        }\n      }\n    }\n  }\n\n  .bg-#{$name},\n  .bg-gradient-#{$name},\n  .card-#{$name}:not(.card-outline) {\n    .btn-tool {\n      color: rgba(color-yiq($color), .8);\n\n      &:hover {\n        color: color-yiq($color);\n      }\n    }\n  }\n\n  .card.bg-#{$name},\n  .card.bg-gradient-#{$name} {\n    .bootstrap-datetimepicker-widget {\n      .table td,\n      .table th {\n        border: none;\n      }\n\n      table thead tr:first-child th:hover,\n      table td.day:hover,\n      table td.hour:hover,\n      table td.minute:hover,\n      table td.second:hover {\n        background-color: darken($color, 8%);\n        color: color-yiq($color);\n      }\n\n      table td.today::before {\n        border-bottom-color: color-yiq($color);\n      }\n\n      table td.active,\n      table td.active:hover {\n        background-color: lighten($color, 10%);\n        color: color-yiq($color);\n      }\n    }\n  }\n}\n\n", "//\n// Component: Cards\n//\n\n// Color variants\n@each $name, $color in $theme-colors {\n  @include cards-variant($name, $color);\n}\n\n@each $name, $color in $colors {\n  @include cards-variant($name, $color);\n}\n\n.card {\n  @include box-shadow($card-shadow);\n  margin-bottom: map-get($spacers, 3);\n\n  &.bg-dark {\n    .card-header {\n      border-color: $card-dark-border-color;\n    }\n\n    &,\n    .card-body {\n      color: $white;\n    }\n  }\n\n  &.maximized-card {\n    height: 100% !important;\n    left: 0;\n    max-height: 100% !important;\n    max-width: 100% !important;\n    position: fixed;\n    top: 0;\n    width: 100% !important;\n    z-index: $zindex-modal-backdrop;\n\n    &.was-collapsed .card-body {\n      display: block !important;\n    }\n\n    .card-body {\n      overflow: auto;\n    }\n\n    [data-widget=\"collapse\"] {\n      display: none;\n    }\n\n    .card-header,\n    .card-footer {\n      @include border-radius(0 !important);\n    }\n  }\n\n  // collapsed mode\n  &.collapsed-card {\n    .card-body,\n    .card-footer {\n      display: none;\n    }\n  }\n\n  .nav.flex-column {\n    > li {\n      border-bottom: 1px solid $card-border-color;\n      margin: 0;\n\n      &:last-of-type {\n        border-bottom: 0;\n      }\n    }\n  }\n\n  // fixed height to 300px\n  &.height-control {\n    .card-body {\n      max-height: 300px;\n      overflow: auto;\n    }\n  }\n\n  .border-right {\n    border-right: 1px solid $card-border-color;\n  }\n\n  .border-left {\n    border-left: 1px solid $card-border-color;\n  }\n\n  &.card-tabs {\n    &:not(.card-outline) {\n      > .card-header {\n        border-bottom: 0;\n\n        .nav-item {\n          &:first-child .nav-link {\n            border-left-color: transparent;\n          }\n        }\n      }\n    }\n\n    &.card-outline {\n      .nav-item {\n        border-bottom: 0;\n\n        &:first-child .nav-link {\n          border-left: 0;\n          margin-left: 0;\n        }\n      }\n    }\n\n    .card-tools {\n      margin: .3rem .5rem;\n    }\n\n    &:not(.expanding-card).collapsed-card {\n      .card-header {\n        border-bottom: 0;\n\n        .nav-tabs {\n          border-bottom: 0;\n\n          .nav-item {\n            margin-bottom: 0;\n          }\n        }\n      }\n    }\n\n    &.expanding-card {\n      .card-header {\n        .nav-tabs {\n          .nav-item {\n            margin-bottom: -1px;\n          }\n        }\n      }\n    }\n  }\n\n  &.card-outline-tabs {\n    border-top: 0;\n\n    .card-header {\n      .nav-item {\n        &:first-child .nav-link {\n          border-left: 0;\n          margin-left: 0;\n        }\n      }\n\n      a {\n        border-top: 3px solid transparent;\n\n        &:hover {\n          border-top: 3px solid $nav-tabs-border-color;\n        }\n\n        &.active {\n          &:hover {\n            margin-top: 0;\n          }\n        }\n      }\n    }\n\n    .card-tools {\n      margin: .5rem .5rem .3rem;\n    }\n\n    &:not(.expanding-card).collapsed-card .card-header {\n      border-bottom: 0;\n\n      .nav-tabs {\n        border-bottom: 0;\n\n        .nav-item {\n          margin-bottom: 0;\n        }\n      }\n    }\n\n    &.expanding-card {\n      .card-header {\n        .nav-tabs {\n          .nav-item {\n            margin-bottom: -1px;\n          }\n        }\n      }\n    }\n  }\n\n}\n\n// Maximized Card Body Scroll fix\nhtml.maximized-card {\n  overflow: hidden;\n}\n\n// Add clearfix to header, body and footer\n.card-header,\n.card-body,\n.card-footer {\n  @include clearfix ();\n}\n\n// Box header\n.card-header {\n  background-color: transparent;\n  border-bottom: 1px solid $card-border-color;\n  padding: (($card-spacer-y / 2) * 2) $card-spacer-x;\n  position: relative;\n\n  @if $enable-rounded {\n    @include border-top-radius($border-radius);\n  }\n\n  .collapsed-card & {\n    border-bottom: 0;\n  }\n\n  > .card-tools {\n    float: right;\n    margin-right: -$card-spacer-x / 2;\n\n    .input-group,\n    .nav,\n    .pagination {\n      margin-bottom: -$card-spacer-y / 2.5;\n      margin-top: -$card-spacer-y / 2.5;\n    }\n\n    [data-toggle=\"tooltip\"] {\n      position: relative;\n    }\n  }\n}\n\n.card-title {\n  float: left;\n  font-size: $card-title-font-size;\n  font-weight: $card-title-font-weight;\n  margin: 0;\n}\n\n.card-text {\n  clear: both;\n}\n\n\n// Box Tools Buttons\n.btn-tool {\n  background-color: transparent;\n  color: $gray-500;\n  font-size: $font-size-sm;\n  margin: -(($card-spacer-y / 2) * 2) 0;\n  padding: .25rem .5rem;\n\n  .btn-group.show &,\n  &:hover {\n    color: $gray-700;\n  }\n\n  .show &,\n  &:focus {\n    box-shadow: none !important;\n  }\n}\n\n.text-sm {\n  .card-title {\n    font-size: $card-title-font-size-sm;\n  }\n\n  .nav-link {\n    padding: $card-nav-link-padding-sm-y $card-nav-link-padding-sm-x;\n  }\n}\n\n// Box Body\n.card-body {\n  // @include border-radius-sides(0, 0, $border-radius, $border-radius);\n  // .no-header & {\n  //   @include border-top-radius($border-radius);\n  // }\n\n  // Tables within the box body\n  > .table {\n    margin-bottom: 0;\n\n    > thead > tr > th,\n    > thead > tr > td {\n      border-top-width: 0;\n    }\n  }\n\n  // Calendar within the box body\n  .fc {\n    margin-top: 5px;\n  }\n\n  .full-width-chart {\n    margin: -19px;\n  }\n\n  &.p-0 .full-width-chart {\n    margin: -9px;\n  }\n}\n\n.chart-legend {\n  @include list-unstyled ();\n  margin: 10px 0;\n\n  > li {\n    @media (max-width: map-get($grid-breakpoints, sm)) {\n      float: left;\n      margin-right: 10px;\n    }\n  }\n}\n\n// Comment Box\n.card-comments {\n  background-color: $gray-100;\n\n  .card-comment {\n    @include clearfix ();\n    border-bottom: 1px solid $gray-200;\n    padding: 8px 0;\n\n    &:last-of-type {\n      border-bottom: 0;\n    }\n\n    &:first-of-type {\n      padding-top: 0;\n    }\n\n    img {\n      height: $card-img-size;\n      width: $card-img-size;\n      float: left;\n    }\n  }\n\n  .comment-text {\n    color: lighten($gray-700, 20%);\n    margin-left: 40px;\n  }\n\n  .username {\n    color: $gray-700;\n    display: block;\n    font-weight: 600;\n  }\n\n  .text-muted {\n    font-size: 12px;\n    font-weight: 400;\n  }\n}\n\n// Widgets\n//-----------\n\n// Widget: TODO LIST\n.todo-list {\n  list-style: none;\n  margin: 0;\n  overflow: auto;\n  padding: 0;\n\n  // Todo list element\n  > li {\n    @include border-radius(2px);\n    background-color: $gray-100;\n    border-left: 2px solid $gray-200;\n    color: $gray-700;\n    margin-bottom: 2px;\n    padding: 10px;\n\n    &:last-of-type {\n      margin-bottom: 0;\n    }\n\n    > input[type=\"checkbox\"] {\n      margin: 0 10px 0 5px;\n    }\n\n    .text {\n      display: inline-block;\n      font-weight: 600;\n      margin-left: 5px;\n    }\n\n    // Time labels\n    .badge {\n      font-size: .7rem;\n      margin-left: 10px;\n    }\n\n    // Tools and options box\n    .tools {\n      color: theme-color(\"danger\");\n      display: none;\n      float: right;\n\n      // icons\n      > .fa,\n      > .fas,\n      > .far,\n      > .fab,\n      > .fal,\n      > .fad,\n      > .svg-inline--fa,\n      > .ion {\n        cursor: pointer;\n        margin-right: 5px;\n      }\n    }\n\n    &:hover .tools {\n      display: inline-block;\n    }\n\n    &.done {\n      color: darken($gray-500, 25%);\n\n      .text {\n        font-weight: 500;\n        text-decoration: line-through;\n      }\n\n      .badge {\n        background-color: $gray-500 !important;\n      }\n    }\n  }\n\n  // Color variants\n  @each $name, $color in $theme-colors {\n    .#{$name} {\n      border-left-color: $color;\n    }\n  }\n\n  @each $name, $color in $colors {\n    .#{$name} {\n      border-left-color: $color;\n    }\n  }\n\n  .handle {\n    cursor: move;\n    display: inline-block;\n    margin: 0 5px;\n  }\n}\n\n// END TODO WIDGET\n\n// Input in box\n.card-input {\n  max-width: 200px;\n}\n\n// Nav Tabs override\n.card-default {\n  .nav-item {\n    &:first-child .nav-link {\n      border-left: 0;\n    }\n  }\n}\n\n.dark-mode {\n\n  .card {\n    background-color: $dark;\n    color: $white;\n\n    .card {\n      background-color: lighten($dark, 5%);\n      color: $white;\n    }\n    .nav.flex-column > li {\n      border-bottom-color: $gray-600;\n    }\n    .card-footer {\n      background-color: rgba($black, .1);\n    }\n    &.card-outline-tabs .card-header a:hover {\n      border-color: $gray-600;\n    }\n    &:not(.card-outline) > .card-header a.active {\n      color: $white;\n    }\n  }\n\n  .card-comments {\n    background-color: lighten($dark, 1.25%);\n    .username {\n      color: $gray-400;\n    }\n    .card-comment {\n      border-bottom-color: lighten($dark, 7.5%);\n    }\n  }\n\n  .todo-list > li {\n    background-color: lighten($dark, 5%);\n    border-color: lighten($dark, 7.5%);\n    color: $white;\n  }\n}\n", "@mixin clearfix() {\n  &::after {\n    display: block;\n    clear: both;\n    content: \"\";\n  }\n}\n", "// Lists\n\n// Unstyled keeps list items block level, just removes default browser padding and list-style\n@mixin list-unstyled() {\n  padding-left: 0;\n  list-style: none;\n}\n", "//\n// Component: Modals\n//\n\n// Overlay\n.modal-dialog {\n  .overlay {\n    background-color: $black;\n    display: block;\n    height: 100%;\n    left: 0;\n    opacity: .7;\n    position: absolute;\n    top: 0;\n    width: 100%;\n    z-index: ($zindex-modal + 2);\n  }\n}\n\n\n// BG Color Variations Fixes\n.modal-content {\n  &.bg-warning {\n    .modal-header,\n    .modal-footer {\n      border-color: $gray-800;\n    }\n  }\n\n  &.bg-primary,\n  &.bg-secondary,\n  &.bg-info,\n  &.bg-danger,\n  &.bg-success, {\n    .close {\n      color: $white;\n      text-shadow: 0 1px 0 $black;\n    }\n  }\n}\n\n.dark-mode {\n  .modal-header,\n  .modal-footer {\n    border-color: $gray-600;\n  }\n  .modal-content {\n    background-color: $dark;\n\n    &.bg-warning {\n      .modal-header,\n      .modal-footer {\n        border-color: $gray-600;\n      }\n      .close {\n        color: $dark !important;\n        text-shadow: 0 1px 0 $gray-700 !important;\n      }\n    }\n\n    &.bg-primary,\n    &.bg-secondary,\n    &.bg-info,\n    &.bg-danger,\n    &.bg-success {\n      .modal-header,\n      .modal-footer {\n        border-color: $white;\n      }\n    }\n  }\n}\n", "//\n// Component: Toasts\n//\n\n.toasts-top-right {\n  position: absolute;\n  right: 0;\n  top: 0;\n  z-index: $zindex-toasts;\n\n  &.fixed {\n    position: fixed;\n  }\n}\n\n.toasts-top-left {\n  left: 0;\n  position: absolute;\n  top: 0;\n  z-index: $zindex-toasts;\n\n  &.fixed {\n    position: fixed;\n  }\n}\n\n.toasts-bottom-right {\n  bottom: 0;\n  position: absolute;\n  right: 0;\n  z-index: $zindex-toasts;\n\n  &.fixed {\n    position: fixed;\n  }\n}\n\n.toasts-bottom-left {\n  bottom: 0;\n  left: 0;\n  position: absolute;\n  z-index: $zindex-toasts;\n\n  &.fixed {\n    position: fixed;\n  }\n}\n\n.dark-mode {\n  .toast {\n    background-color: rgba($dark, .85);\n    color: $white;\n\n    .toast-header {\n      background-color: rgba($dark, .7);\n      color: $gray-100;\n    }\n  }\n}\n\n.toast {\n  @each $name, $color in $theme-colors {\n    @include toast-variant($name, $color);\n  }\n  @each $name, $color in $colors {\n    @include toast-variant($name, $color);\n  }\n}\n", "//\n// Mixins: Toasts\n//\n\n// Toast Variant\n@mixin toast-variant($name, $color) {\n  &.bg-#{$name} {\n    background-color: rgba($color, .9) !important;\n    @if (color-yiq($color) == $yiq-text-light) {\n\n      .close {\n        color: color-yiq($color);\n        text-shadow: 0 1px 0 $black;\n      }\n    }\n\n    .toast-header {\n      background-color: rgba($color, .85);\n      color: color-yiq($color);\n    }\n  }\n}\n\n", "//\n// Component: Button\n//\n\n.btn {\n  &.disabled,\n  &:disabled {\n    cursor: not-allowed;\n  }\n\n  // Flat buttons\n  &.btn-flat {\n    @include border-radius(0);\n    border-width: 1px;\n    box-shadow: none;\n  }\n\n  // input file btn\n  &.btn-file {\n    overflow: hidden;\n    position: relative;\n\n    > input[type=\"file\"] {\n      background-color: $white;\n      cursor: inherit;\n      display: block;\n      font-size: 100px;\n      min-height: 100%;\n      min-width: 100%;\n      opacity: 0;\n      outline: none;\n      position: absolute;\n      right: 0;\n      text-align: right;\n      top: 0;\n    }\n  }\n\n  .text-sm & {\n    font-size: $font-size-sm !important;\n  }\n}\n\n// Button color variations\n.btn-default {\n  background-color: $button-default-background-color;\n  border-color: $button-default-border-color;\n  color: $button-default-color;\n\n  &:hover,\n  &:active,\n  &.hover {\n    background-color: darken($button-default-background-color, 5%);\n    color: darken($button-default-color, 10%);\n  }\n}\n\n// Application buttons\n.btn-app {\n  @include border-radius(3px);\n  background-color: $button-default-background-color;\n  border: 1px solid $button-default-border-color;\n  color: $gray-600;\n  font-size: 12px;\n  height: 60px;\n  margin: 0 0 10px 10px;\n  min-width: 80px;\n  padding: 15px 5px;\n  position: relative;\n  text-align: center;\n\n  // Icons within the btn\n  > .fa,\n  > .fas,\n  > .far,\n  > .fab,\n  > .fal,\n  > .fad,\n  > .svg-inline--fa,\n  > .ion {\n    display: block;\n    font-size: 20px;\n  }\n\n  > .svg-inline--fa {\n    margin: 0 auto;\n  }\n\n  &:hover {\n    background-color: $button-default-background-color;\n    border-color: darken($button-default-border-color, 20%);\n    color: $button-default-color;\n  }\n\n  &:active,\n  &:focus {\n    @include box-shadow(inset 0 3px 5px rgba($black, .125));\n  }\n\n  // The badge\n  > .badge {\n    font-size: 10px;\n    font-weight: 400;\n    position: absolute;\n    right: -10px;\n    top: -3px;\n  }\n}\n\n// Extra Button Size\n\n.btn-xs {\n  @include button-size($button-padding-y-xs, $button-padding-x-xs, $button-font-size-xs, $button-line-height-xs, $button-border-radius-xs);\n}\n\n.dark-mode {\n  .btn-default,\n  .btn-app {\n    background-color: lighten($dark, 2.5%);\n    color: $white;\n    border-color: $gray-600;\n\n    &:hover,\n    &:focus {\n      background-color: lighten($dark, 5%);\n      color: $gray-300;\n      border-color: lighten($gray-600, 2.5%);\n    }\n  }\n  .btn-light {\n    background-color: lighten($dark, 7.5%);\n    color: $white;\n    border-color: $gray-600;\n\n    &:hover,\n    &:focus {\n      background-color: lighten($dark, 10%);\n      color: $gray-300;\n      border-color: lighten($gray-600, 5%);\n    }\n  }\n}\n", "// Button variants\n//\n// Easily pump out default styles, as well as :hover, :focus, :active,\n// and disabled options for all buttons\n\n@mixin button-variant($background, $border, $hover-background: darken($background, 7.5%), $hover-border: darken($border, 10%), $active-background: darken($background, 10%), $active-border: darken($border, 12.5%)) {\n  color: color-yiq($background);\n  @include gradient-bg($background);\n  border-color: $border;\n  @include box-shadow($btn-box-shadow);\n\n  @include hover() {\n    color: color-yiq($hover-background);\n    @include gradient-bg($hover-background);\n    border-color: $hover-border;\n  }\n\n  &:focus,\n  &.focus {\n    color: color-yiq($hover-background);\n    @include gradient-bg($hover-background);\n    border-color: $hover-border;\n    @if $enable-shadows {\n      @include box-shadow($btn-box-shadow, 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5));\n    } @else {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      box-shadow: 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n    }\n  }\n\n  // Disabled comes first so active can properly restyle\n  &.disabled,\n  &:disabled {\n    color: color-yiq($background);\n    background-color: $background;\n    border-color: $border;\n    // Remove CSS gradients if they're enabled\n    @if $enable-gradients {\n      background-image: none;\n    }\n  }\n\n  &:not(:disabled):not(.disabled):active,\n  &:not(:disabled):not(.disabled).active,\n  .show > &.dropdown-toggle {\n    color: color-yiq($active-background);\n    background-color: $active-background;\n    @if $enable-gradients {\n      background-image: none; // Remove the gradient for the pressed/active state\n    }\n    border-color: $active-border;\n\n    &:focus {\n      @if $enable-shadows and $btn-active-box-shadow != none {\n        @include box-shadow($btn-active-box-shadow, 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5));\n      } @else {\n        // Avoid using mixin so we can pass custom focus shadow properly\n        box-shadow: 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n      }\n    }\n  }\n}\n\n@mixin button-outline-variant($color, $color-hover: color-yiq($color), $active-background: $color, $active-border: $color) {\n  color: $color;\n  border-color: $color;\n\n  @include hover() {\n    color: $color-hover;\n    background-color: $active-background;\n    border-color: $active-border;\n  }\n\n  &:focus,\n  &.focus {\n    box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);\n  }\n\n  &.disabled,\n  &:disabled {\n    color: $color;\n    background-color: transparent;\n  }\n\n  &:not(:disabled):not(.disabled):active,\n  &:not(:disabled):not(.disabled).active,\n  .show > &.dropdown-toggle {\n    color: color-yiq($active-background);\n    background-color: $active-background;\n    border-color: $active-border;\n\n    &:focus {\n      @if $enable-shadows and $btn-active-box-shadow != none {\n        @include box-shadow($btn-active-box-shadow, 0 0 0 $btn-focus-width rgba($color, .5));\n      } @else {\n        // Avoid using mixin so we can pass custom focus shadow properly\n        box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);\n      }\n    }\n  }\n}\n\n// Button sizes\n@mixin button-size($padding-y, $padding-x, $font-size, $line-height, $border-radius) {\n  padding: $padding-y $padding-x;\n  @include font-size($font-size);\n  line-height: $line-height;\n  // Manually declare to provide an override to the browser default\n  @include border-radius($border-radius, 0);\n}\n", "//\n// Component: Callout\n//\n\n// Base styles (regardless of theme)\n.callout {\n  @if $enable-rounded {\n    @include border-radius($border-radius);\n  }\n\n  @if $enable-shadows {\n    box-shadow: map-get($elevations, 1);\n  } @else {\n    border: 1px solid $gray-300;\n  }\n\n  background-color: $white;\n  border-left: 5px solid $gray-200;\n  margin-bottom: map-get($spacers, 3);\n  padding: 1rem;\n\n  a {\n    color: $gray-700;\n    text-decoration: underline;\n\n    &:hover {\n      color: $gray-200;\n    }\n  }\n\n  p:last-child {\n    margin-bottom: 0;\n  }\n\n  // Themes for different contexts\n  &.callout-danger {\n    border-left-color: darken(theme-color(\"danger\"), 10%);\n  }\n\n  &.callout-warning {\n    border-left-color: darken(theme-color(\"warning\"), 10%);\n  }\n\n  &.callout-info {\n    border-left-color: darken(theme-color(\"info\"), 10%);\n  }\n\n  &.callout-success {\n    border-left-color: darken(theme-color(\"success\"), 10%);\n  }\n}\n\n.dark-mode {\n  .callout {\n    background-color: lighten($dark, 5%);\n  }\n}\n", "//\n// Component: Alert\n//\n\n.alert {\n  .icon {\n    margin-right: 10px;\n  }\n\n  .close {\n    color: $black;\n    opacity: .2;\n\n    &:hover {\n      opacity: .5;\n    }\n  }\n\n  a {\n    color: $white;\n    text-decoration: underline;\n  }\n}\n\n//<PERSON><PERSON> Variants\n@each $color, $value in $theme-colors {\n  .alert-#{$color} {\n    color: color-yiq($value);\n    background-color: $value;\n    border-color: darken($value, 5%);\n  }\n\n  .alert-default-#{$color} {\n    @include alert-variant(theme-color-level($color, $alert-bg-level), theme-color-level($color, $alert-border-level), theme-color-level($color, $alert-color-level));\n  }\n}\n", "@mixin alert-variant($background, $border, $color) {\n  color: $color;\n  @include gradient-bg($background);\n  border-color: $border;\n\n  hr {\n    border-top-color: darken($border, 5%);\n  }\n\n  .alert-link {\n    color: darken($color, 10%);\n  }\n}\n", "// Bootstrap functions\n//\n// Utility mixins and functions for evaluating source code across our variables, maps, and mixins.\n\n// Ascending\n// Used to evaluate Sass maps like our grid breakpoints.\n@mixin _assert-ascending($map, $map-name) {\n  $prev-key: null;\n  $prev-num: null;\n  @each $key, $num in $map {\n    @if $prev-num == null or unit($num) == \"%\" or unit($prev-num) == \"%\" {\n      // Do nothing\n    } @else if not comparable($prev-num, $num) {\n      @warn \"Potentially invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} whose unit makes it incomparable to #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    } @else if $prev-num >= $num {\n      @warn \"Invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} which isn't greater than #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    }\n    $prev-key: $key;\n    $prev-num: $num;\n  }\n}\n\n// Starts at zero\n// Used to ensure the min-width of the lowest breakpoint starts at 0.\n@mixin _assert-starts-at-zero($map, $map-name: \"$grid-breakpoints\") {\n  @if length($map) > 0 {\n    $values: map-values($map);\n    $first-value: nth($values, 1);\n    @if $first-value != 0 {\n      @warn \"First breakpoint in #{$map-name} must start at 0, but starts at #{$first-value}.\";\n    }\n  }\n}\n\n// Replace `$search` with `$replace` in `$string`\n// Used on our SVG icon backgrounds for custom forms.\n//\n// <AUTHOR> Giraudel\n// @param {String} $string - Initial string\n// @param {String} $search - Substring to replace\n// @param {String} $replace ('') - New value\n// @return {String} - Updated string\n@function str-replace($string, $search, $replace: \"\") {\n  $index: str-index($string, $search);\n\n  @if $index {\n    @return str-slice($string, 1, $index - 1) + $replace + str-replace(str-slice($string, $index + str-length($search)), $search, $replace);\n  }\n\n  @return $string;\n}\n\n// See https://codepen.io/kevinweber/pen/dXWoRw\n//\n// Requires the use of quotes around data URIs.\n\n@function escape-svg($string) {\n  @if str-index($string, \"data:image/svg+xml\") {\n    @each $char, $encoded in $escaped-characters {\n      // Do not escape the url brackets\n      @if str-index($string, \"url(\") == 1 {\n        $string: url(\"#{str-replace(str-slice($string, 6, -3), $char, $encoded)}\");\n      } @else {\n        $string: str-replace($string, $char, $encoded);\n      }\n    }\n  }\n\n  @return $string;\n}\n\n// Color contrast\n@function color-yiq($color, $dark: $yiq-text-dark, $light: $yiq-text-light) {\n  $r: red($color);\n  $g: green($color);\n  $b: blue($color);\n\n  $yiq: (($r * 299) + ($g * 587) + ($b * 114)) / 1000;\n\n  @if ($yiq >= $yiq-contrasted-threshold) {\n    @return $dark;\n  } @else {\n    @return $light;\n  }\n}\n\n// Retrieve color Sass maps\n@function color($key: \"blue\") {\n  @return map-get($colors, $key);\n}\n\n@function theme-color($key: \"primary\") {\n  @return map-get($theme-colors, $key);\n}\n\n@function gray($key: \"100\") {\n  @return map-get($grays, $key);\n}\n\n// Request a theme color level\n@function theme-color-level($color-name: \"primary\", $level: 0) {\n  $color: theme-color($color-name);\n  $color-base: if($level > 0, $black, $white);\n  $level: abs($level);\n\n  @return mix($color-base, $color, $level * $theme-color-interval);\n}\n\n// Return valid calc\n@function add($value1, $value2, $return-calc: true) {\n  @if $value1 == null {\n    @return $value2;\n  }\n\n  @if $value2 == null {\n    @return $value1;\n  }\n\n  @if type-of($value1) == number and type-of($value2) == number and comparable($value1, $value2) {\n    @return $value1 + $value2;\n  }\n\n  @return if($return-calc == true, calc(#{$value1} + #{$value2}), $value1 + unquote(\" + \") + $value2);\n}\n\n@function subtract($value1, $value2, $return-calc: true) {\n  @if $value1 == null and $value2 == null {\n    @return null;\n  }\n\n  @if $value1 == null {\n    @return -$value2;\n  }\n\n  @if $value2 == null {\n    @return $value1;\n  }\n\n  @if type-of($value1) == number and type-of($value2) == number and comparable($value1, $value2) {\n    @return $value1 - $value2;\n  }\n\n  @return if($return-calc == true, calc(#{$value1} - #{$value2}), $value1 + unquote(\" - \") + $value2);\n}\n", "//\n// Component: Table\n//\n\n.table {\n  &:not(.table-dark) {\n    color: inherit;\n  }\n\n  // fixed table head\n  &.table-head-fixed {\n    thead tr:nth-child(1) th {\n      background-color: $white;\n      border-bottom: 0;\n      box-shadow: inset 0 1px 0 $table-border-color, inset 0 -1px 0 $table-border-color;\n      position: sticky;\n      top: 0;\n      z-index: 10;\n    }\n\n    &.table-dark {\n      thead tr {\n        &:nth-child(1) th {\n          background-color: $table-dark-bg;\n          box-shadow: inset 0 1px 0 $table-dark-border-color, inset 0 -1px 0 $table-dark-border-color;\n        }\n      }\n    }\n  }\n\n  // no border\n  &.no-border {\n    &,\n    td,\n    th {\n      border: 0;\n    }\n  }\n\n  // .text-center in tables\n  &.text-center {\n    &,\n    td,\n    th {\n      text-align: center;\n    }\n  }\n\n  &.table-valign-middle {\n    thead > tr > th,\n    thead > tr > td,\n    tbody > tr > th,\n    tbody > tr > td {\n      vertical-align: middle;\n    }\n  }\n\n  .card-body.p-0 & {\n    thead > tr > th,\n    thead > tr > td,\n    tfoot > tr > th,\n    tfoot > tr > td,\n    tbody > tr > th,\n    tbody > tr > td {\n      &:first-of-type {\n        padding-left: map-get($spacers, 4);\n      }\n\n      &:last-of-type {\n        padding-right: map-get($spacers, 4);\n      }\n    }\n  }\n}\n\n// Expandable Table\n\n.table-hover tbody tr.expandable-body:hover {\n  background-color: inherit !important;\n}\n\n[data-widget=\"expandable-table\"] {\n  cursor: pointer;\n\n  i {\n    transition: transform $transition-speed linear;\n  }\n  &[aria-expanded=\"true\"] {\n    td > i {\n      // stylelint-disable selector-max-attribute\n      &[class*=\"right\"] {\n        transform: rotate(90deg);\n      }\n      &[class*=\"left\"] {\n        transform: rotate(-90deg);\n      }\n      // stylelint-enable selector-max-attribute\n    }\n  }\n}\n\n.expandable-body {\n  > td {\n    padding: 0 !important;\n    width: 100%;\n\n    > div,\n    > p {\n      padding: $table-cell-padding;\n    }\n  }\n\n  .table {\n    width: calc(100% - #{$table-cell-padding});\n    margin: 0 0 0 $table-cell-padding;\n\n    tr:first-child {\n      td,\n      th {\n        border-top: none;\n      }\n    }\n  }\n}\n\n.dark-mode {\n  .table-bordered {\n    &,\n    td,\n    th {\n      border-color: $gray-600;\n    }\n  }\n  .table-hover {\n    tbody tr:hover {\n      color: $gray-300;\n      background-color: lighten($dark, 2.5%);\n      border-color: $gray-600;\n    }\n  }\n  .table {\n    thead th {\n      border-bottom-color: $gray-600;\n    }\n    th,\n    td {\n      border-top-color: $gray-600;\n    }\n    &.table-head-fixed {\n      thead tr:nth-child(1) th {\n        background-color: lighten($dark, 5%);\n      }\n    }\n  }\n}\n", "//\n// Component: Carousel\n//\n\n.carousel-control-custom-icon {\n  .carousel-control-prev & {\n    margin-left: -20px;\n  }\n  .carousel-control-next & {\n    margin-right: 20px;\n  }\n\n  > .fa,\n  > .fas,\n  > .far,\n  > .fab,\n  > .fal,\n  > .fad,\n  > .svg-inline--fa,\n  > .ion {\n    display: inline-block;\n    font-size: 40px;\n    margin-top: -20px;\n    position: absolute;\n    top: 50%;\n    z-index: 5;\n  }\n}\n", ".close {\n  float: right;\n  @include font-size($close-font-size);\n  font-weight: $close-font-weight;\n  line-height: 1;\n  color: $close-color;\n  text-shadow: $close-text-shadow;\n  opacity: .5;\n\n  // Override <a>'s hover style\n  @include hover() {\n    color: $close-color;\n    text-decoration: none;\n  }\n\n  &:not(:disabled):not(.disabled) {\n    @include hover-focus() {\n      opacity: .75;\n    }\n  }\n\n  &:focus {\n    outline: none;\n  }\n}\n\n// Additional properties for button version\n// iOS requires the button element instead of an anchor tag.\n// If you want the anchor version, it requires `href=\"#\"`.\n// See https://developer.mozilla.org/en-US/docs/Web/Events/click#Safari_Mobile\n\n// stylelint-disable-next-line selector-no-qualifying-type\nbutton.close {\n  padding: 0;\n  background-color: transparent;\n  border: 0;\n}\n\n// Future-proof disabling of clicks on `<a>` elements\n\n// stylelint-disable-next-line selector-no-qualifying-type\na.close.disabled {\n  pointer-events: none;\n}\n", "// Hover mixin and `$enable-hover-media-query` are deprecated.\n//\n// Originally added during our alphas and maintained during betas, this mixin was\n// designed to prevent `:hover` stickiness on iOS-an issue where hover styles\n// would persist after initial touch.\n//\n// For backward compatibility, we've kept these mixins and updated them to\n// always return their regular pseudo-classes instead of a shimmed media query.\n//\n// Issue: https://github.com/twbs/bootstrap/issues/25195\n\n@mixin hover() {\n  &:hover { @content; }\n}\n\n@mixin hover-focus() {\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin plain-hover-focus() {\n  &,\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin hover-focus-active() {\n  &:hover,\n  &:focus,\n  &:active {\n    @content;\n  }\n}\n"]}