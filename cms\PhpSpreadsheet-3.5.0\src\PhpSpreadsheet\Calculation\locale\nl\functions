############################################################
##
## PhpSpreadsheet - function name translations
##
## Nederlands (Dutch)
##
############################################################


##
## Kubusfuncties (Cube Functions)
##
CUBEKPIMEMBER = KUBUSKPILID
CUBEMEMBER = KUBUSLID
CUBEMEMBERPROPERTY = KUBUSLIDEIGENSCHAP
CUBERANKEDMEMBER = KUBUSGERANGSCHIKTLID
CUBESET = KUBUSSET
CUBESETCOUNT = KUBUSSETAANTAL
CUBEVALUE = KUBUSWAARDE

##
## Databasefuncties (Database Functions)
##
DAVERAGE = DBGEMIDDELDE
DCOUNT = DBAANTAL
DCOUNTA = DBAANTALC
DGET = DBLEZEN
DMAX = DBMAX
DMIN = DBMIN
DPRODUCT = DBPRODUCT
DSTDEV = DBSTDEV
DSTDEVP = DBSTDEVP
DSUM = DBSOM
DVAR = DBVAR
DVARP = DBVARP

##
## Datum- en tijdfuncties (Date & Time Functions)
##
DATE = DATUM
DATESTRING = DATUMNOTATIE
DATEVALUE = DATUMWAARDE
DAY = DAG
DAYS = DAGEN
DAYS360 = DAGEN360
EDATE = ZELFDE.DAG
EOMONTH = LAATSTE.DAG
HOUR = UUR
ISOWEEKNUM = ISO.WEEKNUMMER
MINUTE = MINUUT
MONTH = MAAND
NETWORKDAYS = NETTO.WERKDAGEN
NETWORKDAYS.INTL = NETWERKDAGEN.INTL
NOW = NU
SECOND = SECONDE
THAIDAYOFWEEK = THAIS.WEEKDAG
THAIMONTHOFYEAR = THAIS.MAAND.VAN.JAAR
THAIYEAR = THAIS.JAAR
TIME = TIJD
TIMEVALUE = TIJDWAARDE
TODAY = VANDAAG
WEEKDAY = WEEKDAG
WEEKNUM = WEEKNUMMER
WORKDAY = WERKDAG
WORKDAY.INTL = WERKDAG.INTL
YEAR = JAAR
YEARFRAC = JAAR.DEEL

##
## Technische functies (Engineering Functions)
##
BESSELI = BESSEL.I
BESSELJ = BESSEL.J
BESSELK = BESSEL.K
BESSELY = BESSEL.Y
BIN2DEC = BIN.N.DEC
BIN2HEX = BIN.N.HEX
BIN2OCT = BIN.N.OCT
BITAND = BIT.EN
BITLSHIFT = BIT.VERSCHUIF.LINKS
BITOR = BIT.OF
BITRSHIFT = BIT.VERSCHUIF.RECHTS
BITXOR = BIT.EX.OF
COMPLEX = COMPLEX
CONVERT = CONVERTEREN
DEC2BIN = DEC.N.BIN
DEC2HEX = DEC.N.HEX
DEC2OCT = DEC.N.OCT
DELTA = DELTA
ERF = FOUTFUNCTIE
ERF.PRECISE = FOUTFUNCTIE.NAUWKEURIG
ERFC = FOUT.COMPLEMENT
ERFC.PRECISE = FOUT.COMPLEMENT.NAUWKEURIG
GESTEP = GROTER.DAN
HEX2BIN = HEX.N.BIN
HEX2DEC = HEX.N.DEC
HEX2OCT = HEX.N.OCT
IMABS = C.ABS
IMAGINARY = C.IM.DEEL
IMARGUMENT = C.ARGUMENT
IMCONJUGATE = C.TOEGEVOEGD
IMCOS = C.COS
IMCOSH = C.COSH
IMCOT = C.COT
IMCSC = C.COSEC
IMCSCH = C.COSECH
IMDIV = C.QUOTIENT
IMEXP = C.EXP
IMLN = C.LN
IMLOG10 = C.LOG10
IMLOG2 = C.LOG2
IMPOWER = C.MACHT
IMPRODUCT = C.PRODUCT
IMREAL = C.REEEL.DEEL
IMSEC = C.SEC
IMSECH = C.SECH
IMSIN = C.SIN
IMSINH = C.SINH
IMSQRT = C.WORTEL
IMSUB = C.VERSCHIL
IMSUM = C.SOM
IMTAN = C.TAN
OCT2BIN = OCT.N.BIN
OCT2DEC = OCT.N.DEC
OCT2HEX = OCT.N.HEX

##
## Financiële functies (Financial Functions)
##
ACCRINT = SAMENG.RENTE
ACCRINTM = SAMENG.RENTE.V
AMORDEGRC = AMORDEGRC
AMORLINC = AMORLINC
COUPDAYBS = COUP.DAGEN.BB
COUPDAYS = COUP.DAGEN
COUPDAYSNC = COUP.DAGEN.VV
COUPNCD = COUP.DATUM.NB
COUPNUM = COUP.AANTAL
COUPPCD = COUP.DATUM.VB
CUMIPMT = CUM.RENTE
CUMPRINC = CUM.HOOFDSOM
DB = DB
DDB = DDB
DISC = DISCONTO
DOLLARDE = EURO.DE
DOLLARFR = EURO.BR
DURATION = DUUR
EFFECT = EFFECT.RENTE
FV = TW
FVSCHEDULE = TOEK.WAARDE2
INTRATE = RENTEPERCENTAGE
IPMT = IBET
IRR = IR
ISPMT = ISBET
MDURATION = AANG.DUUR
MIRR = GIR
NOMINAL = NOMINALE.RENTE
NPER = NPER
NPV = NHW
ODDFPRICE = AFW.ET.PRIJS
ODDFYIELD = AFW.ET.REND
ODDLPRICE = AFW.LT.PRIJS
ODDLYIELD = AFW.LT.REND
PDURATION = PDUUR
PMT = BET
PPMT = PBET
PRICE = PRIJS.NOM
PRICEDISC = PRIJS.DISCONTO
PRICEMAT = PRIJS.VERVALDAG
PV = HW
RATE = RENTE
RECEIVED = OPBRENGST
RRI = RRI
SLN = LIN.AFSCHR
SYD = SYD
TBILLEQ = SCHATK.OBL
TBILLPRICE = SCHATK.PRIJS
TBILLYIELD = SCHATK.REND
VDB = VDB
XIRR = IR.SCHEMA
XNPV = NHW2
YIELD = RENDEMENT
YIELDDISC = REND.DISCONTO
YIELDMAT = REND.VERVAL

##
## Informatiefuncties (Information Functions)
##
CELL = CEL
ERROR.TYPE = TYPE.FOUT
INFO = INFO
ISBLANK = ISLEEG
ISERR = ISFOUT2
ISERROR = ISFOUT
ISEVEN = IS.EVEN
ISFORMULA = ISFORMULE
ISLOGICAL = ISLOGISCH
ISNA = ISNB
ISNONTEXT = ISGEENTEKST
ISNUMBER = ISGETAL
ISODD = IS.ONEVEN
ISREF = ISVERWIJZING
ISTEXT = ISTEKST
N = N
NA = NB
SHEET = BLAD
SHEETS = BLADEN
TYPE = TYPE

##
## Logische functies (Logical Functions)
##
AND = EN
FALSE = ONWAAR
IF = ALS
IFERROR = ALS.FOUT
IFNA = ALS.NB
IFS = ALS.VOORWAARDEN
NOT = NIET
OR = OF
SWITCH = SCHAKELEN
TRUE = WAAR
XOR = EX.OF

##
## Zoek- en verwijzingsfuncties (Lookup & Reference Functions)
##
ADDRESS = ADRES
AREAS = BEREIKEN
CHOOSE = KIEZEN
COLUMN = KOLOM
COLUMNS = KOLOMMEN
FORMULATEXT = FORMULETEKST
GETPIVOTDATA = DRAAITABEL.OPHALEN
HLOOKUP = HORIZ.ZOEKEN
HYPERLINK = HYPERLINK
INDEX = INDEX
INDIRECT = INDIRECT
LOOKUP = ZOEKEN
MATCH = VERGELIJKEN
OFFSET = VERSCHUIVING
ROW = RIJ
ROWS = RIJEN
RTD = RTG
TRANSPOSE = TRANSPONEREN
VLOOKUP = VERT.ZOEKEN
*RC = RK

##
## Wiskundige en trigonometrische functies (Math & Trig Functions)
##
ABS = ABS
ACOS = BOOGCOS
ACOSH = BOOGCOSH
ACOT = BOOGCOT
ACOTH = BOOGCOTH
AGGREGATE = AGGREGAAT
ARABIC = ARABISCH
ASIN = BOOGSIN
ASINH = BOOGSINH
ATAN = BOOGTAN
ATAN2 = BOOGTAN2
ATANH = BOOGTANH
BASE = BASIS
CEILING.MATH = AFRONDEN.BOVEN.WISK
CEILING.PRECISE = AFRONDEN.BOVEN.NAUWKEURIG
COMBIN = COMBINATIES
COMBINA = COMBIN.A
COS = COS
COSH = COSH
COT = COT
COTH = COTH
CSC = COSEC
CSCH = COSECH
DECIMAL = DECIMAAL
DEGREES = GRADEN
ECMA.CEILING = ECMA.AFRONDEN.BOVEN
EVEN = EVEN
EXP = EXP
FACT = FACULTEIT
FACTDOUBLE = DUBBELE.FACULTEIT
FLOOR.MATH = AFRONDEN.BENEDEN.WISK
FLOOR.PRECISE = AFRONDEN.BENEDEN.NAUWKEURIG
GCD = GGD
INT = INTEGER
ISO.CEILING = ISO.AFRONDEN.BOVEN
LCM = KGV
LN = LN
LOG = LOG
LOG10 = LOG10
MDETERM = DETERMINANTMAT
MINVERSE = INVERSEMAT
MMULT = PRODUCTMAT
MOD = REST
MROUND = AFRONDEN.N.VEELVOUD
MULTINOMIAL = MULTINOMIAAL
MUNIT = EENHEIDMAT
ODD = ONEVEN
PI = PI
POWER = MACHT
PRODUCT = PRODUCT
QUOTIENT = QUOTIENT
RADIANS = RADIALEN
RAND = ASELECT
RANDBETWEEN = ASELECTTUSSEN
ROMAN = ROMEINS
ROUND = AFRONDEN
ROUNDBAHTDOWN = BAHT.AFR.NAAR.BENEDEN
ROUNDBAHTUP = BAHT.AFR.NAAR.BOVEN
ROUNDDOWN = AFRONDEN.NAAR.BENEDEN
ROUNDUP = AFRONDEN.NAAR.BOVEN
SEC = SEC
SECH = SECH
SERIESSUM = SOM.MACHTREEKS
SIGN = POS.NEG
SIN = SIN
SINH = SINH
SQRT = WORTEL
SQRTPI = WORTEL.PI
SUBTOTAL = SUBTOTAAL
SUM = SOM
SUMIF = SOM.ALS
SUMIFS = SOMMEN.ALS
SUMPRODUCT = SOMPRODUCT
SUMSQ = KWADRATENSOM
SUMX2MY2 = SOM.X2MINY2
SUMX2PY2 = SOM.X2PLUSY2
SUMXMY2 = SOM.XMINY.2
TAN = TAN
TANH = TANH
TRUNC = GEHEEL

##
## Statistische functies (Statistical Functions)
##
AVEDEV = GEM.DEVIATIE
AVERAGE = GEMIDDELDE
AVERAGEA = GEMIDDELDEA
AVERAGEIF = GEMIDDELDE.ALS
AVERAGEIFS = GEMIDDELDEN.ALS
BETA.DIST = BETA.VERD
BETA.INV = BETA.INV
BINOM.DIST = BINOM.VERD
BINOM.DIST.RANGE = BINOM.VERD.BEREIK
BINOM.INV = BINOMIALE.INV
CHISQ.DIST = CHIKW.VERD
CHISQ.DIST.RT = CHIKW.VERD.RECHTS
CHISQ.INV = CHIKW.INV
CHISQ.INV.RT = CHIKW.INV.RECHTS
CHISQ.TEST = CHIKW.TEST
CONFIDENCE.NORM = VERTROUWELIJKHEID.NORM
CONFIDENCE.T = VERTROUWELIJKHEID.T
CORREL = CORRELATIE
COUNT = AANTAL
COUNTA = AANTALARG
COUNTBLANK = AANTAL.LEGE.CELLEN
COUNTIF = AANTAL.ALS
COUNTIFS = AANTALLEN.ALS
COVARIANCE.P = COVARIANTIE.P
COVARIANCE.S = COVARIANTIE.S
DEVSQ = DEV.KWAD
EXPON.DIST = EXPON.VERD.N
F.DIST = F.VERD
F.DIST.RT = F.VERD.RECHTS
F.INV = F.INV
F.INV.RT = F.INV.RECHTS
F.TEST = F.TEST
FISHER = FISHER
FISHERINV = FISHER.INV
FORECAST.ETS = VOORSPELLEN.ETS
FORECAST.ETS.CONFINT = VOORSPELLEN.ETS.CONFINT
FORECAST.ETS.SEASONALITY = VOORSPELLEN.ETS.SEASONALITY
FORECAST.ETS.STAT = FORECAST.ETS.STAT
FORECAST.LINEAR = VOORSPELLEN.LINEAR
FREQUENCY = INTERVAL
GAMMA = GAMMA
GAMMA.DIST = GAMMA.VERD.N
GAMMA.INV = GAMMA.INV.N
GAMMALN = GAMMA.LN
GAMMALN.PRECISE = GAMMA.LN.NAUWKEURIG
GAUSS = GAUSS
GEOMEAN = MEETK.GEM
GROWTH = GROEI
HARMEAN = HARM.GEM
HYPGEOM.DIST = HYPGEOM.VERD
INTERCEPT = SNIJPUNT
KURT = KURTOSIS
LARGE = GROOTSTE
LINEST = LIJNSCH
LOGEST = LOGSCH
LOGNORM.DIST = LOGNORM.VERD
LOGNORM.INV = LOGNORM.INV
MAX = MAX
MAXA = MAXA
MAXIFS = MAX.ALS.VOORWAARDEN
MEDIAN = MEDIAAN
MIN = MIN
MINA = MINA
MINIFS = MIN.ALS.VOORWAARDEN
MODE.MULT = MODUS.MEERV
MODE.SNGL = MODUS.ENKELV
NEGBINOM.DIST = NEGBINOM.VERD
NORM.DIST = NORM.VERD.N
NORM.INV = NORM.INV.N
NORM.S.DIST = NORM.S.VERD
NORM.S.INV = NORM.S.INV
PEARSON = PEARSON
PERCENTILE.EXC = PERCENTIEL.EXC
PERCENTILE.INC = PERCENTIEL.INC
PERCENTRANK.EXC = PROCENTRANG.EXC
PERCENTRANK.INC = PROCENTRANG.INC
PERMUT = PERMUTATIES
PERMUTATIONA = PERMUTATIE.A
PHI = PHI
POISSON.DIST = POISSON.VERD
PROB = KANS
QUARTILE.EXC = KWARTIEL.EXC
QUARTILE.INC = KWARTIEL.INC
RANK.AVG = RANG.GEMIDDELDE
RANK.EQ = RANG.GELIJK
RSQ = R.KWADRAAT
SKEW = SCHEEFHEID
SKEW.P = SCHEEFHEID.P
SLOPE = RICHTING
SMALL = KLEINSTE
STANDARDIZE = NORMALISEREN
STDEV.P = STDEV.P
STDEV.S = STDEV.S
STDEVA = STDEVA
STDEVPA = STDEVPA
STEYX = STAND.FOUT.YX
T.DIST = T.DIST
T.DIST.2T = T.VERD.2T
T.DIST.RT = T.VERD.RECHTS
T.INV = T.INV
T.INV.2T = T.INV.2T
T.TEST = T.TEST
TREND = TREND
TRIMMEAN = GETRIMD.GEM
VAR.P = VAR.P
VAR.S = VAR.S
VARA = VARA
VARPA = VARPA
WEIBULL.DIST = WEIBULL.VERD
Z.TEST = Z.TEST

##
## Tekstfuncties (Text Functions)
##
BAHTTEXT = BAHT.TEKST
CHAR = TEKEN
CLEAN = WISSEN.CONTROL
CODE = CODE
CONCAT = TEKST.SAMENV
DOLLAR = EURO
EXACT = GELIJK
FIND = VIND.ALLES
FIXED = VAST
ISTHAIDIGIT = IS.THAIS.CIJFER
LEFT = LINKS
LEN = LENGTE
LOWER = KLEINE.LETTERS
MID = DEEL
NUMBERSTRING = GETALNOTATIE
NUMBERVALUE = NUMERIEKE.WAARDE
PHONETIC = FONETISCH
PROPER = BEGINLETTERS
REPLACE = VERVANGEN
REPT = HERHALING
RIGHT = RECHTS
SEARCH = VIND.SPEC
SUBSTITUTE = SUBSTITUEREN
T = T
TEXT = TEKST
TEXTJOIN = TEKST.COMBINEREN
THAIDIGIT = THAIS.CIJFER
THAINUMSOUND = THAIS.GETAL.GELUID
THAINUMSTRING = THAIS.GETAL.REEKS
THAISTRINGLENGTH = THAIS.REEKS.LENGTE
TRIM = SPATIES.WISSEN
UNICHAR = UNITEKEN
UNICODE = UNICODE
UPPER = HOOFDLETTERS
VALUE = WAARDE

##
## Webfuncties (Web Functions)
##
ENCODEURL = URL.CODEREN
FILTERXML = XML.FILTEREN
WEBSERVICE = WEBSERVICE

##
## Compatibiliteitsfuncties (Compatibility Functions)
##
BETADIST = BETAVERD
BETAINV = BETAINV
BINOMDIST = BINOMIALE.VERD
CEILING = AFRONDEN.BOVEN
CHIDIST = CHI.KWADRAAT
CHIINV = CHI.KWADRAAT.INV
CHITEST = CHI.TOETS
CONCATENATE = TEKST.SAMENVOEGEN
CONFIDENCE = BETROUWBAARHEID
COVAR = COVARIANTIE
CRITBINOM = CRIT.BINOM
EXPONDIST = EXPON.VERD
FDIST = F.VERDELING
FINV = F.INVERSE
FLOOR = AFRONDEN.BENEDEN
FORECAST = VOORSPELLEN
FTEST = F.TOETS
GAMMADIST = GAMMA.VERD
GAMMAINV = GAMMA.INV
HYPGEOMDIST = HYPERGEO.VERD
LOGINV = LOG.NORM.INV
LOGNORMDIST = LOG.NORM.VERD
MODE = MODUS
NEGBINOMDIST = NEG.BINOM.VERD
NORMDIST = NORM.VERD
NORMINV = NORM.INV
NORMSDIST = STAND.NORM.VERD
NORMSINV = STAND.NORM.INV
PERCENTILE = PERCENTIEL
PERCENTRANK = PERCENT.RANG
POISSON = POISSON
QUARTILE = KWARTIEL
RANK = RANG
STDEV = STDEV
STDEVP = STDEVP
TDIST = T.VERD
TINV = TINV
TTEST = T.TOETS
VAR = VAR
VARP = VARP
WEIBULL = WEIBULL
ZTEST = Z.TOETS
