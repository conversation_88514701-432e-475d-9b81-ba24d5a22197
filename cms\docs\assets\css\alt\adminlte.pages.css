/*!
 *   AdminLTE v3.0.5
 *     Only Pages
 *   Author: Colorlib
 *   Website: AdminLTE.io <http://adminlte.io>
 *   License: Open source - MIT <http://opensource.org/licenses/MIT>
 */
.close, .mailbox-attachment-close {
  float: right;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #ffffff;
  opacity: .5;
}

.close:hover, .mailbox-attachment-close:hover {
  color: #000;
  text-decoration: none;
}

.close:not(:disabled):not(.disabled):hover, .mailbox-attachment-close:not(:disabled):not(.disabled):hover, .close:not(:disabled):not(.disabled):focus, .mailbox-attachment-close:not(:disabled):not(.disabled):focus {
  opacity: .75;
}

button.close, button.mailbox-attachment-close {
  padding: 0;
  background-color: transparent;
  border: 0;
  appearance: none;
}

a.close.disabled, a.disabled.mailbox-attachment-close {
  pointer-events: none;
}

.mailbox-messages > .table {
  margin: 0;
}

.mailbox-controls {
  padding: 5px;
}

.mailbox-controls.with-border {
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.mailbox-read-info {
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  padding: 10px;
}

.mailbox-read-info h3 {
  font-size: 20px;
  margin: 0;
}

.mailbox-read-info h5 {
  margin: 0;
  padding: 5px 0 0;
}

.mailbox-read-time {
  color: #999;
  font-size: 13px;
}

.mailbox-read-message {
  padding: 10px;
}

.mailbox-attachments {
  padding-left: 0;
  list-style: none;
}

.mailbox-attachments li {
  border: 1px solid #eee;
  float: left;
  margin-bottom: 10px;
  margin-right: 10px;
  width: 200px;
}

.mailbox-attachment-name {
  color: #666;
  font-weight: bold;
}

.mailbox-attachment-icon,
.mailbox-attachment-info,
.mailbox-attachment-size {
  display: block;
}

.mailbox-attachment-info {
  background: #f8f9fa;
  padding: 10px;
}

.mailbox-attachment-size {
  color: #999;
  font-size: 12px;
}

.mailbox-attachment-size > span {
  display: inline-block;
  padding-top: 0.75rem;
}

.mailbox-attachment-icon {
  color: #666;
  font-size: 65px;
  max-height: 132.5px;
  padding: 20px 10px;
  text-align: center;
}

.mailbox-attachment-icon.has-img {
  padding: 0;
}

.mailbox-attachment-icon.has-img > img {
  height: auto;
  max-width: 100%;
}

.lockscreen {
  background: #e9ecef;
}

.lockscreen .lockscreen-name {
  font-weight: 600;
  text-align: center;
}

.lockscreen-logo {
  font-size: 35px;
  font-weight: 300;
  margin-bottom: 25px;
  text-align: center;
}

.lockscreen-logo a {
  color: #495057;
}

.lockscreen-wrapper {
  margin: 0 auto;
  margin-top: 10%;
  max-width: 400px;
}

.lockscreen-item {
  border-radius: 4px;
  background: #ffffff;
  margin: 10px auto 30px;
  padding: 0;
  position: relative;
  width: 290px;
}

.lockscreen-image {
  border-radius: 50%;
  background: #ffffff;
  left: -10px;
  padding: 5px;
  position: absolute;
  top: -25px;
  z-index: 10;
}

.lockscreen-image > img {
  border-radius: 50%;
  height: 70px;
  width: 70px;
}

.lockscreen-credentials {
  margin-left: 70px;
}

.lockscreen-credentials .form-control {
  border: 0;
}

.lockscreen-credentials .btn {
  background-color: #ffffff;
  border: 0;
  padding: 0 10px;
}

.lockscreen-footer {
  margin-top: 10px;
}

.login-logo,
.register-logo {
  font-size: 2.1rem;
  font-weight: 300;
  margin-bottom: .9rem;
  text-align: center;
}

.login-logo a,
.register-logo a {
  color: #495057;
}

.login-page,
.register-page {
  align-items: center;
  background: #e9ecef;
  display: flex;
  flex-direction: column;
  height: 100vh;
  justify-content: center;
}

.login-box,
.register-box {
  width: 360px;
}

@media (max-width: 576px) {
  .login-box,
  .register-box {
    margin-top: .5rem;
    width: 90%;
  }
}

.login-card-body,
.register-card-body {
  background: #ffffff;
  border-top: 0;
  color: #666;
  padding: 20px;
}

.login-card-body .input-group .form-control,
.register-card-body .input-group .form-control {
  border-right: 0;
}

.login-card-body .input-group .form-control:focus,
.register-card-body .input-group .form-control:focus {
  box-shadow: none;
}

.login-card-body .input-group .form-control:focus ~ .input-group-append .input-group-text,
.register-card-body .input-group .form-control:focus ~ .input-group-append .input-group-text {
  border-color: #80bdff;
}

.login-card-body .input-group .form-control.is-valid:focus,
.register-card-body .input-group .form-control.is-valid:focus {
  box-shadow: none;
}

.login-card-body .input-group .form-control.is-valid ~ .input-group-append .input-group-text,
.register-card-body .input-group .form-control.is-valid ~ .input-group-append .input-group-text {
  border-color: #28a745;
}

.login-card-body .input-group .form-control.is-invalid:focus,
.register-card-body .input-group .form-control.is-invalid:focus {
  box-shadow: none;
}

.login-card-body .input-group .form-control.is-invalid ~ .input-group-append .input-group-text,
.register-card-body .input-group .form-control.is-invalid ~ .input-group-append .input-group-text {
  border-color: #dc3545;
}

.login-card-body .input-group .input-group-text,
.register-card-body .input-group .input-group-text {
  background-color: transparent;
  border-bottom-right-radius: 0.25rem;
  border-left: 0;
  border-top-right-radius: 0.25rem;
  color: #777;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.login-box-msg,
.register-box-msg {
  margin: 0;
  padding: 0 20px 20px;
  text-align: center;
}

.social-auth-links {
  margin: 10px 0;
}

.error-page {
  margin: 20px auto 0;
  width: 600px;
}

@media (max-width: 767.98px) {
  .error-page {
    width: 100%;
  }
}

.error-page > .headline {
  float: left;
  font-size: 100px;
  font-weight: 300;
}

@media (max-width: 767.98px) {
  .error-page > .headline {
    float: none;
    text-align: center;
  }
}

.error-page > .error-content {
  display: block;
  margin-left: 190px;
}

@media (max-width: 767.98px) {
  .error-page > .error-content {
    margin-left: 0;
  }
}

.error-page > .error-content > h3 {
  font-size: 25px;
  font-weight: 300;
}

@media (max-width: 767.98px) {
  .error-page > .error-content > h3 {
    text-align: center;
  }
}

.invoice {
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.125);
  position: relative;
}

.invoice-title {
  margin-top: 0;
}

.profile-user-img {
  border: 3px solid #adb5bd;
  margin: 0 auto;
  padding: 3px;
  width: 100px;
}

.profile-username {
  font-size: 21px;
  margin-top: 5px;
}

.post {
  border-bottom: 1px solid #adb5bd;
  color: #666;
  margin-bottom: 15px;
  padding-bottom: 15px;
}

.post:last-of-type {
  border-bottom: 0;
  margin-bottom: 0;
  padding-bottom: 0;
}

.post .user-block {
  margin-bottom: 15px;
  width: 100%;
}

.post .row {
  width: 100%;
}

.product-image {
  max-width: 100%;
  height: auto;
  width: 100%;
}

.product-image-thumbs {
  align-items: stretch;
  display: flex;
  margin-top: 2rem;
}

.product-image-thumb {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.075);
  border-radius: 0.25rem;
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  display: flex;
  margin-right: 1rem;
  max-width: 7rem;
  padding: 0.5rem;
}

.product-image-thumb img {
  max-width: 100%;
  height: auto;
  align-self: center;
}

.product-image-thumb:hover {
  opacity: 0.5;
}

.product-share a {
  margin-right: .5rem;
}

.projects td {
  vertical-align: middle;
}

.projects .list-inline {
  margin-bottom: 0;
}

.projects img.table-avatar,
.projects .table-avatar img {
  border-radius: 50%;
  display: inline;
  width: 2.5rem;
}

.projects .project-state {
  text-align: center;
}

/*# sourceMappingURL=adminlte.pages.css.map */