{"version": 3, "sources": ["..\\..\\..\\build\\scss\\AdminLTE-components.scss", "..\\..\\..\\build\\scss\\_forms.scss", "..\\..\\..\\build\\scss\\_bootstrap-variables.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_border-radius.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\vendor\\_rfs.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_gradients.scss", "..\\..\\..\\build\\scss\\mixins\\_custom-forms.scss", "..\\..\\..\\build\\scss\\_variables.scss", "..\\..\\..\\build\\scss\\_progress-bars.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_box-shadow.scss", "..\\..\\..\\build\\scss\\mixins\\_cards.scss", "..\\..\\..\\build\\scss\\_cards.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_clearfix.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_lists.scss", "..\\..\\..\\build\\scss\\_modals.scss", "..\\..\\..\\build\\scss\\_toasts.scss", "..\\..\\..\\build\\scss\\mixins\\_toasts.scss", "..\\..\\..\\build\\scss\\_buttons.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_buttons.scss", "..\\..\\..\\build\\scss\\_callout.scss", "..\\..\\..\\build\\scss\\_alerts.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_alert.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_functions.scss", "..\\..\\..\\build\\scss\\_table.scss", "..\\..\\..\\build\\scss\\_carousel.scss"], "names": [], "mappings": "AAAA;;;;;;ACIW,qBAEP,SAAU,SAEV,mCACE,cAAe,KAGjB,gCACE,iBAAkB,YAClB,OAAQ,EACR,OAAQ,QACR,UAAW,KAEX,QCsTwB,QACA,ODtTxB,SAAU,SACV,MAAO,IACP,IAAK,EAQG,gDACA,+CEzBV,cF0ByB,EAMP,0BAAA,2BAAA,2BAAA,2BAAA,iCAAA,2BAOlB,YC4XoC,oBDvXA,0CAAA,2CAAA,2CAAA,2CAAA,iDAAA,2CADL,oCAAA,qCAAA,qCAAA,qCAAA,2CAAA,qCAQ/B,YCsXoC,qBDjXA,uDAAA,wDAAA,wDAAA,wDAAA,8DAAA,wDAOlC,YC0WkC,qBDpWA,0CAAA,2CAAA,2CAAA,2CAAA,iDAAA,2CADL,oCAAA,qCAAA,qCAAA,qCAAA,2CAAA,qCAQ/B,YC0VoC,sBDrVA,uDAAA,wDAAA,wDAAA,wDAAA,8DAAA,wDAOlC,YC8UkC,sBDzUW,qDACjD,YCgJ4B,ID7I9B,kBG9BI,UAAU,IHgCZ,MC9DQ,QD+DR,QAAS,KACT,WCwUsC,ODvUtC,MAAO,KAGT,iBEzGI,cDgN0B,OEtFxB,UAtCW,QHwBf,iBCvEQ,mBDwER,MCrBc,QDsBd,QAAS,KACT,YCkI4B,IDjI5B,WAAY,MACZ,UAAW,KACX,QC8jB4B,OACA,MD9jB5B,SAAU,SACV,IAAK,KACL,QAAS,EAGE,yBAET,aCrFM,QDoFI,+BAYR,aChGI,QDiGJ,WAAY,EAAE,EAAE,ECkRkB,EAnX9B,oBDoGJ,2CACA,0CACA,QAAS,MAMM,iCAGf,cC2QkC,QD1QlC,oBAAqB,IC4Qa,wBD5QmB,MC4QnB,wBDvQ1B,0BAEV,aCvHM,QDsHI,gCASR,aC/HI,QDgIJ,WAAY,EAAE,EAAE,ECmPkB,EAnX9B,oBDmIJ,4CACA,2CACA,QAAS,MAQT,gDACA,+CACA,QAAS,MAOT,+CACA,MCvJI,QD0JJ,+CACA,8CACA,QAAS,MAMX,uDACA,MCnKM,QDkKe,+DAInB,aCtKI,QD0KN,mDACA,kDACA,QAAS,MAIc,uEIpNvB,iBJqNuB,QACrB,aAAc,QAKO,qEACrB,WAAY,EAAE,EAAE,EC2LkB,EAnX9B,oBD2LiC,mFACrC,aC5LI,QDoMJ,iDACA,aCrMI,QDwMJ,gDACA,+CACA,QAAS,MAIP,uDACA,aC/ME,QDgNF,WAAY,EAAE,EAAE,ECmKgB,EAnX9B,oBInCyC,6FAC7C,WAAW,QACX,aAAc,QAGqC,mGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,IJuBlC,oBIpByC,4FAC7C,WAAY,QAKyC,oGACrD,WAAW,QACX,aAAc,QAG6C,0GAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,IJQlC,oBILiD,mGACrD,WAAY,QAzBiC,+FAC7C,WAAW,QACX,aAAc,QAGqC,qGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,IJIjC,sBIDwC,8FAC7C,WAAY,QAKyC,sGACrD,WAAW,QACX,aAAc,QAG6C,4GAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,IJXjC,sBIcgD,qGACrD,WAAY,QAzBiC,6FAC7C,WAAW,QACX,aAAc,QAGqC,mGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,IJ8BlC,oBI3ByC,4FAC7C,WAAY,QAKyC,oGACrD,WAAW,QACX,aAAc,QAG6C,0GAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,IJelC,oBIZiD,mGACrD,WAAY,QAzBiC,0FAC7C,WAAW,QACX,aAAc,QAGqC,gGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,IJgClC,qBI7ByC,yFAC7C,WAAY,QAKyC,iGACrD,WAAW,QACX,aAAc,QAG6C,uGAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,IJiBlC,qBIdiD,gGACrD,WAAY,QAzBiC,6FAC7C,WAAW,QACX,aAAc,QAGqC,mGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,IJ6BlC,oBI1ByC,4FAC7C,WAAY,QAKyC,oGACrD,WAAW,QACX,aAAc,QAG6C,0GAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,IJclC,oBIXiD,mGACrD,WAAY,QAzBiC,4FAC7C,WAAW,QACX,aAAc,QAGqC,kGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,IJ2BlC,oBIxByC,2FAC7C,WAAY,QAKyC,mGACrD,WAAW,QACX,aAAc,QAG6C,yGAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,IJYlC,oBITiD,kGACrD,WAAY,QAzBiC,2FAC7C,WAAW,QACX,aAAc,QAGqC,iGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,IJDjC,sBIIwC,0FAC7C,WAAY,QAKyC,kGACrD,WAAW,QACX,aAAc,QAG6C,wGAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,IJhBjC,sBImBgD,iGACrD,WAAY,KAzBiC,0FAC7C,WAAW,QACX,aAAc,QAGqC,gGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,IJMjC,mBIHwC,yFAC7C,WAAY,KAKyC,iGACrD,WAAW,QACX,aAAc,QAG6C,uGAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,IJTjC,mBIYgD,gGACrD,WAAY,QAzBiC,+FAC7C,WAAW,QACX,aAAc,QAGqC,qGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,ICNhC,qBDSuC,8FAC7C,WAAY,QAKyC,sGACrD,WAAW,QACX,aAAc,QAG6C,4GAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,ICrBhC,qBDwB+C,qGACrD,WAAY,QAzBiC,0FAC7C,WAAW,QACX,aAAc,KAGqC,gGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,ICLrC,kBDQ4C,yFAC7C,WAAY,KAKyC,iGACrD,WAAW,QACX,aAAc,KAG6C,uGAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,ICpBrC,kBDuBoD,gGACrD,WAAY,QAzBiC,2FAC7C,WAAW,QACX,aAAc,QAGqC,iGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,ICHpC,qBDM2C,0FAC7C,WAAY,QAKyC,kGACrD,WAAW,QACX,aAAc,QAG6C,wGAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,IClBpC,qBDqBmD,iGACrD,WAAY,QAzBiC,0FAC7C,WAAW,QACX,aAAc,QAGqC,gGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,ICFrC,oBDK4C,yFAC7C,WAAY,QAKyC,iGACrD,WAAW,QACX,aAAc,QAG6C,uGAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,ICjBrC,oBDoBoD,gGACrD,WAAY,QAzBiC,6FAC7C,WAAW,QACX,aAAc,QAGqC,mGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,ICAlC,qBDGyC,4FAC7C,WAAY,QAKyC,oGACrD,WAAW,QACX,aAAc,QAG6C,0GAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,ICflC,qBDkBiD,mGACrD,WAAY,QAzBiC,4FAC7C,WAAW,QACX,aAAc,QAGqC,kGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,ICEnC,oBDC0C,2FAC7C,WAAY,QAKyC,mGACrD,WAAW,QACX,aAAc,QAG6C,yGAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,ICbnC,oBDgBkD,kGACrD,WAAY,QAzBiC,0FAC7C,WAAW,QACX,aAAc,QAGqC,gGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,IJuBlC,oBIpByC,yFAC7C,WAAY,QAKyC,iGACrD,WAAW,QACX,aAAc,QAG6C,uGAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,IJQlC,oBILiD,gGACrD,WAAY,QAzBiC,4FAC7C,WAAW,QACX,aAAc,QAGqC,kGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,IJwBlC,qBIrByC,2FAC7C,WAAY,QAKyC,mGACrD,WAAW,QACX,aAAc,QAG6C,yGAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,IJSlC,qBINiD,kGACrD,WAAY,QAzBiC,4FAC7C,WAAW,QACX,aAAc,QAGqC,kGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,IJyBlC,qBItByC,2FAC7C,WAAY,QAKyC,mGACrD,WAAW,QACX,aAAc,QAG6C,yGAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,IJUlC,qBIPiD,kGACrD,WAAY,QAzBiC,0FAC7C,WAAW,QACX,aAAc,QAGqC,gGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,IJ0BlC,qBIvByC,yFAC7C,WAAY,QAKyC,iGACrD,WAAW,QACX,aAAc,QAG6C,uGAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,IJWlC,qBIRiD,gGACrD,WAAY,QAzBiC,yFAC7C,WAAW,QACX,aAAc,QAGqC,+FACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,IJ2BlC,oBIxByC,wFAC7C,WAAY,QAKyC,gGACrD,WAAW,QACX,aAAc,QAG6C,sGAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,IJYlC,oBITiD,+FACrD,WAAY,QAzBiC,4FAC7C,WAAW,QACX,aAAc,QAGqC,kGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,IJ4BlC,qBIzByC,2FAC7C,WAAY,QAKyC,mGACrD,WAAW,QACX,aAAc,QAG6C,yGAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,IJalC,qBIViD,kGACrD,WAAY,QAzBiC,4FAC7C,WAAW,QACX,aAAc,QAGqC,kGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,IJ6BlC,oBI1ByC,2FAC7C,WAAY,QAKyC,mGACrD,WAAW,QACX,aAAc,QAG6C,yGAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,IJclC,oBIXiD,kGACrD,WAAY,QAzBiC,2FAC7C,WAAW,QACX,aAAc,QAGqC,iGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,IJ8BlC,oBI3ByC,0FAC7C,WAAY,QAKyC,kGACrD,WAAW,QACX,aAAc,QAG6C,wGAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,IJelC,oBIZiD,iGACrD,WAAY,QAzBiC,0FAC7C,WAAW,QACX,aAAc,QAGqC,gGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,IJ+BlC,qBI5ByC,yFAC7C,WAAY,QAKyC,iGACrD,WAAW,QACX,aAAc,QAG6C,uGAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,IJgBlC,qBIbiD,gGACrD,WAAY,QAzBiC,0FAC7C,WAAW,QACX,aAAc,QAGqC,gGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,IJgClC,qBI7ByC,yFAC7C,WAAY,QAKyC,iGACrD,WAAW,QACX,aAAc,QAG6C,uGAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,IJiBlC,qBIdiD,gGACrD,WAAY,QAzBiC,2FAC7C,WAAW,KACX,aAAc,KAGqC,iGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,IJFjC,sBIKwC,0FAC7C,WAAY,QAKyC,kGACrD,WAAW,KACX,aAAc,KAG6C,wGAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,IJjBjC,sBIoBgD,iGACrD,WAAY,KAzBiC,0FAC7C,WAAW,QACX,aAAc,QAGqC,gGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,IJIjC,sBIDwC,yFAC7C,WAAY,QAKyC,iGACrD,WAAW,QACX,aAAc,QAG6C,uGAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,IJXjC,sBIcgD,gGACrD,WAAY,QAzBiC,+FAC7C,WAAW,QACX,aAAc,QAGqC,qGACnD,WAAY,EAAE,EAAE,EAAE,IJFb,IAAO,CIEoB,EAAE,EAAE,EAAE,IJMjC,mBIHwC,8FAC7C,WAAY,KAKyC,sGACrD,WAAW,QACX,aAAc,QAG6C,4GAC3D,WAAY,EAAE,EAAE,EAAE,IJjBb,IAAO,CIiBoB,EAAE,EAAE,EAAE,IJTjC,mBIYgD,qGACrD,WAAY,QAOM,yCAElB,QAAS,EADJ,+DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IJRpC,oBIIC,2DAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,IJZpC,oBIIC,oDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IJhBpC,oBIGc,yDAkBlB,iBJrBI,QIoBiB,gEAInB,iBAAkB,QArBF,qDA0BlB,iBJ7BI,QI4Ba,4DAIf,iBAAkB,QA7BF,8CAkClB,iBJrCI,QIoCM,qDAIR,iBAAkB,QArCA,2CAEpB,QAAS,EADJ,iEAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IJ3BnC,sBIuBA,6DAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,IJ/BnC,sBIuBA,sDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IJnCnC,sBIsBe,2DAkBpB,iBJxCK,QIuCgB,kEAInB,iBAAkB,QArBA,uDA0BpB,iBJhDK,QI+CY,8DAIf,iBAAkB,QA7BA,gDAkCpB,iBJxDK,QIuDK,uDAIR,iBAAkB,QArCF,yCAElB,QAAS,EADJ,+DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IJDpC,oBIHC,2DAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,IJLpC,oBIHC,oDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IJTpC,oBIJc,yDAkBlB,iBJdI,QIaiB,gEAInB,iBAAkB,QArBF,qDA0BlB,iBJtBI,QIqBa,4DAIf,iBAAkB,QA7BF,8CAkClB,iBJ9BI,QI6BM,qDAIR,iBAAkB,QArCL,sCAEf,QAAS,EADJ,4DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IJCpC,qBILC,wDAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,IJHpC,qBILC,iDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IJPpC,qBINW,sDAkBf,iBJZI,QIWiB,6DAInB,iBAAkB,QArBL,kDA0Bf,iBJpBI,QImBa,yDAIf,iBAAkB,QA7BL,2CAkCf,iBJ5BI,QI2BM,kDAIR,iBAAkB,QArCF,yCAElB,QAAS,EADJ,+DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IJFpC,oBIFC,2DAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,IJNpC,oBIFC,oDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IJVpC,oBIHc,yDAkBlB,iBJfI,QIciB,gEAInB,iBAAkB,QArBF,qDA0BlB,iBJvBI,QIsBa,4DAIf,iBAAkB,QA7BF,8CAkClB,iBJ/BI,QI8BM,qDAIR,iBAAkB,QArCH,wCAEjB,QAAS,EADJ,8DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IJJpC,oBIAC,0DAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,IJRpC,oBIAC,mDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IJZpC,oBIDa,wDAkBjB,iBJjBI,QIgBiB,+DAInB,iBAAkB,QArBH,oDA0BjB,iBJzBI,QIwBa,2DAIf,iBAAkB,QA7BH,6CAkCjB,iBJjCI,QIgCM,oDAIR,iBAAkB,QArCJ,uCAEhB,QAAS,EADJ,6DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IJhCnC,sBI4BA,yDAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,IJpCnC,sBI4BA,kDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IJxCnC,sBI2BW,uDAkBhB,iBJ7CK,QI4CgB,8DAInB,iBAAkB,KArBJ,mDA0BhB,iBJrDK,QIoDY,0DAIf,iBAAkB,KA7BJ,4CAkChB,iBJ7DK,QI4DK,mDAIR,iBAAkB,KArCL,sCAEf,QAAS,EADJ,4DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IJzBnC,mBIqBA,wDAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,IJ7BnC,mBIqBA,iDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IJjCnC,mBIoBU,sDAkBf,iBJtCK,QIqCgB,6DAInB,iBAAkB,QArBL,kDA0Bf,iBJ9CK,QI6CY,yDAIf,iBAAkB,QA7BL,2CAkCf,iBJtDK,QIqDK,kDAIR,iBAAkB,QArCA,2CAEpB,QAAS,EADJ,iEAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,ICrClC,qBDiCD,6DAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,ICzClC,qBDiCD,sDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IC7ClC,qBDgCc,2DAkBpB,iBClDM,QDiDe,kEAInB,iBAAkB,QArBA,uDA0BpB,iBC1DM,QDyDW,8DAIf,iBAAkB,QA7BA,gDAkCpB,iBClEM,QDiEI,uDAIR,iBAAkB,QArCL,sCAEf,QAAS,EADJ,4DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,ICpCvC,kBDgCI,wDAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,ICxCvC,kBDgCI,iDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IC5CvC,kBD+Bc,sDAkBf,iBCjDC,QDgDoB,6DAInB,iBAAkB,QArBL,kDA0Bf,iBCzDC,QDwDgB,yDAIf,iBAAkB,QA7BL,2CAkCf,iBCjEC,QDgES,kDAIR,iBAAkB,QArCJ,uCAEhB,QAAS,EADJ,6DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IClCtC,qBD8BG,yDAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,ICtCtC,qBD8BG,kDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IC1CtC,qBD6Bc,uDAkBhB,iBC/CE,QD8CmB,8DAInB,iBAAkB,QArBJ,mDA0BhB,iBCvDE,QDsDe,0DAIf,iBAAkB,QA7BJ,4CAkChB,iBC/DE,QD8DQ,mDAIR,iBAAkB,QArCL,sCAEf,QAAS,EADJ,4DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,ICjCvC,oBD6BI,wDAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,ICrCvC,oBD6BI,iDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,ICzCvC,oBD4Bc,sDAkBf,iBC9CC,QD6CoB,6DAInB,iBAAkB,QArBL,kDA0Bf,iBCtDC,QDqDgB,yDAIf,iBAAkB,QA7BL,2CAkCf,iBC9DC,QD6DS,kDAIR,iBAAkB,QArCF,yCAElB,QAAS,EADJ,+DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IC/BpC,qBD2BC,2DAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,ICnCpC,qBD2BC,oDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,ICvCpC,qBD0Bc,yDAkBlB,iBC5CI,QD2CiB,gEAInB,iBAAkB,QArBF,qDA0BlB,iBCpDI,QDmDa,4DAIf,iBAAkB,QA7BF,8CAkClB,iBC5DI,QD2DM,qDAIR,iBAAkB,QArCH,wCAEjB,QAAS,EADJ,8DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IC7BrC,oBDyBE,0DAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,ICjCrC,oBDyBE,mDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,ICrCrC,oBDwBc,wDAkBjB,iBC1CG,QDyCkB,+DAInB,iBAAkB,QArBH,oDA0BjB,iBClDG,QDiDc,2DAIf,iBAAkB,QA7BH,6CAkCjB,iBC1DG,QDyDO,oDAIR,iBAAkB,QArCL,sCAEf,QAAS,EADJ,4DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IJRpC,oBIIC,wDAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,IJZpC,oBIIC,iDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IJhBpC,oBIGW,sDAkBf,iBJrBI,QIoBiB,6DAInB,iBAAkB,QArBL,kDA0Bf,iBJ7BI,QI4Ba,yDAIf,iBAAkB,QA7BL,2CAkCf,iBJrCI,QIoCM,kDAIR,iBAAkB,QArCH,wCAEjB,QAAS,EADJ,8DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IJPpC,qBIGC,0DAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,IJXpC,qBIGC,mDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IJfpC,qBIEa,wDAkBjB,iBJpBI,QImBiB,+DAInB,iBAAkB,QArBH,oDA0BjB,iBJ5BI,QI2Ba,2DAIf,iBAAkB,QA7BH,6CAkCjB,iBJpCI,QImCM,oDAIR,iBAAkB,QArCH,wCAEjB,QAAS,EADJ,8DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IJNpC,qBIEC,0DAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,IJVpC,qBIEC,mDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IJdpC,qBICa,wDAkBjB,iBJnBI,QIkBiB,+DAInB,iBAAkB,QArBH,oDA0BjB,iBJ3BI,QI0Ba,2DAIf,iBAAkB,QA7BH,6CAkCjB,iBJnCI,QIkCM,oDAIR,iBAAkB,QArCL,sCAEf,QAAS,EADJ,4DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IJLpC,qBICC,wDAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,IJTpC,qBICC,iDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IJbpC,qBIAW,sDAkBf,iBJlBI,QIiBiB,6DAInB,iBAAkB,QArBL,kDA0Bf,iBJ1BI,QIyBa,yDAIf,iBAAkB,QA7BL,2CAkCf,iBJlCI,QIiCM,kDAIR,iBAAkB,QArCN,qCAEd,QAAS,EADJ,2DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IJJpC,oBIAC,uDAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,IJRpC,oBIAC,gDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IJZpC,oBIDU,qDAkBd,iBJjBI,QIgBiB,4DAInB,iBAAkB,QArBN,iDA0Bd,iBJzBI,QIwBa,wDAIf,iBAAkB,QA7BN,0CAkCd,iBJjCI,QIgCM,iDAIR,iBAAkB,QArCH,wCAEjB,QAAS,EADJ,8DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IJHpC,qBIDC,0DAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,IJPpC,qBIDC,mDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IJXpC,qBIFa,wDAkBjB,iBJhBI,QIeiB,+DAInB,iBAAkB,QArBH,oDA0BjB,iBJxBI,QIuBa,2DAIf,iBAAkB,QA7BH,6CAkCjB,iBJhCI,QI+BM,oDAIR,iBAAkB,QArCH,wCAEjB,QAAS,EADJ,8DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IJFpC,oBIFC,0DAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,IJNpC,oBIFC,mDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IJVpC,oBIHa,wDAkBjB,iBJfI,QIciB,+DAInB,iBAAkB,QArBH,oDA0BjB,iBJvBI,QIsBa,2DAIf,iBAAkB,QA7BH,6CAkCjB,iBJ/BI,QI8BM,oDAIR,iBAAkB,QArCJ,uCAEhB,QAAS,EADJ,6DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IJDpC,oBIHC,yDAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,IJLpC,oBIHC,kDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IJTpC,oBIJY,uDAkBhB,iBJdI,QIaiB,8DAInB,iBAAkB,QArBJ,mDA0BhB,iBJtBI,QIqBa,0DAIf,iBAAkB,QA7BJ,4CAkChB,iBJ9BI,QI6BM,mDAIR,iBAAkB,QArCL,sCAEf,QAAS,EADJ,4DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IJApC,qBIJC,wDAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,IJJpC,qBIJC,iDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IJRpC,qBILW,sDAkBf,iBJbI,QIYiB,6DAInB,iBAAkB,QArBL,kDA0Bf,iBJrBI,QIoBa,yDAIf,iBAAkB,QA7BL,2CAkCf,iBJ7BI,QI4BM,kDAIR,iBAAkB,QArCL,sCAEf,QAAS,EADJ,4DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IJCpC,qBILC,wDAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,IJHpC,qBILC,iDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IJPpC,qBINW,sDAkBf,iBJZI,QIWiB,6DAInB,iBAAkB,QArBL,kDA0Bf,iBJpBI,QImBa,yDAIf,iBAAkB,QA7BL,2CAkCf,iBJ5BI,QI2BM,kDAIR,iBAAkB,QArCJ,uCAEhB,QAAS,EADJ,6DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IJjCnC,sBI6BA,yDAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,IJrCnC,sBI6BA,kDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IJzCnC,sBI4BW,uDAkBhB,iBJ9CK,KI6CgB,8DAInB,iBAAkB,KArBJ,mDA0BhB,iBJtDK,KIqDY,0DAIf,iBAAkB,KA7BJ,4CAkChB,iBJ9DK,KI6DK,mDAIR,iBAAkB,KArCL,sCAEf,QAAS,EADJ,4DAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IJ3BnC,sBIuBA,wDAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,IJ/BnC,sBIuBA,iDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IJnCnC,sBIsBU,sDAkBf,iBJxCK,QIuCgB,6DAInB,iBAAkB,QArBL,kDA0Bf,iBJhDK,QI+CY,yDAIf,iBAAkB,QA7BL,2CAkCf,iBJxDK,QIuDK,kDAIR,iBAAkB,QArCA,2CAEpB,QAAS,EADJ,iEAIH,WAAY,EAAE,EAAE,EAAE,IJjCf,IAAO,CIiCsB,EAAE,EAAE,EAAE,IJzBnC,mBIqBA,6DAQH,WAAY,EAAE,EAAE,EAAE,IJrCf,IAAO,CIqCsB,EAAE,EAAE,EAAE,IJ7BnC,mBIqBA,sDAYH,WAAY,EAAE,EAAE,EAAE,IJzCf,IAAO,CIyCsB,EAAE,EAAE,EAAE,IJjCnC,mBIoBe,2DAkBpB,iBJtCK,QIqCgB,kEAInB,iBAAkB,QArBA,uDA0BpB,iBJ9CK,QI6CY,8DAIf,iBAAkB,QA7BA,gDAkCpB,iBJtDK,QIqDK,uDAIR,iBAAkB,QEvE1B,UCWM,WDVgB,KLDlB,cIyJyB,ICzJpB,mBAML,QAAS,aACT,OAAQ,MACR,aAAc,KACd,SAAU,SACV,MAAO,KAEL,iCACA,OAAQ,EACR,SAAU,SACV,MAAO,KAVD,+BAAA,sBAgBN,MAAO,KAhBD,+BAAA,sBAqBN,MAAO,KArBD,gCAAA,uBA0BN,MAAO,IAKb,gBACE,cNoFS,MMhFX,aACE,OAAQ,KAGV,aACE,OAAQ,IAGV,cACE,OAAQ,IAMN,uBACE,OAAQ,EEvDN,8CACA,iBR4BE,QQ7BF,8CAIA,gDACE,MRDC,KQIF,uDACC,MR6EM,QQxFD,2BAiBT,WAAY,IAAI,MRcZ,QQTD,qDAEG,WAAY,IAAI,MRfjB,QQaF,sDAMG,WAAY,IAAI,MRGlB,QQON,+BAAA,sBAAA,2CACE,MRjCK,qBQgCE,qCAAA,4BAAA,iDAIL,MRpCG,KQ4CE,qEACA,qEADA,4DACA,4DACL,OAAQ,KAIE,8EACC,+EACE,iFACA,iFAJc,+FACjB,qEACC,sEACE,wEACA,wEAJc,sFAK3B,WAAY,QACZ,MRvDG,KQ0DS,kFAAA,yEACZ,oBR3DG,KQ8DG,2EACO,iFADP,kEACO,wEACb,WAAY,QACZ,MRjEG,KQJH,gDACA,iBRSG,QQVH,gDAIA,kDACE,MRDC,KQIF,yDACC,MR6EM,QQxFC,6BAiBX,WAAY,IAAI,MRLX,QQUF,uDAEG,WAAY,IAAI,MRfjB,QQaF,wDAMG,WAAY,IAAI,MRhBjB,QQ0BP,iCAAA,wBAAA,6CACE,MRjCK,qBQgCE,uCAAA,8BAAA,mDAIL,MRpCG,KQ4CE,uEACA,uEADA,8DACA,8DACL,OAAQ,KAIE,gFACC,iFACE,mFACA,mFAJc,iGACjB,uEACC,wEACE,0EACA,0EAJc,wFAK3B,WAAY,QACZ,MRvDG,KQ0DS,oFAAA,2EACZ,oBR3DG,KQ8DG,6EACO,mFADP,oEACO,0EACb,WAAY,QACZ,MRjEG,KQJH,8CACA,iBRmCE,QQpCF,8CAIA,gDACE,MRDC,KQIF,uDACC,MR6EM,QQxFD,2BAiBT,WAAY,IAAI,MRqBZ,QQhBD,qDAEG,WAAY,IAAI,MRfjB,QQaF,sDAMG,WAAY,IAAI,MRUlB,QQAN,+BAAA,sBAAA,2CACE,MRjCK,qBQgCE,qCAAA,4BAAA,iDAIL,MRpCG,KQ4CE,qEACA,qEADA,4DACA,4DACL,OAAQ,KAIE,8EACC,+EACE,iFACA,iFAJc,+FACjB,qEACC,sEACE,wEACA,wEAJc,sFAK3B,WAAY,QACZ,MRvDG,KQ0DS,kFAAA,yEACZ,oBR3DG,KQ8DG,2EACO,iFADP,kEACO,wEACb,WAAY,QACZ,MRjEG,KQJH,2CACA,iBRqCE,QQtCF,2CAIA,6CACE,MRDC,KQIF,oDACC,MR6EM,QQxFJ,wBAiBN,WAAY,IAAI,MRuBZ,QQlBD,kDAEG,WAAY,IAAI,MRfjB,QQaF,mDAMG,WAAY,IAAI,MRYlB,QQFN,4BAAA,mBAAA,wCACE,MRjCK,qBQgCE,kCAAA,yBAAA,8CAIL,MRpCG,KQ4CE,kEACA,kEADA,yDACA,yDACL,OAAQ,KAIE,2EACC,4EACE,8EACA,8EAJc,4FACjB,kEACC,mEACE,qEACA,qEAJc,mFAK3B,WAAY,QACZ,MRvDG,KQ0DS,+EAAA,sEACZ,oBR3DG,KQ8DG,wEACO,8EADP,+DACO,qEACb,WAAY,QACZ,MRjEG,KQJH,8CACA,iBRkCE,QQnCF,8CAIA,gDACE,MRiFM,QQ9EP,uDACC,MR6EM,QQxFD,2BAiBT,WAAY,IAAI,MRoBZ,QQfD,qDAEG,WAAY,IAAI,MRfjB,QQaF,sDAMG,WAAY,IAAI,MRSlB,QQCN,+BAAA,sBAAA,2CACE,MRiDU,kBQlDH,qCAAA,4BAAA,iDAIL,MR8CQ,QQtCH,qEACA,qEADA,4DACA,4DACL,OAAQ,KAIE,8EACC,+EACE,iFACA,iFAJc,+FACjB,qEACC,sEACE,wEACA,wEAJc,sFAK3B,WAAY,QACZ,MR2BQ,QQxBI,kFAAA,yEACZ,oBRuBQ,QQpBF,2EACO,iFADP,kEACO,wEACb,WAAY,QACZ,MRiBQ,QQtFR,6CACA,iBRgCE,QQjCF,6CAIA,+CACE,MRDC,KQIF,sDACC,MR6EM,QQxFF,0BAiBR,WAAY,IAAI,MRkBZ,QQbD,oDAEG,WAAY,IAAI,MRfjB,QQaF,qDAMG,WAAY,IAAI,MROlB,QQGN,qBAAA,8BAAA,0CACE,MRjCK,qBQgCE,2BAAA,oCAAA,gDAIL,MRpCG,KQ4CE,2DACA,2DADA,oEACA,oEACL,OAAQ,KAIE,oEACC,qEACE,uEACA,uEAJc,qFACjB,6EACC,8EACE,gFACA,gFAJc,8FAK3B,WAAY,QACZ,MRvDG,KQ0DS,wEAAA,iFACZ,oBR3DG,KQ8DG,iEACO,uEADP,0EACO,gFACb,WAAY,QACZ,MRjEG,KQJH,4CACA,iBRIG,QQLH,4CAIA,8CACE,MRiFM,QQ9EP,qDACC,MR6EM,QQxFH,yBAiBP,WAAY,IAAI,MRVX,QQeF,mDAEG,WAAY,IAAI,MRfjB,QQaF,oDAMG,WAAY,IAAI,MRrBjB,QQ+BP,6BAAA,oBAAA,yCACE,MRiDU,kBQlDH,mCAAA,0BAAA,+CAIL,MR8CQ,QQtCH,mEACA,mEADA,0DACA,0DACL,OAAQ,KAIE,4EACC,6EACE,+EACA,+EAJc,6FACjB,mEACC,oEACE,sEACA,sEAJc,oFAK3B,WAAY,QACZ,MR2BQ,QQxBI,gFAAA,uEACZ,oBRuBQ,QQpBF,yEACO,+EADP,gEACO,sEACb,WAAY,KACZ,MRiBQ,QQtFR,2CACA,iBRWG,QQZH,2CAIA,6CACE,MRDC,KQIF,oDACC,MR6EM,QQxFJ,wBAiBN,WAAY,IAAI,MRHX,QQQF,kDAEG,WAAY,IAAI,MRfjB,QQaF,mDAMG,WAAY,IAAI,MRdjB,QQwBP,mBAAA,4BAAA,wCACE,MRjCK,qBQgCE,yBAAA,kCAAA,8CAIL,MRpCG,KQ4CE,yDACA,yDADA,kEACA,kEACL,OAAQ,KAIE,kEACC,mEACE,qEACA,qEAJc,mFACjB,2EACC,4EACE,8EACA,8EAJc,4FAK3B,WAAY,QACZ,MRvDG,KQ0DS,sEAAA,+EACZ,oBR3DG,KQ8DG,+DACO,qEADP,wEACO,8EACb,WAAY,QACZ,MRjEG,KQJH,gDACA,iBHDI,QGAJ,gDAIA,kDACE,MRDC,KQIF,yDACC,MR6EM,QQxFC,6BAiBX,WAAY,IAAI,MHfV,QGoBH,uDAEG,WAAY,IAAI,MRfjB,QQaF,wDAMG,WAAY,IAAI,MH1BhB,QGoCR,iCAAA,wBAAA,6CACE,MRjCK,qBQgCE,uCAAA,8BAAA,mDAIL,MRpCG,KQ4CE,uEACA,uEADA,8DACA,8DACL,OAAQ,KAIE,gFACC,iFACE,mFACA,mFAJc,iGACjB,uEACC,wEACE,0EACA,0EAJc,wFAK3B,WAAY,QACZ,MRvDG,KQ0DS,oFAAA,2EACZ,oBR3DG,KQ8DG,6EACO,mFADP,oEACO,0EACb,WAAY,QACZ,MRjEG,KQJH,2CACA,iBHAD,QGDC,2CAIA,6CACE,MRDC,KQIF,oDACC,MR6EM,QQxFJ,wBAiBN,WAAY,IAAI,MHdf,QGmBE,kDAEG,WAAY,IAAI,MRfjB,QQaF,mDAMG,WAAY,IAAI,MHzBrB,QGmCH,4BAAA,mBAAA,wCACE,MRjCK,qBQgCE,kCAAA,yBAAA,8CAIL,MRpCG,KQ4CE,kEACA,kEADA,yDACA,yDACL,OAAQ,KAIE,2EACC,4EACE,8EACA,8EAJc,4FACjB,kEACC,mEACE,qEACA,qEAJc,mFAK3B,WAAY,QACZ,MRvDG,KQ0DS,+EAAA,sEACZ,oBR3DG,KQ8DG,wEACO,8EADP,+DACO,qEACb,WAAY,QACZ,MRjEG,KQJH,4CACA,iBHEA,QGHA,4CAIA,8CACE,MRDC,KQIF,qDACC,MR6EM,QQxFH,yBAiBP,WAAY,IAAI,MHZd,QGiBC,mDAEG,WAAY,IAAI,MRfjB,QQaF,oDAMG,WAAY,IAAI,MHvBpB,QGiCJ,6BAAA,oBAAA,yCACE,MRjCK,qBQgCE,mCAAA,0BAAA,+CAIL,MRpCG,KQ4CE,mEACA,mEADA,0DACA,0DACL,OAAQ,KAIE,4EACC,6EACE,+EACA,+EAJc,6FACjB,mEACC,oEACE,sEACA,sEAJc,oFAK3B,WAAY,QACZ,MRvDG,KQ0DS,gFAAA,uEACZ,oBR3DG,KQ8DG,yEACO,+EADP,gEACO,sEACb,WAAY,QACZ,MRjEG,KQJH,2CACA,iBHGD,QGJC,2CAIA,6CACE,MRiFM,QQ9EP,oDACC,MR6EM,QQxFJ,wBAiBN,WAAY,IAAI,MHXf,QGgBE,kDAEG,WAAY,IAAI,MRfjB,QQaF,mDAMG,WAAY,IAAI,MHtBrB,QGgCH,4BAAA,mBAAA,wCACE,MRiDU,kBQlDH,kCAAA,yBAAA,8CAIL,MR8CQ,QQtCH,kEACA,kEADA,yDACA,yDACL,OAAQ,KAIE,2EACC,4EACE,8EACA,8EAJc,4FACjB,kEACC,mEACE,qEACA,qEAJc,mFAK3B,WAAY,QACZ,MR2BQ,QQxBI,+EAAA,sEACZ,oBRuBQ,QQpBF,wEACO,8EADP,+DACO,qEACb,WAAY,QACZ,MRiBQ,QQtFR,8CACA,iBHKE,QGNF,8CAIA,gDACE,MRDC,KQIF,uDACC,MR6EM,QQxFD,2BAiBT,WAAY,IAAI,MHTZ,QGcD,qDAEG,WAAY,IAAI,MRfjB,QQaF,sDAMG,WAAY,IAAI,MHpBlB,QG8BN,sBAAA,+BAAA,2CACE,MRjCK,qBQgCE,4BAAA,qCAAA,iDAIL,MRpCG,KQ4CE,4DACA,4DADA,qEACA,qEACL,OAAQ,KAIE,qEACC,sEACE,wEACA,wEAJc,sFACjB,8EACC,+EACE,iFACA,iFAJc,+FAK3B,WAAY,QACZ,MRvDG,KQ0DS,yEAAA,kFACZ,oBR3DG,KQ8DG,kEACO,wEADP,2EACO,iFACb,WAAY,QACZ,MRjEG,KQJH,6CACA,iBHOC,QGRD,6CAIA,+CACE,MRDC,KQIF,sDACC,MR6EM,QQxFF,0BAiBR,WAAY,IAAI,MHPb,QGYA,oDAEG,WAAY,IAAI,MRfjB,QQaF,qDAMG,WAAY,IAAI,MHlBnB,QG4BL,8BAAA,qBAAA,0CACE,MRjCK,qBQgCE,oCAAA,2BAAA,gDAIL,MRpCG,KQ4CE,oEACA,oEADA,2DACA,2DACL,OAAQ,KAIE,6EACC,8EACE,gFACA,gFAJc,8FACjB,oEACC,qEACE,uEACA,uEAJc,qFAK3B,WAAY,QACZ,MRvDG,KQ0DS,iFAAA,wEACZ,oBR3DG,KQ8DG,0EACO,gFADP,iEACO,uEACb,WAAY,QACZ,MRjEG,KQJH,2CACA,iBR4BE,QQ7BF,2CAIA,6CACE,MRDC,KQIF,oDACC,MR6EM,QQxFJ,wBAiBN,WAAY,IAAI,MRcZ,QQTD,kDAEG,WAAY,IAAI,MRfjB,QQaF,mDAMG,WAAY,IAAI,MRGlB,QQON,mBAAA,4BAAA,wCACE,MRjCK,qBQgCE,yBAAA,kCAAA,8CAIL,MRpCG,KQ4CE,yDACA,yDADA,kEACA,kEACL,OAAQ,KAIE,kEACC,mEACE,qEACA,qEAJc,mFACjB,2EACC,4EACE,8EACA,8EAJc,4FAK3B,WAAY,QACZ,MRvDG,KQ0DS,sEAAA,+EACZ,oBR3DG,KQ8DG,+DACO,qEADP,wEACO,8EACb,WAAY,QACZ,MRjEG,KQJH,6CACA,iBR6BE,QQ9BF,6CAIA,+CACE,MRDC,KQIF,sDACC,MR6EM,QQxFF,0BAiBR,WAAY,IAAI,MReZ,QQVD,oDAEG,WAAY,IAAI,MRfjB,QQaF,qDAMG,WAAY,IAAI,MRIlB,QQMN,8BAAA,qBAAA,0CACE,MRjCK,qBQgCE,oCAAA,2BAAA,gDAIL,MRpCG,KQ4CE,oEACA,oEADA,2DACA,2DACL,OAAQ,KAIE,6EACC,8EACE,gFACA,gFAJc,8FACjB,oEACC,qEACE,uEACA,uEAJc,qFAK3B,WAAY,QACZ,MRvDG,KQ0DS,iFAAA,wEACZ,oBR3DG,KQ8DG,0EACO,gFADP,iEACO,uEACb,WAAY,QACZ,MRjEG,KQJH,6CACA,iBR8BE,QQ/BF,6CAIA,+CACE,MRDC,KQIF,sDACC,MR6EM,QQxFF,0BAiBR,WAAY,IAAI,MRgBZ,QQXD,oDAEG,WAAY,IAAI,MRfjB,QQaF,qDAMG,WAAY,IAAI,MRKlB,QQKN,8BAAA,qBAAA,0CACE,MRjCK,qBQgCE,oCAAA,2BAAA,gDAIL,MRpCG,KQ4CE,oEACA,oEADA,2DACA,2DACL,OAAQ,KAIE,6EACC,8EACE,gFACA,gFAJc,8FACjB,oEACC,qEACE,uEACA,uEAJc,qFAK3B,WAAY,QACZ,MRvDG,KQ0DS,iFAAA,wEACZ,oBR3DG,KQ8DG,0EACO,gFADP,iEACO,uEACb,WAAY,QACZ,MRjEG,KQJH,2CACA,iBR+BE,QQhCF,2CAIA,6CACE,MRDC,KQIF,oDACC,MR6EM,QQxFJ,wBAiBN,WAAY,IAAI,MRiBZ,QQZD,kDAEG,WAAY,IAAI,MRfjB,QQaF,mDAMG,WAAY,IAAI,MRMlB,QQIN,4BAAA,mBAAA,wCACE,MRjCK,qBQgCE,kCAAA,yBAAA,8CAIL,MRpCG,KQ4CE,kEACA,kEADA,yDACA,yDACL,OAAQ,KAIE,2EACC,4EACE,8EACA,8EAJc,4FACjB,kEACC,mEACE,qEACA,qEAJc,mFAK3B,WAAY,QACZ,MRvDG,KQ0DS,+EAAA,sEACZ,oBR3DG,KQ8DG,wEACO,8EADP,+DACO,qEACb,WAAY,QACZ,MRjEG,KQJH,0CACA,iBRgCE,QQjCF,0CAIA,4CACE,MRDC,KQIF,mDACC,MR6EM,QQxFL,uBAiBL,WAAY,IAAI,MRkBZ,QQbD,iDAEG,WAAY,IAAI,MRfjB,QQaF,kDAMG,WAAY,IAAI,MROlB,QQGN,2BAAA,kBAAA,uCACE,MRjCK,qBQgCE,iCAAA,wBAAA,6CAIL,MRpCG,KQ4CE,iEACA,iEADA,wDACA,wDACL,OAAQ,KAIE,0EACC,2EACE,6EACA,6EAJc,2FACjB,iEACC,kEACE,oEACA,oEAJc,kFAK3B,WAAY,QACZ,MRvDG,KQ0DS,8EAAA,qEACZ,oBR3DG,KQ8DG,uEACO,6EADP,8DACO,oEACb,WAAY,QACZ,MRjEG,KQJH,6CACA,iBRiCE,QQlCF,6CAIA,+CACE,MRiFM,QQ9EP,sDACC,MR6EM,QQxFF,0BAiBR,WAAY,IAAI,MRmBZ,QQdD,oDAEG,WAAY,IAAI,MRfjB,QQaF,qDAMG,WAAY,IAAI,MRQlB,QQEN,8BAAA,qBAAA,0CACE,MRiDU,kBQlDH,oCAAA,2BAAA,gDAIL,MR8CQ,QQtCH,oEACA,oEADA,2DACA,2DACL,OAAQ,KAIE,6EACC,8EACE,gFACA,gFAJc,8FACjB,oEACC,qEACE,uEACA,uEAJc,qFAK3B,WAAY,QACZ,MR2BQ,QQxBI,iFAAA,wEACZ,oBRuBQ,QQpBF,0EACO,gFADP,iEACO,uEACb,WAAY,QACZ,MRiBQ,QQtFR,6CACA,iBRkCE,QQnCF,6CAIA,+CACE,MRiFM,QQ9EP,sDACC,MR6EM,QQxFF,0BAiBR,WAAY,IAAI,MRoBZ,QQfD,oDAEG,WAAY,IAAI,MRfjB,QQaF,qDAMG,WAAY,IAAI,MRSlB,QQCN,8BAAA,qBAAA,0CACE,MRiDU,kBQlDH,oCAAA,2BAAA,gDAIL,MR8CQ,QQtCH,oEACA,oEADA,2DACA,2DACL,OAAQ,KAIE,6EACC,8EACE,gFACA,gFAJc,8FACjB,oEACC,qEACE,uEACA,uEAJc,qFAK3B,WAAY,QACZ,MR2BQ,QQxBI,iFAAA,wEACZ,oBRuBQ,QQpBF,0EACO,gFADP,iEACO,uEACb,WAAY,QACZ,MRiBQ,QQtFR,4CACA,iBRmCE,QQpCF,4CAIA,8CACE,MRDC,KQIF,qDACC,MR6EM,QQxFH,yBAiBP,WAAY,IAAI,MRqBZ,QQhBD,mDAEG,WAAY,IAAI,MRfjB,QQaF,oDAMG,WAAY,IAAI,MRUlB,QQAN,6BAAA,oBAAA,yCACE,MRjCK,qBQgCE,mCAAA,0BAAA,+CAIL,MRpCG,KQ4CE,mEACA,mEADA,0DACA,0DACL,OAAQ,KAIE,4EACC,6EACE,+EACA,+EAJc,6FACjB,mEACC,oEACE,sEACA,sEAJc,oFAK3B,WAAY,QACZ,MRvDG,KQ0DS,gFAAA,uEACZ,oBR3DG,KQ8DG,yEACO,+EADP,gEACO,sEACb,WAAY,QACZ,MRjEG,KQJH,2CACA,iBRoCE,QQrCF,2CAIA,6CACE,MRDC,KQIF,oDACC,MR6EM,QQxFJ,wBAiBN,WAAY,IAAI,MRsBZ,QQjBD,kDAEG,WAAY,IAAI,MRfjB,QQaF,mDAMG,WAAY,IAAI,MRWlB,QQDN,4BAAA,mBAAA,wCACE,MRjCK,qBQgCE,kCAAA,yBAAA,8CAIL,MRpCG,KQ4CE,kEACA,kEADA,yDACA,yDACL,OAAQ,KAIE,2EACC,4EACE,8EACA,8EAJc,4FACjB,kEACC,mEACE,qEACA,qEAJc,mFAK3B,WAAY,QACZ,MRvDG,KQ0DS,+EAAA,sEACZ,oBR3DG,KQ8DG,wEACO,8EADP,+DACO,qEACb,WAAY,QACZ,MRjEG,KQJH,2CACA,iBRqCE,QQtCF,2CAIA,6CACE,MRDC,KQIF,oDACC,MR6EM,QQxFJ,wBAiBN,WAAY,IAAI,MRuBZ,QQlBD,kDAEG,WAAY,IAAI,MRfjB,QQaF,mDAMG,WAAY,IAAI,MRYlB,QQFN,mBAAA,4BAAA,wCACE,MRjCK,qBQgCE,yBAAA,kCAAA,8CAIL,MRpCG,KQ4CE,yDACA,yDADA,kEACA,kEACL,OAAQ,KAIE,kEACC,mEACE,qEACA,qEAJc,mFACjB,2EACC,4EACE,8EACA,8EAJc,4FAK3B,WAAY,QACZ,MRvDG,KQ0DS,sEAAA,+EACZ,oBR3DG,KQ8DG,+DACO,qEADP,wEACO,8EACb,WAAY,QACZ,MRjEG,KQJH,4CACA,iBRGG,KQJH,4CAIA,8CACE,MRiFM,QQ9EP,qDACC,MR6EM,QQxFH,yBAiBP,WAAY,IAAI,MRXX,KQgBF,mDAEG,WAAY,IAAI,MRfjB,QQaF,oDAMG,WAAY,IAAI,MRtBjB,KQgCP,6BAAA,oBAAA,yCACE,MRiDU,kBQlDH,mCAAA,0BAAA,+CAIL,MR8CQ,QQtCH,mEACA,mEADA,0DACA,0DACL,OAAQ,KAIE,4EACC,6EACE,+EACA,+EAJc,6FACjB,mEACC,oEACE,sEACA,sEAJc,oFAK3B,WAAY,QACZ,MR2BQ,QQxBI,gFAAA,uEACZ,oBRuBQ,QQpBF,yEACO,+EADP,gEACO,sEACb,WAAY,KACZ,MRiBQ,QQtFR,2CACA,iBRSG,QQVH,2CAIA,6CACE,MRDC,KQIF,oDACC,MR6EM,QQxFJ,wBAiBN,WAAY,IAAI,MRLX,QQUF,kDAEG,WAAY,IAAI,MRfjB,QQaF,mDAMG,WAAY,IAAI,MRhBjB,QQ0BP,4BAAA,mBAAA,wCACE,MRjCK,qBQgCE,kCAAA,yBAAA,8CAIL,MRpCG,KQ4CE,kEACA,kEADA,yDACA,yDACL,OAAQ,KAIE,2EACC,4EACE,8EACA,8EAJc,4FACjB,kEACC,mEACE,qEACA,qEAJc,mFAK3B,WAAY,QACZ,MRvDG,KQ0DS,+EAAA,sEACZ,oBR3DG,KQ8DG,wEACO,8EADP,+DACO,qEACb,WAAY,QACZ,MRjEG,KQJH,gDACA,iBRWG,QQZH,gDAIA,kDACE,MRDC,KQIF,yDACC,MR6EM,QQxFC,6BAiBX,WAAY,IAAI,MRHX,QQQF,uDAEG,WAAY,IAAI,MRfjB,QQaF,wDAMG,WAAY,IAAI,MRdjB,QQwBP,iCAAA,wBAAA,6CACE,MRjCK,qBQgCE,uCAAA,8BAAA,mDAIL,MRpCG,KQ4CE,uEACA,uEADA,8DACA,8DACL,OAAQ,KAIE,gFACC,iFACE,mFACA,mFAJc,iGACjB,uEACC,wEACE,0EACA,0EAJc,wFAK3B,WAAY,QACZ,MRvDG,KQ0DS,oFAAA,2EACZ,oBR3DG,KQ8DG,6EACO,mFADP,oEACO,0EACb,WAAY,QACZ,MRjEG,KSEX,MFGM,WFoIQ,EAAE,EAAE,IAAI,gBAAmB,CAAE,EAAE,IAAI,IAAI,eIrInD,cT0GO,KSvGL,2BACE,aJgImB,QItIpB,cAUD,yBACE,MTbK,KSEN,qBAgBD,OAAQ,eACR,KAAM,EACN,WAAY,eACZ,UAAW,eACX,SAAU,MACV,IAAK,EACL,MAAO,eACP,QAAS,KAEO,8CACd,QAAS,gBAGV,4CACC,QAAS,KAIX,kCADA,kCRzCA,cQ2CyB,YAMzB,gCACA,kCACE,QAAS,KAKT,0BACA,cAAe,IAAI,MTzCd,iBS0CL,OAAQ,EAFN,uCAKA,cAAe,EAOnB,gCACE,WAAY,MACZ,SAAU,KAId,oBACE,aAAc,IAAI,MT3DX,iBS8DT,mBACE,YAAa,IAAI,MT/DV,iBSoED,gDACF,cAAe,EAGC,gFACZ,YAAa,KAOnB,uCACE,cAAe,EAED,6DACZ,YAAa,EACb,YAAa,EAKnB,4BACE,OAAQ,MAAA,MAIR,iEACE,cAAe,EAEf,2EACE,cAAe,EAEf,qFACE,cAAe,EASjB,gEACE,cAAe,KAxHtB,wBAgID,WAAY,EAIM,qEACZ,YAAa,EACb,YAAa,EAIjB,uCACE,WAAY,IAAA,MAAA,YADb,6CAIG,WAAY,IAAI,MT7If,QSgJK,oDAEJ,WAAY,EAMpB,oCACE,OAAQ,MAAA,MAAA,MAG4B,yEACpC,cAAe,EAEf,mFACE,cAAe,EAEf,6FACE,cAAe,EAQf,wEACE,cAAe,KAUvB,oBACF,SAAU,OCpMT,kBAAA,oBAAA,oBACC,QAAS,MACT,MAAO,KACP,QAAS,GD4Mb,aACE,iBAAkB,YAClB,cAAe,IAAI,MT7LV,iBS8LT,QAAS,OTwcyB,QSvclC,SAAU,SRtMR,uBDuM0B,OCtM1B,wBDsM0B,OSL9B,6BAWI,cAAe,EAGf,yBACA,MAAO,MACP,aAAc,SAEd,sCACA,8BACA,qCACE,cAAe,OACf,WAAY,OAGb,+CACC,SAAU,SAKhB,YACE,MAAO,KACP,UJ5FqB,OI6FrB,YTA4B,ISC5B,OAAQ,EAGV,WACE,MAAO,KAKT,UACE,WAAY,IACZ,MT9OS,QS+OT,UThB4B,QSiB5B,OAAU,QAA0B,EACpC,QAAS,OAAA,MALX,0BAAS,gBASL,MTnPO,QS0OF,gBAAT,gBAcI,WAAY,eAKd,qBACE,UJ1HsB,KI6HxB,mBACE,QJ5HyB,MACA,MIuIzB,kBACA,cAAe,EAGA,8BADA,8BAEb,iBAAkB,EAKtB,eACE,WAAY,IAGd,6BACE,OAAQ,MAGJ,iCACJ,OAAQ,KAIZ,cEnTE,aAAc,EACd,WAAY,KFoTZ,OAAQ,KAAA,EAGkB,yBADxB,iBAEE,MAAO,KACP,aAAc,MAMpB,eACE,WTzTS,QS2TT,6BAEE,cAAe,IAAI,MT5TZ,QS6TP,QAAS,IAAA,ECzUV,oCACC,QAAS,MACT,MAAO,KACP,QAAS,GDmUE,0CAMT,cAAe,EANN,2CAUT,YAAa,EAGf,iCACE,OJhSQ,SIiSR,MJjSQ,SIkSR,MAAO,KAIX,6BACE,MAAO,QACP,YAAa,KAGf,yBACE,MT/UO,QSgVP,QAAS,MACT,YAAa,IAGf,2BACE,UAAW,KACX,YAAa,IAQjB,WACE,WAAY,KACZ,OAAQ,EACR,SAAU,KACV,QAAS,EAGP,cRlXA,cQmXuB,IACvB,WT7WO,QS8WP,YAAa,IAAI,MT7WV,QS8WP,MTzWO,QS0WP,cAAe,IACf,QAAS,KANP,2BASA,cAAe,EAGT,mCACN,OAAQ,EAAA,KAAA,EAAA,IAGV,oBACE,QAAS,aACT,YAAa,IACb,YAAa,IAIf,qBACE,UAAW,MACX,YAAa,KAIf,qBACE,MT7WI,QS8WJ,QAAS,KACT,MAAO,MAGL,yBAGA,0BADA,0BADA,0BAGA,gCACA,0BACA,OAAQ,QACR,aAAc,IAIV,2BACN,QAAS,aA/CT,mBAmDA,MAAO,QAEP,yBACE,YAAa,IACb,gBAAiB,aAGnB,0BACE,WTlaG,kBSyaP,oBACE,kBTtZI,QSqZN,sBACE,kBTzaK,QSwaP,oBACE,kBT/YI,QS8YN,iBACE,kBT7YI,QS4YN,oBACE,kBThZI,QS+YN,mBACE,kBTlZI,QSiZN,kBACE,kBT9aK,QS6aP,iBACE,kBTvaK,QS4aP,sBACE,kBJzbM,QIwbR,iBACE,kBJxbC,QIubH,kBACE,kBJtbE,QIqbJ,iBACE,kBJrbC,QIobH,oBACE,kBJnbI,QIkbN,mBACE,kBJjbG,QIgbL,iBACE,kBT5ZI,QS2ZN,mBACE,kBT3ZI,QS0ZN,mBACE,kBT1ZI,QSyZN,iBACE,kBTzZI,QSwZN,gBACE,kBTxZI,QSuZN,mBACE,kBTvZI,QSsZN,mBACE,kBTtZI,QSqZN,kBACE,kBTrZI,QSoZN,iBACE,kBTpZI,QSmZN,iBACE,kBTnZI,QSkZN,kBACE,kBTrbK,KSobP,iBACE,kBT/aK,QS8aP,sBACE,kBT7aK,QSibT,mBACE,OAAQ,KACR,QAAS,aACT,OAAQ,EAAA,IAOZ,YACE,UAAW,MAMK,8CACZ,YAAa,EGhdjB,uBACE,iBZcO,KYbP,QAAS,MACT,OAAQ,KACR,KAAM,EACN,QAAS,GACT,SAAU,SACV,IAAK,EACL,MAAO,KACP,QZ+iBgC,KYtiBhC,wCADA,wCAEE,aZNK,QYeP,gCAAA,8BAAA,iCAAA,mCAAA,iCACE,MZxBK,KYyBL,YAAa,EAAA,IAAA,EAAA,KChCnB,kBACE,SAAU,SACV,MAAO,EACP,IAAK,EACL,QbojBkC,KaxjBnB,wBAOb,SAAU,MAId,iBACE,KAAM,EACN,SAAU,SACV,IAAK,EACL,QbyiBkC,Ka7iBpB,uBAOZ,SAAU,MAId,qBACE,OAAQ,EACR,SAAU,SACV,MAAO,EACP,Qb8hBkC,KaliBhB,2BAOhB,SAAU,MAId,oBACE,OAAQ,EACR,KAAM,EACN,SAAU,SACV,QbmhBkC,KavhBjB,0BAOf,SAAU,MCtCX,kBACC,Wd6BM,6Bc1BJ,yBACE,MdAG,KcCH,YAAa,EAAA,IAAA,EAAA,KAIjB,gCACE,WdmBI,oBclBJ,MdPK,KcLR,oBACC,WdUO,+BcPL,2BACE,MdAG,KcCH,YAAa,EAAA,IAAA,EAAA,KAIjB,kCACE,WdAK,sBcCL,MdPK,KcLR,kBACC,WdoCM,6BcjCJ,yBACE,MdAG,KcCH,YAAa,EAAA,IAAA,EAAA,KAIjB,gCACE,Wd0BI,oBczBJ,MdPK,KcLR,eACC,WdsCM,8BcnCJ,sBACE,MdAG,KcCH,YAAa,EAAA,IAAA,EAAA,KAIjB,6BACE,Wd4BI,qBc3BJ,MdPK,KcLR,kBACC,WdmCM,6Bc1BN,gCACE,WdyBI,oBcxBJ,Md2EU,QcvFb,iBACC,WdiCM,6Bc9BJ,wBACE,MdAG,KcCH,YAAa,EAAA,IAAA,EAAA,KAIjB,+BACE,WduBI,oBctBJ,MdPK,KcLR,gBACC,WdKO,+BcIP,8BACE,WdLK,sBcML,Md2EU,QcvFb,eACC,WdYO,4BcTL,sBACE,MdAG,KcCH,YAAa,EAAA,IAAA,EAAA,KAIjB,6BACE,WdEK,mBcDL,MdPK,KcLR,oBACC,WTAQ,8BSGN,2BACE,MdAG,KcCH,YAAa,EAAA,IAAA,EAAA,KAIjB,kCACE,WTVM,qBSWN,MdPK,KcLR,eACC,WTCG,2BSED,sBACE,MdAG,KcCH,YAAa,EAAA,IAAA,EAAA,KAIjB,6BACE,WTTC,kBSUD,MdPK,KcLR,gBACC,WTGI,8BSAF,uBACE,MdAG,KcCH,YAAa,EAAA,IAAA,EAAA,KAIjB,8BACE,WTPE,qBSQF,MdPK,KcLR,eACC,WTIG,6BSKH,6BACE,WTNC,oBSOD,Md2EU,QcvFb,kBACC,WTMM,8BSHJ,yBACE,MdAG,KcCH,YAAa,EAAA,IAAA,EAAA,KAIjB,gCACE,WTJI,qBSKJ,MdPK,KcLR,iBACC,WTQK,6BSLH,wBACE,MdAG,KcCH,YAAa,EAAA,IAAA,EAAA,KAIjB,+BACE,WTFG,oBSGH,MdPK,KcLR,eACC,Wd6BM,6Bc1BJ,sBACE,MdAG,KcCH,YAAa,EAAA,IAAA,EAAA,KAIjB,6BACE,WdmBI,oBclBJ,MdPK,KcLR,iBACC,Wd8BM,8Bc3BJ,wBACE,MdAG,KcCH,YAAa,EAAA,IAAA,EAAA,KAIjB,+BACE,WdoBI,qBcnBJ,MdPK,KcLR,iBACC,Wd+BM,8Bc5BJ,wBACE,MdAG,KcCH,YAAa,EAAA,IAAA,EAAA,KAIjB,+BACE,WdqBI,qBcpBJ,MdPK,KcLR,eACC,WdgCM,8Bc7BJ,sBACE,MdAG,KcCH,YAAa,EAAA,IAAA,EAAA,KAIjB,6BACE,WdsBI,qBcrBJ,MdPK,KcLR,cACC,WdiCM,6Bc9BJ,qBACE,MdAG,KcCH,YAAa,EAAA,IAAA,EAAA,KAIjB,4BACE,WduBI,oBctBJ,MdPK,KcLR,iBACC,WdkCM,8BczBN,+BACE,WdwBI,qBcvBJ,Md2EU,QcvFb,iBACC,WdmCM,6Bc1BN,+BACE,WdyBI,oBcxBJ,Md2EU,QcvFb,gBACC,WdoCM,6BcjCJ,uBACE,MdAG,KcCH,YAAa,EAAA,IAAA,EAAA,KAIjB,8BACE,Wd0BI,oBczBJ,MdPK,KcLR,eACC,WdqCM,8BclCJ,sBACE,MdAG,KcCH,YAAa,EAAA,IAAA,EAAA,KAIjB,6BACE,Wd2BI,qBc1BJ,MdPK,KcLR,eACC,WdsCM,8BcnCJ,sBACE,MdAG,KcCH,YAAa,EAAA,IAAA,EAAA,KAIjB,6BACE,Wd4BI,qBc3BJ,MdPK,KcLR,gBACC,WdIO,+BcKP,8BACE,WdNK,sBcOL,Md2EU,QcvFb,eACC,WdUO,+BcPL,sBACE,MdAG,KcCH,YAAa,EAAA,IAAA,EAAA,KAIjB,6BACE,WdAK,sBcCL,MdPK,KcLR,oBACC,WdYO,4BcTL,2BACE,MdAG,KcCH,YAAa,EAAA,IAAA,EAAA,KAIjB,kCACE,WdEK,mBcDL,MdPK,KePP,cAAA,cAGA,OAAQ,YAHR,cdCA,ccOuB,EACvB,aAAc,IACd,WAAY,KAVZ,cAeA,SAAU,OACV,SAAU,SAEF,+BACN,WfZK,KeaL,OAAQ,QACR,QAAS,MACT,UAAW,MACX,WAAY,KACZ,UAAW,KACX,QAAS,EACT,QAAS,EACT,SAAU,SACV,MAAO,EACP,WAAY,MACZ,IAAK,EA9BX,cAmCI,UfwM0B,kBenM9B,aACE,iBfjCS,QekCT,aVyJ4B,KUxJ5B,MVuJqB,KU1JX,mBAAA,oBAAA,mBAQR,iBAAkB,QAClB,MAAO,QAKX,SdrDI,ccsDqB,IACvB,iBfhDS,QeiDT,OAAQ,IAAI,MV0IgB,KUzI5B,Mf7CS,Qe8CT,UAAW,KACX,OAAQ,KACR,OAAQ,EAAA,EAAA,KAAA,KACR,UAAW,KACX,QAAS,KAAA,IACT,SAAU,SACV,WAAY,OAGV,aAGA,cADA,cADA,cAGA,oBACA,cACA,QAAS,MACT,UAAW,KArBP,eAyBJ,WfvEO,QewEP,aAAc,KACd,MViHmB,KU5If,gBAAA,eR1CF,WQ0EkB,MAAM,EAAE,IAAI,IfrEzB,iBeyEP,gBACA,UAAW,KACX,YAAa,IACb,SAAU,SACV,MAAO,MACP,IAAK,KAMT,QCDE,QXiGoB,QACA,OH3EhB,UAtCW,OciBf,YhBsG4B,IC3M1B,cIwMsB,OYxM1B,ShBAI,cDgN0B,OiB1M1B,WZyMG,EAAA,IAAA,IAAA,eAAA,CAAA,EAAA,IAAA,IAAA,gBYpML,iBjBLS,KiBMT,YAAa,IAAI,MjBJR,QiBKT,cjBuGO,KiBtGP,QAAS,KAET,WACE,MjBJO,QiBKP,gBAAiB,UAFlB,iBAKG,MjBbK,QiBiBR,sBACC,cAAe,EA1BX,wBA+BJ,kBAAmB,QA/Bf,yBAmCJ,kBAAmB,QAnCf,sBAuCJ,kBAAmB,QAvCf,yBA2CJ,kBAAmB,QC3CrB,aACE,aAAc,KAGhB,cACE,MlBWO,KkBVP,QAAS,GAFL,oBAKF,QAAS,GAIb,SACE,MlBRO,KkBSP,gBAAiB,UAMnB,eACE,MlBhBO,KkBiBP,WlBQM,QkBPN,aAAc,QAGhB,uBC/BA,MC8FQ,QjBzFN,iBiByFM,QD5FR,aC4FQ,QD1FR,0BACE,iBAAkB,QAGpB,mCACE,MAAO,QDgBT,iBACE,MlBhBO,KkBiBP,WlBXO,QkBYP,aAAc,QAGhB,yBC/BA,MC8FQ,QjBzFN,iBiByFM,QD5FR,aC4FQ,QD1FR,4BACE,iBAAkB,QAGpB,qCACE,MAAO,QDgBT,eACE,MlBhBO,KkBiBP,WlBeM,QkBdN,aAAc,QAGhB,uBC/BA,MC8FQ,QjBzFN,iBiByFM,QD5FR,aC4FQ,QD1FR,0BACE,iBAAkB,QAGpB,mCACE,MAAO,QDgBT,YACE,MlBhBO,KkBiBP,WlBiBM,QkBhBN,aAAc,QAGhB,oBC/BA,MC8FQ,QjBzFN,iBiByFM,QD5FR,aC4FQ,QD1FR,uBACE,iBAAkB,QAGpB,gCACE,MAAO,QDgBT,eACE,MlBkEY,QkBjEZ,WlBcM,QkBbN,aAAc,QAGhB,uBC/BA,MC8FQ,QjBzFN,iBiByFM,QD5FR,aC4FQ,QD1FR,0BACE,iBAAkB,QAGpB,mCACE,MAAO,QDgBT,cACE,MlBhBO,KkBiBP,WlBYM,QkBXN,aAAc,QAGhB,sBC/BA,MC8FQ,QjBzFN,iBiByFM,QD5FR,aC4FQ,QD1FR,yBACE,iBAAkB,QAGpB,kCACE,MAAO,QDgBT,aACE,MlBkEY,QkBjEZ,WlBhBO,QkBiBP,aAAc,QAGhB,qBC/BA,MC8FQ,QjBzFN,iBiByFM,QD5FR,aC4FQ,QD1FR,wBACE,iBAAkB,QAGpB,iCACE,MAAO,QDgBT,YACE,MlBhBO,KkBiBP,WlBTO,QkBUP,aAAc,QAGhB,oBC/BA,MC8FQ,QjBzFN,iBiByFM,QD5FR,aC4FQ,QD1FR,uBACE,iBAAkB,QAGpB,gCACE,MAAO,QELQ,wBACf,MAAO,QAKe,iDACpB,iBrBDK,KqBEL,cAAe,EACf,WAAY,MAAM,EAAE,IAAI,ErBAnB,OAAO,CqBCA,MAAM,EAAG,KAAI,ErBDpB,QqBEL,SAAU,OACV,IAAK,EACL,QAAS,GAKQ,4DACb,iBrBJC,QqBKD,WAAY,MAAM,EAAE,IAAI,ErBuSJ,OAAuB,CqBtS/B,MAAM,EAAG,KAAI,ErBsSL,QqB5TxB,iBA+BF,oBACA,oBACE,OAAQ,EAjCR,mBAwCF,sBACA,sBACE,WAAY,OAQD,uCADA,uCADA,uCADA,uCAIX,eAAgB,OAQH,gDADA,gDADA,gDADA,gDAKX,arB+DG,OqBjEQ,+CADA,+CADA,+CADA,+CASX,crB2DG,OsB5HM,uBAAA,wBAGb,iBAAkB,KAGlB,sBAGA,uBADA,uBADA,uBAGA,6BACA,uBACA,QAAS,aACT,UAAW,KACX,WAAY,MACZ,SAAU,SACV,IAAK,IACL,QAAS", "sourcesContent": ["/*!\n *   AdminLTE v3.0.5\n *     Only Components\n *   Author: Colorlib\n *   Website: AdminLTE.io <http://adminlte.io>\n *   License: Open source - MIT <http://opensource.org/licenses/MIT>\n */\n// Bootstrap\n// ---------------------------------------------------\n@import '~bootstrap/scss/functions';\n@import 'bootstrap-variables';\n@import '~bootstrap/scss/mixins';\n// @import '~bootstrap/scss/bootstrap';\n\n// Variables and Mixins\n// ---------------------------------------------------\n@import 'variables';\n@import 'mixins';\n\n@import 'parts/components';\n", "//\n// Component: Forms\n//\n \n.form-group {\n  &.has-icon {\n    position: relative;\n\n    .form-control {\n      padding-right: 35px;\n    }\n\n    .form-icon {\n      background-color: transparent;\n      border: 0;\n      cursor: pointer;\n      font-size: 1rem;\n      // margin-top: -3px;\n      padding: $input-btn-padding-y $input-btn-padding-x;\n      position: absolute;\n      right: 3px;\n      top: 0;\n    }\n  }\n}\n\n// Button groups\n.btn-group-vertical {\n  .btn {\n    &.btn-flat:first-of-type,\n    &.btn-flat:last-of-type {\n      @include border-radius(0);\n    }\n  }\n}\n\n// Support icons in form-control\n.form-control-feedback {\n  &.fa,\n  &.fas,\n  &.far,\n  &.fab,\n  &.glyphicon,\n  &.ion {\n    line-height: $input-height;\n  }\n}\n\n.input-lg  + .form-control-feedback,\n.input-group-lg + .form-control-feedback {\n  &.fa,\n  &.fas,\n  &.far,\n  &.fab,\n  &.glyphicon,\n  &.ion {\n    line-height: $input-height-lg;\n  }\n}\n\n.form-group-lg {\n  .form-control + .form-control-feedback {\n    &.fa,\n    &.fas,\n    &.far,\n    &.fab,\n    &.glyphicon,\n    &.ion {\n      line-height: $input-height-lg;\n    }\n  }\n}\n\n.input-sm  + .form-control-feedback,\n.input-group-sm + .form-control-feedback {\n  &.fa,\n  &.fas,\n  &.far,\n  &.fab,\n  &.glyphicon,\n  &.ion {\n    line-height: $input-height-sm;\n  }\n}\n\n.form-group-sm {\n  .form-control + .form-control-feedback {\n    &.fa,\n    &.fas,\n    &.far,\n    &.fab,\n    &.glyphicon,\n    &.ion {\n      line-height: $input-height-sm;\n    }\n  }\n}\n\nlabel:not(.form-check-label):not(.custom-file-label) {\n  font-weight: $font-weight-bold;\n}\n\n.warning-feedback {\n  @include font-size($form-feedback-font-size);\n  color: theme-color('warning');\n  display: none;\n  margin-top: $form-feedback-margin-top;\n  width: 100%;\n}\n\n.warning-tooltip {\n  @include border-radius($form-feedback-tooltip-border-radius);\n  @include font-size($form-feedback-tooltip-font-size);\n  background-color: rgba(theme-color('warning'), $form-feedback-tooltip-opacity);\n  color: color-yiq(theme-color('warning'));\n  display: none;\n  line-height: $form-feedback-tooltip-line-height;\n  margin-top: .1rem;\n  max-width: 100%; // Contain to parent when possible\n  padding: $form-feedback-tooltip-padding-y $form-feedback-tooltip-padding-x;\n  position: absolute;\n  top: 100%;\n  z-index: 5;\n}\n\n.form-control {\n  &.is-warning {\n    border-color: theme-color('warning');\n\n    @if $enable-validation-icons {\n      // padding-right: $input-height-inner;\n      // background-image: none;\n      // background-repeat: no-repeat;\n      // background-position: center right $input-height-inner-quarter;\n      // background-size: $input-height-inner-half $input-height-inner-half;\n    }\n\n    &:focus {\n      border-color: theme-color('warning');\n      box-shadow: 0 0 0 $input-focus-width rgba(theme-color('warning'), .25);\n    }\n\n    ~ .warning-feedback,\n    ~ .warning-tooltip {\n      display: block;\n    }\n  }\n}\n\n// stylelint-disable-next-line selector-no-qualifying-type\ntextarea.form-control {\n  &.is-warning {\n    @if $enable-validation-icons {\n      padding-right: $input-height-inner;\n      background-position: top $input-height-inner-quarter right $input-height-inner-quarter;\n    }\n  }\n}\n\n.custom-select {\n  &.is-warning {\n    border-color: theme-color('warning');\n\n    @if $enable-validation-icons {\n      // padding-right: $custom-select-feedback-icon-padding-right;\n      // background: $custom-select-background, none $custom-select-bg no-repeat $custom-select-feedback-icon-position / $custom-select-feedback-icon-size;\n    }\n\n    &:focus {\n      border-color: theme-color('warning');\n      box-shadow: 0 0 0 $input-focus-width rgba(theme-color('warning'), .25);\n    }\n\n    ~ .warning-feedback,\n    ~ .warning-tooltip {\n      display: block;\n    }\n  }\n}\n\n\n.form-control-file {\n  &.is-warning {\n    ~ .warning-feedback,\n    ~ .warning-tooltip {\n      display: block;\n    }\n  }\n}\n\n.form-check-input {\n  &.is-warning {\n    ~ .form-check-label {\n      color: theme-color('warning');\n    }\n\n    ~ .warning-feedback,\n    ~ .warning-tooltip {\n      display: block;\n    }\n  }\n}\n\n.custom-control-input.is-warning {\n  ~ .custom-control-label {\n    color: theme-color('warning');\n\n    &::before {\n      border-color: theme-color('warning');\n    }\n  }\n\n  ~ .warning-feedback,\n  ~ .warning-tooltip {\n    display: block;\n  }\n\n  &:checked {\n    ~ .custom-control-label::before {\n      @include gradient-bg(lighten(theme-color('warning'), 10%));\n      border-color: lighten(theme-color('warning'), 10%);\n    }\n  }\n\n  &:focus {\n    ~ .custom-control-label::before {\n      box-shadow: 0 0 0 $input-focus-width rgba(theme-color('warning'), .25);\n    }\n\n    &:not(:checked) ~ .custom-control-label::before {\n      border-color: theme-color('warning');\n    }\n  }\n}\n\n// custom file\n.custom-file-input {\n  &.is-warning {\n    ~ .custom-file-label {\n      border-color: theme-color('warning');\n    }\n\n    ~ .warning-feedback,\n    ~ .warning-tooltip {\n      display: block;\n    }\n\n    &:focus {\n      ~ .custom-file-label {\n        border-color: theme-color('warning');\n        box-shadow: 0 0 0 $input-focus-width rgba(theme-color('warning'), .25);\n      }\n    }\n  }\n}\n\n// custom switch color variations\n.custom-switch {\n  @each $name, $color in $theme-colors {\n    @include custom-switch-variant($name, $color);\n  }\n\n  @each $name, $color in $colors {\n    @include custom-switch-variant($name, $color);\n  }\n}\n\n// custom range color variations\n.custom-range {\n  @each $name, $color in $theme-colors {\n    @include custom-range-variant($name, $color);\n  }\n\n  @each $name, $color in $colors {\n    @include custom-range-variant($name, $color);\n  }\n}\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n\n//\n// Color system\n//\n\n// stylelint-disable\n$white:    #ffffff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n\n$grays: () !default;\n$grays: map-merge((\n        \"100\": $gray-100,\n        \"200\": $gray-200,\n        \"300\": $gray-300,\n        \"400\": $gray-400,\n        \"500\": $gray-500,\n        \"600\": $gray-600,\n        \"700\": $gray-700,\n        \"800\": $gray-800,\n        \"900\": $gray-900\n), $grays);\n\n$blue:    #007bff !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #e83e8c !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #28a745 !default;\n$teal:    #20c997 !default;\n$cyan:    #17a2b8 !default;\n\n$colors: () !default;\n$colors: map-merge((\n        \"blue\":       $blue,\n        \"indigo\":     $indigo,\n        \"purple\":     $purple,\n        \"pink\":       $pink,\n        \"red\":        $red,\n        \"orange\":     $orange,\n        \"yellow\":     $yellow,\n        \"green\":      $green,\n        \"teal\":       $teal,\n        \"cyan\":       $cyan,\n        \"white\":      $white,\n        \"gray\":       $gray-600,\n        \"gray-dark\":  $gray-800\n), $colors);\n\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-800 !default;\n\n$theme-colors: () !default;\n$theme-colors: map-merge((\n        \"primary\":    $primary,\n        \"secondary\":  $secondary,\n        \"success\":    $success,\n        \"info\":       $info,\n        \"warning\":    $warning,\n        \"danger\":     $danger,\n        \"light\":      $light,\n        \"dark\":       $dark\n), $theme-colors);\n// stylelint-enable\n\n// Set a specific jump point for requesting color jumps\n$theme-color-interval:      8% !default;\n\n// The yiq lightness value that determines when the lightness of color changes from \"dark\" to \"light\". Acceptable values are between 0 and 255.\n$yiq-contrasted-threshold: 150 !default;\n\n// Customize the light and dark text colors for use in our YIQ color contrast function.\n$yiq-text-dark: #1F2D3D !default;\n$yiq-text-light: $white !default;\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                                true !default;\n$enable-rounded:                              true !default;\n$enable-shadows:                              true !default;\n$enable-gradients:                            false !default;\n$enable-transitions:                          true !default;\n$enable-prefers-reduced-motion-media-query:   true !default;\n$enable-hover-media-query:                    false !default; // Deprecated, no longer affects any compiled CSS\n$enable-grid-classes:                         true !default;\n$enable-pointer-cursor-for-buttons:           true !default;\n$enable-print-styles:                         true !default;\n$enable-responsive-font-sizes:                false !default;\n$enable-validation-icons:                     true !default;\n$enable-deprecation-messages:                 true !default;\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// stylelint-disable\n$spacer: 1rem !default;\n$spacers: () !default;\n$spacers: map-merge((\n        0: 0,\n        1: ($spacer * .25),\n        2: ($spacer * .5),\n        3: $spacer,\n        4: ($spacer * 1.5),\n        5: ($spacer * 3)\n), $spacers);\n\n// This variable affects the `.h-*` and `.w-*` classes.\n$sizes: () !default;\n$sizes: map-merge((\n        25: 25%,\n        50: 50%,\n        75: 75%,\n        100: 100%\n), $sizes);\n// stylelint-enable\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   $white !default;\n$body-color:                $gray-900 !default;\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                theme-color(\"primary\") !default;\n$link-decoration:           none !default;\n$link-hover-color:          darken($link-color, 15%) !default;\n$link-hover-decoration:     none !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n$grid-breakpoints: (\n        xs: 0,\n        sm: 576px,\n        md: 768px,\n        lg: 992px,\n        xl: 1200px\n) !default;\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints);\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n$container-max-widths: (\n        sm: 540px,\n        md: 720px,\n        lg: 960px,\n        xl: 1140px\n) !default;\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           15px !default;\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n$line-height-lg:              1.5 !default;\n$line-height-sm:              1.5 !default;\n\n$border-width:                1px !default;\n$border-color:                $gray-300 !default;\n\n$border-radius:               .25rem !default;\n$border-radius-lg:            .3rem !default;\n$border-radius-sm:            .2rem !default;\n\n$component-active-color:      $white !default;\n$component-active-bg:         theme-color(\"primary\") !default;\n\n$caret-width:                 .3em !default;\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n$transition-collapse:         height .35s ease !default;\n\n\n// Fonts\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      \"Source Sans Pro\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n$font-family-base:            $font-family-sans-serif !default;\n// stylelint-enable value-keyword-case\n\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-lg:                ($font-size-base * 1.25) !default;\n$font-size-sm:                ($font-size-base * .875) !default;\n\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-bold:            700 !default;\n\n$font-weight-base:            $font-weight-normal !default;\n$line-height-base:            1.5 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n\n$headings-margin-bottom:      ($spacer / 2) !default;\n$headings-font-family:        inherit !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              inherit !default;\n\n$display1-size:               6rem !default;\n$display2-size:               5.5rem !default;\n$display3-size:               4.5rem !default;\n$display4-size:               3.5rem !default;\n\n$display1-weight:             300 !default;\n$display2-weight:             300 !default;\n$display3-weight:             300 !default;\n$display4-weight:             300 !default;\n$display-line-height:         $headings-line-height !default;\n\n$lead-font-size:              ($font-size-base * 1.25) !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             80% !default;\n\n$text-muted:                  $gray-600 !default;\n\n$blockquote-small-color:      $gray-600 !default;\n$blockquote-font-size:        ($font-size-base * 1.25) !default;\n\n$hr-border-color:             rgba($black, .1) !default;\n$hr-border-width:             $border-width !default;\n\n$mark-padding:                .2em !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$kbd-box-shadow:              inset 0 -.1rem 0 rgba($black, .25) !default;\n$nested-kbd-font-weight:      $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-bg:                     #fcf8e3 !default;\n\n$hr-margin-y:                 $spacer !default;\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n$table-cell-padding:          .75rem !default;\n$table-cell-padding-sm:       .3rem !default;\n\n$table-bg:                    transparent !default;\n$table-accent-bg:             rgba($black, .05) !default;\n$table-hover-bg:              rgba($black, .075) !default;\n$table-active-bg:             $table-hover-bg !default;\n\n$table-border-width:          $border-width !default;\n$table-border-color:          $gray-300 !default;\n\n$table-head-bg:               $gray-200 !default;\n$table-head-color:            $gray-700 !default;\n\n$table-dark-bg:               $gray-900 !default;\n$table-dark-accent-bg:        rgba($white, .05) !default;\n$table-dark-hover-bg:         rgba($white, .075) !default;\n$table-dark-border-color:     lighten($gray-900, 10%) !default;\n$table-dark-color:            $body-bg !default;\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:       .2rem !default;\n$input-btn-focus-color:       rgba($component-active-bg, .25) !default;\n$input-btn-focus-box-shadow:  0 0 0 $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-line-height-sm:    $line-height-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-line-height-lg:    $line-height-lg !default;\n\n$input-btn-border-width:      $border-width !default;\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-line-height:             $input-btn-line-height !default;\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-line-height-sm:          $input-btn-line-height-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-line-height-lg:          $input-btn-line-height-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              none !default;\n$btn-focus-width:             0 !default;\n$btn-focus-box-shadow:        none !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       none !default;\n\n$btn-link-disabled-color:     $gray-600 !default;\n\n$btn-block-spacing-y:         .5rem !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius !default;\n$btn-border-radius-lg:        $border-radius-lg !default;\n$btn-border-radius-sm:        $border-radius-sm !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n\n// Forms\n\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-line-height-sm:                  $input-btn-line-height-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-line-height-lg:                  $input-btn-line-height-lg !default;\n\n$input-bg:                              $white !default;\n$input-disabled-bg:                     $gray-200 !default;\n\n$input-color:                           $gray-700 !default;\n$input-border-color:                    $gray-400 !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      inset 0 0 0 rgba($black, 0) !default;\n\n$input-border-radius:                   $border-radius !default;\n$input-border-radius-lg:                $border-radius-lg !default;\n$input-border-radius-sm:                $border-radius-sm !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              lighten($component-active-bg, 25%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     0 !default;\n$input-focus-box-shadow:                none !default;\n\n$input-placeholder-color:               lighten($gray-600, 15%) !default;\n\n$input-height-border:                   $input-border-width * 2 !default;\n\n$input-height-inner:                    ($font-size-base * $input-btn-line-height) + ($input-btn-padding-y * 2) !default;\n$input-height-inner-half:               calc(#{$input-line-height * .5em} + #{$input-padding-y}) !default;\n$input-height-inner-quarter:            calc(#{$input-line-height * .25em} + #{$input-padding-y / 2}) !default;\n\n$input-height:                          calc(#{$input-height-inner} + #{$input-height-border}) !default;\n\n$input-height-inner-sm:                 ($font-size-sm * $input-btn-line-height-sm) + ($input-btn-padding-y-sm * 2) !default;\n$input-height-sm:                       calc(#{$input-height-inner-sm} + #{$input-height-border}) !default;\n\n$input-height-inner-lg:                 ($font-size-lg * $input-btn-line-height-lg) + ($input-btn-padding-y-lg * 2) !default;\n$input-height-lg:                       calc(#{$input-height-inner-lg} + #{$input-height-border}) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-text-margin-top:                  .25rem !default;\n\n$form-check-input-gutter:               1.25rem !default;\n$form-check-input-margin-y:             .3rem !default;\n$form-check-input-margin-x:             .25rem !default;\n\n$form-check-inline-margin-x:            .75rem !default;\n$form-check-inline-input-margin-x:      .3125rem !default;\n\n$form-group-margin-bottom:              1rem !default;\n\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  $gray-200 !default;\n$input-group-addon-border-color:        $input-border-color !default;\n\n$custom-control-gutter:                 .5rem !default;\n$custom-control-spacer-x:               1rem !default;\n\n$custom-control-indicator-size:         1rem !default;\n$custom-control-indicator-bg:           $gray-300 !default;\n$custom-control-indicator-bg-size:      50% 50% !default;\n$custom-control-indicator-box-shadow:   inset 0 .25rem .25rem rgba($black, .1) !default;\n\n$custom-control-indicator-disabled-bg:          $gray-200 !default;\n$custom-control-label-disabled-color:           $gray-600 !default;\n\n$custom-control-indicator-checked-color:        $component-active-color !default;\n$custom-control-indicator-checked-bg:           $component-active-bg !default;\n$custom-control-indicator-checked-disabled-bg:  rgba(theme-color(\"primary\"), .5) !default;\n$custom-control-indicator-checked-box-shadow:   none !default;\n\n$custom-control-indicator-focus-box-shadow:     0 0 0 1px $body-bg, $input-btn-focus-box-shadow !default;\n\n$custom-control-indicator-active-color:         $component-active-color !default;\n$custom-control-indicator-active-bg:            lighten($component-active-bg, 35%) !default;\n$custom-control-indicator-active-box-shadow:    none !default;\n\n$custom-checkbox-indicator-border-radius:       $border-radius !default;\n$custom-checkbox-indicator-icon-checked:        str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='#{$custom-control-indicator-checked-color}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$custom-checkbox-indicator-indeterminate-bg:    $component-active-bg !default;\n$custom-checkbox-indicator-indeterminate-color: $custom-control-indicator-checked-color !default;\n$custom-checkbox-indicator-icon-indeterminate:  str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath stroke='#{$custom-checkbox-indicator-indeterminate-color}' d='M0 2h4'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$custom-checkbox-indicator-indeterminate-box-shadow: none !default;\n\n$custom-radio-indicator-border-radius:          50% !default;\n$custom-radio-indicator-icon-checked:           str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='#{$custom-control-indicator-checked-color}'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$custom-select-padding-y:           .375rem !default;\n$custom-select-padding-x:          .75rem !default;\n$custom-select-height:              $input-height !default;\n$custom-select-indicator-padding:   1rem !default; // Extra padding to account for the presence of the background-image based indicator\n$custom-select-line-height:         $input-btn-line-height !default;\n$custom-select-color:               $input-color !default;\n$custom-select-disabled-color:      $gray-600 !default;\n$custom-select-bg:                  $white !default;\n$custom-select-disabled-bg:         $gray-200 !default;\n$custom-select-bg-size:             8px 10px !default; // In pixels because image dimensions\n$custom-select-indicator-color:     $gray-800 !default;\n$custom-select-indicator:           str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='#{$custom-select-indicator-color}' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$custom-select-border-width:        $input-btn-border-width !default;\n$custom-select-border-color:        $input-border-color !default;\n$custom-select-border-radius:       $border-radius !default;\n\n$custom-select-focus-border-color:  $input-focus-border-color !default;\n$custom-select-focus-box-shadow:    none !default;\n\n$custom-select-font-size-sm:        75% !default;\n$custom-select-height-sm:           $input-height-sm !default;\n\n$custom-select-font-size-lg:        125% !default;\n$custom-select-height-lg:           $input-height-lg !default;\n\n$custom-file-height:                $input-height !default;\n$custom-file-focus-border-color:    $input-focus-border-color !default;\n$custom-file-focus-box-shadow:      $custom-select-focus-box-shadow !default;\n\n$custom-file-padding-y:             $input-btn-padding-y !default;\n$custom-file-padding-x:             $input-btn-padding-x !default;\n$custom-file-line-height:           $input-btn-line-height !default;\n$custom-file-color:                 $input-color !default;\n$custom-file-bg:                    $input-bg !default;\n$custom-file-border-width:          $input-btn-border-width !default;\n$custom-file-border-color:          $input-border-color !default;\n$custom-file-border-radius:         $input-border-radius !default;\n$custom-file-box-shadow:            $custom-select-focus-box-shadow !default;\n$custom-file-button-color:          $custom-file-color !default;\n$custom-file-button-bg:             $input-group-addon-bg !default;\n$custom-file-text: (\n        en: \"Browse\"\n) !default;\n\n$custom-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-btn-focus-box-shadow !default;\n\n\n// Form validation\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $small-font-size !default;\n$form-feedback-valid-color:         theme-color(\"success\") !default;\n$form-feedback-invalid-color:       theme-color(\"danger\") !default;\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-bg:                       $white !default;\n$dropdown-border-color:             rgba($black, .15) !default;\n$dropdown-border-radius:            $border-radius !default;\n$dropdown-border-width:             $border-width !default;\n$dropdown-divider-bg:               $gray-200 !default;\n$dropdown-box-shadow:               0 .5rem 1rem rgba($black, .175) !default;\n\n$dropdown-link-color:               $gray-900 !default;\n$dropdown-link-hover-color:         darken($gray-900, 5%) !default;\n$dropdown-link-hover-bg:            $gray-100 !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      $gray-600 !default;\n\n$dropdown-item-padding-y:           .25rem !default;\n$dropdown-item-padding-x:           1rem !default;\n\n$dropdown-header-color:             $gray-600 !default;\n\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-modal-backdrop:             1040 !default;\n$zindex-modal:                      1050 !default;\n$zindex-popover:                    1060 !default;\n$zindex-tooltip:                    1070 !default;\n\n// Navs\n\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-disabled-color:           $gray-600 !default;\n\n$nav-tabs-border-color:             $gray-300 !default;\n$nav-tabs-border-width:             $border-width !default;\n$nav-tabs-border-radius:            $border-radius !default;\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        $gray-700 !default;\n$nav-tabs-link-active-bg:           $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           $border-radius !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n\n// Navbar\n\n$navbar-padding-y:                  ($spacer / 2) !default;\n$navbar-padding-x:                  ($spacer / 2) !default;\n\n$navbar-nav-link-padding-x:         1rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   ($font-size-base * $line-height-base + $nav-link-padding-y * 2) !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) / 2 !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n\n$navbar-dark-color:                 rgba($white, .75) !default;\n$navbar-dark-hover-color:           rgba($white, 1) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg:       str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#{$navbar-dark-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n\n$navbar-light-color:                rgba($black, .5) !default;\n$navbar-light-hover-color:          rgba($black, .7) !default;\n$navbar-light-active-color:         rgba($black, .9) !default;\n$navbar-light-disabled-color:       rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg:      str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#{$navbar-light-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n// Pagination\n\n$pagination-padding-y:              .5rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n$pagination-line-height:            1.25 !default;\n\n$pagination-color:                  $link-color !default;\n$pagination-bg:                     $white !default;\n$pagination-border-width:           $border-width !default;\n$pagination-border-color:           $gray-300 !default;\n\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n\n$pagination-hover-color:            $link-hover-color !default;\n$pagination-hover-bg:               $gray-200 !default;\n$pagination-hover-border-color:     $gray-300 !default;\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $pagination-active-bg !default;\n\n$pagination-disabled-color:         $gray-600 !default;\n$pagination-disabled-bg:            $white !default;\n$pagination-disabled-border-color:  $gray-300 !default;\n\n\n// Jumbotron\n\n$jumbotron-padding:                 2rem !default;\n$jumbotron-bg:                      $gray-200 !default;\n\n\n// Cards\n\n$card-spacer-y:                     .75rem !default;\n$card-spacer-x:                     1.25rem !default;\n$card-border-width:                 0 !default; //$border-width !default;\n$card-border-radius:                $border-radius !default;\n$card-border-color:                 rgba($black, .125) !default;\n$card-inner-border-radius:          calc(#{$card-border-radius} - #{$card-border-width}) !default;\n$card-cap-bg:                       rgba($black, .03) !default;\n$card-bg:                           $white !default;\n\n$card-img-overlay-padding:          1.25rem !default;\n\n$card-group-margin:                 ($grid-gutter-width / 2) !default;\n$card-deck-margin:                  $card-group-margin !default;\n\n$card-columns-count:                3 !default;\n$card-columns-gap:                  1.25rem !default;\n$card-columns-margin:               $card-spacer-y !default;\n\n\n// Tooltips\n\n$tooltip-font-size:           $font-size-sm !default;\n$tooltip-max-width:           200px !default;\n$tooltip-color:               $white !default;\n$tooltip-bg:                  $black !default;\n$tooltip-border-radius:        $border-radius !default;\n$tooltip-opacity:             .9 !default;\n$tooltip-padding-y:           .25rem !default;\n$tooltip-padding-x:           .5rem !default;\n$tooltip-margin:              0 !default;\n\n$tooltip-arrow-width:         .8rem !default;\n$tooltip-arrow-height:        .4rem !default;\n$tooltip-arrow-color:         $tooltip-bg !default;\n\n// Form tooltips must come after regular tooltips\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   $line-height-base !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n\n// Popovers\n\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        $white !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              $border-width !default;\n$popover-border-color:              rgba($black, .2) !default;\n$popover-border-radius:             $border-radius-lg !default;\n$popover-box-shadow:                0 .25rem .5rem rgba($black, .2) !default;\n\n$popover-header-bg:                 darken($popover-bg, 3%) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          .75rem !default;\n\n$popover-body-color:                $body-color !default;\n$popover-body-padding-y:            $popover-header-padding-y !default;\n$popover-body-padding-x:            $popover-header-padding-x !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n$popover-arrow-color:               $popover-bg !default;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05) !default;\n\n\n// Badges\n\n$badge-font-size:                   75% !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-padding-y:                   .25em !default;\n$badge-padding-x:                   .4em !default;\n$badge-border-radius:               $border-radius !default;\n\n$badge-pill-padding-x:              .6em !default;\n// Use a higher than normal value to ensure completely rounded edges when\n// customizing padding or font-size on labels.\n$badge-pill-border-radius:          10rem !default;\n\n\n// Modals\n\n// Padding applied to the modal body\n$modal-inner-padding:         1rem !default;\n\n$modal-dialog-margin:         .5rem !default;\n$modal-dialog-margin-y-sm-up: 1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-bg:               $white !default;\n$modal-content-border-color:     rgba($black, .2) !default;\n$modal-content-border-width:     $border-width !default;\n$modal-content-box-shadow-xs:    0 .25rem .5rem rgba($black, .5) !default;\n$modal-content-box-shadow-sm-up: 0 .5rem 1rem rgba($black, .5) !default;\n\n$modal-backdrop-bg:           $black !default;\n$modal-backdrop-opacity:      .5 !default;\n$modal-header-border-color:   $gray-200 !default;\n$modal-footer-border-color:   $modal-header-border-color !default;\n$modal-header-border-width:   $modal-content-border-width !default;\n$modal-footer-border-width:   $modal-header-border-width !default;\n$modal-header-padding:        1rem !default;\n\n$modal-lg:                          800px !default;\n$modal-md:                          500px !default;\n$modal-sm:                          300px !default;\n\n$modal-transition:                  transform .3s ease-out !default;\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n$alert-padding-y:                   .75rem !default;\n$alert-padding-x:                   1.25rem !default;\n$alert-margin-bottom:               1rem !default;\n$alert-border-radius:               $border-radius !default;\n$alert-link-font-weight:            $font-weight-bold !default;\n$alert-border-width:                $border-width !default;\n\n$alert-bg-level:                    -10 !default;\n$alert-border-level:                -9 !default;\n$alert-color-level:                 6 !default;\n\n\n// Progress bars\n\n$progress-height:                   1rem !default;\n$progress-font-size:                ($font-size-base * .75) !default;\n$progress-bg:                       $gray-200 !default;\n$progress-border-radius:            $border-radius !default;\n$progress-box-shadow:               inset 0 .1rem .1rem rgba($black, .1) !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   theme-color(\"primary\") !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n\n// List group\n\n$list-group-bg:                     $white !default;\n$list-group-border-color:           rgba($black, .125) !default;\n$list-group-border-width:           $border-width !default;\n$list-group-border-radius:          $border-radius !default;\n\n$list-group-item-padding-y:         .75rem !default;\n$list-group-item-padding-x:         1.25rem !default;\n\n$list-group-hover-bg:               $gray-100 !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         $gray-600 !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           $gray-700 !default;\n$list-group-action-hover-color:     $list-group-action-color !default;\n\n$list-group-action-active-color:    $body-color !default;\n$list-group-action-active-bg:       $gray-200 !default;\n\n\n// Image thumbnails\n\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      $body-bg !default;\n$thumbnail-border-width:            $border-width !default;\n$thumbnail-border-color:            $gray-300 !default;\n$thumbnail-border-radius:           $border-radius !default;\n$thumbnail-box-shadow:              0 1px 2px rgba($black, .075) !default;\n\n\n// Figures\n\n$figure-caption-font-size:          90% !default;\n$figure-caption-color:              $gray-600 !default;\n\n\n// Breadcrumbs\n\n$breadcrumb-padding-y:              .75rem !default;\n$breadcrumb-padding-x:              1rem !default;\n$breadcrumb-item-padding:           .5rem !default;\n\n$breadcrumb-margin-bottom:          1rem !default;\n\n$breadcrumb-bg:                     $gray-200 !default;\n$breadcrumb-divider-color:          $gray-600 !default;\n$breadcrumb-active-color:           $gray-600 !default;\n$breadcrumb-divider:                \"/\" !default;\n\n\n// Carousel\n\n$carousel-control-color:            $white !default;\n$carousel-control-width:            15% !default;\n$carousel-control-opacity:          .5 !default;\n\n$carousel-indicator-width:          30px !default;\n$carousel-indicator-height:         3px !default;\n$carousel-indicator-spacer:         3px !default;\n$carousel-indicator-active-bg:      $white !default;\n\n$carousel-caption-width:            70% !default;\n$carousel-caption-color:            $white !default;\n\n$carousel-control-icon-width:       20px !default;\n\n$carousel-control-prev-icon-bg:     str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3E%3Cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$carousel-control-next-icon-bg:     str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3E%3Cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$carousel-transition:               transform .6s ease !default;\n\n\n// Close\n\n$close-font-size:                   $font-size-base * 1.5 !default;\n$close-font-weight:                 $font-weight-bold !default;\n$close-color:                       $black !default;\n$close-text-shadow:                 0 1px 0 $white !default;\n\n// Code\n\n$code-font-size:                    87.5% !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .2rem !default;\n$kbd-padding-x:                     .4rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         $white !default;\n$kbd-bg:                            $gray-900 !default;\n\n$pre-color:                         $gray-900 !default;\n$pre-scrollable-max-height:         340px !default;\n\n\n// Printing\n$print-page-size:                   a3 !default;\n$print-body-min-width:              map-get($grid-breakpoints, \"lg\") !default;\n", "// stylelint-disable property-blacklist\n// Single side border-radius\n\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: $radius;\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n    border-top-right-radius: $radius;\n  }\n}\n\n@mixin border-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: $radius;\n    border-bottom-right-radius: $radius;\n  }\n}\n\n@mixin border-bottom-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: $radius;\n    border-bottom-left-radius: $radius;\n  }\n}\n\n@mixin border-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n    border-bottom-left-radius: $radius;\n  }\n}\n\n@mixin border-top-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n  }\n}\n\n@mixin border-top-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: $radius;\n  }\n}\n\n@mixin border-bottom-right-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: $radius;\n  }\n}\n\n@mixin border-bottom-left-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: $radius;\n  }\n}\n", "// stylelint-disable property-blacklist, scss/dollar-variable-default\n\n// SCSS RFS mixin\n//\n// Automated font-resizing\n//\n// See https://github.com/twbs/rfs\n\n// Configuration\n\n// Base font size\n$rfs-base-font-size: 1.25rem !default;\n$rfs-font-size-unit: rem !default;\n\n// Breakpoint at where font-size starts decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n// Resize font-size based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != \"number\" or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-responsive-font-sizes to false\n$enable-responsive-font-sizes: true !default;\n\n// Cache $rfs-base-font-size unit\n$rfs-base-font-size-unit: unit($rfs-base-font-size);\n\n// Remove px-unit from $rfs-base-font-size for calculations\n@if $rfs-base-font-size-unit == \"px\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1);\n}\n@else if $rfs-base-font-size-unit == \"rem\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1 / $rfs-rem-value);\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == \"px\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == \"rem\" or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1 / $rfs-rem-value);\n}\n\n// Responsive font-size mixin\n@mixin rfs($fs, $important: false) {\n  // Cache $fs unit\n  $fs-unit: if(type-of($fs) == \"number\", unit($fs), false);\n\n  // Add !important suffix if needed\n  $rfs-suffix: if($important, \" !important\", \"\");\n\n  // If $fs isn't a number (like inherit) or $fs has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n  @if not $fs-unit or $fs-unit != \"\" and $fs-unit != \"px\" and $fs-unit != \"rem\" or $fs == 0 {\n    font-size: #{$fs}#{$rfs-suffix};\n  }\n  @else {\n    // Variables for storing static and fluid rescaling\n    $rfs-static: null;\n    $rfs-fluid: null;\n\n    // Remove px-unit from $fs for calculations\n    @if $fs-unit == \"px\" {\n      $fs: $fs / ($fs * 0 + 1);\n    }\n    @else if $fs-unit == \"rem\" {\n      $fs: $fs / ($fs * 0 + 1 / $rfs-rem-value);\n    }\n\n    // Set default font-size\n    @if $rfs-font-size-unit == rem {\n      $rfs-static: #{$fs / $rfs-rem-value}rem#{$rfs-suffix};\n    }\n    @else if $rfs-font-size-unit == px {\n      $rfs-static: #{$fs}px#{$rfs-suffix};\n    }\n    @else {\n      @error \"`#{$rfs-font-size-unit}` is not a valid unit for $rfs-font-size-unit. Use `px` or `rem`.\";\n    }\n\n    // Only add media query if font-size is bigger as the minimum font-size\n    // If $rfs-factor == 1, no rescaling will take place\n    @if $fs > $rfs-base-font-size and $enable-responsive-font-sizes {\n      $min-width: null;\n      $variable-unit: null;\n\n      // Calculate minimum font-size for given font-size\n      $fs-min: $rfs-base-font-size + ($fs - $rfs-base-font-size) / $rfs-factor;\n\n      // Calculate difference between given font-size and minimum font-size for given font-size\n      $fs-diff: $fs - $fs-min;\n\n      // Base font-size formatting\n      // No need to check if the unit is valid, because we did that before\n      $min-width: if($rfs-font-size-unit == rem, #{$fs-min / $rfs-rem-value}rem, #{$fs-min}px);\n\n      // If two-dimensional, use smallest of screen width and height\n      $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n      // Calculate the variable width between 0 and $rfs-breakpoint\n      $variable-width: #{$fs-diff * 100 / $rfs-breakpoint}#{$variable-unit};\n\n      // Set the calculated font-size.\n      $rfs-fluid: calc(#{$min-width} + #{$variable-width}) #{$rfs-suffix};\n    }\n\n    // Rendering\n    @if $rfs-fluid == null {\n      // Only render static font-size if no fluid font-size is available\n      font-size: $rfs-static;\n    }\n    @else {\n      $mq-value: null;\n\n      // RFS breakpoint formatting\n      @if $rfs-breakpoint-unit == em or $rfs-breakpoint-unit == rem {\n        $mq-value: #{$rfs-breakpoint / $rfs-rem-value}#{$rfs-breakpoint-unit};\n      }\n      @else if $rfs-breakpoint-unit == px {\n        $mq-value: #{$rfs-breakpoint}px;\n      }\n      @else {\n        @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n      }\n\n      @if $rfs-class == \"disable\" {\n        // Adding an extra class increases specificity,\n        // which prevents the media query to override the font size\n        &,\n        .disable-responsive-font-size &,\n        &.disable-responsive-font-size {\n          font-size: $rfs-static;\n        }\n      }\n      @else {\n        font-size: $rfs-static;\n      }\n\n      @if $rfs-two-dimensional {\n        @media (max-width: #{$mq-value}), (max-height: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n      @else {\n        @media (max-width: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n    }\n  }\n}\n\n// The font-size & responsive-font-size mixin uses RFS to rescale font sizes\n@mixin font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n\n@mixin responsive-font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n", "// Gradients\n\n@mixin gradient-bg($color) {\n  @if $enable-gradients {\n    background: $color linear-gradient(180deg, mix($body-bg, $color, 15%), $color) repeat-x;\n  } @else {\n    background-color: $color;\n  }\n}\n\n// Horizontal gradient, from left to right\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-x($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to right, $start-color $start-percent, $end-color $end-percent);\n  background-repeat: repeat-x;\n}\n\n// Vertical gradient, from top to bottom\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-y($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to bottom, $start-color $start-percent, $end-color $end-percent);\n  background-repeat: repeat-x;\n}\n\n@mixin gradient-directional($start-color: $gray-700, $end-color: $gray-800, $deg: 45deg) {\n  background-image: linear-gradient($deg, $start-color, $end-color);\n  background-repeat: repeat-x;\n}\n@mixin gradient-x-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient(to right, $start-color, $mid-color $color-stop, $end-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-y-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient($start-color, $mid-color $color-stop, $end-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-radial($inner-color: $gray-700, $outer-color: $gray-800) {\n  background-image: radial-gradient(circle, $inner-color, $outer-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-striped($color: rgba($white, .15), $angle: 45deg) {\n  background-image: linear-gradient($angle, $color 25%, transparent 25%, transparent 50%, $color 50%, $color 75%, transparent 75%, transparent);\n}\n", "//\n// Mixins: Custom Forms\n//\n\n// Custom Switch Variant\n@mixin custom-switch-variant($name, $color) {\n  &.custom-switch-off-#{$name} {\n    & .custom-control-input ~ .custom-control-label::before {\n      background: #{$color};\n      border-color: darken($color, 20%);\n    }\n\n    & .custom-control-input:focus ~ .custom-control-label::before {\n      box-shadow: 0 0 0 1px $body-bg, 0 0 0 2px rgba($color, .25);\n    }\n\n    & .custom-control-input ~ .custom-control-label::after {\n      background: darken($color, 25%);\n    }\n  }\n\n  &.custom-switch-on-#{$name} {\n    & .custom-control-input:checked ~ .custom-control-label::before {\n      background: #{$color};\n      border-color: darken($color, 20%);\n    }\n\n    & .custom-control-input:checked:focus ~ .custom-control-label::before {\n      box-shadow: 0 0 0 1px $body-bg, 0 0 0 2px rgba($color, .25);\n    }\n\n    & .custom-control-input:checked ~ .custom-control-label::after {\n      background: lighten($color, 30%);\n    }\n  }\n}\n\n// Custom Range Variant\n@mixin custom-range-variant($name, $color) {\n  &.custom-range-#{$name} {\n    &:focus {\n      outline: none;\n\n      &::-webkit-slider-thumb {\n        box-shadow: 0 0 0 1px $body-bg, 0 0 0 2px rgba($color, .25);\n      }\n\n      &::-moz-range-thumb     {\n        box-shadow: 0 0 0 1px $body-bg, 0 0 0 2px rgba($color, .25);\n      }\n\n      &::-ms-thumb            {\n        box-shadow: 0 0 0 1px $body-bg, 0 0 0 2px rgba($color, .25);\n      }\n    }\n\n    &::-webkit-slider-thumb {\n      background-color: $color;\n\n      &:active {\n        background-color: lighten($color, 35%);\n      }\n    }\n\n    &::-moz-range-thumb {\n      background-color: $color;\n\n      &:active {\n        background-color: lighten($color, 35%);\n      }\n    }\n\n    &::-ms-thumb {\n      background-color: $color;\n\n      &:active {\n        background-color: lighten($color, 35%);\n      }\n    }\n  }\n}\n", "//\n// Core: Variables\n//\n\n// COLORS\n// --------------------------------------------------------\n$blue: #0073b7 !default;\n$lightblue: #3c8dbc !default;\n$navy: #001f3f !default;\n$teal: #39cccc !default;\n$olive: #3d9970 !default;\n$lime: #01ff70 !default;\n$orange: #ff851b !default;\n$fuchsia: #f012be !default;\n$purple: #605ca8 !default;\n$maroon: #d81b60 !default;\n$black: #111 !default;\n$gray-x-light: #d2d6de !default;\n\n$colors: map-merge((\n    'lightblue': $lightblue,\n    'navy': $navy,\n    'olive': $olive,\n    'lime': $lime,\n    'fuchsia': $fuchsia,\n    'maroon': $maroon,\n), $colors);\n\n// LAYOUT\n// --------------------------------------------------------\n\n$font-size-root: 1rem !default;\n\n// Sidebar\n$sidebar-width: 250px !default;\n$sidebar-padding-x: 0.5rem !default;\n$sidebar-padding-y: 0 !default;\n\n// Boxed layout maximum width\n$boxed-layout-max-width: 1250px !default;\n\n// When to show the smaller logo\n$screen-header-collapse: map-get($grid-breakpoints, md) !default;\n\n// Body background (Affects main content background only)\n$main-bg: #f4f6f9 !default;\n\n// Content padding\n$content-padding-y: 0 !default;\n$content-padding-x: $navbar-padding-x !default;\n\n// IMAGE SIZES\n// --------------------------------------------------------\n$img-size-sm: 1.875rem !default;\n$img-size-md: 3.75rem !default;\n$img-size-lg: 6.25rem !default;\n$img-size-push: .625rem !default;\n\n// MAIN HEADER\n// --------------------------------------------------------\n$main-header-bottom-border-width: $border-width !default;\n$main-header-bottom-border-color: $gray-300 !default;\n$main-header-bottom-border: $main-header-bottom-border-width solid $main-header-bottom-border-color !default;\n$main-header-link-padding-y: $navbar-padding-y !default;\n$main-header-link-padding-x: $navbar-padding-x !default;\n$main-header-brand-padding-y: $navbar-brand-padding-y !default;\n$main-header-brand-padding-x: $navbar-padding-x !default;\n$main-header-height-inner: ($nav-link-height + ($main-header-link-padding-y * 2)) !default;\n$main-header-height: calc(#{$main-header-height-inner} + #{$main-header-bottom-border-width}) !default;\n$nav-link-sm-padding-y: .35rem !default;\n$nav-link-sm-height: ($font-size-sm * $line-height-sm + $nav-link-sm-padding-y * 1.785) !default;\n$main-header-height-sm-inner: ($nav-link-sm-height + ($main-header-link-padding-y * 2)) !default;\n$main-header-height-sm: calc(#{$main-header-height-sm-inner} + #{$main-header-bottom-border-width}) !default;\n\n\n// Main header skins\n$main-header-dark-form-control-bg: hsla(100, 100%, 100%, 0.2) !default;\n$main-header-dark-form-control-focused-bg: hsla(100, 100%, 100%, 0.6) !default;\n$main-header-dark-form-control-focused-color: $gray-800 !default;\n$main-header-dark-form-control-border: 0 !default;\n$main-header-dark-form-control-focused-border: 0 !default;\n$main-header-dark-placeholder-color: hsla(100, 100%, 100%, 0.6) !default;\n\n$main-header-light-form-control-bg: darken($gray-100, 2%) !default;\n$main-header-light-form-control-focused-bg: $gray-200 !default;\n$main-header-light-form-control-focused-color: $gray-800 !default;\n$main-header-light-form-control-border: 0 !default;\n$main-header-light-form-control-focused-border: 0 !default;\n$main-header-light-placeholder-color: hsla(0, 0%, 0%, 0.6) !default;\n\n// MAIN FOOTER\n// --------------------------------------------------------\n$main-footer-padding: 1rem !default;\n$main-footer-padding-sm: $main-footer-padding * .812 !default;\n$main-footer-border-top-width: 1px !default;\n$main-footer-border-top-color: $gray-300 !default;\n$main-footer-border-top: $main-footer-border-top-width solid $main-footer-border-top-color !default;\n$main-footer-height-inner: (($font-size-root * $line-height-base) + ($main-footer-padding * 2)) !default;\n$main-footer-height: calc(#{$main-footer-height-inner} + #{$main-footer-border-top-width}) !default;\n$main-footer-height-sm-inner: (($font-size-sm * $line-height-base) + ($main-footer-padding-sm * 2)) !default;\n$main-footer-height-sm: calc(#{$main-footer-height-sm-inner} + #{$main-footer-border-top-width}) !default;\n$main-footer-bg: $white !default;\n\n// SIDEBAR SKINS\n// --------------------------------------------------------\n\n// Dark sidebar\n$sidebar-dark-bg: $dark !default;\n$sidebar-dark-hover-bg: hsla(100, 100%, 100%, 0.1) !default;\n$sidebar-dark-color: #C2C7D0 !default;\n$sidebar-dark-hover-color: $white !default;\n$sidebar-dark-active-color: $white !default;\n$sidebar-dark-submenu-bg: transparent !default;\n$sidebar-dark-submenu-color: #C2C7D0 !default;\n$sidebar-dark-submenu-hover-color: $white !default;\n$sidebar-dark-submenu-hover-bg: $sidebar-dark-hover-bg !default;\n$sidebar-dark-submenu-active-color: $sidebar-dark-bg !default;\n$sidebar-dark-submenu-active-bg: hsla(100, 100%, 100%, 0.9) !default;\n$sidebar-dark-header-color: $white !default;\n\n// Light sidebar\n$sidebar-light-bg: $white !default;\n$sidebar-light-hover-bg: rgba($black, .1) !default;\n$sidebar-light-color: $gray-800 !default;\n$sidebar-light-hover-color: $gray-900 !default;\n$sidebar-light-active-color: $black !default;\n$sidebar-light-submenu-bg: transparent !default;\n$sidebar-light-submenu-color: #777 !default;\n$sidebar-light-submenu-hover-color: #000 !default;\n$sidebar-light-submenu-hover-bg: $sidebar-light-hover-bg !default;\n$sidebar-light-submenu-active-color: $sidebar-light-hover-color !default;\n$sidebar-light-submenu-active-bg: $sidebar-light-submenu-hover-bg !default;\n$sidebar-light-header-color: $gray-800 !default;\n\n// SIDEBAR MINI\n// --------------------------------------------------------\n$sidebar-mini-width: ($nav-link-padding-x + $sidebar-padding-x + .8rem) * 2 !default;\n$sidebar-nav-icon-width: $sidebar-mini-width - (($sidebar-padding-x + $nav-link-padding-x) * 2) !default;\n$sidebar-user-image-width: $sidebar-nav-icon-width + ($nav-link-padding-x / 2) !default;\n\n// CONTROL SIDEBAR\n// --------------------------------------------------------\n$control-sidebar-width: $sidebar-width !default;\n\n// Cards\n// --------------------------------------------------------\n$card-border-color: $gray-100 !default;\n$card-dark-border-color: lighten($gray-900, 10%) !default;\n$card-shadow: 0 0 1px rgba(0, 0, 0, .125), 0 1px 3px rgba(0, 0, 0, .2) !default;\n$card-title-font-size: 1.1rem !default;\n$card-title-font-size-sm: 1rem !default;\n$card-title-font-weight: $font-weight-normal !default;\n$card-nav-link-padding-sm-y: .4rem !default;\n$card-nav-link-padding-sm-x: .8rem !default;\n$card-img-size: $img-size-sm !default;\n\n// PROGRESS BARS\n// --------------------------------------------------------\n$progress-bar-border-radius: 1px !default;\n$progress-bar-sm-border-radius: 1px !default;\n$progress-bar-xs-border-radius: 1px !default;\n\n// DIRECT CHAT\n// --------------------------------------------------------\n$direct-chat-height: 250px !default;\n$direct-chat-default-msg-bg: $gray-x-light !default;\n$direct-chat-default-font-color: #444 !default;\n$direct-chat-default-msg-border-color: $gray-x-light !default;\n\n// CHAT WIDGET\n// --------------------------------------------------------\n$attachment-border-radius: 3px !default;\n\n// Z-INDEX\n// --------------------------------------------------------\n$zindex-main-header: $zindex-fixed + 4 !default;\n$zindex-main-sidebar: $zindex-fixed + 8 !default;\n$zindex-main-footer: $zindex-fixed + 2 !default;\n$zindex-control-sidebar: $zindex-fixed + 1 !default;\n$zindex-sidebar-mini-links: 010 !default;\n$zindex-toasts: $zindex-main-sidebar + 2 !default;\n\n// TRANSITIONS SETTINGS\n// --------------------------------------------------------\n\n// Transition global options\n$transition-speed: 0.3s !default;\n$transition-fn: ease-in-out !default;\n\n// TEXT\n// --------------------------------------------------------\n$font-size-xs: ($font-size-base * .75) !default;\n$font-size-xl: ($font-size-base * 2) !default;\n\n\n// BUTTON\n// --------------------------------------------------------\n$button-default-background-color: $gray-100 !default;\n$button-default-color: #444 !default;\n$button-default-border-color: #ddd !default;\n\n$button-padding-y-xs: .125rem !default;\n$button-padding-x-xs: .25rem !default;\n$button-line-height-xs: $line-height-sm !default;\n$button-font-size-xs: ($font-size-base * .75) !default;\n$button-border-radius-xs: .15rem !default;\n\n  \n// ELEVATION\n// --------------------------------------------------------\n$elevations: ();\n$elevations: map-merge((\n    1: unquote('0 1px 3px ' + rgba($black, 0.12) + ', 0 1px 2px ' + rgba($black, 0.24)),\n    2: unquote('0 3px 6px ' + rgba($black, 0.16) + ', 0 3px 6px ' + rgba($black, 0.23)),\n    3: unquote('0 10px 20px ' + rgba($black, 0.19) + ', 0 6px 6px ' + rgba($black, 0.23)),\n    4: unquote('0 14px 28px ' + rgba($black, 0.25) + ', 0 10px 10px ' + rgba($black, 0.22)),\n    5: unquote('0 19px 38px ' + rgba($black, 0.30) + ', 0 15px 12px ' + rgba($black, 0.22)),\n), $elevations);\n  \n// RIBBON\n// --------------------------------------------------------\n$ribbon-border-size: 3px !default;\n$ribbon-line-height: 100% !default;\n$ribbon-padding: .375rem 0 !default;\n$ribbon-font-size: .8rem !default;\n$ribbon-width: 90px !default;\n$ribbon-wrapper-size: 70px !default;\n$ribbon-top: 10px !default;\n$ribbon-right: -2px !default;\n$ribbon-lg-wrapper-size: 120px !default;\n$ribbon-lg-width: 160px !default;\n$ribbon-lg-top: 26px !default;\n$ribbon-lg-right: 0px !default;\n$ribbon-xl-wrapper-size: 180px !default;\n$ribbon-xl-width: 240px !default;\n$ribbon-xl-top: 47px !default;\n$ribbon-xl-right: 4px !default;\n", "//\n// Component: Progress Bar\n//\n\n//General CSS\n.progress {\n  @include box-shadow(none);\n  @include border-radius($progress-bar-border-radius);\n\n  // Vertical bars\n  &.vertical {\n    display: inline-block;\n    height: 200px;\n    margin-right: 10px;\n    position: relative;\n    width: 30px;\n\n    > .progress-bar {\n      bottom: 0;\n      position: absolute;\n      width: 100%;\n    }\n\n    //Sizes\n    &.sm,\n    &.progress-sm {\n      width: 20px;\n    }\n\n    &.xs,\n    &.progress-xs {\n      width: 10px;\n    }\n\n    &.xxs,\n    &.progress-xxs {\n      width: 3px;\n    }\n  }\n}\n\n.progress-group {\n  margin-bottom: map-get($spacers, 2);\n}\n\n// size variation\n.progress-sm {\n  height: 10px;\n}\n\n.progress-xs {\n  height: 7px;\n}\n\n.progress-xxs {\n  height: 3px;\n}\n\n// Remove margins from progress bars when put in a table\n.table {\n  tr > td {\n    .progress {\n      margin: 0;\n    }\n  }\n}\n", "@mixin box-shadow($shadow...) {\n  @if $enable-shadows {\n    $result: ();\n\n    @if (length($shadow) == 1) {\n      // We can pass `@include box-shadow(none);`\n      $result: $shadow;\n    } @else {\n      // Filter to avoid invalid properties for example `box-shadow: none, 1px 1px black;`\n      @for $i from 1 through length($shadow) {\n        @if nth($shadow, $i) != \"none\" {\n          $result: append($result, nth($shadow, $i), \"comma\");\n        }\n      }\n    }\n    @if (length($result) > 0) {\n      box-shadow: $result;\n    }\n  }\n}\n", "//\n// Mixins: Cards Variant\n//\n\n@mixin cards-variant($name, $color) {\n  .card-#{$name} {\n    &:not(.card-outline) {\n      > .card-header {\n        background-color: $color;\n\n        &,\n        a {\n          color: color-yiq($color);\n        }\n\n        a.active {\n          color: color-yiq($white);\n        }\n      }\n    }\n\n    &.card-outline {\n      border-top: 3px solid $color;\n    }\n\n    &.card-outline-tabs {\n      > .card-header {\n        a {\n          &:hover {\n            border-top: 3px solid $nav-tabs-border-color;\n          }\n\n          &.active {\n            border-top: 3px solid $color;\n          }\n        }\n      }\n    }\n  }\n\n  .bg-#{$name},\n  .bg-gradient-#{$name},\n  .card-#{$name}:not(.card-outline) {\n    .btn-tool {\n      color: rgba(color-yiq($color), 0.8);\n\n      &:hover {\n        color: color-yiq($color);\n      }\n    }\n  }\n\n  .card.bg-#{$name},\n  .card.bg-gradient-#{$name} {\n    .bootstrap-datetimepicker-widget {\n      .table td,\n      .table th {\n        border: none;\n      }\n\n      table thead tr:first-child th:hover,\n      table td.day:hover,\n      table td.hour:hover,\n      table td.minute:hover,\n      table td.second:hover {\n        background: darken($color, 8%);\n        color: color-yiq($color);\n      }\n\n      table td.today::before {\n        border-bottom-color: color-yiq($color);\n      }\n\n      table td.active,\n      table td.active:hover {\n        background: lighten($color, 10%);\n        color: color-yiq($color);\n      }\n    }\n  }\n}\n\n", "//\n// Component: Cards\n//\n\n// Color variants\n@each $name, $color in $theme-colors {\n  @include cards-variant($name, $color);\n}\n\n@each $name, $color in $colors {\n  @include cards-variant($name, $color);\n}\n\n.card {\n  @include box-shadow($card-shadow);\n  margin-bottom: map-get($spacers, 3);\n\n  &.bg-dark {\n    .card-header {\n      border-color: $card-dark-border-color;\n    }\n\n    &,\n    .card-body {\n      color: $white;\n    }\n  }\n\n  &.maximized-card {\n    height: 100% !important;\n    left: 0;\n    max-height: 100% !important;\n    max-width: 100% !important;\n    position: fixed;\n    top: 0;\n    width: 100% !important;\n    z-index: 9999;\n\n    &.was-collapsed .card-body {\n      display: block !important;\n    }\n\n    [data-widget='collapse'] {\n      display: none;\n    }\n\n    .card-header,\n    .card-footer {\n      @include border-radius(0 !important);\n    }\n  }\n\n  // collapsed mode\n  &.collapsed-card {\n    .card-body,\n    .card-footer {\n      display: none;\n    }\n  }\n\n  .nav.flex-column {\n    > li {\n      border-bottom: 1px solid $card-border-color;\n      margin: 0;\n\n      &:last-of-type {\n        border-bottom: 0;\n      }\n    }\n  }\n\n  // fixed height to 300px\n  &.height-control {\n    .card-body {\n      max-height: 300px;\n      overflow: auto;\n    }\n  }\n\n  .border-right {\n    border-right: 1px solid $card-border-color;\n  }\n\n  .border-left {\n    border-left: 1px solid $card-border-color;\n  }\n\n  &.card-tabs {\n    &:not(.card-outline) {\n      & > .card-header {\n        border-bottom: 0;\n\n        .nav-item {\n          &:first-child .nav-link {\n            margin-left: -1px;\n          }\n        }\n      }\n    }\n\n    &.card-outline {\n      .nav-item {\n        border-bottom: 0;\n\n        &:first-child .nav-link {\n          border-left: 0;\n          margin-left: 0;\n        }\n      }\n    }\n\n    .card-tools {\n      margin: .3rem .5rem;\n    }\n\n    &:not(.expanding-card).collapsed-card {\n      .card-header {\n        border-bottom: 0;\n\n        .nav-tabs {\n          border-bottom: 0;\n\n          .nav-item {\n            margin-bottom: 0;\n          }\n        }\n      }\n    }\n\n    &.expanding-card {\n      .card-header {\n        .nav-tabs {\n          .nav-item {\n            margin-bottom: -1px;\n          }\n        }\n      }\n    }\n  }\n\n  &.card-outline-tabs {\n    border-top: 0;\n\n    .card-header {\n      .nav-item {\n        &:first-child .nav-link {\n          border-left: 0;\n          margin-left: 0;\n        }\n      }\n\n      a {\n        border-top: 3px solid transparent;\n\n        &:hover {\n          border-top: 3px solid $nav-tabs-border-color;\n        }\n\n        &.active {\n          &:hover {\n            margin-top: 0;\n          }\n        }\n      }\n    }\n\n    .card-tools {\n      margin: .5rem .5rem .3rem;\n    }\n\n    &:not(.expanding-card).collapsed-card .card-header {\n      border-bottom: 0;\n\n      .nav-tabs {\n        border-bottom: 0;\n\n        .nav-item {\n          margin-bottom: 0;\n        }\n      }\n    }\n\n    &.expanding-card {\n      .card-header {\n        .nav-tabs {\n          .nav-item {\n            margin-bottom: -1px;\n          }\n        }\n      }\n    }\n  }\n\n}\n\n// Maximized Card Body Scroll fix\nhtml.maximized-card {\n  overflow: hidden;\n}\n\n// Add clearfix to header, body and footer\n.card-header,\n.card-body,\n.card-footer {\n  @include clearfix;\n}\n\n// Box header\n.card-header {\n  background-color: transparent;\n  border-bottom: 1px solid $card-border-color;\n  padding: (($card-spacer-y / 2) * 2) $card-spacer-x;\n  position: relative;\n\n  @if $enable-rounded {\n    @include border-top-radius($border-radius);\n  }\n\n  .collapsed-card & {\n    border-bottom: 0;\n  }\n\n  > .card-tools {\n    float: right;\n    margin-right: -$card-spacer-x / 2;\n\n    .input-group,\n    .nav,\n    .pagination {\n      margin-bottom: -$card-spacer-y / 2.5;\n      margin-top: -$card-spacer-y / 2.5;\n    }\n\n    [data-toggle='tooltip'] {\n      position: relative;\n    }\n  }\n}\n\n.card-title {\n  float: left;\n  font-size: $card-title-font-size;\n  font-weight: $card-title-font-weight;\n  margin: 0;\n}\n\n.card-text {\n  clear: both;\n}\n\n\n// Box Tools Buttons\n.btn-tool {\n  background: transparent;\n  color: $gray-500;\n  font-size: $font-size-sm;\n  margin: -(($card-spacer-y / 2) * 2) 0;\n  padding: .25rem .5rem;\n\n  .btn-group.show &,\n  &:hover {\n    color: $gray-700;\n  }\n\n  .show &,\n  &:focus {\n    box-shadow: none !important;\n  }\n}\n\n.text-sm {\n  .card-title {\n    font-size: $card-title-font-size-sm;\n  }\n\n  .nav-link {\n    padding: $card-nav-link-padding-sm-y $card-nav-link-padding-sm-x;\n  }\n}\n\n// Box Body\n.card-body {\n  // @include border-radius-sides(0, 0, $border-radius, $border-radius);\n  // .no-header & {\n  //   @include border-top-radius($border-radius);\n  // }\n\n  // Tables within the box body\n  > .table {\n    margin-bottom: 0;\n\n    > thead > tr > th,\n    > thead > tr > td {\n      border-top-width: 0;\n    }\n  }\n\n  // Calendar within the box body\n  .fc {\n    margin-top: 5px;\n  }\n\n  .full-width-chart {\n    margin: -19px;\n  }\n\n  &.p-0 .full-width-chart {\n    margin: -9px;\n  }\n}\n\n.chart-legend {\n  @include list-unstyled;\n  margin: 10px 0;\n\n  > li {\n    @media (max-width: map-get($grid-breakpoints, sm)) {\n      float: left;\n      margin-right: 10px;\n    }\n  }\n}\n\n// Comment Box\n.card-comments {\n  background: $gray-100;\n\n  .card-comment {\n    @include clearfix;\n    border-bottom: 1px solid $gray-200;\n    padding: 8px 0;\n\n    &:last-of-type {\n      border-bottom: 0;\n    }\n\n    &:first-of-type {\n      padding-top: 0;\n    }\n\n    img {\n      height: $card-img-size;\n      width: $card-img-size;\n      float: left;\n    }\n  }\n\n  .comment-text {\n    color: lighten($gray-700, 20%);\n    margin-left: 40px;\n  }\n\n  .username {\n    color: $gray-700;\n    display: block;\n    font-weight: 600;\n  }\n\n  .text-muted {\n    font-size: 12px;\n    font-weight: 400;\n  }\n}\n\n// Widgets\n//-----------\n\n// Widget: TODO LIST\n.todo-list {\n  list-style: none;\n  margin: 0;\n  overflow: auto;\n  padding: 0;\n\n  // Todo list element\n  > li {\n    @include border-radius(2px);\n    background: $gray-100;\n    border-left: 2px solid $gray-200;\n    color: $gray-700;\n    margin-bottom: 2px;\n    padding: 10px;\n\n    &:last-of-type {\n      margin-bottom: 0;\n    }\n\n    > input[type='checkbox'] {\n      margin: 0 10px 0 5px;\n    }\n\n    .text {\n      display: inline-block;\n      font-weight: 600;\n      margin-left: 5px;\n    }\n\n    // Time labels\n    .badge {\n      font-size: .7rem;\n      margin-left: 10px;\n    }\n\n    // Tools and options box\n    .tools {\n      color: theme-color('danger');\n      display: none;\n      float: right;\n\n      // icons\n      > .fa,\n      > .fas,\n      > .far,\n      > .fab,\n      > .glyphicon,\n      > .ion {\n        cursor: pointer;\n        margin-right: 5px;\n      }\n    }\n\n    &:hover .tools {\n      display: inline-block;\n    }\n\n    &.done {\n      color: darken($gray-500, 25%);\n\n      .text {\n        font-weight: 500;\n        text-decoration: line-through;\n      }\n\n      .badge {\n        background: $gray-500 !important;\n      }\n    }\n  }\n\n  // Color variants\n  @each $name, $color in $theme-colors {\n    .#{$name} {\n      border-left-color: $color;\n    }\n  }\n\n  @each $name, $color in $colors {\n    .#{$name} {\n      border-left-color: $color;\n    }\n  }\n\n  .handle {\n    cursor: move;\n    display: inline-block;\n    margin: 0 5px;\n  }\n}\n\n// END TODO WIDGET\n\n// Input in box\n.card-input {\n  max-width: 200px;\n}\n\n// Nav Tabs override\n.card-default {\n  .nav-item {\n    &:first-child .nav-link {\n      border-left: 0;\n    }\n  }\n}\n", "@mixin clearfix() {\n  &::after {\n    display: block;\n    clear: both;\n    content: \"\";\n  }\n}\n", "// Lists\n\n// Unstyled keeps list items block level, just removes default browser padding and list-style\n@mixin list-unstyled() {\n  padding-left: 0;\n  list-style: none;\n}\n", "//\n// Component: Modals\n//\n\n// Overlay\n.modal-dialog {\n  .overlay {\n    background-color: $black;\n    display: block;\n    height: 100%;\n    left: 0;\n    opacity: .7;\n    position: absolute;\n    top: 0;\n    width: 100%;\n    z-index: ($zindex-modal + 2);\n  }\n}\n\n\n// BG Color Variations Fixes\n.modal-content {\n  &.bg-warning {\n    .modal-header,\n    .modal-footer {\n      border-color: $gray-800;\n    }\n  }\n\n  &.bg-primary,\n  &.bg-secondary,\n  &.bg-info,\n  &.bg-danger,\n  &.bg-success, {\n    .close {\n      color: $white;\n      text-shadow: 0 1px 0 #000;\n    }\n  }\n}\n", "//\n// Component: Toasts\n//\n\n.toasts-top-right {\n  position: absolute;\n  right: 0;\n  top: 0;\n  z-index: $zindex-toasts;\n\n  &.fixed {\n    position: fixed;\n  }\n}\n\n.toasts-top-left {\n  left: 0;\n  position: absolute;\n  top: 0;\n  z-index: $zindex-toasts;\n\n  &.fixed {\n    position: fixed;\n  }\n}\n\n.toasts-bottom-right {\n  bottom: 0;\n  position: absolute;\n  right: 0;\n  z-index: $zindex-toasts;\n\n  &.fixed {\n    position: fixed;\n  }\n}\n\n.toasts-bottom-left {\n  bottom: 0;\n  left: 0;\n  position: absolute;\n  z-index: $zindex-toasts;\n\n  &.fixed {\n    position: fixed;\n  }\n}\n\n.toast {\n  @each $name, $color in $theme-colors {\n    @include toast-variant($name, $color);\n  }\n  @each $name, $color in $colors {\n    @include toast-variant($name, $color);\n  }\n}\n", "//\n// Mixins: Toasts\n//\n\n// Toast Variant\n@mixin toast-variant($name, $color) {\n  &.bg-#{$name} {\n    background: rgba($color, .9) !important;\n    @if (color-yiq($color) == $yiq-text-light) {\n\n      .close {\n        color: color-yiq($color);\n        text-shadow: 0 1px 0 #000;\n      }\n    }\n\n    .toast-header {\n      background: rgba($color, .85);\n      color: color-yiq($color);\n    }\n  }\n}\n\n", "//\n// Component: Button\n//\n\n.btn {\n  &.disabled,\n  &:disabled {\n    cursor: not-allowed;\n  }\n\n  // Flat buttons\n  &.btn-flat {\n    @include border-radius(0);\n    border-width: 1px;\n    box-shadow: none;\n  }\n\n  // input file btn\n  &.btn-file {\n    overflow: hidden;\n    position: relative;\n\n    > input[type='file'] {\n      background: $white;\n      cursor: inherit;\n      display: block;\n      font-size: 100px;\n      min-height: 100%;\n      min-width: 100%;\n      opacity: 0;\n      outline: none;\n      position: absolute;\n      right: 0;\n      text-align: right;\n      top: 0;\n    }\n  }\n\n  .text-sm & {\n    font-size: $font-size-sm !important;\n  }\n}\n\n// Button color variations\n.btn-default {\n  background-color: $button-default-background-color;\n  border-color: $button-default-border-color;\n  color: $button-default-color;\n\n  &:hover,\n  &:active,\n  &.hover {\n    background-color: darken($button-default-background-color, 5%);\n    color: darken($button-default-color, 10%);\n  }\n}\n\n// Application buttons\n.btn-app {\n  @include border-radius(3px);\n  background-color: $button-default-background-color;\n  border: 1px solid $button-default-border-color;\n  color: $gray-600;\n  font-size: 12px;\n  height: 60px;\n  margin: 0 0 10px 10px;\n  min-width: 80px;\n  padding: 15px 5px;\n  position: relative;\n  text-align: center;\n\n  // Icons within the btn\n  > .fa,\n  > .fas,\n  > .far,\n  > .fab,\n  > .glyphicon,\n  > .ion {\n    display: block;\n    font-size: 20px;\n  }\n\n  &:hover {\n    background: $button-default-background-color;\n    border-color: darken($button-default-border-color, 20%);\n    color: $button-default-color;\n  }\n\n  &:active,\n  &:focus {\n    @include box-shadow(inset 0 3px 5px rgba($black, 0.125));\n  }\n\n  // The badge\n  > .badge {\n    font-size: 10px;\n    font-weight: 400;\n    position: absolute;\n    right: -10px;\n    top: -3px;\n  }\n}\n\n// Extra Button Size\n\n.btn-xs {\n  @include button-size($button-padding-y-xs, $button-padding-x-xs, $button-font-size-xs, $button-line-height-xs, $button-border-radius-xs);\n}\n", "// Button variants\n//\n// Easily pump out default styles, as well as :hover, :focus, :active,\n// and disabled options for all buttons\n\n@mixin button-variant($background, $border, $hover-background: darken($background, 7.5%), $hover-border: darken($border, 10%), $active-background: darken($background, 10%), $active-border: darken($border, 12.5%)) {\n  color: color-yiq($background);\n  @include gradient-bg($background);\n  border-color: $border;\n  @include box-shadow($btn-box-shadow);\n\n  @include hover() {\n    color: color-yiq($hover-background);\n    @include gradient-bg($hover-background);\n    border-color: $hover-border;\n  }\n\n  &:focus,\n  &.focus {\n    color: color-yiq($hover-background);\n    @include gradient-bg($hover-background);\n    border-color: $hover-border;\n    // Avoid using mixin so we can pass custom focus shadow properly\n    @if $enable-shadows {\n      box-shadow: $btn-box-shadow, 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n    } @else {\n      box-shadow: 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n    }\n  }\n\n  // Disabled comes first so active can properly restyle\n  &.disabled,\n  &:disabled {\n    color: color-yiq($background);\n    background-color: $background;\n    border-color: $border;\n    // Remove CSS gradients if they're enabled\n    @if $enable-gradients {\n      background-image: none;\n    }\n  }\n\n  &:not(:disabled):not(.disabled):active,\n  &:not(:disabled):not(.disabled).active,\n  .show > &.dropdown-toggle {\n    color: color-yiq($active-background);\n    background-color: $active-background;\n    @if $enable-gradients {\n      background-image: none; // Remove the gradient for the pressed/active state\n    }\n    border-color: $active-border;\n\n    &:focus {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      @if $enable-shadows and $btn-active-box-shadow != none {\n        box-shadow: $btn-active-box-shadow, 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n      } @else {\n        box-shadow: 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n      }\n    }\n  }\n}\n\n@mixin button-outline-variant($color, $color-hover: color-yiq($color), $active-background: $color, $active-border: $color) {\n  color: $color;\n  border-color: $color;\n\n  @include hover() {\n    color: $color-hover;\n    background-color: $active-background;\n    border-color: $active-border;\n  }\n\n  &:focus,\n  &.focus {\n    box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);\n  }\n\n  &.disabled,\n  &:disabled {\n    color: $color;\n    background-color: transparent;\n  }\n\n  &:not(:disabled):not(.disabled):active,\n  &:not(:disabled):not(.disabled).active,\n  .show > &.dropdown-toggle {\n    color: color-yiq($active-background);\n    background-color: $active-background;\n    border-color: $active-border;\n\n    &:focus {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      @if $enable-shadows and $btn-active-box-shadow != none {\n        box-shadow: $btn-active-box-shadow, 0 0 0 $btn-focus-width rgba($color, .5);\n      } @else {\n        box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);\n      }\n    }\n  }\n}\n\n// Button sizes\n@mixin button-size($padding-y, $padding-x, $font-size, $line-height, $border-radius) {\n  padding: $padding-y $padding-x;\n  @include font-size($font-size);\n  line-height: $line-height;\n  // Manually declare to provide an override to the browser default\n  @include border-radius($border-radius, 0);\n}\n", "//\n// Component: Callout\n//\n\n// Base styles (regardless of theme)\n.callout {\n  @if $enable-rounded {\n    @include border-radius($border-radius);\n  }\n\n  @if $enable-shadows {\n    box-shadow: map-get($elevations, 1);\n  } @else {\n    border: 1px solid $gray-300;\n  }\n\n  background-color: $white;\n  border-left: 5px solid $gray-200;\n  margin-bottom: map-get($spacers, 3);\n  padding: 1rem;\n\n  a {\n    color: $gray-700;\n    text-decoration: underline;\n\n    &:hover {\n      color: $gray-200;\n    }\n  }\n\n  p:last-child {\n    margin-bottom: 0;\n  }\n\n  // Themes for different contexts\n  &.callout-danger {\n    border-left-color: darken(theme-color('danger'), 10%);\n  }\n\n  &.callout-warning {\n    border-left-color: darken(theme-color('warning'), 10%);\n  }\n\n  &.callout-info {\n    border-left-color: darken(theme-color('info'), 10%);\n  }\n\n  &.callout-success {\n    border-left-color: darken(theme-color('success'), 10%);\n  }\n}\n", "//\n// Component: Alert\n//\n\n.alert {\n  .icon {\n    margin-right: 10px;\n  }\n\n  .close {\n    color: $black;\n    opacity: .2;\n\n    &:hover {\n      opacity: .5;\n    }\n  }\n\n  a {\n    color: $white;\n    text-decoration: underline;\n  }\n}\n\n//<PERSON><PERSON> Variants\n@each $color, $value in $theme-colors {\n  .alert-#{$color} {\n    color: color-yiq($value);\n    background: $value;\n    border-color: darken($value, 5%);\n  }\n\n  .alert-default-#{$color} {\n    @include alert-variant(theme-color-level($color, $alert-bg-level), theme-color-level($color, $alert-border-level), theme-color-level($color, $alert-color-level));\n  }\n}\n", "@mixin alert-variant($background, $border, $color) {\n  color: $color;\n  @include gradient-bg($background);\n  border-color: $border;\n\n  hr {\n    border-top-color: darken($border, 5%);\n  }\n\n  .alert-link {\n    color: darken($color, 10%);\n  }\n}\n", "// Bootstrap functions\n//\n// Utility mixins and functions for evaluating source code across our variables, maps, and mixins.\n\n// Ascending\n// Used to evaluate Sass maps like our grid breakpoints.\n@mixin _assert-ascending($map, $map-name) {\n  $prev-key: null;\n  $prev-num: null;\n  @each $key, $num in $map {\n    @if $prev-num == null or unit($num) == \"%\" or unit($prev-num) == \"%\" {\n      // Do nothing\n    } @else if not comparable($prev-num, $num) {\n      @warn \"Potentially invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} whose unit makes it incomparable to #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    } @else if $prev-num >= $num {\n      @warn \"Invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} which isn't greater than #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    }\n    $prev-key: $key;\n    $prev-num: $num;\n  }\n}\n\n// Starts at zero\n// Used to ensure the min-width of the lowest breakpoint starts at 0.\n@mixin _assert-starts-at-zero($map, $map-name: \"$grid-breakpoints\") {\n  $values: map-values($map);\n  $first-value: nth($values, 1);\n  @if $first-value != 0 {\n    @warn \"First breakpoint in #{$map-name} must start at 0, but starts at #{$first-value}.\";\n  }\n}\n\n// Replace `$search` with `$replace` in `$string`\n// Used on our SVG icon backgrounds for custom forms.\n//\n// <AUTHOR> Giraudel\n// @param {String} $string - Initial string\n// @param {String} $search - Substring to replace\n// @param {String} $replace ('') - New value\n// @return {String} - Updated string\n@function str-replace($string, $search, $replace: \"\") {\n  $index: str-index($string, $search);\n\n  @if $index {\n    @return str-slice($string, 1, $index - 1) + $replace + str-replace(str-slice($string, $index + str-length($search)), $search, $replace);\n  }\n\n  @return $string;\n}\n\n// See https://codepen.io/kevinweber/pen/dXWoRw\n@function escape-svg($string) {\n  @if str-index($string, \"data:image/svg+xml\") {\n    @each $char, $encoded in $escaped-characters {\n      $string: str-replace($string, $char, $encoded);\n    }\n  }\n\n  @return $string;\n}\n\n// Color contrast\n@function color-yiq($color, $dark: $yiq-text-dark, $light: $yiq-text-light) {\n  $r: red($color);\n  $g: green($color);\n  $b: blue($color);\n\n  $yiq: (($r * 299) + ($g * 587) + ($b * 114)) / 1000;\n\n  @if ($yiq >= $yiq-contrasted-threshold) {\n    @return $dark;\n  } @else {\n    @return $light;\n  }\n}\n\n// Retrieve color Sass maps\n@function color($key: \"blue\") {\n  @return map-get($colors, $key);\n}\n\n@function theme-color($key: \"primary\") {\n  @return map-get($theme-colors, $key);\n}\n\n@function gray($key: \"100\") {\n  @return map-get($grays, $key);\n}\n\n// Request a theme color level\n@function theme-color-level($color-name: \"primary\", $level: 0) {\n  $color: theme-color($color-name);\n  $color-base: if($level > 0, $black, $white);\n  $level: abs($level);\n\n  @return mix($color-base, $color, $level * $theme-color-interval);\n}\n\n// Return valid calc\n@function add($value1, $value2, $return-calc: true) {\n  @if $value1 == null {\n    @return $value2;\n  }\n\n  @if $value2 == null {\n    @return $value1;\n  }\n\n  @if type-of($value1) == number and type-of($value2) == number and comparable($value1, $value2) {\n    @return $value1 + $value2;\n  }\n\n  @return if($return-calc == true, calc(#{$value1} + #{$value2}), $value1 + unquote(\" + \") + $value2);\n}\n\n@function subtract($value1, $value2, $return-calc: true) {\n  @if $value1 == null and $value2 == null {\n    @return null;\n  }\n\n  @if $value1 == null {\n    @return -$value2;\n  }\n\n  @if $value2 == null {\n    @return $value1;\n  }\n\n  @if type-of($value1) == number and type-of($value2) == number and comparable($value1, $value2) {\n    @return $value1 - $value2;\n  }\n\n  @return if($return-calc == true, calc(#{$value1} - #{$value2}), $value1 + unquote(\" - \") + $value2);\n}\n", "//\n// Component: Table\n//\n\n.table {\n  &:not(.table-dark) {\n    color: inherit;\n  }\n\n  // fixed table head\n  &.table-head-fixed {\n    thead tr:nth-child(1) th {\n      background-color: $white;\n      border-bottom: 0;\n      box-shadow: inset 0 1px 0 $table-border-color,\n                  inset 0 -1px 0 $table-border-color;\n      position: sticky;\n      top: 0;\n      z-index: 10;\n    }\n\n    &.table-dark {\n      thead tr {\n        &:nth-child(1) th {\n          background-color: $table-dark-bg;\n          box-shadow: inset 0 1px 0 $table-dark-border-color,\n                      inset 0 -1px 0 $table-dark-border-color;\n        }\n      }\n    }\n  }\n\n  // no border\n  &.no-border {\n    &,\n    td,\n    th {\n      border: 0;\n    }\n  }\n\n  // .text-center in tables\n  &.text-center {\n    &,\n    td,\n    th {\n      text-align: center;\n    }\n  }\n\n  &.table-valign-middle {\n    thead > tr > th,\n    thead > tr > td,\n    tbody > tr > th,\n    tbody > tr > td {\n      vertical-align: middle;\n    }\n  }\n\n  .card-body.p-0 & {\n    thead > tr > th,\n    thead > tr > td,\n    tbody > tr > th,\n    tbody > tr > td {\n      &:first-of-type {\n        padding-left: map-get($spacers, 4);\n      }\n\n      &:last-of-type {\n        padding-right: map-get($spacers, 4);\n      }\n    }\n  }\n}\n", "//\n// Component: Carousel\n//\n\n.carousel-control {\n  &.left,\n  &.right {\n    background-image: none;\n  }\n\n  > .fa,\n  > .fas,\n  > .far,\n  > .fab,\n  > .glyphicon,\n  > .ion {\n    display: inline-block;\n    font-size: 40px;\n    margin-top: -20px;\n    position: absolute;\n    top: 50%;\n    z-index: 5;\n  }\n}\n"]}